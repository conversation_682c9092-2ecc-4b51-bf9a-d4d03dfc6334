('c:\\Users\\<USER>\\Desktop\\Mes_projets\\WritingToolsClean\\Windows_and_Linux\\build\\Writing '
 'Tools\\PYZ-00.pyz',
 [('PySide6',
   'c:\\Users\\<USER>\\Desktop\\Mes_projets\\WritingToolsClean\\Windows_and_Linux\\myvenv\\Lib\\site-packages\\PySide6\\__init__.py',
   'PYMODULE'),
  ('PySide6.support',
   'c:\\Users\\<USER>\\Desktop\\Mes_projets\\WritingToolsClean\\Windows_and_Linux\\myvenv\\Lib\\site-packages\\PySide6\\support\\__init__.py',
   'PYMODULE'),
  ('PySide6.support.deprecated',
   'c:\\Users\\<USER>\\Desktop\\Mes_projets\\WritingToolsClean\\Windows_and_Linux\\myvenv\\Lib\\site-packages\\PySide6\\support\\deprecated.py',
   'PYMODULE'),
  ('WritingToolApp',
   'c:\\Users\\<USER>\\Desktop\\Mes_projets\\WritingToolsClean\\Windows_and_Linux\\WritingToolApp.py',
   'PYMODULE'),
  ('__future__',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\__future__.py',
   'PYMODULE'),
  ('_aix_support',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\_aix_support.py',
   'PYMODULE'),
  ('_compat_pickle',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\_compat_pickle.py',
   'PYMODULE'),
  ('_compression',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\_compression.py',
   'PYMODULE'),
  ('_distutils_hack',
   'c:\\Users\\<USER>\\Desktop\\Mes_projets\\WritingToolsClean\\Windows_and_Linux\\myvenv\\Lib\\site-packages\\_distutils_hack\\__init__.py',
   'PYMODULE'),
  ('_distutils_hack.override',
   'c:\\Users\\<USER>\\Desktop\\Mes_projets\\WritingToolsClean\\Windows_and_Linux\\myvenv\\Lib\\site-packages\\_distutils_hack\\override.py',
   'PYMODULE'),
  ('_py_abc',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\_py_abc.py',
   'PYMODULE'),
  ('_pydatetime',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\_pydatetime.py',
   'PYMODULE'),
  ('_pydecimal',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\_pydecimal.py',
   'PYMODULE'),
  ('_pyi_rth_utils',
   'c:\\Users\\<USER>\\Desktop\\Mes_projets\\WritingToolsClean\\Windows_and_Linux\\myvenv\\Lib\\site-packages\\PyInstaller\\fake-modules\\_pyi_rth_utils\\__init__.py',
   'PYMODULE'),
  ('_pyi_rth_utils.qt',
   'c:\\Users\\<USER>\\Desktop\\Mes_projets\\WritingToolsClean\\Windows_and_Linux\\myvenv\\Lib\\site-packages\\PyInstaller\\fake-modules\\_pyi_rth_utils\\qt.py',
   'PYMODULE'),
  ('_sitebuiltins',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\_sitebuiltins.py',
   'PYMODULE'),
  ('_strptime',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\_strptime.py',
   'PYMODULE'),
  ('_threading_local',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\_threading_local.py',
   'PYMODULE'),
  ('aiprovider',
   'c:\\Users\\<USER>\\Desktop\\Mes_projets\\WritingToolsClean\\Windows_and_Linux\\aiprovider.py',
   'PYMODULE'),
  ('annotated_types',
   'c:\\Users\\<USER>\\Desktop\\Mes_projets\\WritingToolsClean\\Windows_and_Linux\\myvenv\\Lib\\site-packages\\annotated_types\\__init__.py',
   'PYMODULE'),
  ('anyio',
   'c:\\Users\\<USER>\\Desktop\\Mes_projets\\WritingToolsClean\\Windows_and_Linux\\myvenv\\Lib\\site-packages\\anyio\\__init__.py',
   'PYMODULE'),
  ('anyio._backends',
   'c:\\Users\\<USER>\\Desktop\\Mes_projets\\WritingToolsClean\\Windows_and_Linux\\myvenv\\Lib\\site-packages\\anyio\\_backends\\__init__.py',
   'PYMODULE'),
  ('anyio._backends._asyncio',
   'c:\\Users\\<USER>\\Desktop\\Mes_projets\\WritingToolsClean\\Windows_and_Linux\\myvenv\\Lib\\site-packages\\anyio\\_backends\\_asyncio.py',
   'PYMODULE'),
  ('anyio._backends._trio',
   'c:\\Users\\<USER>\\Desktop\\Mes_projets\\WritingToolsClean\\Windows_and_Linux\\myvenv\\Lib\\site-packages\\anyio\\_backends\\_trio.py',
   'PYMODULE'),
  ('anyio._core',
   'c:\\Users\\<USER>\\Desktop\\Mes_projets\\WritingToolsClean\\Windows_and_Linux\\myvenv\\Lib\\site-packages\\anyio\\_core\\__init__.py',
   'PYMODULE'),
  ('anyio._core._asyncio_selector_thread',
   'c:\\Users\\<USER>\\Desktop\\Mes_projets\\WritingToolsClean\\Windows_and_Linux\\myvenv\\Lib\\site-packages\\anyio\\_core\\_asyncio_selector_thread.py',
   'PYMODULE'),
  ('anyio._core._eventloop',
   'c:\\Users\\<USER>\\Desktop\\Mes_projets\\WritingToolsClean\\Windows_and_Linux\\myvenv\\Lib\\site-packages\\anyio\\_core\\_eventloop.py',
   'PYMODULE'),
  ('anyio._core._exceptions',
   'c:\\Users\\<USER>\\Desktop\\Mes_projets\\WritingToolsClean\\Windows_and_Linux\\myvenv\\Lib\\site-packages\\anyio\\_core\\_exceptions.py',
   'PYMODULE'),
  ('anyio._core._fileio',
   'c:\\Users\\<USER>\\Desktop\\Mes_projets\\WritingToolsClean\\Windows_and_Linux\\myvenv\\Lib\\site-packages\\anyio\\_core\\_fileio.py',
   'PYMODULE'),
  ('anyio._core._resources',
   'c:\\Users\\<USER>\\Desktop\\Mes_projets\\WritingToolsClean\\Windows_and_Linux\\myvenv\\Lib\\site-packages\\anyio\\_core\\_resources.py',
   'PYMODULE'),
  ('anyio._core._signals',
   'c:\\Users\\<USER>\\Desktop\\Mes_projets\\WritingToolsClean\\Windows_and_Linux\\myvenv\\Lib\\site-packages\\anyio\\_core\\_signals.py',
   'PYMODULE'),
  ('anyio._core._sockets',
   'c:\\Users\\<USER>\\Desktop\\Mes_projets\\WritingToolsClean\\Windows_and_Linux\\myvenv\\Lib\\site-packages\\anyio\\_core\\_sockets.py',
   'PYMODULE'),
  ('anyio._core._streams',
   'c:\\Users\\<USER>\\Desktop\\Mes_projets\\WritingToolsClean\\Windows_and_Linux\\myvenv\\Lib\\site-packages\\anyio\\_core\\_streams.py',
   'PYMODULE'),
  ('anyio._core._subprocesses',
   'c:\\Users\\<USER>\\Desktop\\Mes_projets\\WritingToolsClean\\Windows_and_Linux\\myvenv\\Lib\\site-packages\\anyio\\_core\\_subprocesses.py',
   'PYMODULE'),
  ('anyio._core._synchronization',
   'c:\\Users\\<USER>\\Desktop\\Mes_projets\\WritingToolsClean\\Windows_and_Linux\\myvenv\\Lib\\site-packages\\anyio\\_core\\_synchronization.py',
   'PYMODULE'),
  ('anyio._core._tasks',
   'c:\\Users\\<USER>\\Desktop\\Mes_projets\\WritingToolsClean\\Windows_and_Linux\\myvenv\\Lib\\site-packages\\anyio\\_core\\_tasks.py',
   'PYMODULE'),
  ('anyio._core._tempfile',
   'c:\\Users\\<USER>\\Desktop\\Mes_projets\\WritingToolsClean\\Windows_and_Linux\\myvenv\\Lib\\site-packages\\anyio\\_core\\_tempfile.py',
   'PYMODULE'),
  ('anyio._core._testing',
   'c:\\Users\\<USER>\\Desktop\\Mes_projets\\WritingToolsClean\\Windows_and_Linux\\myvenv\\Lib\\site-packages\\anyio\\_core\\_testing.py',
   'PYMODULE'),
  ('anyio._core._typedattr',
   'c:\\Users\\<USER>\\Desktop\\Mes_projets\\WritingToolsClean\\Windows_and_Linux\\myvenv\\Lib\\site-packages\\anyio\\_core\\_typedattr.py',
   'PYMODULE'),
  ('anyio.abc',
   'c:\\Users\\<USER>\\Desktop\\Mes_projets\\WritingToolsClean\\Windows_and_Linux\\myvenv\\Lib\\site-packages\\anyio\\abc\\__init__.py',
   'PYMODULE'),
  ('anyio.abc._eventloop',
   'c:\\Users\\<USER>\\Desktop\\Mes_projets\\WritingToolsClean\\Windows_and_Linux\\myvenv\\Lib\\site-packages\\anyio\\abc\\_eventloop.py',
   'PYMODULE'),
  ('anyio.abc._resources',
   'c:\\Users\\<USER>\\Desktop\\Mes_projets\\WritingToolsClean\\Windows_and_Linux\\myvenv\\Lib\\site-packages\\anyio\\abc\\_resources.py',
   'PYMODULE'),
  ('anyio.abc._sockets',
   'c:\\Users\\<USER>\\Desktop\\Mes_projets\\WritingToolsClean\\Windows_and_Linux\\myvenv\\Lib\\site-packages\\anyio\\abc\\_sockets.py',
   'PYMODULE'),
  ('anyio.abc._streams',
   'c:\\Users\\<USER>\\Desktop\\Mes_projets\\WritingToolsClean\\Windows_and_Linux\\myvenv\\Lib\\site-packages\\anyio\\abc\\_streams.py',
   'PYMODULE'),
  ('anyio.abc._subprocesses',
   'c:\\Users\\<USER>\\Desktop\\Mes_projets\\WritingToolsClean\\Windows_and_Linux\\myvenv\\Lib\\site-packages\\anyio\\abc\\_subprocesses.py',
   'PYMODULE'),
  ('anyio.abc._tasks',
   'c:\\Users\\<USER>\\Desktop\\Mes_projets\\WritingToolsClean\\Windows_and_Linux\\myvenv\\Lib\\site-packages\\anyio\\abc\\_tasks.py',
   'PYMODULE'),
  ('anyio.abc._testing',
   'c:\\Users\\<USER>\\Desktop\\Mes_projets\\WritingToolsClean\\Windows_and_Linux\\myvenv\\Lib\\site-packages\\anyio\\abc\\_testing.py',
   'PYMODULE'),
  ('anyio.from_thread',
   'c:\\Users\\<USER>\\Desktop\\Mes_projets\\WritingToolsClean\\Windows_and_Linux\\myvenv\\Lib\\site-packages\\anyio\\from_thread.py',
   'PYMODULE'),
  ('anyio.lowlevel',
   'c:\\Users\\<USER>\\Desktop\\Mes_projets\\WritingToolsClean\\Windows_and_Linux\\myvenv\\Lib\\site-packages\\anyio\\lowlevel.py',
   'PYMODULE'),
  ('anyio.streams',
   'c:\\Users\\<USER>\\Desktop\\Mes_projets\\WritingToolsClean\\Windows_and_Linux\\myvenv\\Lib\\site-packages\\anyio\\streams\\__init__.py',
   'PYMODULE'),
  ('anyio.streams.memory',
   'c:\\Users\\<USER>\\Desktop\\Mes_projets\\WritingToolsClean\\Windows_and_Linux\\myvenv\\Lib\\site-packages\\anyio\\streams\\memory.py',
   'PYMODULE'),
  ('anyio.streams.stapled',
   'c:\\Users\\<USER>\\Desktop\\Mes_projets\\WritingToolsClean\\Windows_and_Linux\\myvenv\\Lib\\site-packages\\anyio\\streams\\stapled.py',
   'PYMODULE'),
  ('anyio.streams.tls',
   'c:\\Users\\<USER>\\Desktop\\Mes_projets\\WritingToolsClean\\Windows_and_Linux\\myvenv\\Lib\\site-packages\\anyio\\streams\\tls.py',
   'PYMODULE'),
  ('anyio.to_thread',
   'c:\\Users\\<USER>\\Desktop\\Mes_projets\\WritingToolsClean\\Windows_and_Linux\\myvenv\\Lib\\site-packages\\anyio\\to_thread.py',
   'PYMODULE'),
  ('argparse',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\argparse.py',
   'PYMODULE'),
  ('ast',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\ast.py',
   'PYMODULE'),
  ('asyncio',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\asyncio\\__init__.py',
   'PYMODULE'),
  ('asyncio.base_events',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\asyncio\\base_events.py',
   'PYMODULE'),
  ('asyncio.base_futures',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\asyncio\\base_futures.py',
   'PYMODULE'),
  ('asyncio.base_subprocess',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\asyncio\\base_subprocess.py',
   'PYMODULE'),
  ('asyncio.base_tasks',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\asyncio\\base_tasks.py',
   'PYMODULE'),
  ('asyncio.constants',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\asyncio\\constants.py',
   'PYMODULE'),
  ('asyncio.coroutines',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\asyncio\\coroutines.py',
   'PYMODULE'),
  ('asyncio.events',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\asyncio\\events.py',
   'PYMODULE'),
  ('asyncio.exceptions',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\asyncio\\exceptions.py',
   'PYMODULE'),
  ('asyncio.format_helpers',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\asyncio\\format_helpers.py',
   'PYMODULE'),
  ('asyncio.futures',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\asyncio\\futures.py',
   'PYMODULE'),
  ('asyncio.locks',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\asyncio\\locks.py',
   'PYMODULE'),
  ('asyncio.log',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\asyncio\\log.py',
   'PYMODULE'),
  ('asyncio.mixins',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\asyncio\\mixins.py',
   'PYMODULE'),
  ('asyncio.proactor_events',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\asyncio\\proactor_events.py',
   'PYMODULE'),
  ('asyncio.protocols',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\asyncio\\protocols.py',
   'PYMODULE'),
  ('asyncio.queues',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\asyncio\\queues.py',
   'PYMODULE'),
  ('asyncio.runners',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\asyncio\\runners.py',
   'PYMODULE'),
  ('asyncio.selector_events',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\asyncio\\selector_events.py',
   'PYMODULE'),
  ('asyncio.sslproto',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\asyncio\\sslproto.py',
   'PYMODULE'),
  ('asyncio.staggered',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\asyncio\\staggered.py',
   'PYMODULE'),
  ('asyncio.streams',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\asyncio\\streams.py',
   'PYMODULE'),
  ('asyncio.subprocess',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\asyncio\\subprocess.py',
   'PYMODULE'),
  ('asyncio.taskgroups',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\asyncio\\taskgroups.py',
   'PYMODULE'),
  ('asyncio.tasks',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\asyncio\\tasks.py',
   'PYMODULE'),
  ('asyncio.threads',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\asyncio\\threads.py',
   'PYMODULE'),
  ('asyncio.timeouts',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\asyncio\\timeouts.py',
   'PYMODULE'),
  ('asyncio.transports',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\asyncio\\transports.py',
   'PYMODULE'),
  ('asyncio.trsock',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\asyncio\\trsock.py',
   'PYMODULE'),
  ('asyncio.unix_events',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\asyncio\\unix_events.py',
   'PYMODULE'),
  ('asyncio.windows_events',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\asyncio\\windows_events.py',
   'PYMODULE'),
  ('asyncio.windows_utils',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\asyncio\\windows_utils.py',
   'PYMODULE'),
  ('backports',
   'c:\\Users\\<USER>\\Desktop\\Mes_projets\\WritingToolsClean\\Windows_and_Linux\\myvenv\\Lib\\site-packages\\setuptools\\_vendor\\backports\\__init__.py',
   'PYMODULE'),
  ('base64',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\base64.py',
   'PYMODULE'),
  ('bdb',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\bdb.py',
   'PYMODULE'),
  ('bisect',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\bisect.py',
   'PYMODULE'),
  ('bz2',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\bz2.py',
   'PYMODULE'),
  ('cachetools',
   'c:\\Users\\<USER>\\Desktop\\Mes_projets\\WritingToolsClean\\Windows_and_Linux\\myvenv\\Lib\\site-packages\\cachetools\\__init__.py',
   'PYMODULE'),
  ('cachetools._decorators',
   'c:\\Users\\<USER>\\Desktop\\Mes_projets\\WritingToolsClean\\Windows_and_Linux\\myvenv\\Lib\\site-packages\\cachetools\\_decorators.py',
   'PYMODULE'),
  ('cachetools.keys',
   'c:\\Users\\<USER>\\Desktop\\Mes_projets\\WritingToolsClean\\Windows_and_Linux\\myvenv\\Lib\\site-packages\\cachetools\\keys.py',
   'PYMODULE'),
  ('calendar',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\calendar.py',
   'PYMODULE'),
  ('certifi',
   'c:\\Users\\<USER>\\Desktop\\Mes_projets\\WritingToolsClean\\Windows_and_Linux\\myvenv\\Lib\\site-packages\\certifi\\__init__.py',
   'PYMODULE'),
  ('certifi.core',
   'c:\\Users\\<USER>\\Desktop\\Mes_projets\\WritingToolsClean\\Windows_and_Linux\\myvenv\\Lib\\site-packages\\certifi\\core.py',
   'PYMODULE'),
  ('charset_normalizer',
   'c:\\Users\\<USER>\\Desktop\\Mes_projets\\WritingToolsClean\\Windows_and_Linux\\myvenv\\Lib\\site-packages\\charset_normalizer\\__init__.py',
   'PYMODULE'),
  ('charset_normalizer.api',
   'c:\\Users\\<USER>\\Desktop\\Mes_projets\\WritingToolsClean\\Windows_and_Linux\\myvenv\\Lib\\site-packages\\charset_normalizer\\api.py',
   'PYMODULE'),
  ('charset_normalizer.cd',
   'c:\\Users\\<USER>\\Desktop\\Mes_projets\\WritingToolsClean\\Windows_and_Linux\\myvenv\\Lib\\site-packages\\charset_normalizer\\cd.py',
   'PYMODULE'),
  ('charset_normalizer.constant',
   'c:\\Users\\<USER>\\Desktop\\Mes_projets\\WritingToolsClean\\Windows_and_Linux\\myvenv\\Lib\\site-packages\\charset_normalizer\\constant.py',
   'PYMODULE'),
  ('charset_normalizer.legacy',
   'c:\\Users\\<USER>\\Desktop\\Mes_projets\\WritingToolsClean\\Windows_and_Linux\\myvenv\\Lib\\site-packages\\charset_normalizer\\legacy.py',
   'PYMODULE'),
  ('charset_normalizer.models',
   'c:\\Users\\<USER>\\Desktop\\Mes_projets\\WritingToolsClean\\Windows_and_Linux\\myvenv\\Lib\\site-packages\\charset_normalizer\\models.py',
   'PYMODULE'),
  ('charset_normalizer.utils',
   'c:\\Users\\<USER>\\Desktop\\Mes_projets\\WritingToolsClean\\Windows_and_Linux\\myvenv\\Lib\\site-packages\\charset_normalizer\\utils.py',
   'PYMODULE'),
  ('charset_normalizer.version',
   'c:\\Users\\<USER>\\Desktop\\Mes_projets\\WritingToolsClean\\Windows_and_Linux\\myvenv\\Lib\\site-packages\\charset_normalizer\\version.py',
   'PYMODULE'),
  ('cmd',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\cmd.py',
   'PYMODULE'),
  ('code',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\code.py',
   'PYMODULE'),
  ('codeop',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\codeop.py',
   'PYMODULE'),
  ('colorama',
   'c:\\Users\\<USER>\\Desktop\\Mes_projets\\WritingToolsClean\\Windows_and_Linux\\myvenv\\Lib\\site-packages\\colorama\\__init__.py',
   'PYMODULE'),
  ('colorama.ansi',
   'c:\\Users\\<USER>\\Desktop\\Mes_projets\\WritingToolsClean\\Windows_and_Linux\\myvenv\\Lib\\site-packages\\colorama\\ansi.py',
   'PYMODULE'),
  ('colorama.ansitowin32',
   'c:\\Users\\<USER>\\Desktop\\Mes_projets\\WritingToolsClean\\Windows_and_Linux\\myvenv\\Lib\\site-packages\\colorama\\ansitowin32.py',
   'PYMODULE'),
  ('colorama.initialise',
   'c:\\Users\\<USER>\\Desktop\\Mes_projets\\WritingToolsClean\\Windows_and_Linux\\myvenv\\Lib\\site-packages\\colorama\\initialise.py',
   'PYMODULE'),
  ('colorama.win32',
   'c:\\Users\\<USER>\\Desktop\\Mes_projets\\WritingToolsClean\\Windows_and_Linux\\myvenv\\Lib\\site-packages\\colorama\\win32.py',
   'PYMODULE'),
  ('colorama.winterm',
   'c:\\Users\\<USER>\\Desktop\\Mes_projets\\WritingToolsClean\\Windows_and_Linux\\myvenv\\Lib\\site-packages\\colorama\\winterm.py',
   'PYMODULE'),
  ('colorsys',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\colorsys.py',
   'PYMODULE'),
  ('concurrent',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\concurrent\\__init__.py',
   'PYMODULE'),
  ('concurrent.futures',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\concurrent\\futures\\__init__.py',
   'PYMODULE'),
  ('concurrent.futures._base',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\concurrent\\futures\\_base.py',
   'PYMODULE'),
  ('concurrent.futures.process',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\concurrent\\futures\\process.py',
   'PYMODULE'),
  ('concurrent.futures.thread',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\concurrent\\futures\\thread.py',
   'PYMODULE'),
  ('configparser',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\configparser.py',
   'PYMODULE'),
  ('contextlib',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\contextlib.py',
   'PYMODULE'),
  ('contextvars',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\contextvars.py',
   'PYMODULE'),
  ('copy',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\copy.py',
   'PYMODULE'),
  ('csv',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\csv.py',
   'PYMODULE'),
  ('ctypes',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\ctypes\\__init__.py',
   'PYMODULE'),
  ('ctypes._aix',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\ctypes\\_aix.py',
   'PYMODULE'),
  ('ctypes._endian',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\ctypes\\_endian.py',
   'PYMODULE'),
  ('ctypes.macholib',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\ctypes\\macholib\\__init__.py',
   'PYMODULE'),
  ('ctypes.macholib.dyld',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\ctypes\\macholib\\dyld.py',
   'PYMODULE'),
  ('ctypes.macholib.dylib',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\ctypes\\macholib\\dylib.py',
   'PYMODULE'),
  ('ctypes.macholib.framework',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\ctypes\\macholib\\framework.py',
   'PYMODULE'),
  ('ctypes.util',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\ctypes\\util.py',
   'PYMODULE'),
  ('ctypes.wintypes',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\ctypes\\wintypes.py',
   'PYMODULE'),
  ('darkdetect',
   'c:\\Users\\<USER>\\Desktop\\Mes_projets\\WritingToolsClean\\Windows_and_Linux\\myvenv\\Lib\\site-packages\\darkdetect\\__init__.py',
   'PYMODULE'),
  ('darkdetect._dummy',
   'c:\\Users\\<USER>\\Desktop\\Mes_projets\\WritingToolsClean\\Windows_and_Linux\\myvenv\\Lib\\site-packages\\darkdetect\\_dummy.py',
   'PYMODULE'),
  ('darkdetect._linux_detect',
   'c:\\Users\\<USER>\\Desktop\\Mes_projets\\WritingToolsClean\\Windows_and_Linux\\myvenv\\Lib\\site-packages\\darkdetect\\_linux_detect.py',
   'PYMODULE'),
  ('darkdetect._mac_detect',
   'c:\\Users\\<USER>\\Desktop\\Mes_projets\\WritingToolsClean\\Windows_and_Linux\\myvenv\\Lib\\site-packages\\darkdetect\\_mac_detect.py',
   'PYMODULE'),
  ('darkdetect._windows_detect',
   'c:\\Users\\<USER>\\Desktop\\Mes_projets\\WritingToolsClean\\Windows_and_Linux\\myvenv\\Lib\\site-packages\\darkdetect\\_windows_detect.py',
   'PYMODULE'),
  ('dataclasses',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\dataclasses.py',
   'PYMODULE'),
  ('datetime',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\datetime.py',
   'PYMODULE'),
  ('decimal',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\decimal.py',
   'PYMODULE'),
  ('difflib',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\difflib.py',
   'PYMODULE'),
  ('dis',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\dis.py',
   'PYMODULE'),
  ('distro',
   'c:\\Users\\<USER>\\Desktop\\Mes_projets\\WritingToolsClean\\Windows_and_Linux\\myvenv\\Lib\\site-packages\\distro\\__init__.py',
   'PYMODULE'),
  ('distro.distro',
   'c:\\Users\\<USER>\\Desktop\\Mes_projets\\WritingToolsClean\\Windows_and_Linux\\myvenv\\Lib\\site-packages\\distro\\distro.py',
   'PYMODULE'),
  ('doctest',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\doctest.py',
   'PYMODULE'),
  ('email',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\email\\__init__.py',
   'PYMODULE'),
  ('email._encoded_words',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\email\\_encoded_words.py',
   'PYMODULE'),
  ('email._header_value_parser',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\email\\_header_value_parser.py',
   'PYMODULE'),
  ('email._parseaddr',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\email\\_parseaddr.py',
   'PYMODULE'),
  ('email._policybase',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\email\\_policybase.py',
   'PYMODULE'),
  ('email.base64mime',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\email\\base64mime.py',
   'PYMODULE'),
  ('email.charset',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\email\\charset.py',
   'PYMODULE'),
  ('email.contentmanager',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\email\\contentmanager.py',
   'PYMODULE'),
  ('email.encoders',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\email\\encoders.py',
   'PYMODULE'),
  ('email.errors',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\email\\errors.py',
   'PYMODULE'),
  ('email.feedparser',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\email\\feedparser.py',
   'PYMODULE'),
  ('email.generator',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\email\\generator.py',
   'PYMODULE'),
  ('email.header',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\email\\header.py',
   'PYMODULE'),
  ('email.headerregistry',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\email\\headerregistry.py',
   'PYMODULE'),
  ('email.iterators',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\email\\iterators.py',
   'PYMODULE'),
  ('email.message',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\email\\message.py',
   'PYMODULE'),
  ('email.mime',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\email\\mime\\__init__.py',
   'PYMODULE'),
  ('email.mime.base',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\email\\mime\\base.py',
   'PYMODULE'),
  ('email.mime.multipart',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\email\\mime\\multipart.py',
   'PYMODULE'),
  ('email.mime.nonmultipart',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\email\\mime\\nonmultipart.py',
   'PYMODULE'),
  ('email.parser',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\email\\parser.py',
   'PYMODULE'),
  ('email.policy',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\email\\policy.py',
   'PYMODULE'),
  ('email.quoprimime',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\email\\quoprimime.py',
   'PYMODULE'),
  ('email.utils',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\email\\utils.py',
   'PYMODULE'),
  ('fnmatch',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\fnmatch.py',
   'PYMODULE'),
  ('fractions',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\fractions.py',
   'PYMODULE'),
  ('ftplib',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\ftplib.py',
   'PYMODULE'),
  ('getopt',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\getopt.py',
   'PYMODULE'),
  ('getpass',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\getpass.py',
   'PYMODULE'),
  ('gettext',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\gettext.py',
   'PYMODULE'),
  ('glob',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\glob.py',
   'PYMODULE'),
  ('google', '-', 'PYMODULE'),
  ('google._upb', '-', 'PYMODULE'),
  ('google.ai', '-', 'PYMODULE'),
  ('google.ai.generativelanguage',
   'c:\\Users\\<USER>\\Desktop\\Mes_projets\\WritingToolsClean\\Windows_and_Linux\\myvenv\\Lib\\site-packages\\google\\ai\\generativelanguage\\__init__.py',
   'PYMODULE'),
  ('google.ai.generativelanguage.gapic_version',
   'c:\\Users\\<USER>\\Desktop\\Mes_projets\\WritingToolsClean\\Windows_and_Linux\\myvenv\\Lib\\site-packages\\google\\ai\\generativelanguage\\gapic_version.py',
   'PYMODULE'),
  ('google.ai.generativelanguage_v1beta',
   'c:\\Users\\<USER>\\Desktop\\Mes_projets\\WritingToolsClean\\Windows_and_Linux\\myvenv\\Lib\\site-packages\\google\\ai\\generativelanguage_v1beta\\__init__.py',
   'PYMODULE'),
  ('google.ai.generativelanguage_v1beta.gapic_version',
   'c:\\Users\\<USER>\\Desktop\\Mes_projets\\WritingToolsClean\\Windows_and_Linux\\myvenv\\Lib\\site-packages\\google\\ai\\generativelanguage_v1beta\\gapic_version.py',
   'PYMODULE'),
  ('google.ai.generativelanguage_v1beta.services',
   'c:\\Users\\<USER>\\Desktop\\Mes_projets\\WritingToolsClean\\Windows_and_Linux\\myvenv\\Lib\\site-packages\\google\\ai\\generativelanguage_v1beta\\services\\__init__.py',
   'PYMODULE'),
  ('google.ai.generativelanguage_v1beta.services.cache_service',
   'c:\\Users\\<USER>\\Desktop\\Mes_projets\\WritingToolsClean\\Windows_and_Linux\\myvenv\\Lib\\site-packages\\google\\ai\\generativelanguage_v1beta\\services\\cache_service\\__init__.py',
   'PYMODULE'),
  ('google.ai.generativelanguage_v1beta.services.cache_service.async_client',
   'c:\\Users\\<USER>\\Desktop\\Mes_projets\\WritingToolsClean\\Windows_and_Linux\\myvenv\\Lib\\site-packages\\google\\ai\\generativelanguage_v1beta\\services\\cache_service\\async_client.py',
   'PYMODULE'),
  ('google.ai.generativelanguage_v1beta.services.cache_service.client',
   'c:\\Users\\<USER>\\Desktop\\Mes_projets\\WritingToolsClean\\Windows_and_Linux\\myvenv\\Lib\\site-packages\\google\\ai\\generativelanguage_v1beta\\services\\cache_service\\client.py',
   'PYMODULE'),
  ('google.ai.generativelanguage_v1beta.services.cache_service.pagers',
   'c:\\Users\\<USER>\\Desktop\\Mes_projets\\WritingToolsClean\\Windows_and_Linux\\myvenv\\Lib\\site-packages\\google\\ai\\generativelanguage_v1beta\\services\\cache_service\\pagers.py',
   'PYMODULE'),
  ('google.ai.generativelanguage_v1beta.services.cache_service.transports',
   'c:\\Users\\<USER>\\Desktop\\Mes_projets\\WritingToolsClean\\Windows_and_Linux\\myvenv\\Lib\\site-packages\\google\\ai\\generativelanguage_v1beta\\services\\cache_service\\transports\\__init__.py',
   'PYMODULE'),
  ('google.ai.generativelanguage_v1beta.services.cache_service.transports.base',
   'c:\\Users\\<USER>\\Desktop\\Mes_projets\\WritingToolsClean\\Windows_and_Linux\\myvenv\\Lib\\site-packages\\google\\ai\\generativelanguage_v1beta\\services\\cache_service\\transports\\base.py',
   'PYMODULE'),
  ('google.ai.generativelanguage_v1beta.services.cache_service.transports.grpc',
   'c:\\Users\\<USER>\\Desktop\\Mes_projets\\WritingToolsClean\\Windows_and_Linux\\myvenv\\Lib\\site-packages\\google\\ai\\generativelanguage_v1beta\\services\\cache_service\\transports\\grpc.py',
   'PYMODULE'),
  ('google.ai.generativelanguage_v1beta.services.cache_service.transports.grpc_asyncio',
   'c:\\Users\\<USER>\\Desktop\\Mes_projets\\WritingToolsClean\\Windows_and_Linux\\myvenv\\Lib\\site-packages\\google\\ai\\generativelanguage_v1beta\\services\\cache_service\\transports\\grpc_asyncio.py',
   'PYMODULE'),
  ('google.ai.generativelanguage_v1beta.services.cache_service.transports.rest',
   'c:\\Users\\<USER>\\Desktop\\Mes_projets\\WritingToolsClean\\Windows_and_Linux\\myvenv\\Lib\\site-packages\\google\\ai\\generativelanguage_v1beta\\services\\cache_service\\transports\\rest.py',
   'PYMODULE'),
  ('google.ai.generativelanguage_v1beta.services.cache_service.transports.rest_base',
   'c:\\Users\\<USER>\\Desktop\\Mes_projets\\WritingToolsClean\\Windows_and_Linux\\myvenv\\Lib\\site-packages\\google\\ai\\generativelanguage_v1beta\\services\\cache_service\\transports\\rest_base.py',
   'PYMODULE'),
  ('google.ai.generativelanguage_v1beta.services.discuss_service',
   'c:\\Users\\<USER>\\Desktop\\Mes_projets\\WritingToolsClean\\Windows_and_Linux\\myvenv\\Lib\\site-packages\\google\\ai\\generativelanguage_v1beta\\services\\discuss_service\\__init__.py',
   'PYMODULE'),
  ('google.ai.generativelanguage_v1beta.services.discuss_service.async_client',
   'c:\\Users\\<USER>\\Desktop\\Mes_projets\\WritingToolsClean\\Windows_and_Linux\\myvenv\\Lib\\site-packages\\google\\ai\\generativelanguage_v1beta\\services\\discuss_service\\async_client.py',
   'PYMODULE'),
  ('google.ai.generativelanguage_v1beta.services.discuss_service.client',
   'c:\\Users\\<USER>\\Desktop\\Mes_projets\\WritingToolsClean\\Windows_and_Linux\\myvenv\\Lib\\site-packages\\google\\ai\\generativelanguage_v1beta\\services\\discuss_service\\client.py',
   'PYMODULE'),
  ('google.ai.generativelanguage_v1beta.services.discuss_service.transports',
   'c:\\Users\\<USER>\\Desktop\\Mes_projets\\WritingToolsClean\\Windows_and_Linux\\myvenv\\Lib\\site-packages\\google\\ai\\generativelanguage_v1beta\\services\\discuss_service\\transports\\__init__.py',
   'PYMODULE'),
  ('google.ai.generativelanguage_v1beta.services.discuss_service.transports.base',
   'c:\\Users\\<USER>\\Desktop\\Mes_projets\\WritingToolsClean\\Windows_and_Linux\\myvenv\\Lib\\site-packages\\google\\ai\\generativelanguage_v1beta\\services\\discuss_service\\transports\\base.py',
   'PYMODULE'),
  ('google.ai.generativelanguage_v1beta.services.discuss_service.transports.grpc',
   'c:\\Users\\<USER>\\Desktop\\Mes_projets\\WritingToolsClean\\Windows_and_Linux\\myvenv\\Lib\\site-packages\\google\\ai\\generativelanguage_v1beta\\services\\discuss_service\\transports\\grpc.py',
   'PYMODULE'),
  ('google.ai.generativelanguage_v1beta.services.discuss_service.transports.grpc_asyncio',
   'c:\\Users\\<USER>\\Desktop\\Mes_projets\\WritingToolsClean\\Windows_and_Linux\\myvenv\\Lib\\site-packages\\google\\ai\\generativelanguage_v1beta\\services\\discuss_service\\transports\\grpc_asyncio.py',
   'PYMODULE'),
  ('google.ai.generativelanguage_v1beta.services.discuss_service.transports.rest',
   'c:\\Users\\<USER>\\Desktop\\Mes_projets\\WritingToolsClean\\Windows_and_Linux\\myvenv\\Lib\\site-packages\\google\\ai\\generativelanguage_v1beta\\services\\discuss_service\\transports\\rest.py',
   'PYMODULE'),
  ('google.ai.generativelanguage_v1beta.services.discuss_service.transports.rest_base',
   'c:\\Users\\<USER>\\Desktop\\Mes_projets\\WritingToolsClean\\Windows_and_Linux\\myvenv\\Lib\\site-packages\\google\\ai\\generativelanguage_v1beta\\services\\discuss_service\\transports\\rest_base.py',
   'PYMODULE'),
  ('google.ai.generativelanguage_v1beta.services.file_service',
   'c:\\Users\\<USER>\\Desktop\\Mes_projets\\WritingToolsClean\\Windows_and_Linux\\myvenv\\Lib\\site-packages\\google\\ai\\generativelanguage_v1beta\\services\\file_service\\__init__.py',
   'PYMODULE'),
  ('google.ai.generativelanguage_v1beta.services.file_service.async_client',
   'c:\\Users\\<USER>\\Desktop\\Mes_projets\\WritingToolsClean\\Windows_and_Linux\\myvenv\\Lib\\site-packages\\google\\ai\\generativelanguage_v1beta\\services\\file_service\\async_client.py',
   'PYMODULE'),
  ('google.ai.generativelanguage_v1beta.services.file_service.client',
   'c:\\Users\\<USER>\\Desktop\\Mes_projets\\WritingToolsClean\\Windows_and_Linux\\myvenv\\Lib\\site-packages\\google\\ai\\generativelanguage_v1beta\\services\\file_service\\client.py',
   'PYMODULE'),
  ('google.ai.generativelanguage_v1beta.services.file_service.pagers',
   'c:\\Users\\<USER>\\Desktop\\Mes_projets\\WritingToolsClean\\Windows_and_Linux\\myvenv\\Lib\\site-packages\\google\\ai\\generativelanguage_v1beta\\services\\file_service\\pagers.py',
   'PYMODULE'),
  ('google.ai.generativelanguage_v1beta.services.file_service.transports',
   'c:\\Users\\<USER>\\Desktop\\Mes_projets\\WritingToolsClean\\Windows_and_Linux\\myvenv\\Lib\\site-packages\\google\\ai\\generativelanguage_v1beta\\services\\file_service\\transports\\__init__.py',
   'PYMODULE'),
  ('google.ai.generativelanguage_v1beta.services.file_service.transports.base',
   'c:\\Users\\<USER>\\Desktop\\Mes_projets\\WritingToolsClean\\Windows_and_Linux\\myvenv\\Lib\\site-packages\\google\\ai\\generativelanguage_v1beta\\services\\file_service\\transports\\base.py',
   'PYMODULE'),
  ('google.ai.generativelanguage_v1beta.services.file_service.transports.grpc',
   'c:\\Users\\<USER>\\Desktop\\Mes_projets\\WritingToolsClean\\Windows_and_Linux\\myvenv\\Lib\\site-packages\\google\\ai\\generativelanguage_v1beta\\services\\file_service\\transports\\grpc.py',
   'PYMODULE'),
  ('google.ai.generativelanguage_v1beta.services.file_service.transports.grpc_asyncio',
   'c:\\Users\\<USER>\\Desktop\\Mes_projets\\WritingToolsClean\\Windows_and_Linux\\myvenv\\Lib\\site-packages\\google\\ai\\generativelanguage_v1beta\\services\\file_service\\transports\\grpc_asyncio.py',
   'PYMODULE'),
  ('google.ai.generativelanguage_v1beta.services.file_service.transports.rest',
   'c:\\Users\\<USER>\\Desktop\\Mes_projets\\WritingToolsClean\\Windows_and_Linux\\myvenv\\Lib\\site-packages\\google\\ai\\generativelanguage_v1beta\\services\\file_service\\transports\\rest.py',
   'PYMODULE'),
  ('google.ai.generativelanguage_v1beta.services.file_service.transports.rest_base',
   'c:\\Users\\<USER>\\Desktop\\Mes_projets\\WritingToolsClean\\Windows_and_Linux\\myvenv\\Lib\\site-packages\\google\\ai\\generativelanguage_v1beta\\services\\file_service\\transports\\rest_base.py',
   'PYMODULE'),
  ('google.ai.generativelanguage_v1beta.services.generative_service',
   'c:\\Users\\<USER>\\Desktop\\Mes_projets\\WritingToolsClean\\Windows_and_Linux\\myvenv\\Lib\\site-packages\\google\\ai\\generativelanguage_v1beta\\services\\generative_service\\__init__.py',
   'PYMODULE'),
  ('google.ai.generativelanguage_v1beta.services.generative_service.async_client',
   'c:\\Users\\<USER>\\Desktop\\Mes_projets\\WritingToolsClean\\Windows_and_Linux\\myvenv\\Lib\\site-packages\\google\\ai\\generativelanguage_v1beta\\services\\generative_service\\async_client.py',
   'PYMODULE'),
  ('google.ai.generativelanguage_v1beta.services.generative_service.client',
   'c:\\Users\\<USER>\\Desktop\\Mes_projets\\WritingToolsClean\\Windows_and_Linux\\myvenv\\Lib\\site-packages\\google\\ai\\generativelanguage_v1beta\\services\\generative_service\\client.py',
   'PYMODULE'),
  ('google.ai.generativelanguage_v1beta.services.generative_service.transports',
   'c:\\Users\\<USER>\\Desktop\\Mes_projets\\WritingToolsClean\\Windows_and_Linux\\myvenv\\Lib\\site-packages\\google\\ai\\generativelanguage_v1beta\\services\\generative_service\\transports\\__init__.py',
   'PYMODULE'),
  ('google.ai.generativelanguage_v1beta.services.generative_service.transports.base',
   'c:\\Users\\<USER>\\Desktop\\Mes_projets\\WritingToolsClean\\Windows_and_Linux\\myvenv\\Lib\\site-packages\\google\\ai\\generativelanguage_v1beta\\services\\generative_service\\transports\\base.py',
   'PYMODULE'),
  ('google.ai.generativelanguage_v1beta.services.generative_service.transports.grpc',
   'c:\\Users\\<USER>\\Desktop\\Mes_projets\\WritingToolsClean\\Windows_and_Linux\\myvenv\\Lib\\site-packages\\google\\ai\\generativelanguage_v1beta\\services\\generative_service\\transports\\grpc.py',
   'PYMODULE'),
  ('google.ai.generativelanguage_v1beta.services.generative_service.transports.grpc_asyncio',
   'c:\\Users\\<USER>\\Desktop\\Mes_projets\\WritingToolsClean\\Windows_and_Linux\\myvenv\\Lib\\site-packages\\google\\ai\\generativelanguage_v1beta\\services\\generative_service\\transports\\grpc_asyncio.py',
   'PYMODULE'),
  ('google.ai.generativelanguage_v1beta.services.generative_service.transports.rest',
   'c:\\Users\\<USER>\\Desktop\\Mes_projets\\WritingToolsClean\\Windows_and_Linux\\myvenv\\Lib\\site-packages\\google\\ai\\generativelanguage_v1beta\\services\\generative_service\\transports\\rest.py',
   'PYMODULE'),
  ('google.ai.generativelanguage_v1beta.services.generative_service.transports.rest_base',
   'c:\\Users\\<USER>\\Desktop\\Mes_projets\\WritingToolsClean\\Windows_and_Linux\\myvenv\\Lib\\site-packages\\google\\ai\\generativelanguage_v1beta\\services\\generative_service\\transports\\rest_base.py',
   'PYMODULE'),
  ('google.ai.generativelanguage_v1beta.services.model_service',
   'c:\\Users\\<USER>\\Desktop\\Mes_projets\\WritingToolsClean\\Windows_and_Linux\\myvenv\\Lib\\site-packages\\google\\ai\\generativelanguage_v1beta\\services\\model_service\\__init__.py',
   'PYMODULE'),
  ('google.ai.generativelanguage_v1beta.services.model_service.async_client',
   'c:\\Users\\<USER>\\Desktop\\Mes_projets\\WritingToolsClean\\Windows_and_Linux\\myvenv\\Lib\\site-packages\\google\\ai\\generativelanguage_v1beta\\services\\model_service\\async_client.py',
   'PYMODULE'),
  ('google.ai.generativelanguage_v1beta.services.model_service.client',
   'c:\\Users\\<USER>\\Desktop\\Mes_projets\\WritingToolsClean\\Windows_and_Linux\\myvenv\\Lib\\site-packages\\google\\ai\\generativelanguage_v1beta\\services\\model_service\\client.py',
   'PYMODULE'),
  ('google.ai.generativelanguage_v1beta.services.model_service.pagers',
   'c:\\Users\\<USER>\\Desktop\\Mes_projets\\WritingToolsClean\\Windows_and_Linux\\myvenv\\Lib\\site-packages\\google\\ai\\generativelanguage_v1beta\\services\\model_service\\pagers.py',
   'PYMODULE'),
  ('google.ai.generativelanguage_v1beta.services.model_service.transports',
   'c:\\Users\\<USER>\\Desktop\\Mes_projets\\WritingToolsClean\\Windows_and_Linux\\myvenv\\Lib\\site-packages\\google\\ai\\generativelanguage_v1beta\\services\\model_service\\transports\\__init__.py',
   'PYMODULE'),
  ('google.ai.generativelanguage_v1beta.services.model_service.transports.base',
   'c:\\Users\\<USER>\\Desktop\\Mes_projets\\WritingToolsClean\\Windows_and_Linux\\myvenv\\Lib\\site-packages\\google\\ai\\generativelanguage_v1beta\\services\\model_service\\transports\\base.py',
   'PYMODULE'),
  ('google.ai.generativelanguage_v1beta.services.model_service.transports.grpc',
   'c:\\Users\\<USER>\\Desktop\\Mes_projets\\WritingToolsClean\\Windows_and_Linux\\myvenv\\Lib\\site-packages\\google\\ai\\generativelanguage_v1beta\\services\\model_service\\transports\\grpc.py',
   'PYMODULE'),
  ('google.ai.generativelanguage_v1beta.services.model_service.transports.grpc_asyncio',
   'c:\\Users\\<USER>\\Desktop\\Mes_projets\\WritingToolsClean\\Windows_and_Linux\\myvenv\\Lib\\site-packages\\google\\ai\\generativelanguage_v1beta\\services\\model_service\\transports\\grpc_asyncio.py',
   'PYMODULE'),
  ('google.ai.generativelanguage_v1beta.services.model_service.transports.rest',
   'c:\\Users\\<USER>\\Desktop\\Mes_projets\\WritingToolsClean\\Windows_and_Linux\\myvenv\\Lib\\site-packages\\google\\ai\\generativelanguage_v1beta\\services\\model_service\\transports\\rest.py',
   'PYMODULE'),
  ('google.ai.generativelanguage_v1beta.services.model_service.transports.rest_base',
   'c:\\Users\\<USER>\\Desktop\\Mes_projets\\WritingToolsClean\\Windows_and_Linux\\myvenv\\Lib\\site-packages\\google\\ai\\generativelanguage_v1beta\\services\\model_service\\transports\\rest_base.py',
   'PYMODULE'),
  ('google.ai.generativelanguage_v1beta.services.permission_service',
   'c:\\Users\\<USER>\\Desktop\\Mes_projets\\WritingToolsClean\\Windows_and_Linux\\myvenv\\Lib\\site-packages\\google\\ai\\generativelanguage_v1beta\\services\\permission_service\\__init__.py',
   'PYMODULE'),
  ('google.ai.generativelanguage_v1beta.services.permission_service.async_client',
   'c:\\Users\\<USER>\\Desktop\\Mes_projets\\WritingToolsClean\\Windows_and_Linux\\myvenv\\Lib\\site-packages\\google\\ai\\generativelanguage_v1beta\\services\\permission_service\\async_client.py',
   'PYMODULE'),
  ('google.ai.generativelanguage_v1beta.services.permission_service.client',
   'c:\\Users\\<USER>\\Desktop\\Mes_projets\\WritingToolsClean\\Windows_and_Linux\\myvenv\\Lib\\site-packages\\google\\ai\\generativelanguage_v1beta\\services\\permission_service\\client.py',
   'PYMODULE'),
  ('google.ai.generativelanguage_v1beta.services.permission_service.pagers',
   'c:\\Users\\<USER>\\Desktop\\Mes_projets\\WritingToolsClean\\Windows_and_Linux\\myvenv\\Lib\\site-packages\\google\\ai\\generativelanguage_v1beta\\services\\permission_service\\pagers.py',
   'PYMODULE'),
  ('google.ai.generativelanguage_v1beta.services.permission_service.transports',
   'c:\\Users\\<USER>\\Desktop\\Mes_projets\\WritingToolsClean\\Windows_and_Linux\\myvenv\\Lib\\site-packages\\google\\ai\\generativelanguage_v1beta\\services\\permission_service\\transports\\__init__.py',
   'PYMODULE'),
  ('google.ai.generativelanguage_v1beta.services.permission_service.transports.base',
   'c:\\Users\\<USER>\\Desktop\\Mes_projets\\WritingToolsClean\\Windows_and_Linux\\myvenv\\Lib\\site-packages\\google\\ai\\generativelanguage_v1beta\\services\\permission_service\\transports\\base.py',
   'PYMODULE'),
  ('google.ai.generativelanguage_v1beta.services.permission_service.transports.grpc',
   'c:\\Users\\<USER>\\Desktop\\Mes_projets\\WritingToolsClean\\Windows_and_Linux\\myvenv\\Lib\\site-packages\\google\\ai\\generativelanguage_v1beta\\services\\permission_service\\transports\\grpc.py',
   'PYMODULE'),
  ('google.ai.generativelanguage_v1beta.services.permission_service.transports.grpc_asyncio',
   'c:\\Users\\<USER>\\Desktop\\Mes_projets\\WritingToolsClean\\Windows_and_Linux\\myvenv\\Lib\\site-packages\\google\\ai\\generativelanguage_v1beta\\services\\permission_service\\transports\\grpc_asyncio.py',
   'PYMODULE'),
  ('google.ai.generativelanguage_v1beta.services.permission_service.transports.rest',
   'c:\\Users\\<USER>\\Desktop\\Mes_projets\\WritingToolsClean\\Windows_and_Linux\\myvenv\\Lib\\site-packages\\google\\ai\\generativelanguage_v1beta\\services\\permission_service\\transports\\rest.py',
   'PYMODULE'),
  ('google.ai.generativelanguage_v1beta.services.permission_service.transports.rest_base',
   'c:\\Users\\<USER>\\Desktop\\Mes_projets\\WritingToolsClean\\Windows_and_Linux\\myvenv\\Lib\\site-packages\\google\\ai\\generativelanguage_v1beta\\services\\permission_service\\transports\\rest_base.py',
   'PYMODULE'),
  ('google.ai.generativelanguage_v1beta.services.prediction_service',
   'c:\\Users\\<USER>\\Desktop\\Mes_projets\\WritingToolsClean\\Windows_and_Linux\\myvenv\\Lib\\site-packages\\google\\ai\\generativelanguage_v1beta\\services\\prediction_service\\__init__.py',
   'PYMODULE'),
  ('google.ai.generativelanguage_v1beta.services.prediction_service.async_client',
   'c:\\Users\\<USER>\\Desktop\\Mes_projets\\WritingToolsClean\\Windows_and_Linux\\myvenv\\Lib\\site-packages\\google\\ai\\generativelanguage_v1beta\\services\\prediction_service\\async_client.py',
   'PYMODULE'),
  ('google.ai.generativelanguage_v1beta.services.prediction_service.client',
   'c:\\Users\\<USER>\\Desktop\\Mes_projets\\WritingToolsClean\\Windows_and_Linux\\myvenv\\Lib\\site-packages\\google\\ai\\generativelanguage_v1beta\\services\\prediction_service\\client.py',
   'PYMODULE'),
  ('google.ai.generativelanguage_v1beta.services.prediction_service.transports',
   'c:\\Users\\<USER>\\Desktop\\Mes_projets\\WritingToolsClean\\Windows_and_Linux\\myvenv\\Lib\\site-packages\\google\\ai\\generativelanguage_v1beta\\services\\prediction_service\\transports\\__init__.py',
   'PYMODULE'),
  ('google.ai.generativelanguage_v1beta.services.prediction_service.transports.base',
   'c:\\Users\\<USER>\\Desktop\\Mes_projets\\WritingToolsClean\\Windows_and_Linux\\myvenv\\Lib\\site-packages\\google\\ai\\generativelanguage_v1beta\\services\\prediction_service\\transports\\base.py',
   'PYMODULE'),
  ('google.ai.generativelanguage_v1beta.services.prediction_service.transports.grpc',
   'c:\\Users\\<USER>\\Desktop\\Mes_projets\\WritingToolsClean\\Windows_and_Linux\\myvenv\\Lib\\site-packages\\google\\ai\\generativelanguage_v1beta\\services\\prediction_service\\transports\\grpc.py',
   'PYMODULE'),
  ('google.ai.generativelanguage_v1beta.services.prediction_service.transports.grpc_asyncio',
   'c:\\Users\\<USER>\\Desktop\\Mes_projets\\WritingToolsClean\\Windows_and_Linux\\myvenv\\Lib\\site-packages\\google\\ai\\generativelanguage_v1beta\\services\\prediction_service\\transports\\grpc_asyncio.py',
   'PYMODULE'),
  ('google.ai.generativelanguage_v1beta.services.prediction_service.transports.rest',
   'c:\\Users\\<USER>\\Desktop\\Mes_projets\\WritingToolsClean\\Windows_and_Linux\\myvenv\\Lib\\site-packages\\google\\ai\\generativelanguage_v1beta\\services\\prediction_service\\transports\\rest.py',
   'PYMODULE'),
  ('google.ai.generativelanguage_v1beta.services.prediction_service.transports.rest_base',
   'c:\\Users\\<USER>\\Desktop\\Mes_projets\\WritingToolsClean\\Windows_and_Linux\\myvenv\\Lib\\site-packages\\google\\ai\\generativelanguage_v1beta\\services\\prediction_service\\transports\\rest_base.py',
   'PYMODULE'),
  ('google.ai.generativelanguage_v1beta.services.retriever_service',
   'c:\\Users\\<USER>\\Desktop\\Mes_projets\\WritingToolsClean\\Windows_and_Linux\\myvenv\\Lib\\site-packages\\google\\ai\\generativelanguage_v1beta\\services\\retriever_service\\__init__.py',
   'PYMODULE'),
  ('google.ai.generativelanguage_v1beta.services.retriever_service.async_client',
   'c:\\Users\\<USER>\\Desktop\\Mes_projets\\WritingToolsClean\\Windows_and_Linux\\myvenv\\Lib\\site-packages\\google\\ai\\generativelanguage_v1beta\\services\\retriever_service\\async_client.py',
   'PYMODULE'),
  ('google.ai.generativelanguage_v1beta.services.retriever_service.client',
   'c:\\Users\\<USER>\\Desktop\\Mes_projets\\WritingToolsClean\\Windows_and_Linux\\myvenv\\Lib\\site-packages\\google\\ai\\generativelanguage_v1beta\\services\\retriever_service\\client.py',
   'PYMODULE'),
  ('google.ai.generativelanguage_v1beta.services.retriever_service.pagers',
   'c:\\Users\\<USER>\\Desktop\\Mes_projets\\WritingToolsClean\\Windows_and_Linux\\myvenv\\Lib\\site-packages\\google\\ai\\generativelanguage_v1beta\\services\\retriever_service\\pagers.py',
   'PYMODULE'),
  ('google.ai.generativelanguage_v1beta.services.retriever_service.transports',
   'c:\\Users\\<USER>\\Desktop\\Mes_projets\\WritingToolsClean\\Windows_and_Linux\\myvenv\\Lib\\site-packages\\google\\ai\\generativelanguage_v1beta\\services\\retriever_service\\transports\\__init__.py',
   'PYMODULE'),
  ('google.ai.generativelanguage_v1beta.services.retriever_service.transports.base',
   'c:\\Users\\<USER>\\Desktop\\Mes_projets\\WritingToolsClean\\Windows_and_Linux\\myvenv\\Lib\\site-packages\\google\\ai\\generativelanguage_v1beta\\services\\retriever_service\\transports\\base.py',
   'PYMODULE'),
  ('google.ai.generativelanguage_v1beta.services.retriever_service.transports.grpc',
   'c:\\Users\\<USER>\\Desktop\\Mes_projets\\WritingToolsClean\\Windows_and_Linux\\myvenv\\Lib\\site-packages\\google\\ai\\generativelanguage_v1beta\\services\\retriever_service\\transports\\grpc.py',
   'PYMODULE'),
  ('google.ai.generativelanguage_v1beta.services.retriever_service.transports.grpc_asyncio',
   'c:\\Users\\<USER>\\Desktop\\Mes_projets\\WritingToolsClean\\Windows_and_Linux\\myvenv\\Lib\\site-packages\\google\\ai\\generativelanguage_v1beta\\services\\retriever_service\\transports\\grpc_asyncio.py',
   'PYMODULE'),
  ('google.ai.generativelanguage_v1beta.services.retriever_service.transports.rest',
   'c:\\Users\\<USER>\\Desktop\\Mes_projets\\WritingToolsClean\\Windows_and_Linux\\myvenv\\Lib\\site-packages\\google\\ai\\generativelanguage_v1beta\\services\\retriever_service\\transports\\rest.py',
   'PYMODULE'),
  ('google.ai.generativelanguage_v1beta.services.retriever_service.transports.rest_base',
   'c:\\Users\\<USER>\\Desktop\\Mes_projets\\WritingToolsClean\\Windows_and_Linux\\myvenv\\Lib\\site-packages\\google\\ai\\generativelanguage_v1beta\\services\\retriever_service\\transports\\rest_base.py',
   'PYMODULE'),
  ('google.ai.generativelanguage_v1beta.services.text_service',
   'c:\\Users\\<USER>\\Desktop\\Mes_projets\\WritingToolsClean\\Windows_and_Linux\\myvenv\\Lib\\site-packages\\google\\ai\\generativelanguage_v1beta\\services\\text_service\\__init__.py',
   'PYMODULE'),
  ('google.ai.generativelanguage_v1beta.services.text_service.async_client',
   'c:\\Users\\<USER>\\Desktop\\Mes_projets\\WritingToolsClean\\Windows_and_Linux\\myvenv\\Lib\\site-packages\\google\\ai\\generativelanguage_v1beta\\services\\text_service\\async_client.py',
   'PYMODULE'),
  ('google.ai.generativelanguage_v1beta.services.text_service.client',
   'c:\\Users\\<USER>\\Desktop\\Mes_projets\\WritingToolsClean\\Windows_and_Linux\\myvenv\\Lib\\site-packages\\google\\ai\\generativelanguage_v1beta\\services\\text_service\\client.py',
   'PYMODULE'),
  ('google.ai.generativelanguage_v1beta.services.text_service.transports',
   'c:\\Users\\<USER>\\Desktop\\Mes_projets\\WritingToolsClean\\Windows_and_Linux\\myvenv\\Lib\\site-packages\\google\\ai\\generativelanguage_v1beta\\services\\text_service\\transports\\__init__.py',
   'PYMODULE'),
  ('google.ai.generativelanguage_v1beta.services.text_service.transports.base',
   'c:\\Users\\<USER>\\Desktop\\Mes_projets\\WritingToolsClean\\Windows_and_Linux\\myvenv\\Lib\\site-packages\\google\\ai\\generativelanguage_v1beta\\services\\text_service\\transports\\base.py',
   'PYMODULE'),
  ('google.ai.generativelanguage_v1beta.services.text_service.transports.grpc',
   'c:\\Users\\<USER>\\Desktop\\Mes_projets\\WritingToolsClean\\Windows_and_Linux\\myvenv\\Lib\\site-packages\\google\\ai\\generativelanguage_v1beta\\services\\text_service\\transports\\grpc.py',
   'PYMODULE'),
  ('google.ai.generativelanguage_v1beta.services.text_service.transports.grpc_asyncio',
   'c:\\Users\\<USER>\\Desktop\\Mes_projets\\WritingToolsClean\\Windows_and_Linux\\myvenv\\Lib\\site-packages\\google\\ai\\generativelanguage_v1beta\\services\\text_service\\transports\\grpc_asyncio.py',
   'PYMODULE'),
  ('google.ai.generativelanguage_v1beta.services.text_service.transports.rest',
   'c:\\Users\\<USER>\\Desktop\\Mes_projets\\WritingToolsClean\\Windows_and_Linux\\myvenv\\Lib\\site-packages\\google\\ai\\generativelanguage_v1beta\\services\\text_service\\transports\\rest.py',
   'PYMODULE'),
  ('google.ai.generativelanguage_v1beta.services.text_service.transports.rest_base',
   'c:\\Users\\<USER>\\Desktop\\Mes_projets\\WritingToolsClean\\Windows_and_Linux\\myvenv\\Lib\\site-packages\\google\\ai\\generativelanguage_v1beta\\services\\text_service\\transports\\rest_base.py',
   'PYMODULE'),
  ('google.ai.generativelanguage_v1beta.types',
   'c:\\Users\\<USER>\\Desktop\\Mes_projets\\WritingToolsClean\\Windows_and_Linux\\myvenv\\Lib\\site-packages\\google\\ai\\generativelanguage_v1beta\\types\\__init__.py',
   'PYMODULE'),
  ('google.ai.generativelanguage_v1beta.types.cache_service',
   'c:\\Users\\<USER>\\Desktop\\Mes_projets\\WritingToolsClean\\Windows_and_Linux\\myvenv\\Lib\\site-packages\\google\\ai\\generativelanguage_v1beta\\types\\cache_service.py',
   'PYMODULE'),
  ('google.ai.generativelanguage_v1beta.types.cached_content',
   'c:\\Users\\<USER>\\Desktop\\Mes_projets\\WritingToolsClean\\Windows_and_Linux\\myvenv\\Lib\\site-packages\\google\\ai\\generativelanguage_v1beta\\types\\cached_content.py',
   'PYMODULE'),
  ('google.ai.generativelanguage_v1beta.types.citation',
   'c:\\Users\\<USER>\\Desktop\\Mes_projets\\WritingToolsClean\\Windows_and_Linux\\myvenv\\Lib\\site-packages\\google\\ai\\generativelanguage_v1beta\\types\\citation.py',
   'PYMODULE'),
  ('google.ai.generativelanguage_v1beta.types.content',
   'c:\\Users\\<USER>\\Desktop\\Mes_projets\\WritingToolsClean\\Windows_and_Linux\\myvenv\\Lib\\site-packages\\google\\ai\\generativelanguage_v1beta\\types\\content.py',
   'PYMODULE'),
  ('google.ai.generativelanguage_v1beta.types.discuss_service',
   'c:\\Users\\<USER>\\Desktop\\Mes_projets\\WritingToolsClean\\Windows_and_Linux\\myvenv\\Lib\\site-packages\\google\\ai\\generativelanguage_v1beta\\types\\discuss_service.py',
   'PYMODULE'),
  ('google.ai.generativelanguage_v1beta.types.file',
   'c:\\Users\\<USER>\\Desktop\\Mes_projets\\WritingToolsClean\\Windows_and_Linux\\myvenv\\Lib\\site-packages\\google\\ai\\generativelanguage_v1beta\\types\\file.py',
   'PYMODULE'),
  ('google.ai.generativelanguage_v1beta.types.file_service',
   'c:\\Users\\<USER>\\Desktop\\Mes_projets\\WritingToolsClean\\Windows_and_Linux\\myvenv\\Lib\\site-packages\\google\\ai\\generativelanguage_v1beta\\types\\file_service.py',
   'PYMODULE'),
  ('google.ai.generativelanguage_v1beta.types.generative_service',
   'c:\\Users\\<USER>\\Desktop\\Mes_projets\\WritingToolsClean\\Windows_and_Linux\\myvenv\\Lib\\site-packages\\google\\ai\\generativelanguage_v1beta\\types\\generative_service.py',
   'PYMODULE'),
  ('google.ai.generativelanguage_v1beta.types.model',
   'c:\\Users\\<USER>\\Desktop\\Mes_projets\\WritingToolsClean\\Windows_and_Linux\\myvenv\\Lib\\site-packages\\google\\ai\\generativelanguage_v1beta\\types\\model.py',
   'PYMODULE'),
  ('google.ai.generativelanguage_v1beta.types.model_service',
   'c:\\Users\\<USER>\\Desktop\\Mes_projets\\WritingToolsClean\\Windows_and_Linux\\myvenv\\Lib\\site-packages\\google\\ai\\generativelanguage_v1beta\\types\\model_service.py',
   'PYMODULE'),
  ('google.ai.generativelanguage_v1beta.types.permission',
   'c:\\Users\\<USER>\\Desktop\\Mes_projets\\WritingToolsClean\\Windows_and_Linux\\myvenv\\Lib\\site-packages\\google\\ai\\generativelanguage_v1beta\\types\\permission.py',
   'PYMODULE'),
  ('google.ai.generativelanguage_v1beta.types.permission_service',
   'c:\\Users\\<USER>\\Desktop\\Mes_projets\\WritingToolsClean\\Windows_and_Linux\\myvenv\\Lib\\site-packages\\google\\ai\\generativelanguage_v1beta\\types\\permission_service.py',
   'PYMODULE'),
  ('google.ai.generativelanguage_v1beta.types.prediction_service',
   'c:\\Users\\<USER>\\Desktop\\Mes_projets\\WritingToolsClean\\Windows_and_Linux\\myvenv\\Lib\\site-packages\\google\\ai\\generativelanguage_v1beta\\types\\prediction_service.py',
   'PYMODULE'),
  ('google.ai.generativelanguage_v1beta.types.retriever',
   'c:\\Users\\<USER>\\Desktop\\Mes_projets\\WritingToolsClean\\Windows_and_Linux\\myvenv\\Lib\\site-packages\\google\\ai\\generativelanguage_v1beta\\types\\retriever.py',
   'PYMODULE'),
  ('google.ai.generativelanguage_v1beta.types.retriever_service',
   'c:\\Users\\<USER>\\Desktop\\Mes_projets\\WritingToolsClean\\Windows_and_Linux\\myvenv\\Lib\\site-packages\\google\\ai\\generativelanguage_v1beta\\types\\retriever_service.py',
   'PYMODULE'),
  ('google.ai.generativelanguage_v1beta.types.safety',
   'c:\\Users\\<USER>\\Desktop\\Mes_projets\\WritingToolsClean\\Windows_and_Linux\\myvenv\\Lib\\site-packages\\google\\ai\\generativelanguage_v1beta\\types\\safety.py',
   'PYMODULE'),
  ('google.ai.generativelanguage_v1beta.types.text_service',
   'c:\\Users\\<USER>\\Desktop\\Mes_projets\\WritingToolsClean\\Windows_and_Linux\\myvenv\\Lib\\site-packages\\google\\ai\\generativelanguage_v1beta\\types\\text_service.py',
   'PYMODULE'),
  ('google.ai.generativelanguage_v1beta.types.tuned_model',
   'c:\\Users\\<USER>\\Desktop\\Mes_projets\\WritingToolsClean\\Windows_and_Linux\\myvenv\\Lib\\site-packages\\google\\ai\\generativelanguage_v1beta\\types\\tuned_model.py',
   'PYMODULE'),
  ('google.api', '-', 'PYMODULE'),
  ('google.api.annotations_pb2',
   'c:\\Users\\<USER>\\Desktop\\Mes_projets\\WritingToolsClean\\Windows_and_Linux\\myvenv\\Lib\\site-packages\\google\\api\\annotations_pb2.py',
   'PYMODULE'),
  ('google.api.client_pb2',
   'c:\\Users\\<USER>\\Desktop\\Mes_projets\\WritingToolsClean\\Windows_and_Linux\\myvenv\\Lib\\site-packages\\google\\api\\client_pb2.py',
   'PYMODULE'),
  ('google.api.http_pb2',
   'c:\\Users\\<USER>\\Desktop\\Mes_projets\\WritingToolsClean\\Windows_and_Linux\\myvenv\\Lib\\site-packages\\google\\api\\http_pb2.py',
   'PYMODULE'),
  ('google.api.launch_stage_pb2',
   'c:\\Users\\<USER>\\Desktop\\Mes_projets\\WritingToolsClean\\Windows_and_Linux\\myvenv\\Lib\\site-packages\\google\\api\\launch_stage_pb2.py',
   'PYMODULE'),
  ('google.api_core',
   'c:\\Users\\<USER>\\Desktop\\Mes_projets\\WritingToolsClean\\Windows_and_Linux\\myvenv\\Lib\\site-packages\\google\\api_core\\__init__.py',
   'PYMODULE'),
  ('google.api_core._rest_streaming_base',
   'c:\\Users\\<USER>\\Desktop\\Mes_projets\\WritingToolsClean\\Windows_and_Linux\\myvenv\\Lib\\site-packages\\google\\api_core\\_rest_streaming_base.py',
   'PYMODULE'),
  ('google.api_core.client_info',
   'c:\\Users\\<USER>\\Desktop\\Mes_projets\\WritingToolsClean\\Windows_and_Linux\\myvenv\\Lib\\site-packages\\google\\api_core\\client_info.py',
   'PYMODULE'),
  ('google.api_core.client_logging',
   'c:\\Users\\<USER>\\Desktop\\Mes_projets\\WritingToolsClean\\Windows_and_Linux\\myvenv\\Lib\\site-packages\\google\\api_core\\client_logging.py',
   'PYMODULE'),
  ('google.api_core.client_options',
   'c:\\Users\\<USER>\\Desktop\\Mes_projets\\WritingToolsClean\\Windows_and_Linux\\myvenv\\Lib\\site-packages\\google\\api_core\\client_options.py',
   'PYMODULE'),
  ('google.api_core.datetime_helpers',
   'c:\\Users\\<USER>\\Desktop\\Mes_projets\\WritingToolsClean\\Windows_and_Linux\\myvenv\\Lib\\site-packages\\google\\api_core\\datetime_helpers.py',
   'PYMODULE'),
  ('google.api_core.exceptions',
   'c:\\Users\\<USER>\\Desktop\\Mes_projets\\WritingToolsClean\\Windows_and_Linux\\myvenv\\Lib\\site-packages\\google\\api_core\\exceptions.py',
   'PYMODULE'),
  ('google.api_core.future',
   'c:\\Users\\<USER>\\Desktop\\Mes_projets\\WritingToolsClean\\Windows_and_Linux\\myvenv\\Lib\\site-packages\\google\\api_core\\future\\__init__.py',
   'PYMODULE'),
  ('google.api_core.future._helpers',
   'c:\\Users\\<USER>\\Desktop\\Mes_projets\\WritingToolsClean\\Windows_and_Linux\\myvenv\\Lib\\site-packages\\google\\api_core\\future\\_helpers.py',
   'PYMODULE'),
  ('google.api_core.future.async_future',
   'c:\\Users\\<USER>\\Desktop\\Mes_projets\\WritingToolsClean\\Windows_and_Linux\\myvenv\\Lib\\site-packages\\google\\api_core\\future\\async_future.py',
   'PYMODULE'),
  ('google.api_core.future.base',
   'c:\\Users\\<USER>\\Desktop\\Mes_projets\\WritingToolsClean\\Windows_and_Linux\\myvenv\\Lib\\site-packages\\google\\api_core\\future\\base.py',
   'PYMODULE'),
  ('google.api_core.future.polling',
   'c:\\Users\\<USER>\\Desktop\\Mes_projets\\WritingToolsClean\\Windows_and_Linux\\myvenv\\Lib\\site-packages\\google\\api_core\\future\\polling.py',
   'PYMODULE'),
  ('google.api_core.gapic_v1',
   'c:\\Users\\<USER>\\Desktop\\Mes_projets\\WritingToolsClean\\Windows_and_Linux\\myvenv\\Lib\\site-packages\\google\\api_core\\gapic_v1\\__init__.py',
   'PYMODULE'),
  ('google.api_core.gapic_v1.client_info',
   'c:\\Users\\<USER>\\Desktop\\Mes_projets\\WritingToolsClean\\Windows_and_Linux\\myvenv\\Lib\\site-packages\\google\\api_core\\gapic_v1\\client_info.py',
   'PYMODULE'),
  ('google.api_core.gapic_v1.config',
   'c:\\Users\\<USER>\\Desktop\\Mes_projets\\WritingToolsClean\\Windows_and_Linux\\myvenv\\Lib\\site-packages\\google\\api_core\\gapic_v1\\config.py',
   'PYMODULE'),
  ('google.api_core.gapic_v1.config_async',
   'c:\\Users\\<USER>\\Desktop\\Mes_projets\\WritingToolsClean\\Windows_and_Linux\\myvenv\\Lib\\site-packages\\google\\api_core\\gapic_v1\\config_async.py',
   'PYMODULE'),
  ('google.api_core.gapic_v1.method',
   'c:\\Users\\<USER>\\Desktop\\Mes_projets\\WritingToolsClean\\Windows_and_Linux\\myvenv\\Lib\\site-packages\\google\\api_core\\gapic_v1\\method.py',
   'PYMODULE'),
  ('google.api_core.gapic_v1.method_async',
   'c:\\Users\\<USER>\\Desktop\\Mes_projets\\WritingToolsClean\\Windows_and_Linux\\myvenv\\Lib\\site-packages\\google\\api_core\\gapic_v1\\method_async.py',
   'PYMODULE'),
  ('google.api_core.gapic_v1.routing_header',
   'c:\\Users\\<USER>\\Desktop\\Mes_projets\\WritingToolsClean\\Windows_and_Linux\\myvenv\\Lib\\site-packages\\google\\api_core\\gapic_v1\\routing_header.py',
   'PYMODULE'),
  ('google.api_core.grpc_helpers',
   'c:\\Users\\<USER>\\Desktop\\Mes_projets\\WritingToolsClean\\Windows_and_Linux\\myvenv\\Lib\\site-packages\\google\\api_core\\grpc_helpers.py',
   'PYMODULE'),
  ('google.api_core.grpc_helpers_async',
   'c:\\Users\\<USER>\\Desktop\\Mes_projets\\WritingToolsClean\\Windows_and_Linux\\myvenv\\Lib\\site-packages\\google\\api_core\\grpc_helpers_async.py',
   'PYMODULE'),
  ('google.api_core.operation',
   'c:\\Users\\<USER>\\Desktop\\Mes_projets\\WritingToolsClean\\Windows_and_Linux\\myvenv\\Lib\\site-packages\\google\\api_core\\operation.py',
   'PYMODULE'),
  ('google.api_core.operation_async',
   'c:\\Users\\<USER>\\Desktop\\Mes_projets\\WritingToolsClean\\Windows_and_Linux\\myvenv\\Lib\\site-packages\\google\\api_core\\operation_async.py',
   'PYMODULE'),
  ('google.api_core.operations_v1',
   'c:\\Users\\<USER>\\Desktop\\Mes_projets\\WritingToolsClean\\Windows_and_Linux\\myvenv\\Lib\\site-packages\\google\\api_core\\operations_v1\\__init__.py',
   'PYMODULE'),
  ('google.api_core.operations_v1.abstract_operations_base_client',
   'c:\\Users\\<USER>\\Desktop\\Mes_projets\\WritingToolsClean\\Windows_and_Linux\\myvenv\\Lib\\site-packages\\google\\api_core\\operations_v1\\abstract_operations_base_client.py',
   'PYMODULE'),
  ('google.api_core.operations_v1.abstract_operations_client',
   'c:\\Users\\<USER>\\Desktop\\Mes_projets\\WritingToolsClean\\Windows_and_Linux\\myvenv\\Lib\\site-packages\\google\\api_core\\operations_v1\\abstract_operations_client.py',
   'PYMODULE'),
  ('google.api_core.operations_v1.operations_async_client',
   'c:\\Users\\<USER>\\Desktop\\Mes_projets\\WritingToolsClean\\Windows_and_Linux\\myvenv\\Lib\\site-packages\\google\\api_core\\operations_v1\\operations_async_client.py',
   'PYMODULE'),
  ('google.api_core.operations_v1.operations_client',
   'c:\\Users\\<USER>\\Desktop\\Mes_projets\\WritingToolsClean\\Windows_and_Linux\\myvenv\\Lib\\site-packages\\google\\api_core\\operations_v1\\operations_client.py',
   'PYMODULE'),
  ('google.api_core.operations_v1.operations_rest_client_async',
   'c:\\Users\\<USER>\\Desktop\\Mes_projets\\WritingToolsClean\\Windows_and_Linux\\myvenv\\Lib\\site-packages\\google\\api_core\\operations_v1\\operations_rest_client_async.py',
   'PYMODULE'),
  ('google.api_core.operations_v1.pagers',
   'c:\\Users\\<USER>\\Desktop\\Mes_projets\\WritingToolsClean\\Windows_and_Linux\\myvenv\\Lib\\site-packages\\google\\api_core\\operations_v1\\pagers.py',
   'PYMODULE'),
  ('google.api_core.operations_v1.pagers_async',
   'c:\\Users\\<USER>\\Desktop\\Mes_projets\\WritingToolsClean\\Windows_and_Linux\\myvenv\\Lib\\site-packages\\google\\api_core\\operations_v1\\pagers_async.py',
   'PYMODULE'),
  ('google.api_core.operations_v1.pagers_base',
   'c:\\Users\\<USER>\\Desktop\\Mes_projets\\WritingToolsClean\\Windows_and_Linux\\myvenv\\Lib\\site-packages\\google\\api_core\\operations_v1\\pagers_base.py',
   'PYMODULE'),
  ('google.api_core.operations_v1.transports',
   'c:\\Users\\<USER>\\Desktop\\Mes_projets\\WritingToolsClean\\Windows_and_Linux\\myvenv\\Lib\\site-packages\\google\\api_core\\operations_v1\\transports\\__init__.py',
   'PYMODULE'),
  ('google.api_core.operations_v1.transports.base',
   'c:\\Users\\<USER>\\Desktop\\Mes_projets\\WritingToolsClean\\Windows_and_Linux\\myvenv\\Lib\\site-packages\\google\\api_core\\operations_v1\\transports\\base.py',
   'PYMODULE'),
  ('google.api_core.operations_v1.transports.rest',
   'c:\\Users\\<USER>\\Desktop\\Mes_projets\\WritingToolsClean\\Windows_and_Linux\\myvenv\\Lib\\site-packages\\google\\api_core\\operations_v1\\transports\\rest.py',
   'PYMODULE'),
  ('google.api_core.operations_v1.transports.rest_asyncio',
   'c:\\Users\\<USER>\\Desktop\\Mes_projets\\WritingToolsClean\\Windows_and_Linux\\myvenv\\Lib\\site-packages\\google\\api_core\\operations_v1\\transports\\rest_asyncio.py',
   'PYMODULE'),
  ('google.api_core.page_iterator',
   'c:\\Users\\<USER>\\Desktop\\Mes_projets\\WritingToolsClean\\Windows_and_Linux\\myvenv\\Lib\\site-packages\\google\\api_core\\page_iterator.py',
   'PYMODULE'),
  ('google.api_core.page_iterator_async',
   'c:\\Users\\<USER>\\Desktop\\Mes_projets\\WritingToolsClean\\Windows_and_Linux\\myvenv\\Lib\\site-packages\\google\\api_core\\page_iterator_async.py',
   'PYMODULE'),
  ('google.api_core.path_template',
   'c:\\Users\\<USER>\\Desktop\\Mes_projets\\WritingToolsClean\\Windows_and_Linux\\myvenv\\Lib\\site-packages\\google\\api_core\\path_template.py',
   'PYMODULE'),
  ('google.api_core.protobuf_helpers',
   'c:\\Users\\<USER>\\Desktop\\Mes_projets\\WritingToolsClean\\Windows_and_Linux\\myvenv\\Lib\\site-packages\\google\\api_core\\protobuf_helpers.py',
   'PYMODULE'),
  ('google.api_core.rest_helpers',
   'c:\\Users\\<USER>\\Desktop\\Mes_projets\\WritingToolsClean\\Windows_and_Linux\\myvenv\\Lib\\site-packages\\google\\api_core\\rest_helpers.py',
   'PYMODULE'),
  ('google.api_core.rest_streaming',
   'c:\\Users\\<USER>\\Desktop\\Mes_projets\\WritingToolsClean\\Windows_and_Linux\\myvenv\\Lib\\site-packages\\google\\api_core\\rest_streaming.py',
   'PYMODULE'),
  ('google.api_core.retry',
   'c:\\Users\\<USER>\\Desktop\\Mes_projets\\WritingToolsClean\\Windows_and_Linux\\myvenv\\Lib\\site-packages\\google\\api_core\\retry\\__init__.py',
   'PYMODULE'),
  ('google.api_core.retry.retry_base',
   'c:\\Users\\<USER>\\Desktop\\Mes_projets\\WritingToolsClean\\Windows_and_Linux\\myvenv\\Lib\\site-packages\\google\\api_core\\retry\\retry_base.py',
   'PYMODULE'),
  ('google.api_core.retry.retry_streaming',
   'c:\\Users\\<USER>\\Desktop\\Mes_projets\\WritingToolsClean\\Windows_and_Linux\\myvenv\\Lib\\site-packages\\google\\api_core\\retry\\retry_streaming.py',
   'PYMODULE'),
  ('google.api_core.retry.retry_streaming_async',
   'c:\\Users\\<USER>\\Desktop\\Mes_projets\\WritingToolsClean\\Windows_and_Linux\\myvenv\\Lib\\site-packages\\google\\api_core\\retry\\retry_streaming_async.py',
   'PYMODULE'),
  ('google.api_core.retry.retry_unary',
   'c:\\Users\\<USER>\\Desktop\\Mes_projets\\WritingToolsClean\\Windows_and_Linux\\myvenv\\Lib\\site-packages\\google\\api_core\\retry\\retry_unary.py',
   'PYMODULE'),
  ('google.api_core.retry.retry_unary_async',
   'c:\\Users\\<USER>\\Desktop\\Mes_projets\\WritingToolsClean\\Windows_and_Linux\\myvenv\\Lib\\site-packages\\google\\api_core\\retry\\retry_unary_async.py',
   'PYMODULE'),
  ('google.api_core.retry_async',
   'c:\\Users\\<USER>\\Desktop\\Mes_projets\\WritingToolsClean\\Windows_and_Linux\\myvenv\\Lib\\site-packages\\google\\api_core\\retry_async.py',
   'PYMODULE'),
  ('google.api_core.timeout',
   'c:\\Users\\<USER>\\Desktop\\Mes_projets\\WritingToolsClean\\Windows_and_Linux\\myvenv\\Lib\\site-packages\\google\\api_core\\timeout.py',
   'PYMODULE'),
  ('google.api_core.universe',
   'c:\\Users\\<USER>\\Desktop\\Mes_projets\\WritingToolsClean\\Windows_and_Linux\\myvenv\\Lib\\site-packages\\google\\api_core\\universe.py',
   'PYMODULE'),
  ('google.api_core.version',
   'c:\\Users\\<USER>\\Desktop\\Mes_projets\\WritingToolsClean\\Windows_and_Linux\\myvenv\\Lib\\site-packages\\google\\api_core\\version.py',
   'PYMODULE'),
  ('google.api_core.version_header',
   'c:\\Users\\<USER>\\Desktop\\Mes_projets\\WritingToolsClean\\Windows_and_Linux\\myvenv\\Lib\\site-packages\\google\\api_core\\version_header.py',
   'PYMODULE'),
  ('google.auth',
   'c:\\Users\\<USER>\\Desktop\\Mes_projets\\WritingToolsClean\\Windows_and_Linux\\myvenv\\Lib\\site-packages\\google\\auth\\__init__.py',
   'PYMODULE'),
  ('google.auth._cloud_sdk',
   'c:\\Users\\<USER>\\Desktop\\Mes_projets\\WritingToolsClean\\Windows_and_Linux\\myvenv\\Lib\\site-packages\\google\\auth\\_cloud_sdk.py',
   'PYMODULE'),
  ('google.auth._credentials_base',
   'c:\\Users\\<USER>\\Desktop\\Mes_projets\\WritingToolsClean\\Windows_and_Linux\\myvenv\\Lib\\site-packages\\google\\auth\\_credentials_base.py',
   'PYMODULE'),
  ('google.auth._default',
   'c:\\Users\\<USER>\\Desktop\\Mes_projets\\WritingToolsClean\\Windows_and_Linux\\myvenv\\Lib\\site-packages\\google\\auth\\_default.py',
   'PYMODULE'),
  ('google.auth._exponential_backoff',
   'c:\\Users\\<USER>\\Desktop\\Mes_projets\\WritingToolsClean\\Windows_and_Linux\\myvenv\\Lib\\site-packages\\google\\auth\\_exponential_backoff.py',
   'PYMODULE'),
  ('google.auth._helpers',
   'c:\\Users\\<USER>\\Desktop\\Mes_projets\\WritingToolsClean\\Windows_and_Linux\\myvenv\\Lib\\site-packages\\google\\auth\\_helpers.py',
   'PYMODULE'),
  ('google.auth._refresh_worker',
   'c:\\Users\\<USER>\\Desktop\\Mes_projets\\WritingToolsClean\\Windows_and_Linux\\myvenv\\Lib\\site-packages\\google\\auth\\_refresh_worker.py',
   'PYMODULE'),
  ('google.auth._service_account_info',
   'c:\\Users\\<USER>\\Desktop\\Mes_projets\\WritingToolsClean\\Windows_and_Linux\\myvenv\\Lib\\site-packages\\google\\auth\\_service_account_info.py',
   'PYMODULE'),
  ('google.auth.aio',
   'c:\\Users\\<USER>\\Desktop\\Mes_projets\\WritingToolsClean\\Windows_and_Linux\\myvenv\\Lib\\site-packages\\google\\auth\\aio\\__init__.py',
   'PYMODULE'),
  ('google.auth.aio._helpers',
   'c:\\Users\\<USER>\\Desktop\\Mes_projets\\WritingToolsClean\\Windows_and_Linux\\myvenv\\Lib\\site-packages\\google\\auth\\aio\\_helpers.py',
   'PYMODULE'),
  ('google.auth.aio.credentials',
   'c:\\Users\\<USER>\\Desktop\\Mes_projets\\WritingToolsClean\\Windows_and_Linux\\myvenv\\Lib\\site-packages\\google\\auth\\aio\\credentials.py',
   'PYMODULE'),
  ('google.auth.aio.transport',
   'c:\\Users\\<USER>\\Desktop\\Mes_projets\\WritingToolsClean\\Windows_and_Linux\\myvenv\\Lib\\site-packages\\google\\auth\\aio\\transport\\__init__.py',
   'PYMODULE'),
  ('google.auth.aio.transport.aiohttp',
   'c:\\Users\\<USER>\\Desktop\\Mes_projets\\WritingToolsClean\\Windows_and_Linux\\myvenv\\Lib\\site-packages\\google\\auth\\aio\\transport\\aiohttp.py',
   'PYMODULE'),
  ('google.auth.aio.transport.sessions',
   'c:\\Users\\<USER>\\Desktop\\Mes_projets\\WritingToolsClean\\Windows_and_Linux\\myvenv\\Lib\\site-packages\\google\\auth\\aio\\transport\\sessions.py',
   'PYMODULE'),
  ('google.auth.api_key',
   'c:\\Users\\<USER>\\Desktop\\Mes_projets\\WritingToolsClean\\Windows_and_Linux\\myvenv\\Lib\\site-packages\\google\\auth\\api_key.py',
   'PYMODULE'),
  ('google.auth.app_engine',
   'c:\\Users\\<USER>\\Desktop\\Mes_projets\\WritingToolsClean\\Windows_and_Linux\\myvenv\\Lib\\site-packages\\google\\auth\\app_engine.py',
   'PYMODULE'),
  ('google.auth.aws',
   'c:\\Users\\<USER>\\Desktop\\Mes_projets\\WritingToolsClean\\Windows_and_Linux\\myvenv\\Lib\\site-packages\\google\\auth\\aws.py',
   'PYMODULE'),
  ('google.auth.compute_engine',
   'c:\\Users\\<USER>\\Desktop\\Mes_projets\\WritingToolsClean\\Windows_and_Linux\\myvenv\\Lib\\site-packages\\google\\auth\\compute_engine\\__init__.py',
   'PYMODULE'),
  ('google.auth.compute_engine._metadata',
   'c:\\Users\\<USER>\\Desktop\\Mes_projets\\WritingToolsClean\\Windows_and_Linux\\myvenv\\Lib\\site-packages\\google\\auth\\compute_engine\\_metadata.py',
   'PYMODULE'),
  ('google.auth.compute_engine.credentials',
   'c:\\Users\\<USER>\\Desktop\\Mes_projets\\WritingToolsClean\\Windows_and_Linux\\myvenv\\Lib\\site-packages\\google\\auth\\compute_engine\\credentials.py',
   'PYMODULE'),
  ('google.auth.credentials',
   'c:\\Users\\<USER>\\Desktop\\Mes_projets\\WritingToolsClean\\Windows_and_Linux\\myvenv\\Lib\\site-packages\\google\\auth\\credentials.py',
   'PYMODULE'),
  ('google.auth.crypt',
   'c:\\Users\\<USER>\\Desktop\\Mes_projets\\WritingToolsClean\\Windows_and_Linux\\myvenv\\Lib\\site-packages\\google\\auth\\crypt\\__init__.py',
   'PYMODULE'),
  ('google.auth.crypt._cryptography_rsa',
   'c:\\Users\\<USER>\\Desktop\\Mes_projets\\WritingToolsClean\\Windows_and_Linux\\myvenv\\Lib\\site-packages\\google\\auth\\crypt\\_cryptography_rsa.py',
   'PYMODULE'),
  ('google.auth.crypt._python_rsa',
   'c:\\Users\\<USER>\\Desktop\\Mes_projets\\WritingToolsClean\\Windows_and_Linux\\myvenv\\Lib\\site-packages\\google\\auth\\crypt\\_python_rsa.py',
   'PYMODULE'),
  ('google.auth.crypt.base',
   'c:\\Users\\<USER>\\Desktop\\Mes_projets\\WritingToolsClean\\Windows_and_Linux\\myvenv\\Lib\\site-packages\\google\\auth\\crypt\\base.py',
   'PYMODULE'),
  ('google.auth.crypt.es256',
   'c:\\Users\\<USER>\\Desktop\\Mes_projets\\WritingToolsClean\\Windows_and_Linux\\myvenv\\Lib\\site-packages\\google\\auth\\crypt\\es256.py',
   'PYMODULE'),
  ('google.auth.crypt.rsa',
   'c:\\Users\\<USER>\\Desktop\\Mes_projets\\WritingToolsClean\\Windows_and_Linux\\myvenv\\Lib\\site-packages\\google\\auth\\crypt\\rsa.py',
   'PYMODULE'),
  ('google.auth.environment_vars',
   'c:\\Users\\<USER>\\Desktop\\Mes_projets\\WritingToolsClean\\Windows_and_Linux\\myvenv\\Lib\\site-packages\\google\\auth\\environment_vars.py',
   'PYMODULE'),
  ('google.auth.exceptions',
   'c:\\Users\\<USER>\\Desktop\\Mes_projets\\WritingToolsClean\\Windows_and_Linux\\myvenv\\Lib\\site-packages\\google\\auth\\exceptions.py',
   'PYMODULE'),
  ('google.auth.external_account',
   'c:\\Users\\<USER>\\Desktop\\Mes_projets\\WritingToolsClean\\Windows_and_Linux\\myvenv\\Lib\\site-packages\\google\\auth\\external_account.py',
   'PYMODULE'),
  ('google.auth.external_account_authorized_user',
   'c:\\Users\\<USER>\\Desktop\\Mes_projets\\WritingToolsClean\\Windows_and_Linux\\myvenv\\Lib\\site-packages\\google\\auth\\external_account_authorized_user.py',
   'PYMODULE'),
  ('google.auth.iam',
   'c:\\Users\\<USER>\\Desktop\\Mes_projets\\WritingToolsClean\\Windows_and_Linux\\myvenv\\Lib\\site-packages\\google\\auth\\iam.py',
   'PYMODULE'),
  ('google.auth.identity_pool',
   'c:\\Users\\<USER>\\Desktop\\Mes_projets\\WritingToolsClean\\Windows_and_Linux\\myvenv\\Lib\\site-packages\\google\\auth\\identity_pool.py',
   'PYMODULE'),
  ('google.auth.impersonated_credentials',
   'c:\\Users\\<USER>\\Desktop\\Mes_projets\\WritingToolsClean\\Windows_and_Linux\\myvenv\\Lib\\site-packages\\google\\auth\\impersonated_credentials.py',
   'PYMODULE'),
  ('google.auth.jwt',
   'c:\\Users\\<USER>\\Desktop\\Mes_projets\\WritingToolsClean\\Windows_and_Linux\\myvenv\\Lib\\site-packages\\google\\auth\\jwt.py',
   'PYMODULE'),
  ('google.auth.metrics',
   'c:\\Users\\<USER>\\Desktop\\Mes_projets\\WritingToolsClean\\Windows_and_Linux\\myvenv\\Lib\\site-packages\\google\\auth\\metrics.py',
   'PYMODULE'),
  ('google.auth.pluggable',
   'c:\\Users\\<USER>\\Desktop\\Mes_projets\\WritingToolsClean\\Windows_and_Linux\\myvenv\\Lib\\site-packages\\google\\auth\\pluggable.py',
   'PYMODULE'),
  ('google.auth.transport',
   'c:\\Users\\<USER>\\Desktop\\Mes_projets\\WritingToolsClean\\Windows_and_Linux\\myvenv\\Lib\\site-packages\\google\\auth\\transport\\__init__.py',
   'PYMODULE'),
  ('google.auth.transport._custom_tls_signer',
   'c:\\Users\\<USER>\\Desktop\\Mes_projets\\WritingToolsClean\\Windows_and_Linux\\myvenv\\Lib\\site-packages\\google\\auth\\transport\\_custom_tls_signer.py',
   'PYMODULE'),
  ('google.auth.transport._http_client',
   'c:\\Users\\<USER>\\Desktop\\Mes_projets\\WritingToolsClean\\Windows_and_Linux\\myvenv\\Lib\\site-packages\\google\\auth\\transport\\_http_client.py',
   'PYMODULE'),
  ('google.auth.transport._mtls_helper',
   'c:\\Users\\<USER>\\Desktop\\Mes_projets\\WritingToolsClean\\Windows_and_Linux\\myvenv\\Lib\\site-packages\\google\\auth\\transport\\_mtls_helper.py',
   'PYMODULE'),
  ('google.auth.transport.grpc',
   'c:\\Users\\<USER>\\Desktop\\Mes_projets\\WritingToolsClean\\Windows_and_Linux\\myvenv\\Lib\\site-packages\\google\\auth\\transport\\grpc.py',
   'PYMODULE'),
  ('google.auth.transport.mtls',
   'c:\\Users\\<USER>\\Desktop\\Mes_projets\\WritingToolsClean\\Windows_and_Linux\\myvenv\\Lib\\site-packages\\google\\auth\\transport\\mtls.py',
   'PYMODULE'),
  ('google.auth.transport.requests',
   'c:\\Users\\<USER>\\Desktop\\Mes_projets\\WritingToolsClean\\Windows_and_Linux\\myvenv\\Lib\\site-packages\\google\\auth\\transport\\requests.py',
   'PYMODULE'),
  ('google.auth.version',
   'c:\\Users\\<USER>\\Desktop\\Mes_projets\\WritingToolsClean\\Windows_and_Linux\\myvenv\\Lib\\site-packages\\google\\auth\\version.py',
   'PYMODULE'),
  ('google.generativeai',
   'c:\\Users\\<USER>\\Desktop\\Mes_projets\\WritingToolsClean\\Windows_and_Linux\\myvenv\\Lib\\site-packages\\google\\generativeai\\__init__.py',
   'PYMODULE'),
  ('google.generativeai.caching',
   'c:\\Users\\<USER>\\Desktop\\Mes_projets\\WritingToolsClean\\Windows_and_Linux\\myvenv\\Lib\\site-packages\\google\\generativeai\\caching.py',
   'PYMODULE'),
  ('google.generativeai.client',
   'c:\\Users\\<USER>\\Desktop\\Mes_projets\\WritingToolsClean\\Windows_and_Linux\\myvenv\\Lib\\site-packages\\google\\generativeai\\client.py',
   'PYMODULE'),
  ('google.generativeai.embedding',
   'c:\\Users\\<USER>\\Desktop\\Mes_projets\\WritingToolsClean\\Windows_and_Linux\\myvenv\\Lib\\site-packages\\google\\generativeai\\embedding.py',
   'PYMODULE'),
  ('google.generativeai.files',
   'c:\\Users\\<USER>\\Desktop\\Mes_projets\\WritingToolsClean\\Windows_and_Linux\\myvenv\\Lib\\site-packages\\google\\generativeai\\files.py',
   'PYMODULE'),
  ('google.generativeai.generative_models',
   'c:\\Users\\<USER>\\Desktop\\Mes_projets\\WritingToolsClean\\Windows_and_Linux\\myvenv\\Lib\\site-packages\\google\\generativeai\\generative_models.py',
   'PYMODULE'),
  ('google.generativeai.models',
   'c:\\Users\\<USER>\\Desktop\\Mes_projets\\WritingToolsClean\\Windows_and_Linux\\myvenv\\Lib\\site-packages\\google\\generativeai\\models.py',
   'PYMODULE'),
  ('google.generativeai.operations',
   'c:\\Users\\<USER>\\Desktop\\Mes_projets\\WritingToolsClean\\Windows_and_Linux\\myvenv\\Lib\\site-packages\\google\\generativeai\\operations.py',
   'PYMODULE'),
  ('google.generativeai.protos',
   'c:\\Users\\<USER>\\Desktop\\Mes_projets\\WritingToolsClean\\Windows_and_Linux\\myvenv\\Lib\\site-packages\\google\\generativeai\\protos.py',
   'PYMODULE'),
  ('google.generativeai.responder',
   'c:\\Users\\<USER>\\Desktop\\Mes_projets\\WritingToolsClean\\Windows_and_Linux\\myvenv\\Lib\\site-packages\\google\\generativeai\\responder.py',
   'PYMODULE'),
  ('google.generativeai.string_utils',
   'c:\\Users\\<USER>\\Desktop\\Mes_projets\\WritingToolsClean\\Windows_and_Linux\\myvenv\\Lib\\site-packages\\google\\generativeai\\string_utils.py',
   'PYMODULE'),
  ('google.generativeai.types',
   'c:\\Users\\<USER>\\Desktop\\Mes_projets\\WritingToolsClean\\Windows_and_Linux\\myvenv\\Lib\\site-packages\\google\\generativeai\\types\\__init__.py',
   'PYMODULE'),
  ('google.generativeai.types.caching_types',
   'c:\\Users\\<USER>\\Desktop\\Mes_projets\\WritingToolsClean\\Windows_and_Linux\\myvenv\\Lib\\site-packages\\google\\generativeai\\types\\caching_types.py',
   'PYMODULE'),
  ('google.generativeai.types.citation_types',
   'c:\\Users\\<USER>\\Desktop\\Mes_projets\\WritingToolsClean\\Windows_and_Linux\\myvenv\\Lib\\site-packages\\google\\generativeai\\types\\citation_types.py',
   'PYMODULE'),
  ('google.generativeai.types.content_types',
   'c:\\Users\\<USER>\\Desktop\\Mes_projets\\WritingToolsClean\\Windows_and_Linux\\myvenv\\Lib\\site-packages\\google\\generativeai\\types\\content_types.py',
   'PYMODULE'),
  ('google.generativeai.types.file_types',
   'c:\\Users\\<USER>\\Desktop\\Mes_projets\\WritingToolsClean\\Windows_and_Linux\\myvenv\\Lib\\site-packages\\google\\generativeai\\types\\file_types.py',
   'PYMODULE'),
  ('google.generativeai.types.generation_types',
   'c:\\Users\\<USER>\\Desktop\\Mes_projets\\WritingToolsClean\\Windows_and_Linux\\myvenv\\Lib\\site-packages\\google\\generativeai\\types\\generation_types.py',
   'PYMODULE'),
  ('google.generativeai.types.helper_types',
   'c:\\Users\\<USER>\\Desktop\\Mes_projets\\WritingToolsClean\\Windows_and_Linux\\myvenv\\Lib\\site-packages\\google\\generativeai\\types\\helper_types.py',
   'PYMODULE'),
  ('google.generativeai.types.model_types',
   'c:\\Users\\<USER>\\Desktop\\Mes_projets\\WritingToolsClean\\Windows_and_Linux\\myvenv\\Lib\\site-packages\\google\\generativeai\\types\\model_types.py',
   'PYMODULE'),
  ('google.generativeai.types.permission_types',
   'c:\\Users\\<USER>\\Desktop\\Mes_projets\\WritingToolsClean\\Windows_and_Linux\\myvenv\\Lib\\site-packages\\google\\generativeai\\types\\permission_types.py',
   'PYMODULE'),
  ('google.generativeai.types.safety_types',
   'c:\\Users\\<USER>\\Desktop\\Mes_projets\\WritingToolsClean\\Windows_and_Linux\\myvenv\\Lib\\site-packages\\google\\generativeai\\types\\safety_types.py',
   'PYMODULE'),
  ('google.generativeai.types.text_types',
   'c:\\Users\\<USER>\\Desktop\\Mes_projets\\WritingToolsClean\\Windows_and_Linux\\myvenv\\Lib\\site-packages\\google\\generativeai\\types\\text_types.py',
   'PYMODULE'),
  ('google.generativeai.utils',
   'c:\\Users\\<USER>\\Desktop\\Mes_projets\\WritingToolsClean\\Windows_and_Linux\\myvenv\\Lib\\site-packages\\google\\generativeai\\utils.py',
   'PYMODULE'),
  ('google.generativeai.version',
   'c:\\Users\\<USER>\\Desktop\\Mes_projets\\WritingToolsClean\\Windows_and_Linux\\myvenv\\Lib\\site-packages\\google\\generativeai\\version.py',
   'PYMODULE'),
  ('google.longrunning', '-', 'PYMODULE'),
  ('google.longrunning.operations_grpc_pb2',
   'c:\\Users\\<USER>\\Desktop\\Mes_projets\\WritingToolsClean\\Windows_and_Linux\\myvenv\\Lib\\site-packages\\google\\longrunning\\operations_grpc_pb2.py',
   'PYMODULE'),
  ('google.longrunning.operations_pb2',
   'c:\\Users\\<USER>\\Desktop\\Mes_projets\\WritingToolsClean\\Windows_and_Linux\\myvenv\\Lib\\site-packages\\google\\longrunning\\operations_pb2.py',
   'PYMODULE'),
  ('google.longrunning.operations_pb2_grpc',
   'c:\\Users\\<USER>\\Desktop\\Mes_projets\\WritingToolsClean\\Windows_and_Linux\\myvenv\\Lib\\site-packages\\google\\longrunning\\operations_pb2_grpc.py',
   'PYMODULE'),
  ('google.longrunning.operations_proto_pb2',
   'c:\\Users\\<USER>\\Desktop\\Mes_projets\\WritingToolsClean\\Windows_and_Linux\\myvenv\\Lib\\site-packages\\google\\longrunning\\operations_proto_pb2.py',
   'PYMODULE'),
  ('google.oauth2',
   'c:\\Users\\<USER>\\Desktop\\Mes_projets\\WritingToolsClean\\Windows_and_Linux\\myvenv\\Lib\\site-packages\\google\\oauth2\\__init__.py',
   'PYMODULE'),
  ('google.oauth2._client',
   'c:\\Users\\<USER>\\Desktop\\Mes_projets\\WritingToolsClean\\Windows_and_Linux\\myvenv\\Lib\\site-packages\\google\\oauth2\\_client.py',
   'PYMODULE'),
  ('google.oauth2.challenges',
   'c:\\Users\\<USER>\\Desktop\\Mes_projets\\WritingToolsClean\\Windows_and_Linux\\myvenv\\Lib\\site-packages\\google\\oauth2\\challenges.py',
   'PYMODULE'),
  ('google.oauth2.credentials',
   'c:\\Users\\<USER>\\Desktop\\Mes_projets\\WritingToolsClean\\Windows_and_Linux\\myvenv\\Lib\\site-packages\\google\\oauth2\\credentials.py',
   'PYMODULE'),
  ('google.oauth2.gdch_credentials',
   'c:\\Users\\<USER>\\Desktop\\Mes_projets\\WritingToolsClean\\Windows_and_Linux\\myvenv\\Lib\\site-packages\\google\\oauth2\\gdch_credentials.py',
   'PYMODULE'),
  ('google.oauth2.reauth',
   'c:\\Users\\<USER>\\Desktop\\Mes_projets\\WritingToolsClean\\Windows_and_Linux\\myvenv\\Lib\\site-packages\\google\\oauth2\\reauth.py',
   'PYMODULE'),
  ('google.oauth2.service_account',
   'c:\\Users\\<USER>\\Desktop\\Mes_projets\\WritingToolsClean\\Windows_and_Linux\\myvenv\\Lib\\site-packages\\google\\oauth2\\service_account.py',
   'PYMODULE'),
  ('google.oauth2.sts',
   'c:\\Users\\<USER>\\Desktop\\Mes_projets\\WritingToolsClean\\Windows_and_Linux\\myvenv\\Lib\\site-packages\\google\\oauth2\\sts.py',
   'PYMODULE'),
  ('google.oauth2.utils',
   'c:\\Users\\<USER>\\Desktop\\Mes_projets\\WritingToolsClean\\Windows_and_Linux\\myvenv\\Lib\\site-packages\\google\\oauth2\\utils.py',
   'PYMODULE'),
  ('google.oauth2.webauthn_handler',
   'c:\\Users\\<USER>\\Desktop\\Mes_projets\\WritingToolsClean\\Windows_and_Linux\\myvenv\\Lib\\site-packages\\google\\oauth2\\webauthn_handler.py',
   'PYMODULE'),
  ('google.oauth2.webauthn_handler_factory',
   'c:\\Users\\<USER>\\Desktop\\Mes_projets\\WritingToolsClean\\Windows_and_Linux\\myvenv\\Lib\\site-packages\\google\\oauth2\\webauthn_handler_factory.py',
   'PYMODULE'),
  ('google.oauth2.webauthn_types',
   'c:\\Users\\<USER>\\Desktop\\Mes_projets\\WritingToolsClean\\Windows_and_Linux\\myvenv\\Lib\\site-packages\\google\\oauth2\\webauthn_types.py',
   'PYMODULE'),
  ('google.protobuf',
   'c:\\Users\\<USER>\\Desktop\\Mes_projets\\WritingToolsClean\\Windows_and_Linux\\myvenv\\Lib\\site-packages\\google\\protobuf\\__init__.py',
   'PYMODULE'),
  ('google.protobuf.any_pb2',
   'c:\\Users\\<USER>\\Desktop\\Mes_projets\\WritingToolsClean\\Windows_and_Linux\\myvenv\\Lib\\site-packages\\google\\protobuf\\any_pb2.py',
   'PYMODULE'),
  ('google.protobuf.descriptor',
   'c:\\Users\\<USER>\\Desktop\\Mes_projets\\WritingToolsClean\\Windows_and_Linux\\myvenv\\Lib\\site-packages\\google\\protobuf\\descriptor.py',
   'PYMODULE'),
  ('google.protobuf.descriptor_database',
   'c:\\Users\\<USER>\\Desktop\\Mes_projets\\WritingToolsClean\\Windows_and_Linux\\myvenv\\Lib\\site-packages\\google\\protobuf\\descriptor_database.py',
   'PYMODULE'),
  ('google.protobuf.descriptor_pb2',
   'c:\\Users\\<USER>\\Desktop\\Mes_projets\\WritingToolsClean\\Windows_and_Linux\\myvenv\\Lib\\site-packages\\google\\protobuf\\descriptor_pb2.py',
   'PYMODULE'),
  ('google.protobuf.descriptor_pool',
   'c:\\Users\\<USER>\\Desktop\\Mes_projets\\WritingToolsClean\\Windows_and_Linux\\myvenv\\Lib\\site-packages\\google\\protobuf\\descriptor_pool.py',
   'PYMODULE'),
  ('google.protobuf.duration_pb2',
   'c:\\Users\\<USER>\\Desktop\\Mes_projets\\WritingToolsClean\\Windows_and_Linux\\myvenv\\Lib\\site-packages\\google\\protobuf\\duration_pb2.py',
   'PYMODULE'),
  ('google.protobuf.empty_pb2',
   'c:\\Users\\<USER>\\Desktop\\Mes_projets\\WritingToolsClean\\Windows_and_Linux\\myvenv\\Lib\\site-packages\\google\\protobuf\\empty_pb2.py',
   'PYMODULE'),
  ('google.protobuf.field_mask_pb2',
   'c:\\Users\\<USER>\\Desktop\\Mes_projets\\WritingToolsClean\\Windows_and_Linux\\myvenv\\Lib\\site-packages\\google\\protobuf\\field_mask_pb2.py',
   'PYMODULE'),
  ('google.protobuf.internal',
   'c:\\Users\\<USER>\\Desktop\\Mes_projets\\WritingToolsClean\\Windows_and_Linux\\myvenv\\Lib\\site-packages\\google\\protobuf\\internal\\__init__.py',
   'PYMODULE'),
  ('google.protobuf.internal.api_implementation',
   'c:\\Users\\<USER>\\Desktop\\Mes_projets\\WritingToolsClean\\Windows_and_Linux\\myvenv\\Lib\\site-packages\\google\\protobuf\\internal\\api_implementation.py',
   'PYMODULE'),
  ('google.protobuf.internal.builder',
   'c:\\Users\\<USER>\\Desktop\\Mes_projets\\WritingToolsClean\\Windows_and_Linux\\myvenv\\Lib\\site-packages\\google\\protobuf\\internal\\builder.py',
   'PYMODULE'),
  ('google.protobuf.internal.containers',
   'c:\\Users\\<USER>\\Desktop\\Mes_projets\\WritingToolsClean\\Windows_and_Linux\\myvenv\\Lib\\site-packages\\google\\protobuf\\internal\\containers.py',
   'PYMODULE'),
  ('google.protobuf.internal.decoder',
   'c:\\Users\\<USER>\\Desktop\\Mes_projets\\WritingToolsClean\\Windows_and_Linux\\myvenv\\Lib\\site-packages\\google\\protobuf\\internal\\decoder.py',
   'PYMODULE'),
  ('google.protobuf.internal.encoder',
   'c:\\Users\\<USER>\\Desktop\\Mes_projets\\WritingToolsClean\\Windows_and_Linux\\myvenv\\Lib\\site-packages\\google\\protobuf\\internal\\encoder.py',
   'PYMODULE'),
  ('google.protobuf.internal.enum_type_wrapper',
   'c:\\Users\\<USER>\\Desktop\\Mes_projets\\WritingToolsClean\\Windows_and_Linux\\myvenv\\Lib\\site-packages\\google\\protobuf\\internal\\enum_type_wrapper.py',
   'PYMODULE'),
  ('google.protobuf.internal.extension_dict',
   'c:\\Users\\<USER>\\Desktop\\Mes_projets\\WritingToolsClean\\Windows_and_Linux\\myvenv\\Lib\\site-packages\\google\\protobuf\\internal\\extension_dict.py',
   'PYMODULE'),
  ('google.protobuf.internal.field_mask',
   'c:\\Users\\<USER>\\Desktop\\Mes_projets\\WritingToolsClean\\Windows_and_Linux\\myvenv\\Lib\\site-packages\\google\\protobuf\\internal\\field_mask.py',
   'PYMODULE'),
  ('google.protobuf.internal.message_listener',
   'c:\\Users\\<USER>\\Desktop\\Mes_projets\\WritingToolsClean\\Windows_and_Linux\\myvenv\\Lib\\site-packages\\google\\protobuf\\internal\\message_listener.py',
   'PYMODULE'),
  ('google.protobuf.internal.python_edition_defaults',
   'c:\\Users\\<USER>\\Desktop\\Mes_projets\\WritingToolsClean\\Windows_and_Linux\\myvenv\\Lib\\site-packages\\google\\protobuf\\internal\\python_edition_defaults.py',
   'PYMODULE'),
  ('google.protobuf.internal.python_message',
   'c:\\Users\\<USER>\\Desktop\\Mes_projets\\WritingToolsClean\\Windows_and_Linux\\myvenv\\Lib\\site-packages\\google\\protobuf\\internal\\python_message.py',
   'PYMODULE'),
  ('google.protobuf.internal.type_checkers',
   'c:\\Users\\<USER>\\Desktop\\Mes_projets\\WritingToolsClean\\Windows_and_Linux\\myvenv\\Lib\\site-packages\\google\\protobuf\\internal\\type_checkers.py',
   'PYMODULE'),
  ('google.protobuf.internal.well_known_types',
   'c:\\Users\\<USER>\\Desktop\\Mes_projets\\WritingToolsClean\\Windows_and_Linux\\myvenv\\Lib\\site-packages\\google\\protobuf\\internal\\well_known_types.py',
   'PYMODULE'),
  ('google.protobuf.internal.wire_format',
   'c:\\Users\\<USER>\\Desktop\\Mes_projets\\WritingToolsClean\\Windows_and_Linux\\myvenv\\Lib\\site-packages\\google\\protobuf\\internal\\wire_format.py',
   'PYMODULE'),
  ('google.protobuf.json_format',
   'c:\\Users\\<USER>\\Desktop\\Mes_projets\\WritingToolsClean\\Windows_and_Linux\\myvenv\\Lib\\site-packages\\google\\protobuf\\json_format.py',
   'PYMODULE'),
  ('google.protobuf.message',
   'c:\\Users\\<USER>\\Desktop\\Mes_projets\\WritingToolsClean\\Windows_and_Linux\\myvenv\\Lib\\site-packages\\google\\protobuf\\message.py',
   'PYMODULE'),
  ('google.protobuf.message_factory',
   'c:\\Users\\<USER>\\Desktop\\Mes_projets\\WritingToolsClean\\Windows_and_Linux\\myvenv\\Lib\\site-packages\\google\\protobuf\\message_factory.py',
   'PYMODULE'),
  ('google.protobuf.pyext',
   'c:\\Users\\<USER>\\Desktop\\Mes_projets\\WritingToolsClean\\Windows_and_Linux\\myvenv\\Lib\\site-packages\\google\\protobuf\\pyext\\__init__.py',
   'PYMODULE'),
  ('google.protobuf.pyext.cpp_message',
   'c:\\Users\\<USER>\\Desktop\\Mes_projets\\WritingToolsClean\\Windows_and_Linux\\myvenv\\Lib\\site-packages\\google\\protobuf\\pyext\\cpp_message.py',
   'PYMODULE'),
  ('google.protobuf.reflection',
   'c:\\Users\\<USER>\\Desktop\\Mes_projets\\WritingToolsClean\\Windows_and_Linux\\myvenv\\Lib\\site-packages\\google\\protobuf\\reflection.py',
   'PYMODULE'),
  ('google.protobuf.runtime_version',
   'c:\\Users\\<USER>\\Desktop\\Mes_projets\\WritingToolsClean\\Windows_and_Linux\\myvenv\\Lib\\site-packages\\google\\protobuf\\runtime_version.py',
   'PYMODULE'),
  ('google.protobuf.service_reflection',
   'c:\\Users\\<USER>\\Desktop\\Mes_projets\\WritingToolsClean\\Windows_and_Linux\\myvenv\\Lib\\site-packages\\google\\protobuf\\service_reflection.py',
   'PYMODULE'),
  ('google.protobuf.struct_pb2',
   'c:\\Users\\<USER>\\Desktop\\Mes_projets\\WritingToolsClean\\Windows_and_Linux\\myvenv\\Lib\\site-packages\\google\\protobuf\\struct_pb2.py',
   'PYMODULE'),
  ('google.protobuf.symbol_database',
   'c:\\Users\\<USER>\\Desktop\\Mes_projets\\WritingToolsClean\\Windows_and_Linux\\myvenv\\Lib\\site-packages\\google\\protobuf\\symbol_database.py',
   'PYMODULE'),
  ('google.protobuf.text_encoding',
   'c:\\Users\\<USER>\\Desktop\\Mes_projets\\WritingToolsClean\\Windows_and_Linux\\myvenv\\Lib\\site-packages\\google\\protobuf\\text_encoding.py',
   'PYMODULE'),
  ('google.protobuf.text_format',
   'c:\\Users\\<USER>\\Desktop\\Mes_projets\\WritingToolsClean\\Windows_and_Linux\\myvenv\\Lib\\site-packages\\google\\protobuf\\text_format.py',
   'PYMODULE'),
  ('google.protobuf.timestamp_pb2',
   'c:\\Users\\<USER>\\Desktop\\Mes_projets\\WritingToolsClean\\Windows_and_Linux\\myvenv\\Lib\\site-packages\\google\\protobuf\\timestamp_pb2.py',
   'PYMODULE'),
  ('google.protobuf.unknown_fields',
   'c:\\Users\\<USER>\\Desktop\\Mes_projets\\WritingToolsClean\\Windows_and_Linux\\myvenv\\Lib\\site-packages\\google\\protobuf\\unknown_fields.py',
   'PYMODULE'),
  ('google.protobuf.wrappers_pb2',
   'c:\\Users\\<USER>\\Desktop\\Mes_projets\\WritingToolsClean\\Windows_and_Linux\\myvenv\\Lib\\site-packages\\google\\protobuf\\wrappers_pb2.py',
   'PYMODULE'),
  ('google.rpc', '-', 'PYMODULE'),
  ('google.rpc.code_pb2',
   'c:\\Users\\<USER>\\Desktop\\Mes_projets\\WritingToolsClean\\Windows_and_Linux\\myvenv\\Lib\\site-packages\\google\\rpc\\code_pb2.py',
   'PYMODULE'),
  ('google.rpc.error_details_pb2',
   'c:\\Users\\<USER>\\Desktop\\Mes_projets\\WritingToolsClean\\Windows_and_Linux\\myvenv\\Lib\\site-packages\\google\\rpc\\error_details_pb2.py',
   'PYMODULE'),
  ('google.rpc.status_pb2',
   'c:\\Users\\<USER>\\Desktop\\Mes_projets\\WritingToolsClean\\Windows_and_Linux\\myvenv\\Lib\\site-packages\\google\\rpc\\status_pb2.py',
   'PYMODULE'),
  ('google_auth_httplib2',
   'c:\\Users\\<USER>\\Desktop\\Mes_projets\\WritingToolsClean\\Windows_and_Linux\\myvenv\\Lib\\site-packages\\google_auth_httplib2.py',
   'PYMODULE'),
  ('googleapiclient',
   'c:\\Users\\<USER>\\Desktop\\Mes_projets\\WritingToolsClean\\Windows_and_Linux\\myvenv\\Lib\\site-packages\\googleapiclient\\__init__.py',
   'PYMODULE'),
  ('googleapiclient._auth',
   'c:\\Users\\<USER>\\Desktop\\Mes_projets\\WritingToolsClean\\Windows_and_Linux\\myvenv\\Lib\\site-packages\\googleapiclient\\_auth.py',
   'PYMODULE'),
  ('googleapiclient._helpers',
   'c:\\Users\\<USER>\\Desktop\\Mes_projets\\WritingToolsClean\\Windows_and_Linux\\myvenv\\Lib\\site-packages\\googleapiclient\\_helpers.py',
   'PYMODULE'),
  ('googleapiclient.discovery',
   'c:\\Users\\<USER>\\Desktop\\Mes_projets\\WritingToolsClean\\Windows_and_Linux\\myvenv\\Lib\\site-packages\\googleapiclient\\discovery.py',
   'PYMODULE'),
  ('googleapiclient.discovery_cache',
   'c:\\Users\\<USER>\\Desktop\\Mes_projets\\WritingToolsClean\\Windows_and_Linux\\myvenv\\Lib\\site-packages\\googleapiclient\\discovery_cache\\__init__.py',
   'PYMODULE'),
  ('googleapiclient.discovery_cache.appengine_memcache',
   'c:\\Users\\<USER>\\Desktop\\Mes_projets\\WritingToolsClean\\Windows_and_Linux\\myvenv\\Lib\\site-packages\\googleapiclient\\discovery_cache\\appengine_memcache.py',
   'PYMODULE'),
  ('googleapiclient.discovery_cache.base',
   'c:\\Users\\<USER>\\Desktop\\Mes_projets\\WritingToolsClean\\Windows_and_Linux\\myvenv\\Lib\\site-packages\\googleapiclient\\discovery_cache\\base.py',
   'PYMODULE'),
  ('googleapiclient.discovery_cache.file_cache',
   'c:\\Users\\<USER>\\Desktop\\Mes_projets\\WritingToolsClean\\Windows_and_Linux\\myvenv\\Lib\\site-packages\\googleapiclient\\discovery_cache\\file_cache.py',
   'PYMODULE'),
  ('googleapiclient.errors',
   'c:\\Users\\<USER>\\Desktop\\Mes_projets\\WritingToolsClean\\Windows_and_Linux\\myvenv\\Lib\\site-packages\\googleapiclient\\errors.py',
   'PYMODULE'),
  ('googleapiclient.http',
   'c:\\Users\\<USER>\\Desktop\\Mes_projets\\WritingToolsClean\\Windows_and_Linux\\myvenv\\Lib\\site-packages\\googleapiclient\\http.py',
   'PYMODULE'),
  ('googleapiclient.mimeparse',
   'c:\\Users\\<USER>\\Desktop\\Mes_projets\\WritingToolsClean\\Windows_and_Linux\\myvenv\\Lib\\site-packages\\googleapiclient\\mimeparse.py',
   'PYMODULE'),
  ('googleapiclient.model',
   'c:\\Users\\<USER>\\Desktop\\Mes_projets\\WritingToolsClean\\Windows_and_Linux\\myvenv\\Lib\\site-packages\\googleapiclient\\model.py',
   'PYMODULE'),
  ('googleapiclient.schema',
   'c:\\Users\\<USER>\\Desktop\\Mes_projets\\WritingToolsClean\\Windows_and_Linux\\myvenv\\Lib\\site-packages\\googleapiclient\\schema.py',
   'PYMODULE'),
  ('googleapiclient.version',
   'c:\\Users\\<USER>\\Desktop\\Mes_projets\\WritingToolsClean\\Windows_and_Linux\\myvenv\\Lib\\site-packages\\googleapiclient\\version.py',
   'PYMODULE'),
  ('grpc',
   'c:\\Users\\<USER>\\Desktop\\Mes_projets\\WritingToolsClean\\Windows_and_Linux\\myvenv\\Lib\\site-packages\\grpc\\__init__.py',
   'PYMODULE'),
  ('grpc._auth',
   'c:\\Users\\<USER>\\Desktop\\Mes_projets\\WritingToolsClean\\Windows_and_Linux\\myvenv\\Lib\\site-packages\\grpc\\_auth.py',
   'PYMODULE'),
  ('grpc._channel',
   'c:\\Users\\<USER>\\Desktop\\Mes_projets\\WritingToolsClean\\Windows_and_Linux\\myvenv\\Lib\\site-packages\\grpc\\_channel.py',
   'PYMODULE'),
  ('grpc._common',
   'c:\\Users\\<USER>\\Desktop\\Mes_projets\\WritingToolsClean\\Windows_and_Linux\\myvenv\\Lib\\site-packages\\grpc\\_common.py',
   'PYMODULE'),
  ('grpc._compression',
   'c:\\Users\\<USER>\\Desktop\\Mes_projets\\WritingToolsClean\\Windows_and_Linux\\myvenv\\Lib\\site-packages\\grpc\\_compression.py',
   'PYMODULE'),
  ('grpc._cython',
   'c:\\Users\\<USER>\\Desktop\\Mes_projets\\WritingToolsClean\\Windows_and_Linux\\myvenv\\Lib\\site-packages\\grpc\\_cython\\__init__.py',
   'PYMODULE'),
  ('grpc._grpcio_metadata',
   'c:\\Users\\<USER>\\Desktop\\Mes_projets\\WritingToolsClean\\Windows_and_Linux\\myvenv\\Lib\\site-packages\\grpc\\_grpcio_metadata.py',
   'PYMODULE'),
  ('grpc._interceptor',
   'c:\\Users\\<USER>\\Desktop\\Mes_projets\\WritingToolsClean\\Windows_and_Linux\\myvenv\\Lib\\site-packages\\grpc\\_interceptor.py',
   'PYMODULE'),
  ('grpc._observability',
   'c:\\Users\\<USER>\\Desktop\\Mes_projets\\WritingToolsClean\\Windows_and_Linux\\myvenv\\Lib\\site-packages\\grpc\\_observability.py',
   'PYMODULE'),
  ('grpc._plugin_wrapping',
   'c:\\Users\\<USER>\\Desktop\\Mes_projets\\WritingToolsClean\\Windows_and_Linux\\myvenv\\Lib\\site-packages\\grpc\\_plugin_wrapping.py',
   'PYMODULE'),
  ('grpc._runtime_protos',
   'c:\\Users\\<USER>\\Desktop\\Mes_projets\\WritingToolsClean\\Windows_and_Linux\\myvenv\\Lib\\site-packages\\grpc\\_runtime_protos.py',
   'PYMODULE'),
  ('grpc._server',
   'c:\\Users\\<USER>\\Desktop\\Mes_projets\\WritingToolsClean\\Windows_and_Linux\\myvenv\\Lib\\site-packages\\grpc\\_server.py',
   'PYMODULE'),
  ('grpc._simple_stubs',
   'c:\\Users\\<USER>\\Desktop\\Mes_projets\\WritingToolsClean\\Windows_and_Linux\\myvenv\\Lib\\site-packages\\grpc\\_simple_stubs.py',
   'PYMODULE'),
  ('grpc._typing',
   'c:\\Users\\<USER>\\Desktop\\Mes_projets\\WritingToolsClean\\Windows_and_Linux\\myvenv\\Lib\\site-packages\\grpc\\_typing.py',
   'PYMODULE'),
  ('grpc._utilities',
   'c:\\Users\\<USER>\\Desktop\\Mes_projets\\WritingToolsClean\\Windows_and_Linux\\myvenv\\Lib\\site-packages\\grpc\\_utilities.py',
   'PYMODULE'),
  ('grpc.aio',
   'c:\\Users\\<USER>\\Desktop\\Mes_projets\\WritingToolsClean\\Windows_and_Linux\\myvenv\\Lib\\site-packages\\grpc\\aio\\__init__.py',
   'PYMODULE'),
  ('grpc.aio._base_call',
   'c:\\Users\\<USER>\\Desktop\\Mes_projets\\WritingToolsClean\\Windows_and_Linux\\myvenv\\Lib\\site-packages\\grpc\\aio\\_base_call.py',
   'PYMODULE'),
  ('grpc.aio._base_channel',
   'c:\\Users\\<USER>\\Desktop\\Mes_projets\\WritingToolsClean\\Windows_and_Linux\\myvenv\\Lib\\site-packages\\grpc\\aio\\_base_channel.py',
   'PYMODULE'),
  ('grpc.aio._base_server',
   'c:\\Users\\<USER>\\Desktop\\Mes_projets\\WritingToolsClean\\Windows_and_Linux\\myvenv\\Lib\\site-packages\\grpc\\aio\\_base_server.py',
   'PYMODULE'),
  ('grpc.aio._call',
   'c:\\Users\\<USER>\\Desktop\\Mes_projets\\WritingToolsClean\\Windows_and_Linux\\myvenv\\Lib\\site-packages\\grpc\\aio\\_call.py',
   'PYMODULE'),
  ('grpc.aio._channel',
   'c:\\Users\\<USER>\\Desktop\\Mes_projets\\WritingToolsClean\\Windows_and_Linux\\myvenv\\Lib\\site-packages\\grpc\\aio\\_channel.py',
   'PYMODULE'),
  ('grpc.aio._interceptor',
   'c:\\Users\\<USER>\\Desktop\\Mes_projets\\WritingToolsClean\\Windows_and_Linux\\myvenv\\Lib\\site-packages\\grpc\\aio\\_interceptor.py',
   'PYMODULE'),
  ('grpc.aio._metadata',
   'c:\\Users\\<USER>\\Desktop\\Mes_projets\\WritingToolsClean\\Windows_and_Linux\\myvenv\\Lib\\site-packages\\grpc\\aio\\_metadata.py',
   'PYMODULE'),
  ('grpc.aio._server',
   'c:\\Users\\<USER>\\Desktop\\Mes_projets\\WritingToolsClean\\Windows_and_Linux\\myvenv\\Lib\\site-packages\\grpc\\aio\\_server.py',
   'PYMODULE'),
  ('grpc.aio._typing',
   'c:\\Users\\<USER>\\Desktop\\Mes_projets\\WritingToolsClean\\Windows_and_Linux\\myvenv\\Lib\\site-packages\\grpc\\aio\\_typing.py',
   'PYMODULE'),
  ('grpc.aio._utils',
   'c:\\Users\\<USER>\\Desktop\\Mes_projets\\WritingToolsClean\\Windows_and_Linux\\myvenv\\Lib\\site-packages\\grpc\\aio\\_utils.py',
   'PYMODULE'),
  ('grpc.experimental',
   'c:\\Users\\<USER>\\Desktop\\Mes_projets\\WritingToolsClean\\Windows_and_Linux\\myvenv\\Lib\\site-packages\\grpc\\experimental\\__init__.py',
   'PYMODULE'),
  ('grpc.experimental.aio',
   'c:\\Users\\<USER>\\Desktop\\Mes_projets\\WritingToolsClean\\Windows_and_Linux\\myvenv\\Lib\\site-packages\\grpc\\experimental\\aio\\__init__.py',
   'PYMODULE'),
  ('grpc_status',
   'c:\\Users\\<USER>\\Desktop\\Mes_projets\\WritingToolsClean\\Windows_and_Linux\\myvenv\\Lib\\site-packages\\grpc_status\\__init__.py',
   'PYMODULE'),
  ('grpc_status._async',
   'c:\\Users\\<USER>\\Desktop\\Mes_projets\\WritingToolsClean\\Windows_and_Linux\\myvenv\\Lib\\site-packages\\grpc_status\\_async.py',
   'PYMODULE'),
  ('grpc_status._common',
   'c:\\Users\\<USER>\\Desktop\\Mes_projets\\WritingToolsClean\\Windows_and_Linux\\myvenv\\Lib\\site-packages\\grpc_status\\_common.py',
   'PYMODULE'),
  ('grpc_status.rpc_status',
   'c:\\Users\\<USER>\\Desktop\\Mes_projets\\WritingToolsClean\\Windows_and_Linux\\myvenv\\Lib\\site-packages\\grpc_status\\rpc_status.py',
   'PYMODULE'),
  ('gzip',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\gzip.py',
   'PYMODULE'),
  ('h11',
   'c:\\Users\\<USER>\\Desktop\\Mes_projets\\WritingToolsClean\\Windows_and_Linux\\myvenv\\Lib\\site-packages\\h11\\__init__.py',
   'PYMODULE'),
  ('h11._abnf',
   'c:\\Users\\<USER>\\Desktop\\Mes_projets\\WritingToolsClean\\Windows_and_Linux\\myvenv\\Lib\\site-packages\\h11\\_abnf.py',
   'PYMODULE'),
  ('h11._connection',
   'c:\\Users\\<USER>\\Desktop\\Mes_projets\\WritingToolsClean\\Windows_and_Linux\\myvenv\\Lib\\site-packages\\h11\\_connection.py',
   'PYMODULE'),
  ('h11._events',
   'c:\\Users\\<USER>\\Desktop\\Mes_projets\\WritingToolsClean\\Windows_and_Linux\\myvenv\\Lib\\site-packages\\h11\\_events.py',
   'PYMODULE'),
  ('h11._headers',
   'c:\\Users\\<USER>\\Desktop\\Mes_projets\\WritingToolsClean\\Windows_and_Linux\\myvenv\\Lib\\site-packages\\h11\\_headers.py',
   'PYMODULE'),
  ('h11._readers',
   'c:\\Users\\<USER>\\Desktop\\Mes_projets\\WritingToolsClean\\Windows_and_Linux\\myvenv\\Lib\\site-packages\\h11\\_readers.py',
   'PYMODULE'),
  ('h11._receivebuffer',
   'c:\\Users\\<USER>\\Desktop\\Mes_projets\\WritingToolsClean\\Windows_and_Linux\\myvenv\\Lib\\site-packages\\h11\\_receivebuffer.py',
   'PYMODULE'),
  ('h11._state',
   'c:\\Users\\<USER>\\Desktop\\Mes_projets\\WritingToolsClean\\Windows_and_Linux\\myvenv\\Lib\\site-packages\\h11\\_state.py',
   'PYMODULE'),
  ('h11._util',
   'c:\\Users\\<USER>\\Desktop\\Mes_projets\\WritingToolsClean\\Windows_and_Linux\\myvenv\\Lib\\site-packages\\h11\\_util.py',
   'PYMODULE'),
  ('h11._version',
   'c:\\Users\\<USER>\\Desktop\\Mes_projets\\WritingToolsClean\\Windows_and_Linux\\myvenv\\Lib\\site-packages\\h11\\_version.py',
   'PYMODULE'),
  ('h11._writers',
   'c:\\Users\\<USER>\\Desktop\\Mes_projets\\WritingToolsClean\\Windows_and_Linux\\myvenv\\Lib\\site-packages\\h11\\_writers.py',
   'PYMODULE'),
  ('hashlib',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\hashlib.py',
   'PYMODULE'),
  ('hmac',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\hmac.py',
   'PYMODULE'),
  ('html',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\html\\__init__.py',
   'PYMODULE'),
  ('html.entities',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\html\\entities.py',
   'PYMODULE'),
  ('http',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\http\\__init__.py',
   'PYMODULE'),
  ('http.client',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\http\\client.py',
   'PYMODULE'),
  ('http.cookiejar',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\http\\cookiejar.py',
   'PYMODULE'),
  ('http.cookies',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\http\\cookies.py',
   'PYMODULE'),
  ('http.server',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\http\\server.py',
   'PYMODULE'),
  ('httpcore',
   'c:\\Users\\<USER>\\Desktop\\Mes_projets\\WritingToolsClean\\Windows_and_Linux\\myvenv\\Lib\\site-packages\\httpcore\\__init__.py',
   'PYMODULE'),
  ('httpcore._api',
   'c:\\Users\\<USER>\\Desktop\\Mes_projets\\WritingToolsClean\\Windows_and_Linux\\myvenv\\Lib\\site-packages\\httpcore\\_api.py',
   'PYMODULE'),
  ('httpcore._async',
   'c:\\Users\\<USER>\\Desktop\\Mes_projets\\WritingToolsClean\\Windows_and_Linux\\myvenv\\Lib\\site-packages\\httpcore\\_async\\__init__.py',
   'PYMODULE'),
  ('httpcore._async.connection',
   'c:\\Users\\<USER>\\Desktop\\Mes_projets\\WritingToolsClean\\Windows_and_Linux\\myvenv\\Lib\\site-packages\\httpcore\\_async\\connection.py',
   'PYMODULE'),
  ('httpcore._async.connection_pool',
   'c:\\Users\\<USER>\\Desktop\\Mes_projets\\WritingToolsClean\\Windows_and_Linux\\myvenv\\Lib\\site-packages\\httpcore\\_async\\connection_pool.py',
   'PYMODULE'),
  ('httpcore._async.http11',
   'c:\\Users\\<USER>\\Desktop\\Mes_projets\\WritingToolsClean\\Windows_and_Linux\\myvenv\\Lib\\site-packages\\httpcore\\_async\\http11.py',
   'PYMODULE'),
  ('httpcore._async.http2',
   'c:\\Users\\<USER>\\Desktop\\Mes_projets\\WritingToolsClean\\Windows_and_Linux\\myvenv\\Lib\\site-packages\\httpcore\\_async\\http2.py',
   'PYMODULE'),
  ('httpcore._async.http_proxy',
   'c:\\Users\\<USER>\\Desktop\\Mes_projets\\WritingToolsClean\\Windows_and_Linux\\myvenv\\Lib\\site-packages\\httpcore\\_async\\http_proxy.py',
   'PYMODULE'),
  ('httpcore._async.interfaces',
   'c:\\Users\\<USER>\\Desktop\\Mes_projets\\WritingToolsClean\\Windows_and_Linux\\myvenv\\Lib\\site-packages\\httpcore\\_async\\interfaces.py',
   'PYMODULE'),
  ('httpcore._async.socks_proxy',
   'c:\\Users\\<USER>\\Desktop\\Mes_projets\\WritingToolsClean\\Windows_and_Linux\\myvenv\\Lib\\site-packages\\httpcore\\_async\\socks_proxy.py',
   'PYMODULE'),
  ('httpcore._backends',
   'c:\\Users\\<USER>\\Desktop\\Mes_projets\\WritingToolsClean\\Windows_and_Linux\\myvenv\\Lib\\site-packages\\httpcore\\_backends\\__init__.py',
   'PYMODULE'),
  ('httpcore._backends.anyio',
   'c:\\Users\\<USER>\\Desktop\\Mes_projets\\WritingToolsClean\\Windows_and_Linux\\myvenv\\Lib\\site-packages\\httpcore\\_backends\\anyio.py',
   'PYMODULE'),
  ('httpcore._backends.auto',
   'c:\\Users\\<USER>\\Desktop\\Mes_projets\\WritingToolsClean\\Windows_and_Linux\\myvenv\\Lib\\site-packages\\httpcore\\_backends\\auto.py',
   'PYMODULE'),
  ('httpcore._backends.base',
   'c:\\Users\\<USER>\\Desktop\\Mes_projets\\WritingToolsClean\\Windows_and_Linux\\myvenv\\Lib\\site-packages\\httpcore\\_backends\\base.py',
   'PYMODULE'),
  ('httpcore._backends.mock',
   'c:\\Users\\<USER>\\Desktop\\Mes_projets\\WritingToolsClean\\Windows_and_Linux\\myvenv\\Lib\\site-packages\\httpcore\\_backends\\mock.py',
   'PYMODULE'),
  ('httpcore._backends.sync',
   'c:\\Users\\<USER>\\Desktop\\Mes_projets\\WritingToolsClean\\Windows_and_Linux\\myvenv\\Lib\\site-packages\\httpcore\\_backends\\sync.py',
   'PYMODULE'),
  ('httpcore._backends.trio',
   'c:\\Users\\<USER>\\Desktop\\Mes_projets\\WritingToolsClean\\Windows_and_Linux\\myvenv\\Lib\\site-packages\\httpcore\\_backends\\trio.py',
   'PYMODULE'),
  ('httpcore._exceptions',
   'c:\\Users\\<USER>\\Desktop\\Mes_projets\\WritingToolsClean\\Windows_and_Linux\\myvenv\\Lib\\site-packages\\httpcore\\_exceptions.py',
   'PYMODULE'),
  ('httpcore._models',
   'c:\\Users\\<USER>\\Desktop\\Mes_projets\\WritingToolsClean\\Windows_and_Linux\\myvenv\\Lib\\site-packages\\httpcore\\_models.py',
   'PYMODULE'),
  ('httpcore._ssl',
   'c:\\Users\\<USER>\\Desktop\\Mes_projets\\WritingToolsClean\\Windows_and_Linux\\myvenv\\Lib\\site-packages\\httpcore\\_ssl.py',
   'PYMODULE'),
  ('httpcore._sync',
   'c:\\Users\\<USER>\\Desktop\\Mes_projets\\WritingToolsClean\\Windows_and_Linux\\myvenv\\Lib\\site-packages\\httpcore\\_sync\\__init__.py',
   'PYMODULE'),
  ('httpcore._sync.connection',
   'c:\\Users\\<USER>\\Desktop\\Mes_projets\\WritingToolsClean\\Windows_and_Linux\\myvenv\\Lib\\site-packages\\httpcore\\_sync\\connection.py',
   'PYMODULE'),
  ('httpcore._sync.connection_pool',
   'c:\\Users\\<USER>\\Desktop\\Mes_projets\\WritingToolsClean\\Windows_and_Linux\\myvenv\\Lib\\site-packages\\httpcore\\_sync\\connection_pool.py',
   'PYMODULE'),
  ('httpcore._sync.http11',
   'c:\\Users\\<USER>\\Desktop\\Mes_projets\\WritingToolsClean\\Windows_and_Linux\\myvenv\\Lib\\site-packages\\httpcore\\_sync\\http11.py',
   'PYMODULE'),
  ('httpcore._sync.http2',
   'c:\\Users\\<USER>\\Desktop\\Mes_projets\\WritingToolsClean\\Windows_and_Linux\\myvenv\\Lib\\site-packages\\httpcore\\_sync\\http2.py',
   'PYMODULE'),
  ('httpcore._sync.http_proxy',
   'c:\\Users\\<USER>\\Desktop\\Mes_projets\\WritingToolsClean\\Windows_and_Linux\\myvenv\\Lib\\site-packages\\httpcore\\_sync\\http_proxy.py',
   'PYMODULE'),
  ('httpcore._sync.interfaces',
   'c:\\Users\\<USER>\\Desktop\\Mes_projets\\WritingToolsClean\\Windows_and_Linux\\myvenv\\Lib\\site-packages\\httpcore\\_sync\\interfaces.py',
   'PYMODULE'),
  ('httpcore._sync.socks_proxy',
   'c:\\Users\\<USER>\\Desktop\\Mes_projets\\WritingToolsClean\\Windows_and_Linux\\myvenv\\Lib\\site-packages\\httpcore\\_sync\\socks_proxy.py',
   'PYMODULE'),
  ('httpcore._synchronization',
   'c:\\Users\\<USER>\\Desktop\\Mes_projets\\WritingToolsClean\\Windows_and_Linux\\myvenv\\Lib\\site-packages\\httpcore\\_synchronization.py',
   'PYMODULE'),
  ('httpcore._trace',
   'c:\\Users\\<USER>\\Desktop\\Mes_projets\\WritingToolsClean\\Windows_and_Linux\\myvenv\\Lib\\site-packages\\httpcore\\_trace.py',
   'PYMODULE'),
  ('httpcore._utils',
   'c:\\Users\\<USER>\\Desktop\\Mes_projets\\WritingToolsClean\\Windows_and_Linux\\myvenv\\Lib\\site-packages\\httpcore\\_utils.py',
   'PYMODULE'),
  ('httplib2',
   'c:\\Users\\<USER>\\Desktop\\Mes_projets\\WritingToolsClean\\Windows_and_Linux\\myvenv\\Lib\\site-packages\\httplib2\\__init__.py',
   'PYMODULE'),
  ('httplib2.auth',
   'c:\\Users\\<USER>\\Desktop\\Mes_projets\\WritingToolsClean\\Windows_and_Linux\\myvenv\\Lib\\site-packages\\httplib2\\auth.py',
   'PYMODULE'),
  ('httplib2.certs',
   'c:\\Users\\<USER>\\Desktop\\Mes_projets\\WritingToolsClean\\Windows_and_Linux\\myvenv\\Lib\\site-packages\\httplib2\\certs.py',
   'PYMODULE'),
  ('httplib2.error',
   'c:\\Users\\<USER>\\Desktop\\Mes_projets\\WritingToolsClean\\Windows_and_Linux\\myvenv\\Lib\\site-packages\\httplib2\\error.py',
   'PYMODULE'),
  ('httplib2.iri2uri',
   'c:\\Users\\<USER>\\Desktop\\Mes_projets\\WritingToolsClean\\Windows_and_Linux\\myvenv\\Lib\\site-packages\\httplib2\\iri2uri.py',
   'PYMODULE'),
  ('httplib2.socks',
   'c:\\Users\\<USER>\\Desktop\\Mes_projets\\WritingToolsClean\\Windows_and_Linux\\myvenv\\Lib\\site-packages\\httplib2\\socks.py',
   'PYMODULE'),
  ('httpx',
   'c:\\Users\\<USER>\\Desktop\\Mes_projets\\WritingToolsClean\\Windows_and_Linux\\myvenv\\Lib\\site-packages\\httpx\\__init__.py',
   'PYMODULE'),
  ('httpx.__version__',
   'c:\\Users\\<USER>\\Desktop\\Mes_projets\\WritingToolsClean\\Windows_and_Linux\\myvenv\\Lib\\site-packages\\httpx\\__version__.py',
   'PYMODULE'),
  ('httpx._api',
   'c:\\Users\\<USER>\\Desktop\\Mes_projets\\WritingToolsClean\\Windows_and_Linux\\myvenv\\Lib\\site-packages\\httpx\\_api.py',
   'PYMODULE'),
  ('httpx._auth',
   'c:\\Users\\<USER>\\Desktop\\Mes_projets\\WritingToolsClean\\Windows_and_Linux\\myvenv\\Lib\\site-packages\\httpx\\_auth.py',
   'PYMODULE'),
  ('httpx._client',
   'c:\\Users\\<USER>\\Desktop\\Mes_projets\\WritingToolsClean\\Windows_and_Linux\\myvenv\\Lib\\site-packages\\httpx\\_client.py',
   'PYMODULE'),
  ('httpx._config',
   'c:\\Users\\<USER>\\Desktop\\Mes_projets\\WritingToolsClean\\Windows_and_Linux\\myvenv\\Lib\\site-packages\\httpx\\_config.py',
   'PYMODULE'),
  ('httpx._content',
   'c:\\Users\\<USER>\\Desktop\\Mes_projets\\WritingToolsClean\\Windows_and_Linux\\myvenv\\Lib\\site-packages\\httpx\\_content.py',
   'PYMODULE'),
  ('httpx._decoders',
   'c:\\Users\\<USER>\\Desktop\\Mes_projets\\WritingToolsClean\\Windows_and_Linux\\myvenv\\Lib\\site-packages\\httpx\\_decoders.py',
   'PYMODULE'),
  ('httpx._exceptions',
   'c:\\Users\\<USER>\\Desktop\\Mes_projets\\WritingToolsClean\\Windows_and_Linux\\myvenv\\Lib\\site-packages\\httpx\\_exceptions.py',
   'PYMODULE'),
  ('httpx._main',
   'c:\\Users\\<USER>\\Desktop\\Mes_projets\\WritingToolsClean\\Windows_and_Linux\\myvenv\\Lib\\site-packages\\httpx\\_main.py',
   'PYMODULE'),
  ('httpx._models',
   'c:\\Users\\<USER>\\Desktop\\Mes_projets\\WritingToolsClean\\Windows_and_Linux\\myvenv\\Lib\\site-packages\\httpx\\_models.py',
   'PYMODULE'),
  ('httpx._multipart',
   'c:\\Users\\<USER>\\Desktop\\Mes_projets\\WritingToolsClean\\Windows_and_Linux\\myvenv\\Lib\\site-packages\\httpx\\_multipart.py',
   'PYMODULE'),
  ('httpx._status_codes',
   'c:\\Users\\<USER>\\Desktop\\Mes_projets\\WritingToolsClean\\Windows_and_Linux\\myvenv\\Lib\\site-packages\\httpx\\_status_codes.py',
   'PYMODULE'),
  ('httpx._transports',
   'c:\\Users\\<USER>\\Desktop\\Mes_projets\\WritingToolsClean\\Windows_and_Linux\\myvenv\\Lib\\site-packages\\httpx\\_transports\\__init__.py',
   'PYMODULE'),
  ('httpx._transports.asgi',
   'c:\\Users\\<USER>\\Desktop\\Mes_projets\\WritingToolsClean\\Windows_and_Linux\\myvenv\\Lib\\site-packages\\httpx\\_transports\\asgi.py',
   'PYMODULE'),
  ('httpx._transports.base',
   'c:\\Users\\<USER>\\Desktop\\Mes_projets\\WritingToolsClean\\Windows_and_Linux\\myvenv\\Lib\\site-packages\\httpx\\_transports\\base.py',
   'PYMODULE'),
  ('httpx._transports.default',
   'c:\\Users\\<USER>\\Desktop\\Mes_projets\\WritingToolsClean\\Windows_and_Linux\\myvenv\\Lib\\site-packages\\httpx\\_transports\\default.py',
   'PYMODULE'),
  ('httpx._transports.mock',
   'c:\\Users\\<USER>\\Desktop\\Mes_projets\\WritingToolsClean\\Windows_and_Linux\\myvenv\\Lib\\site-packages\\httpx\\_transports\\mock.py',
   'PYMODULE'),
  ('httpx._transports.wsgi',
   'c:\\Users\\<USER>\\Desktop\\Mes_projets\\WritingToolsClean\\Windows_and_Linux\\myvenv\\Lib\\site-packages\\httpx\\_transports\\wsgi.py',
   'PYMODULE'),
  ('httpx._types',
   'c:\\Users\\<USER>\\Desktop\\Mes_projets\\WritingToolsClean\\Windows_and_Linux\\myvenv\\Lib\\site-packages\\httpx\\_types.py',
   'PYMODULE'),
  ('httpx._urlparse',
   'c:\\Users\\<USER>\\Desktop\\Mes_projets\\WritingToolsClean\\Windows_and_Linux\\myvenv\\Lib\\site-packages\\httpx\\_urlparse.py',
   'PYMODULE'),
  ('httpx._urls',
   'c:\\Users\\<USER>\\Desktop\\Mes_projets\\WritingToolsClean\\Windows_and_Linux\\myvenv\\Lib\\site-packages\\httpx\\_urls.py',
   'PYMODULE'),
  ('httpx._utils',
   'c:\\Users\\<USER>\\Desktop\\Mes_projets\\WritingToolsClean\\Windows_and_Linux\\myvenv\\Lib\\site-packages\\httpx\\_utils.py',
   'PYMODULE'),
  ('idna',
   'c:\\Users\\<USER>\\Desktop\\Mes_projets\\WritingToolsClean\\Windows_and_Linux\\myvenv\\Lib\\site-packages\\idna\\__init__.py',
   'PYMODULE'),
  ('idna.core',
   'c:\\Users\\<USER>\\Desktop\\Mes_projets\\WritingToolsClean\\Windows_and_Linux\\myvenv\\Lib\\site-packages\\idna\\core.py',
   'PYMODULE'),
  ('idna.idnadata',
   'c:\\Users\\<USER>\\Desktop\\Mes_projets\\WritingToolsClean\\Windows_and_Linux\\myvenv\\Lib\\site-packages\\idna\\idnadata.py',
   'PYMODULE'),
  ('idna.intranges',
   'c:\\Users\\<USER>\\Desktop\\Mes_projets\\WritingToolsClean\\Windows_and_Linux\\myvenv\\Lib\\site-packages\\idna\\intranges.py',
   'PYMODULE'),
  ('idna.package_data',
   'c:\\Users\\<USER>\\Desktop\\Mes_projets\\WritingToolsClean\\Windows_and_Linux\\myvenv\\Lib\\site-packages\\idna\\package_data.py',
   'PYMODULE'),
  ('idna.uts46data',
   'c:\\Users\\<USER>\\Desktop\\Mes_projets\\WritingToolsClean\\Windows_and_Linux\\myvenv\\Lib\\site-packages\\idna\\uts46data.py',
   'PYMODULE'),
  ('importlib',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\importlib\\__init__.py',
   'PYMODULE'),
  ('importlib._abc',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\importlib\\_abc.py',
   'PYMODULE'),
  ('importlib._bootstrap',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\importlib\\_bootstrap.py',
   'PYMODULE'),
  ('importlib._bootstrap_external',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\importlib\\_bootstrap_external.py',
   'PYMODULE'),
  ('importlib.abc',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\importlib\\abc.py',
   'PYMODULE'),
  ('importlib.machinery',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\importlib\\machinery.py',
   'PYMODULE'),
  ('importlib.metadata',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\importlib\\metadata\\__init__.py',
   'PYMODULE'),
  ('importlib.metadata._adapters',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\importlib\\metadata\\_adapters.py',
   'PYMODULE'),
  ('importlib.metadata._collections',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\importlib\\metadata\\_collections.py',
   'PYMODULE'),
  ('importlib.metadata._functools',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\importlib\\metadata\\_functools.py',
   'PYMODULE'),
  ('importlib.metadata._itertools',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\importlib\\metadata\\_itertools.py',
   'PYMODULE'),
  ('importlib.metadata._meta',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\importlib\\metadata\\_meta.py',
   'PYMODULE'),
  ('importlib.metadata._text',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\importlib\\metadata\\_text.py',
   'PYMODULE'),
  ('importlib.readers',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\importlib\\readers.py',
   'PYMODULE'),
  ('importlib.resources',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\importlib\\resources\\__init__.py',
   'PYMODULE'),
  ('importlib.resources._adapters',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\importlib\\resources\\_adapters.py',
   'PYMODULE'),
  ('importlib.resources._common',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\importlib\\resources\\_common.py',
   'PYMODULE'),
  ('importlib.resources._itertools',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\importlib\\resources\\_itertools.py',
   'PYMODULE'),
  ('importlib.resources._legacy',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\importlib\\resources\\_legacy.py',
   'PYMODULE'),
  ('importlib.resources.abc',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\importlib\\resources\\abc.py',
   'PYMODULE'),
  ('importlib.resources.readers',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\importlib\\resources\\readers.py',
   'PYMODULE'),
  ('importlib.util',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\importlib\\util.py',
   'PYMODULE'),
  ('inspect',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\inspect.py',
   'PYMODULE'),
  ('ipaddress',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\ipaddress.py',
   'PYMODULE'),
  ('jaraco', '-', 'PYMODULE'),
  ('jiter',
   'c:\\Users\\<USER>\\Desktop\\Mes_projets\\WritingToolsClean\\Windows_and_Linux\\myvenv\\Lib\\site-packages\\jiter\\__init__.py',
   'PYMODULE'),
  ('json',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\json\\__init__.py',
   'PYMODULE'),
  ('json.decoder',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\json\\decoder.py',
   'PYMODULE'),
  ('json.encoder',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\json\\encoder.py',
   'PYMODULE'),
  ('json.scanner',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\json\\scanner.py',
   'PYMODULE'),
  ('logging',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\logging\\__init__.py',
   'PYMODULE'),
  ('lzma',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\lzma.py',
   'PYMODULE'),
  ('markdown2',
   'c:\\Users\\<USER>\\Desktop\\Mes_projets\\WritingToolsClean\\Windows_and_Linux\\myvenv\\Lib\\site-packages\\markdown2.py',
   'PYMODULE'),
  ('mimetypes',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\mimetypes.py',
   'PYMODULE'),
  ('multiprocessing',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\multiprocessing\\__init__.py',
   'PYMODULE'),
  ('multiprocessing.connection',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\multiprocessing\\connection.py',
   'PYMODULE'),
  ('multiprocessing.context',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\multiprocessing\\context.py',
   'PYMODULE'),
  ('multiprocessing.dummy',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\multiprocessing\\dummy\\__init__.py',
   'PYMODULE'),
  ('multiprocessing.dummy.connection',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\multiprocessing\\dummy\\connection.py',
   'PYMODULE'),
  ('multiprocessing.forkserver',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\multiprocessing\\forkserver.py',
   'PYMODULE'),
  ('multiprocessing.heap',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\multiprocessing\\heap.py',
   'PYMODULE'),
  ('multiprocessing.managers',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\multiprocessing\\managers.py',
   'PYMODULE'),
  ('multiprocessing.pool',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\multiprocessing\\pool.py',
   'PYMODULE'),
  ('multiprocessing.popen_fork',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\multiprocessing\\popen_fork.py',
   'PYMODULE'),
  ('multiprocessing.popen_forkserver',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\multiprocessing\\popen_forkserver.py',
   'PYMODULE'),
  ('multiprocessing.popen_spawn_posix',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\multiprocessing\\popen_spawn_posix.py',
   'PYMODULE'),
  ('multiprocessing.popen_spawn_win32',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\multiprocessing\\popen_spawn_win32.py',
   'PYMODULE'),
  ('multiprocessing.process',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\multiprocessing\\process.py',
   'PYMODULE'),
  ('multiprocessing.queues',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\multiprocessing\\queues.py',
   'PYMODULE'),
  ('multiprocessing.reduction',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\multiprocessing\\reduction.py',
   'PYMODULE'),
  ('multiprocessing.resource_sharer',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\multiprocessing\\resource_sharer.py',
   'PYMODULE'),
  ('multiprocessing.resource_tracker',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\multiprocessing\\resource_tracker.py',
   'PYMODULE'),
  ('multiprocessing.shared_memory',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\multiprocessing\\shared_memory.py',
   'PYMODULE'),
  ('multiprocessing.sharedctypes',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\multiprocessing\\sharedctypes.py',
   'PYMODULE'),
  ('multiprocessing.spawn',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\multiprocessing\\spawn.py',
   'PYMODULE'),
  ('multiprocessing.synchronize',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\multiprocessing\\synchronize.py',
   'PYMODULE'),
  ('multiprocessing.util',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\multiprocessing\\util.py',
   'PYMODULE'),
  ('netrc',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\netrc.py',
   'PYMODULE'),
  ('nturl2path',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\nturl2path.py',
   'PYMODULE'),
  ('numbers',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\numbers.py',
   'PYMODULE'),
  ('ollama',
   'c:\\Users\\<USER>\\Desktop\\Mes_projets\\WritingToolsClean\\Windows_and_Linux\\myvenv\\Lib\\site-packages\\ollama\\__init__.py',
   'PYMODULE'),
  ('ollama._client',
   'c:\\Users\\<USER>\\Desktop\\Mes_projets\\WritingToolsClean\\Windows_and_Linux\\myvenv\\Lib\\site-packages\\ollama\\_client.py',
   'PYMODULE'),
  ('ollama._types',
   'c:\\Users\\<USER>\\Desktop\\Mes_projets\\WritingToolsClean\\Windows_and_Linux\\myvenv\\Lib\\site-packages\\ollama\\_types.py',
   'PYMODULE'),
  ('ollama._utils',
   'c:\\Users\\<USER>\\Desktop\\Mes_projets\\WritingToolsClean\\Windows_and_Linux\\myvenv\\Lib\\site-packages\\ollama\\_utils.py',
   'PYMODULE'),
  ('opcode',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\opcode.py',
   'PYMODULE'),
  ('openai',
   'c:\\Users\\<USER>\\Desktop\\Mes_projets\\WritingToolsClean\\Windows_and_Linux\\myvenv\\Lib\\site-packages\\openai\\__init__.py',
   'PYMODULE'),
  ('openai._base_client',
   'c:\\Users\\<USER>\\Desktop\\Mes_projets\\WritingToolsClean\\Windows_and_Linux\\myvenv\\Lib\\site-packages\\openai\\_base_client.py',
   'PYMODULE'),
  ('openai._client',
   'c:\\Users\\<USER>\\Desktop\\Mes_projets\\WritingToolsClean\\Windows_and_Linux\\myvenv\\Lib\\site-packages\\openai\\_client.py',
   'PYMODULE'),
  ('openai._compat',
   'c:\\Users\\<USER>\\Desktop\\Mes_projets\\WritingToolsClean\\Windows_and_Linux\\myvenv\\Lib\\site-packages\\openai\\_compat.py',
   'PYMODULE'),
  ('openai._constants',
   'c:\\Users\\<USER>\\Desktop\\Mes_projets\\WritingToolsClean\\Windows_and_Linux\\myvenv\\Lib\\site-packages\\openai\\_constants.py',
   'PYMODULE'),
  ('openai._exceptions',
   'c:\\Users\\<USER>\\Desktop\\Mes_projets\\WritingToolsClean\\Windows_and_Linux\\myvenv\\Lib\\site-packages\\openai\\_exceptions.py',
   'PYMODULE'),
  ('openai._extras',
   'c:\\Users\\<USER>\\Desktop\\Mes_projets\\WritingToolsClean\\Windows_and_Linux\\myvenv\\Lib\\site-packages\\openai\\_extras\\__init__.py',
   'PYMODULE'),
  ('openai._extras._common',
   'c:\\Users\\<USER>\\Desktop\\Mes_projets\\WritingToolsClean\\Windows_and_Linux\\myvenv\\Lib\\site-packages\\openai\\_extras\\_common.py',
   'PYMODULE'),
  ('openai._extras.numpy_proxy',
   'c:\\Users\\<USER>\\Desktop\\Mes_projets\\WritingToolsClean\\Windows_and_Linux\\myvenv\\Lib\\site-packages\\openai\\_extras\\numpy_proxy.py',
   'PYMODULE'),
  ('openai._extras.pandas_proxy',
   'c:\\Users\\<USER>\\Desktop\\Mes_projets\\WritingToolsClean\\Windows_and_Linux\\myvenv\\Lib\\site-packages\\openai\\_extras\\pandas_proxy.py',
   'PYMODULE'),
  ('openai._extras.sounddevice_proxy',
   'c:\\Users\\<USER>\\Desktop\\Mes_projets\\WritingToolsClean\\Windows_and_Linux\\myvenv\\Lib\\site-packages\\openai\\_extras\\sounddevice_proxy.py',
   'PYMODULE'),
  ('openai._files',
   'c:\\Users\\<USER>\\Desktop\\Mes_projets\\WritingToolsClean\\Windows_and_Linux\\myvenv\\Lib\\site-packages\\openai\\_files.py',
   'PYMODULE'),
  ('openai._legacy_response',
   'c:\\Users\\<USER>\\Desktop\\Mes_projets\\WritingToolsClean\\Windows_and_Linux\\myvenv\\Lib\\site-packages\\openai\\_legacy_response.py',
   'PYMODULE'),
  ('openai._models',
   'c:\\Users\\<USER>\\Desktop\\Mes_projets\\WritingToolsClean\\Windows_and_Linux\\myvenv\\Lib\\site-packages\\openai\\_models.py',
   'PYMODULE'),
  ('openai._module_client',
   'c:\\Users\\<USER>\\Desktop\\Mes_projets\\WritingToolsClean\\Windows_and_Linux\\myvenv\\Lib\\site-packages\\openai\\_module_client.py',
   'PYMODULE'),
  ('openai._qs',
   'c:\\Users\\<USER>\\Desktop\\Mes_projets\\WritingToolsClean\\Windows_and_Linux\\myvenv\\Lib\\site-packages\\openai\\_qs.py',
   'PYMODULE'),
  ('openai._resource',
   'c:\\Users\\<USER>\\Desktop\\Mes_projets\\WritingToolsClean\\Windows_and_Linux\\myvenv\\Lib\\site-packages\\openai\\_resource.py',
   'PYMODULE'),
  ('openai._response',
   'c:\\Users\\<USER>\\Desktop\\Mes_projets\\WritingToolsClean\\Windows_and_Linux\\myvenv\\Lib\\site-packages\\openai\\_response.py',
   'PYMODULE'),
  ('openai._streaming',
   'c:\\Users\\<USER>\\Desktop\\Mes_projets\\WritingToolsClean\\Windows_and_Linux\\myvenv\\Lib\\site-packages\\openai\\_streaming.py',
   'PYMODULE'),
  ('openai._types',
   'c:\\Users\\<USER>\\Desktop\\Mes_projets\\WritingToolsClean\\Windows_and_Linux\\myvenv\\Lib\\site-packages\\openai\\_types.py',
   'PYMODULE'),
  ('openai._utils',
   'c:\\Users\\<USER>\\Desktop\\Mes_projets\\WritingToolsClean\\Windows_and_Linux\\myvenv\\Lib\\site-packages\\openai\\_utils\\__init__.py',
   'PYMODULE'),
  ('openai._utils._logs',
   'c:\\Users\\<USER>\\Desktop\\Mes_projets\\WritingToolsClean\\Windows_and_Linux\\myvenv\\Lib\\site-packages\\openai\\_utils\\_logs.py',
   'PYMODULE'),
  ('openai._utils._proxy',
   'c:\\Users\\<USER>\\Desktop\\Mes_projets\\WritingToolsClean\\Windows_and_Linux\\myvenv\\Lib\\site-packages\\openai\\_utils\\_proxy.py',
   'PYMODULE'),
  ('openai._utils._reflection',
   'c:\\Users\\<USER>\\Desktop\\Mes_projets\\WritingToolsClean\\Windows_and_Linux\\myvenv\\Lib\\site-packages\\openai\\_utils\\_reflection.py',
   'PYMODULE'),
  ('openai._utils._resources_proxy',
   'c:\\Users\\<USER>\\Desktop\\Mes_projets\\WritingToolsClean\\Windows_and_Linux\\myvenv\\Lib\\site-packages\\openai\\_utils\\_resources_proxy.py',
   'PYMODULE'),
  ('openai._utils._streams',
   'c:\\Users\\<USER>\\Desktop\\Mes_projets\\WritingToolsClean\\Windows_and_Linux\\myvenv\\Lib\\site-packages\\openai\\_utils\\_streams.py',
   'PYMODULE'),
  ('openai._utils._sync',
   'c:\\Users\\<USER>\\Desktop\\Mes_projets\\WritingToolsClean\\Windows_and_Linux\\myvenv\\Lib\\site-packages\\openai\\_utils\\_sync.py',
   'PYMODULE'),
  ('openai._utils._transform',
   'c:\\Users\\<USER>\\Desktop\\Mes_projets\\WritingToolsClean\\Windows_and_Linux\\myvenv\\Lib\\site-packages\\openai\\_utils\\_transform.py',
   'PYMODULE'),
  ('openai._utils._typing',
   'c:\\Users\\<USER>\\Desktop\\Mes_projets\\WritingToolsClean\\Windows_and_Linux\\myvenv\\Lib\\site-packages\\openai\\_utils\\_typing.py',
   'PYMODULE'),
  ('openai._utils._utils',
   'c:\\Users\\<USER>\\Desktop\\Mes_projets\\WritingToolsClean\\Windows_and_Linux\\myvenv\\Lib\\site-packages\\openai\\_utils\\_utils.py',
   'PYMODULE'),
  ('openai._version',
   'c:\\Users\\<USER>\\Desktop\\Mes_projets\\WritingToolsClean\\Windows_and_Linux\\myvenv\\Lib\\site-packages\\openai\\_version.py',
   'PYMODULE'),
  ('openai.lib',
   'c:\\Users\\<USER>\\Desktop\\Mes_projets\\WritingToolsClean\\Windows_and_Linux\\myvenv\\Lib\\site-packages\\openai\\lib\\__init__.py',
   'PYMODULE'),
  ('openai.lib._old_api',
   'c:\\Users\\<USER>\\Desktop\\Mes_projets\\WritingToolsClean\\Windows_and_Linux\\myvenv\\Lib\\site-packages\\openai\\lib\\_old_api.py',
   'PYMODULE'),
  ('openai.lib._parsing',
   'c:\\Users\\<USER>\\Desktop\\Mes_projets\\WritingToolsClean\\Windows_and_Linux\\myvenv\\Lib\\site-packages\\openai\\lib\\_parsing\\__init__.py',
   'PYMODULE'),
  ('openai.lib._parsing._completions',
   'c:\\Users\\<USER>\\Desktop\\Mes_projets\\WritingToolsClean\\Windows_and_Linux\\myvenv\\Lib\\site-packages\\openai\\lib\\_parsing\\_completions.py',
   'PYMODULE'),
  ('openai.lib._parsing._responses',
   'c:\\Users\\<USER>\\Desktop\\Mes_projets\\WritingToolsClean\\Windows_and_Linux\\myvenv\\Lib\\site-packages\\openai\\lib\\_parsing\\_responses.py',
   'PYMODULE'),
  ('openai.lib._pydantic',
   'c:\\Users\\<USER>\\Desktop\\Mes_projets\\WritingToolsClean\\Windows_and_Linux\\myvenv\\Lib\\site-packages\\openai\\lib\\_pydantic.py',
   'PYMODULE'),
  ('openai.lib._tools',
   'c:\\Users\\<USER>\\Desktop\\Mes_projets\\WritingToolsClean\\Windows_and_Linux\\myvenv\\Lib\\site-packages\\openai\\lib\\_tools.py',
   'PYMODULE'),
  ('openai.lib.azure',
   'c:\\Users\\<USER>\\Desktop\\Mes_projets\\WritingToolsClean\\Windows_and_Linux\\myvenv\\Lib\\site-packages\\openai\\lib\\azure.py',
   'PYMODULE'),
  ('openai.lib.streaming',
   'c:\\Users\\<USER>\\Desktop\\Mes_projets\\WritingToolsClean\\Windows_and_Linux\\myvenv\\Lib\\site-packages\\openai\\lib\\streaming\\__init__.py',
   'PYMODULE'),
  ('openai.lib.streaming._assistants',
   'c:\\Users\\<USER>\\Desktop\\Mes_projets\\WritingToolsClean\\Windows_and_Linux\\myvenv\\Lib\\site-packages\\openai\\lib\\streaming\\_assistants.py',
   'PYMODULE'),
  ('openai.lib.streaming._deltas',
   'c:\\Users\\<USER>\\Desktop\\Mes_projets\\WritingToolsClean\\Windows_and_Linux\\myvenv\\Lib\\site-packages\\openai\\lib\\streaming\\_deltas.py',
   'PYMODULE'),
  ('openai.lib.streaming.chat',
   'c:\\Users\\<USER>\\Desktop\\Mes_projets\\WritingToolsClean\\Windows_and_Linux\\myvenv\\Lib\\site-packages\\openai\\lib\\streaming\\chat\\__init__.py',
   'PYMODULE'),
  ('openai.lib.streaming.chat._completions',
   'c:\\Users\\<USER>\\Desktop\\Mes_projets\\WritingToolsClean\\Windows_and_Linux\\myvenv\\Lib\\site-packages\\openai\\lib\\streaming\\chat\\_completions.py',
   'PYMODULE'),
  ('openai.lib.streaming.chat._events',
   'c:\\Users\\<USER>\\Desktop\\Mes_projets\\WritingToolsClean\\Windows_and_Linux\\myvenv\\Lib\\site-packages\\openai\\lib\\streaming\\chat\\_events.py',
   'PYMODULE'),
  ('openai.lib.streaming.chat._types',
   'c:\\Users\\<USER>\\Desktop\\Mes_projets\\WritingToolsClean\\Windows_and_Linux\\myvenv\\Lib\\site-packages\\openai\\lib\\streaming\\chat\\_types.py',
   'PYMODULE'),
  ('openai.lib.streaming.responses',
   'c:\\Users\\<USER>\\Desktop\\Mes_projets\\WritingToolsClean\\Windows_and_Linux\\myvenv\\Lib\\site-packages\\openai\\lib\\streaming\\responses\\__init__.py',
   'PYMODULE'),
  ('openai.lib.streaming.responses._events',
   'c:\\Users\\<USER>\\Desktop\\Mes_projets\\WritingToolsClean\\Windows_and_Linux\\myvenv\\Lib\\site-packages\\openai\\lib\\streaming\\responses\\_events.py',
   'PYMODULE'),
  ('openai.lib.streaming.responses._responses',
   'c:\\Users\\<USER>\\Desktop\\Mes_projets\\WritingToolsClean\\Windows_and_Linux\\myvenv\\Lib\\site-packages\\openai\\lib\\streaming\\responses\\_responses.py',
   'PYMODULE'),
  ('openai.lib.streaming.responses._types',
   'c:\\Users\\<USER>\\Desktop\\Mes_projets\\WritingToolsClean\\Windows_and_Linux\\myvenv\\Lib\\site-packages\\openai\\lib\\streaming\\responses\\_types.py',
   'PYMODULE'),
  ('openai.pagination',
   'c:\\Users\\<USER>\\Desktop\\Mes_projets\\WritingToolsClean\\Windows_and_Linux\\myvenv\\Lib\\site-packages\\openai\\pagination.py',
   'PYMODULE'),
  ('openai.resources',
   'c:\\Users\\<USER>\\Desktop\\Mes_projets\\WritingToolsClean\\Windows_and_Linux\\myvenv\\Lib\\site-packages\\openai\\resources\\__init__.py',
   'PYMODULE'),
  ('openai.resources.audio',
   'c:\\Users\\<USER>\\Desktop\\Mes_projets\\WritingToolsClean\\Windows_and_Linux\\myvenv\\Lib\\site-packages\\openai\\resources\\audio\\__init__.py',
   'PYMODULE'),
  ('openai.resources.audio.audio',
   'c:\\Users\\<USER>\\Desktop\\Mes_projets\\WritingToolsClean\\Windows_and_Linux\\myvenv\\Lib\\site-packages\\openai\\resources\\audio\\audio.py',
   'PYMODULE'),
  ('openai.resources.audio.speech',
   'c:\\Users\\<USER>\\Desktop\\Mes_projets\\WritingToolsClean\\Windows_and_Linux\\myvenv\\Lib\\site-packages\\openai\\resources\\audio\\speech.py',
   'PYMODULE'),
  ('openai.resources.audio.transcriptions',
   'c:\\Users\\<USER>\\Desktop\\Mes_projets\\WritingToolsClean\\Windows_and_Linux\\myvenv\\Lib\\site-packages\\openai\\resources\\audio\\transcriptions.py',
   'PYMODULE'),
  ('openai.resources.audio.translations',
   'c:\\Users\\<USER>\\Desktop\\Mes_projets\\WritingToolsClean\\Windows_and_Linux\\myvenv\\Lib\\site-packages\\openai\\resources\\audio\\translations.py',
   'PYMODULE'),
  ('openai.resources.batches',
   'c:\\Users\\<USER>\\Desktop\\Mes_projets\\WritingToolsClean\\Windows_and_Linux\\myvenv\\Lib\\site-packages\\openai\\resources\\batches.py',
   'PYMODULE'),
  ('openai.resources.beta',
   'c:\\Users\\<USER>\\Desktop\\Mes_projets\\WritingToolsClean\\Windows_and_Linux\\myvenv\\Lib\\site-packages\\openai\\resources\\beta\\__init__.py',
   'PYMODULE'),
  ('openai.resources.beta.assistants',
   'c:\\Users\\<USER>\\Desktop\\Mes_projets\\WritingToolsClean\\Windows_and_Linux\\myvenv\\Lib\\site-packages\\openai\\resources\\beta\\assistants.py',
   'PYMODULE'),
  ('openai.resources.beta.beta',
   'c:\\Users\\<USER>\\Desktop\\Mes_projets\\WritingToolsClean\\Windows_and_Linux\\myvenv\\Lib\\site-packages\\openai\\resources\\beta\\beta.py',
   'PYMODULE'),
  ('openai.resources.beta.realtime',
   'c:\\Users\\<USER>\\Desktop\\Mes_projets\\WritingToolsClean\\Windows_and_Linux\\myvenv\\Lib\\site-packages\\openai\\resources\\beta\\realtime\\__init__.py',
   'PYMODULE'),
  ('openai.resources.beta.realtime.realtime',
   'c:\\Users\\<USER>\\Desktop\\Mes_projets\\WritingToolsClean\\Windows_and_Linux\\myvenv\\Lib\\site-packages\\openai\\resources\\beta\\realtime\\realtime.py',
   'PYMODULE'),
  ('openai.resources.beta.realtime.sessions',
   'c:\\Users\\<USER>\\Desktop\\Mes_projets\\WritingToolsClean\\Windows_and_Linux\\myvenv\\Lib\\site-packages\\openai\\resources\\beta\\realtime\\sessions.py',
   'PYMODULE'),
  ('openai.resources.beta.realtime.transcription_sessions',
   'c:\\Users\\<USER>\\Desktop\\Mes_projets\\WritingToolsClean\\Windows_and_Linux\\myvenv\\Lib\\site-packages\\openai\\resources\\beta\\realtime\\transcription_sessions.py',
   'PYMODULE'),
  ('openai.resources.beta.threads',
   'c:\\Users\\<USER>\\Desktop\\Mes_projets\\WritingToolsClean\\Windows_and_Linux\\myvenv\\Lib\\site-packages\\openai\\resources\\beta\\threads\\__init__.py',
   'PYMODULE'),
  ('openai.resources.beta.threads.messages',
   'c:\\Users\\<USER>\\Desktop\\Mes_projets\\WritingToolsClean\\Windows_and_Linux\\myvenv\\Lib\\site-packages\\openai\\resources\\beta\\threads\\messages.py',
   'PYMODULE'),
  ('openai.resources.beta.threads.runs',
   'c:\\Users\\<USER>\\Desktop\\Mes_projets\\WritingToolsClean\\Windows_and_Linux\\myvenv\\Lib\\site-packages\\openai\\resources\\beta\\threads\\runs\\__init__.py',
   'PYMODULE'),
  ('openai.resources.beta.threads.runs.runs',
   'c:\\Users\\<USER>\\Desktop\\Mes_projets\\WritingToolsClean\\Windows_and_Linux\\myvenv\\Lib\\site-packages\\openai\\resources\\beta\\threads\\runs\\runs.py',
   'PYMODULE'),
  ('openai.resources.beta.threads.runs.steps',
   'c:\\Users\\<USER>\\Desktop\\Mes_projets\\WritingToolsClean\\Windows_and_Linux\\myvenv\\Lib\\site-packages\\openai\\resources\\beta\\threads\\runs\\steps.py',
   'PYMODULE'),
  ('openai.resources.beta.threads.threads',
   'c:\\Users\\<USER>\\Desktop\\Mes_projets\\WritingToolsClean\\Windows_and_Linux\\myvenv\\Lib\\site-packages\\openai\\resources\\beta\\threads\\threads.py',
   'PYMODULE'),
  ('openai.resources.chat',
   'c:\\Users\\<USER>\\Desktop\\Mes_projets\\WritingToolsClean\\Windows_and_Linux\\myvenv\\Lib\\site-packages\\openai\\resources\\chat\\__init__.py',
   'PYMODULE'),
  ('openai.resources.chat.chat',
   'c:\\Users\\<USER>\\Desktop\\Mes_projets\\WritingToolsClean\\Windows_and_Linux\\myvenv\\Lib\\site-packages\\openai\\resources\\chat\\chat.py',
   'PYMODULE'),
  ('openai.resources.chat.completions',
   'c:\\Users\\<USER>\\Desktop\\Mes_projets\\WritingToolsClean\\Windows_and_Linux\\myvenv\\Lib\\site-packages\\openai\\resources\\chat\\completions\\__init__.py',
   'PYMODULE'),
  ('openai.resources.chat.completions.completions',
   'c:\\Users\\<USER>\\Desktop\\Mes_projets\\WritingToolsClean\\Windows_and_Linux\\myvenv\\Lib\\site-packages\\openai\\resources\\chat\\completions\\completions.py',
   'PYMODULE'),
  ('openai.resources.chat.completions.messages',
   'c:\\Users\\<USER>\\Desktop\\Mes_projets\\WritingToolsClean\\Windows_and_Linux\\myvenv\\Lib\\site-packages\\openai\\resources\\chat\\completions\\messages.py',
   'PYMODULE'),
  ('openai.resources.completions',
   'c:\\Users\\<USER>\\Desktop\\Mes_projets\\WritingToolsClean\\Windows_and_Linux\\myvenv\\Lib\\site-packages\\openai\\resources\\completions.py',
   'PYMODULE'),
  ('openai.resources.containers',
   'c:\\Users\\<USER>\\Desktop\\Mes_projets\\WritingToolsClean\\Windows_and_Linux\\myvenv\\Lib\\site-packages\\openai\\resources\\containers\\__init__.py',
   'PYMODULE'),
  ('openai.resources.containers.containers',
   'c:\\Users\\<USER>\\Desktop\\Mes_projets\\WritingToolsClean\\Windows_and_Linux\\myvenv\\Lib\\site-packages\\openai\\resources\\containers\\containers.py',
   'PYMODULE'),
  ('openai.resources.containers.files',
   'c:\\Users\\<USER>\\Desktop\\Mes_projets\\WritingToolsClean\\Windows_and_Linux\\myvenv\\Lib\\site-packages\\openai\\resources\\containers\\files\\__init__.py',
   'PYMODULE'),
  ('openai.resources.containers.files.content',
   'c:\\Users\\<USER>\\Desktop\\Mes_projets\\WritingToolsClean\\Windows_and_Linux\\myvenv\\Lib\\site-packages\\openai\\resources\\containers\\files\\content.py',
   'PYMODULE'),
  ('openai.resources.containers.files.files',
   'c:\\Users\\<USER>\\Desktop\\Mes_projets\\WritingToolsClean\\Windows_and_Linux\\myvenv\\Lib\\site-packages\\openai\\resources\\containers\\files\\files.py',
   'PYMODULE'),
  ('openai.resources.embeddings',
   'c:\\Users\\<USER>\\Desktop\\Mes_projets\\WritingToolsClean\\Windows_and_Linux\\myvenv\\Lib\\site-packages\\openai\\resources\\embeddings.py',
   'PYMODULE'),
  ('openai.resources.evals',
   'c:\\Users\\<USER>\\Desktop\\Mes_projets\\WritingToolsClean\\Windows_and_Linux\\myvenv\\Lib\\site-packages\\openai\\resources\\evals\\__init__.py',
   'PYMODULE'),
  ('openai.resources.evals.evals',
   'c:\\Users\\<USER>\\Desktop\\Mes_projets\\WritingToolsClean\\Windows_and_Linux\\myvenv\\Lib\\site-packages\\openai\\resources\\evals\\evals.py',
   'PYMODULE'),
  ('openai.resources.evals.runs',
   'c:\\Users\\<USER>\\Desktop\\Mes_projets\\WritingToolsClean\\Windows_and_Linux\\myvenv\\Lib\\site-packages\\openai\\resources\\evals\\runs\\__init__.py',
   'PYMODULE'),
  ('openai.resources.evals.runs.output_items',
   'c:\\Users\\<USER>\\Desktop\\Mes_projets\\WritingToolsClean\\Windows_and_Linux\\myvenv\\Lib\\site-packages\\openai\\resources\\evals\\runs\\output_items.py',
   'PYMODULE'),
  ('openai.resources.evals.runs.runs',
   'c:\\Users\\<USER>\\Desktop\\Mes_projets\\WritingToolsClean\\Windows_and_Linux\\myvenv\\Lib\\site-packages\\openai\\resources\\evals\\runs\\runs.py',
   'PYMODULE'),
  ('openai.resources.files',
   'c:\\Users\\<USER>\\Desktop\\Mes_projets\\WritingToolsClean\\Windows_and_Linux\\myvenv\\Lib\\site-packages\\openai\\resources\\files.py',
   'PYMODULE'),
  ('openai.resources.fine_tuning',
   'c:\\Users\\<USER>\\Desktop\\Mes_projets\\WritingToolsClean\\Windows_and_Linux\\myvenv\\Lib\\site-packages\\openai\\resources\\fine_tuning\\__init__.py',
   'PYMODULE'),
  ('openai.resources.fine_tuning.alpha',
   'c:\\Users\\<USER>\\Desktop\\Mes_projets\\WritingToolsClean\\Windows_and_Linux\\myvenv\\Lib\\site-packages\\openai\\resources\\fine_tuning\\alpha\\__init__.py',
   'PYMODULE'),
  ('openai.resources.fine_tuning.alpha.alpha',
   'c:\\Users\\<USER>\\Desktop\\Mes_projets\\WritingToolsClean\\Windows_and_Linux\\myvenv\\Lib\\site-packages\\openai\\resources\\fine_tuning\\alpha\\alpha.py',
   'PYMODULE'),
  ('openai.resources.fine_tuning.alpha.graders',
   'c:\\Users\\<USER>\\Desktop\\Mes_projets\\WritingToolsClean\\Windows_and_Linux\\myvenv\\Lib\\site-packages\\openai\\resources\\fine_tuning\\alpha\\graders.py',
   'PYMODULE'),
  ('openai.resources.fine_tuning.checkpoints',
   'c:\\Users\\<USER>\\Desktop\\Mes_projets\\WritingToolsClean\\Windows_and_Linux\\myvenv\\Lib\\site-packages\\openai\\resources\\fine_tuning\\checkpoints\\__init__.py',
   'PYMODULE'),
  ('openai.resources.fine_tuning.checkpoints.checkpoints',
   'c:\\Users\\<USER>\\Desktop\\Mes_projets\\WritingToolsClean\\Windows_and_Linux\\myvenv\\Lib\\site-packages\\openai\\resources\\fine_tuning\\checkpoints\\checkpoints.py',
   'PYMODULE'),
  ('openai.resources.fine_tuning.checkpoints.permissions',
   'c:\\Users\\<USER>\\Desktop\\Mes_projets\\WritingToolsClean\\Windows_and_Linux\\myvenv\\Lib\\site-packages\\openai\\resources\\fine_tuning\\checkpoints\\permissions.py',
   'PYMODULE'),
  ('openai.resources.fine_tuning.fine_tuning',
   'c:\\Users\\<USER>\\Desktop\\Mes_projets\\WritingToolsClean\\Windows_and_Linux\\myvenv\\Lib\\site-packages\\openai\\resources\\fine_tuning\\fine_tuning.py',
   'PYMODULE'),
  ('openai.resources.fine_tuning.jobs',
   'c:\\Users\\<USER>\\Desktop\\Mes_projets\\WritingToolsClean\\Windows_and_Linux\\myvenv\\Lib\\site-packages\\openai\\resources\\fine_tuning\\jobs\\__init__.py',
   'PYMODULE'),
  ('openai.resources.fine_tuning.jobs.checkpoints',
   'c:\\Users\\<USER>\\Desktop\\Mes_projets\\WritingToolsClean\\Windows_and_Linux\\myvenv\\Lib\\site-packages\\openai\\resources\\fine_tuning\\jobs\\checkpoints.py',
   'PYMODULE'),
  ('openai.resources.fine_tuning.jobs.jobs',
   'c:\\Users\\<USER>\\Desktop\\Mes_projets\\WritingToolsClean\\Windows_and_Linux\\myvenv\\Lib\\site-packages\\openai\\resources\\fine_tuning\\jobs\\jobs.py',
   'PYMODULE'),
  ('openai.resources.images',
   'c:\\Users\\<USER>\\Desktop\\Mes_projets\\WritingToolsClean\\Windows_and_Linux\\myvenv\\Lib\\site-packages\\openai\\resources\\images.py',
   'PYMODULE'),
  ('openai.resources.models',
   'c:\\Users\\<USER>\\Desktop\\Mes_projets\\WritingToolsClean\\Windows_and_Linux\\myvenv\\Lib\\site-packages\\openai\\resources\\models.py',
   'PYMODULE'),
  ('openai.resources.moderations',
   'c:\\Users\\<USER>\\Desktop\\Mes_projets\\WritingToolsClean\\Windows_and_Linux\\myvenv\\Lib\\site-packages\\openai\\resources\\moderations.py',
   'PYMODULE'),
  ('openai.resources.responses',
   'c:\\Users\\<USER>\\Desktop\\Mes_projets\\WritingToolsClean\\Windows_and_Linux\\myvenv\\Lib\\site-packages\\openai\\resources\\responses\\__init__.py',
   'PYMODULE'),
  ('openai.resources.responses.input_items',
   'c:\\Users\\<USER>\\Desktop\\Mes_projets\\WritingToolsClean\\Windows_and_Linux\\myvenv\\Lib\\site-packages\\openai\\resources\\responses\\input_items.py',
   'PYMODULE'),
  ('openai.resources.responses.responses',
   'c:\\Users\\<USER>\\Desktop\\Mes_projets\\WritingToolsClean\\Windows_and_Linux\\myvenv\\Lib\\site-packages\\openai\\resources\\responses\\responses.py',
   'PYMODULE'),
  ('openai.resources.uploads',
   'c:\\Users\\<USER>\\Desktop\\Mes_projets\\WritingToolsClean\\Windows_and_Linux\\myvenv\\Lib\\site-packages\\openai\\resources\\uploads\\__init__.py',
   'PYMODULE'),
  ('openai.resources.uploads.parts',
   'c:\\Users\\<USER>\\Desktop\\Mes_projets\\WritingToolsClean\\Windows_and_Linux\\myvenv\\Lib\\site-packages\\openai\\resources\\uploads\\parts.py',
   'PYMODULE'),
  ('openai.resources.uploads.uploads',
   'c:\\Users\\<USER>\\Desktop\\Mes_projets\\WritingToolsClean\\Windows_and_Linux\\myvenv\\Lib\\site-packages\\openai\\resources\\uploads\\uploads.py',
   'PYMODULE'),
  ('openai.resources.vector_stores',
   'c:\\Users\\<USER>\\Desktop\\Mes_projets\\WritingToolsClean\\Windows_and_Linux\\myvenv\\Lib\\site-packages\\openai\\resources\\vector_stores\\__init__.py',
   'PYMODULE'),
  ('openai.resources.vector_stores.file_batches',
   'c:\\Users\\<USER>\\Desktop\\Mes_projets\\WritingToolsClean\\Windows_and_Linux\\myvenv\\Lib\\site-packages\\openai\\resources\\vector_stores\\file_batches.py',
   'PYMODULE'),
  ('openai.resources.vector_stores.files',
   'c:\\Users\\<USER>\\Desktop\\Mes_projets\\WritingToolsClean\\Windows_and_Linux\\myvenv\\Lib\\site-packages\\openai\\resources\\vector_stores\\files.py',
   'PYMODULE'),
  ('openai.resources.vector_stores.vector_stores',
   'c:\\Users\\<USER>\\Desktop\\Mes_projets\\WritingToolsClean\\Windows_and_Linux\\myvenv\\Lib\\site-packages\\openai\\resources\\vector_stores\\vector_stores.py',
   'PYMODULE'),
  ('openai.resources.webhooks',
   'c:\\Users\\<USER>\\Desktop\\Mes_projets\\WritingToolsClean\\Windows_and_Linux\\myvenv\\Lib\\site-packages\\openai\\resources\\webhooks.py',
   'PYMODULE'),
  ('openai.types',
   'c:\\Users\\<USER>\\Desktop\\Mes_projets\\WritingToolsClean\\Windows_and_Linux\\myvenv\\Lib\\site-packages\\openai\\types\\__init__.py',
   'PYMODULE'),
  ('openai.types.audio',
   'c:\\Users\\<USER>\\Desktop\\Mes_projets\\WritingToolsClean\\Windows_and_Linux\\myvenv\\Lib\\site-packages\\openai\\types\\audio\\__init__.py',
   'PYMODULE'),
  ('openai.types.audio.speech_create_params',
   'c:\\Users\\<USER>\\Desktop\\Mes_projets\\WritingToolsClean\\Windows_and_Linux\\myvenv\\Lib\\site-packages\\openai\\types\\audio\\speech_create_params.py',
   'PYMODULE'),
  ('openai.types.audio.speech_model',
   'c:\\Users\\<USER>\\Desktop\\Mes_projets\\WritingToolsClean\\Windows_and_Linux\\myvenv\\Lib\\site-packages\\openai\\types\\audio\\speech_model.py',
   'PYMODULE'),
  ('openai.types.audio.transcription',
   'c:\\Users\\<USER>\\Desktop\\Mes_projets\\WritingToolsClean\\Windows_and_Linux\\myvenv\\Lib\\site-packages\\openai\\types\\audio\\transcription.py',
   'PYMODULE'),
  ('openai.types.audio.transcription_create_params',
   'c:\\Users\\<USER>\\Desktop\\Mes_projets\\WritingToolsClean\\Windows_and_Linux\\myvenv\\Lib\\site-packages\\openai\\types\\audio\\transcription_create_params.py',
   'PYMODULE'),
  ('openai.types.audio.transcription_create_response',
   'c:\\Users\\<USER>\\Desktop\\Mes_projets\\WritingToolsClean\\Windows_and_Linux\\myvenv\\Lib\\site-packages\\openai\\types\\audio\\transcription_create_response.py',
   'PYMODULE'),
  ('openai.types.audio.transcription_include',
   'c:\\Users\\<USER>\\Desktop\\Mes_projets\\WritingToolsClean\\Windows_and_Linux\\myvenv\\Lib\\site-packages\\openai\\types\\audio\\transcription_include.py',
   'PYMODULE'),
  ('openai.types.audio.transcription_segment',
   'c:\\Users\\<USER>\\Desktop\\Mes_projets\\WritingToolsClean\\Windows_and_Linux\\myvenv\\Lib\\site-packages\\openai\\types\\audio\\transcription_segment.py',
   'PYMODULE'),
  ('openai.types.audio.transcription_stream_event',
   'c:\\Users\\<USER>\\Desktop\\Mes_projets\\WritingToolsClean\\Windows_and_Linux\\myvenv\\Lib\\site-packages\\openai\\types\\audio\\transcription_stream_event.py',
   'PYMODULE'),
  ('openai.types.audio.transcription_text_delta_event',
   'c:\\Users\\<USER>\\Desktop\\Mes_projets\\WritingToolsClean\\Windows_and_Linux\\myvenv\\Lib\\site-packages\\openai\\types\\audio\\transcription_text_delta_event.py',
   'PYMODULE'),
  ('openai.types.audio.transcription_text_done_event',
   'c:\\Users\\<USER>\\Desktop\\Mes_projets\\WritingToolsClean\\Windows_and_Linux\\myvenv\\Lib\\site-packages\\openai\\types\\audio\\transcription_text_done_event.py',
   'PYMODULE'),
  ('openai.types.audio.transcription_verbose',
   'c:\\Users\\<USER>\\Desktop\\Mes_projets\\WritingToolsClean\\Windows_and_Linux\\myvenv\\Lib\\site-packages\\openai\\types\\audio\\transcription_verbose.py',
   'PYMODULE'),
  ('openai.types.audio.transcription_word',
   'c:\\Users\\<USER>\\Desktop\\Mes_projets\\WritingToolsClean\\Windows_and_Linux\\myvenv\\Lib\\site-packages\\openai\\types\\audio\\transcription_word.py',
   'PYMODULE'),
  ('openai.types.audio.translation',
   'c:\\Users\\<USER>\\Desktop\\Mes_projets\\WritingToolsClean\\Windows_and_Linux\\myvenv\\Lib\\site-packages\\openai\\types\\audio\\translation.py',
   'PYMODULE'),
  ('openai.types.audio.translation_create_params',
   'c:\\Users\\<USER>\\Desktop\\Mes_projets\\WritingToolsClean\\Windows_and_Linux\\myvenv\\Lib\\site-packages\\openai\\types\\audio\\translation_create_params.py',
   'PYMODULE'),
  ('openai.types.audio.translation_create_response',
   'c:\\Users\\<USER>\\Desktop\\Mes_projets\\WritingToolsClean\\Windows_and_Linux\\myvenv\\Lib\\site-packages\\openai\\types\\audio\\translation_create_response.py',
   'PYMODULE'),
  ('openai.types.audio.translation_verbose',
   'c:\\Users\\<USER>\\Desktop\\Mes_projets\\WritingToolsClean\\Windows_and_Linux\\myvenv\\Lib\\site-packages\\openai\\types\\audio\\translation_verbose.py',
   'PYMODULE'),
  ('openai.types.audio_model',
   'c:\\Users\\<USER>\\Desktop\\Mes_projets\\WritingToolsClean\\Windows_and_Linux\\myvenv\\Lib\\site-packages\\openai\\types\\audio_model.py',
   'PYMODULE'),
  ('openai.types.audio_response_format',
   'c:\\Users\\<USER>\\Desktop\\Mes_projets\\WritingToolsClean\\Windows_and_Linux\\myvenv\\Lib\\site-packages\\openai\\types\\audio_response_format.py',
   'PYMODULE'),
  ('openai.types.auto_file_chunking_strategy_param',
   'c:\\Users\\<USER>\\Desktop\\Mes_projets\\WritingToolsClean\\Windows_and_Linux\\myvenv\\Lib\\site-packages\\openai\\types\\auto_file_chunking_strategy_param.py',
   'PYMODULE'),
  ('openai.types.batch',
   'c:\\Users\\<USER>\\Desktop\\Mes_projets\\WritingToolsClean\\Windows_and_Linux\\myvenv\\Lib\\site-packages\\openai\\types\\batch.py',
   'PYMODULE'),
  ('openai.types.batch_create_params',
   'c:\\Users\\<USER>\\Desktop\\Mes_projets\\WritingToolsClean\\Windows_and_Linux\\myvenv\\Lib\\site-packages\\openai\\types\\batch_create_params.py',
   'PYMODULE'),
  ('openai.types.batch_error',
   'c:\\Users\\<USER>\\Desktop\\Mes_projets\\WritingToolsClean\\Windows_and_Linux\\myvenv\\Lib\\site-packages\\openai\\types\\batch_error.py',
   'PYMODULE'),
  ('openai.types.batch_list_params',
   'c:\\Users\\<USER>\\Desktop\\Mes_projets\\WritingToolsClean\\Windows_and_Linux\\myvenv\\Lib\\site-packages\\openai\\types\\batch_list_params.py',
   'PYMODULE'),
  ('openai.types.batch_request_counts',
   'c:\\Users\\<USER>\\Desktop\\Mes_projets\\WritingToolsClean\\Windows_and_Linux\\myvenv\\Lib\\site-packages\\openai\\types\\batch_request_counts.py',
   'PYMODULE'),
  ('openai.types.beta',
   'c:\\Users\\<USER>\\Desktop\\Mes_projets\\WritingToolsClean\\Windows_and_Linux\\myvenv\\Lib\\site-packages\\openai\\types\\beta\\__init__.py',
   'PYMODULE'),
  ('openai.types.beta.assistant',
   'c:\\Users\\<USER>\\Desktop\\Mes_projets\\WritingToolsClean\\Windows_and_Linux\\myvenv\\Lib\\site-packages\\openai\\types\\beta\\assistant.py',
   'PYMODULE'),
  ('openai.types.beta.assistant_create_params',
   'c:\\Users\\<USER>\\Desktop\\Mes_projets\\WritingToolsClean\\Windows_and_Linux\\myvenv\\Lib\\site-packages\\openai\\types\\beta\\assistant_create_params.py',
   'PYMODULE'),
  ('openai.types.beta.assistant_deleted',
   'c:\\Users\\<USER>\\Desktop\\Mes_projets\\WritingToolsClean\\Windows_and_Linux\\myvenv\\Lib\\site-packages\\openai\\types\\beta\\assistant_deleted.py',
   'PYMODULE'),
  ('openai.types.beta.assistant_list_params',
   'c:\\Users\\<USER>\\Desktop\\Mes_projets\\WritingToolsClean\\Windows_and_Linux\\myvenv\\Lib\\site-packages\\openai\\types\\beta\\assistant_list_params.py',
   'PYMODULE'),
  ('openai.types.beta.assistant_response_format_option',
   'c:\\Users\\<USER>\\Desktop\\Mes_projets\\WritingToolsClean\\Windows_and_Linux\\myvenv\\Lib\\site-packages\\openai\\types\\beta\\assistant_response_format_option.py',
   'PYMODULE'),
  ('openai.types.beta.assistant_response_format_option_param',
   'c:\\Users\\<USER>\\Desktop\\Mes_projets\\WritingToolsClean\\Windows_and_Linux\\myvenv\\Lib\\site-packages\\openai\\types\\beta\\assistant_response_format_option_param.py',
   'PYMODULE'),
  ('openai.types.beta.assistant_stream_event',
   'c:\\Users\\<USER>\\Desktop\\Mes_projets\\WritingToolsClean\\Windows_and_Linux\\myvenv\\Lib\\site-packages\\openai\\types\\beta\\assistant_stream_event.py',
   'PYMODULE'),
  ('openai.types.beta.assistant_tool',
   'c:\\Users\\<USER>\\Desktop\\Mes_projets\\WritingToolsClean\\Windows_and_Linux\\myvenv\\Lib\\site-packages\\openai\\types\\beta\\assistant_tool.py',
   'PYMODULE'),
  ('openai.types.beta.assistant_tool_choice',
   'c:\\Users\\<USER>\\Desktop\\Mes_projets\\WritingToolsClean\\Windows_and_Linux\\myvenv\\Lib\\site-packages\\openai\\types\\beta\\assistant_tool_choice.py',
   'PYMODULE'),
  ('openai.types.beta.assistant_tool_choice_function',
   'c:\\Users\\<USER>\\Desktop\\Mes_projets\\WritingToolsClean\\Windows_and_Linux\\myvenv\\Lib\\site-packages\\openai\\types\\beta\\assistant_tool_choice_function.py',
   'PYMODULE'),
  ('openai.types.beta.assistant_tool_choice_function_param',
   'c:\\Users\\<USER>\\Desktop\\Mes_projets\\WritingToolsClean\\Windows_and_Linux\\myvenv\\Lib\\site-packages\\openai\\types\\beta\\assistant_tool_choice_function_param.py',
   'PYMODULE'),
  ('openai.types.beta.assistant_tool_choice_option',
   'c:\\Users\\<USER>\\Desktop\\Mes_projets\\WritingToolsClean\\Windows_and_Linux\\myvenv\\Lib\\site-packages\\openai\\types\\beta\\assistant_tool_choice_option.py',
   'PYMODULE'),
  ('openai.types.beta.assistant_tool_choice_option_param',
   'c:\\Users\\<USER>\\Desktop\\Mes_projets\\WritingToolsClean\\Windows_and_Linux\\myvenv\\Lib\\site-packages\\openai\\types\\beta\\assistant_tool_choice_option_param.py',
   'PYMODULE'),
  ('openai.types.beta.assistant_tool_choice_param',
   'c:\\Users\\<USER>\\Desktop\\Mes_projets\\WritingToolsClean\\Windows_and_Linux\\myvenv\\Lib\\site-packages\\openai\\types\\beta\\assistant_tool_choice_param.py',
   'PYMODULE'),
  ('openai.types.beta.assistant_tool_param',
   'c:\\Users\\<USER>\\Desktop\\Mes_projets\\WritingToolsClean\\Windows_and_Linux\\myvenv\\Lib\\site-packages\\openai\\types\\beta\\assistant_tool_param.py',
   'PYMODULE'),
  ('openai.types.beta.assistant_update_params',
   'c:\\Users\\<USER>\\Desktop\\Mes_projets\\WritingToolsClean\\Windows_and_Linux\\myvenv\\Lib\\site-packages\\openai\\types\\beta\\assistant_update_params.py',
   'PYMODULE'),
  ('openai.types.beta.code_interpreter_tool',
   'c:\\Users\\<USER>\\Desktop\\Mes_projets\\WritingToolsClean\\Windows_and_Linux\\myvenv\\Lib\\site-packages\\openai\\types\\beta\\code_interpreter_tool.py',
   'PYMODULE'),
  ('openai.types.beta.code_interpreter_tool_param',
   'c:\\Users\\<USER>\\Desktop\\Mes_projets\\WritingToolsClean\\Windows_and_Linux\\myvenv\\Lib\\site-packages\\openai\\types\\beta\\code_interpreter_tool_param.py',
   'PYMODULE'),
  ('openai.types.beta.file_search_tool',
   'c:\\Users\\<USER>\\Desktop\\Mes_projets\\WritingToolsClean\\Windows_and_Linux\\myvenv\\Lib\\site-packages\\openai\\types\\beta\\file_search_tool.py',
   'PYMODULE'),
  ('openai.types.beta.file_search_tool_param',
   'c:\\Users\\<USER>\\Desktop\\Mes_projets\\WritingToolsClean\\Windows_and_Linux\\myvenv\\Lib\\site-packages\\openai\\types\\beta\\file_search_tool_param.py',
   'PYMODULE'),
  ('openai.types.beta.function_tool',
   'c:\\Users\\<USER>\\Desktop\\Mes_projets\\WritingToolsClean\\Windows_and_Linux\\myvenv\\Lib\\site-packages\\openai\\types\\beta\\function_tool.py',
   'PYMODULE'),
  ('openai.types.beta.function_tool_param',
   'c:\\Users\\<USER>\\Desktop\\Mes_projets\\WritingToolsClean\\Windows_and_Linux\\myvenv\\Lib\\site-packages\\openai\\types\\beta\\function_tool_param.py',
   'PYMODULE'),
  ('openai.types.beta.realtime',
   'c:\\Users\\<USER>\\Desktop\\Mes_projets\\WritingToolsClean\\Windows_and_Linux\\myvenv\\Lib\\site-packages\\openai\\types\\beta\\realtime\\__init__.py',
   'PYMODULE'),
  ('openai.types.beta.realtime.conversation_created_event',
   'c:\\Users\\<USER>\\Desktop\\Mes_projets\\WritingToolsClean\\Windows_and_Linux\\myvenv\\Lib\\site-packages\\openai\\types\\beta\\realtime\\conversation_created_event.py',
   'PYMODULE'),
  ('openai.types.beta.realtime.conversation_item',
   'c:\\Users\\<USER>\\Desktop\\Mes_projets\\WritingToolsClean\\Windows_and_Linux\\myvenv\\Lib\\site-packages\\openai\\types\\beta\\realtime\\conversation_item.py',
   'PYMODULE'),
  ('openai.types.beta.realtime.conversation_item_content',
   'c:\\Users\\<USER>\\Desktop\\Mes_projets\\WritingToolsClean\\Windows_and_Linux\\myvenv\\Lib\\site-packages\\openai\\types\\beta\\realtime\\conversation_item_content.py',
   'PYMODULE'),
  ('openai.types.beta.realtime.conversation_item_content_param',
   'c:\\Users\\<USER>\\Desktop\\Mes_projets\\WritingToolsClean\\Windows_and_Linux\\myvenv\\Lib\\site-packages\\openai\\types\\beta\\realtime\\conversation_item_content_param.py',
   'PYMODULE'),
  ('openai.types.beta.realtime.conversation_item_create_event',
   'c:\\Users\\<USER>\\Desktop\\Mes_projets\\WritingToolsClean\\Windows_and_Linux\\myvenv\\Lib\\site-packages\\openai\\types\\beta\\realtime\\conversation_item_create_event.py',
   'PYMODULE'),
  ('openai.types.beta.realtime.conversation_item_create_event_param',
   'c:\\Users\\<USER>\\Desktop\\Mes_projets\\WritingToolsClean\\Windows_and_Linux\\myvenv\\Lib\\site-packages\\openai\\types\\beta\\realtime\\conversation_item_create_event_param.py',
   'PYMODULE'),
  ('openai.types.beta.realtime.conversation_item_created_event',
   'c:\\Users\\<USER>\\Desktop\\Mes_projets\\WritingToolsClean\\Windows_and_Linux\\myvenv\\Lib\\site-packages\\openai\\types\\beta\\realtime\\conversation_item_created_event.py',
   'PYMODULE'),
  ('openai.types.beta.realtime.conversation_item_delete_event',
   'c:\\Users\\<USER>\\Desktop\\Mes_projets\\WritingToolsClean\\Windows_and_Linux\\myvenv\\Lib\\site-packages\\openai\\types\\beta\\realtime\\conversation_item_delete_event.py',
   'PYMODULE'),
  ('openai.types.beta.realtime.conversation_item_delete_event_param',
   'c:\\Users\\<USER>\\Desktop\\Mes_projets\\WritingToolsClean\\Windows_and_Linux\\myvenv\\Lib\\site-packages\\openai\\types\\beta\\realtime\\conversation_item_delete_event_param.py',
   'PYMODULE'),
  ('openai.types.beta.realtime.conversation_item_deleted_event',
   'c:\\Users\\<USER>\\Desktop\\Mes_projets\\WritingToolsClean\\Windows_and_Linux\\myvenv\\Lib\\site-packages\\openai\\types\\beta\\realtime\\conversation_item_deleted_event.py',
   'PYMODULE'),
  ('openai.types.beta.realtime.conversation_item_input_audio_transcription_completed_event',
   'c:\\Users\\<USER>\\Desktop\\Mes_projets\\WritingToolsClean\\Windows_and_Linux\\myvenv\\Lib\\site-packages\\openai\\types\\beta\\realtime\\conversation_item_input_audio_transcription_completed_event.py',
   'PYMODULE'),
  ('openai.types.beta.realtime.conversation_item_input_audio_transcription_delta_event',
   'c:\\Users\\<USER>\\Desktop\\Mes_projets\\WritingToolsClean\\Windows_and_Linux\\myvenv\\Lib\\site-packages\\openai\\types\\beta\\realtime\\conversation_item_input_audio_transcription_delta_event.py',
   'PYMODULE'),
  ('openai.types.beta.realtime.conversation_item_input_audio_transcription_failed_event',
   'c:\\Users\\<USER>\\Desktop\\Mes_projets\\WritingToolsClean\\Windows_and_Linux\\myvenv\\Lib\\site-packages\\openai\\types\\beta\\realtime\\conversation_item_input_audio_transcription_failed_event.py',
   'PYMODULE'),
  ('openai.types.beta.realtime.conversation_item_param',
   'c:\\Users\\<USER>\\Desktop\\Mes_projets\\WritingToolsClean\\Windows_and_Linux\\myvenv\\Lib\\site-packages\\openai\\types\\beta\\realtime\\conversation_item_param.py',
   'PYMODULE'),
  ('openai.types.beta.realtime.conversation_item_retrieve_event',
   'c:\\Users\\<USER>\\Desktop\\Mes_projets\\WritingToolsClean\\Windows_and_Linux\\myvenv\\Lib\\site-packages\\openai\\types\\beta\\realtime\\conversation_item_retrieve_event.py',
   'PYMODULE'),
  ('openai.types.beta.realtime.conversation_item_retrieve_event_param',
   'c:\\Users\\<USER>\\Desktop\\Mes_projets\\WritingToolsClean\\Windows_and_Linux\\myvenv\\Lib\\site-packages\\openai\\types\\beta\\realtime\\conversation_item_retrieve_event_param.py',
   'PYMODULE'),
  ('openai.types.beta.realtime.conversation_item_truncate_event',
   'c:\\Users\\<USER>\\Desktop\\Mes_projets\\WritingToolsClean\\Windows_and_Linux\\myvenv\\Lib\\site-packages\\openai\\types\\beta\\realtime\\conversation_item_truncate_event.py',
   'PYMODULE'),
  ('openai.types.beta.realtime.conversation_item_truncate_event_param',
   'c:\\Users\\<USER>\\Desktop\\Mes_projets\\WritingToolsClean\\Windows_and_Linux\\myvenv\\Lib\\site-packages\\openai\\types\\beta\\realtime\\conversation_item_truncate_event_param.py',
   'PYMODULE'),
  ('openai.types.beta.realtime.conversation_item_truncated_event',
   'c:\\Users\\<USER>\\Desktop\\Mes_projets\\WritingToolsClean\\Windows_and_Linux\\myvenv\\Lib\\site-packages\\openai\\types\\beta\\realtime\\conversation_item_truncated_event.py',
   'PYMODULE'),
  ('openai.types.beta.realtime.conversation_item_with_reference',
   'c:\\Users\\<USER>\\Desktop\\Mes_projets\\WritingToolsClean\\Windows_and_Linux\\myvenv\\Lib\\site-packages\\openai\\types\\beta\\realtime\\conversation_item_with_reference.py',
   'PYMODULE'),
  ('openai.types.beta.realtime.conversation_item_with_reference_param',
   'c:\\Users\\<USER>\\Desktop\\Mes_projets\\WritingToolsClean\\Windows_and_Linux\\myvenv\\Lib\\site-packages\\openai\\types\\beta\\realtime\\conversation_item_with_reference_param.py',
   'PYMODULE'),
  ('openai.types.beta.realtime.error_event',
   'c:\\Users\\<USER>\\Desktop\\Mes_projets\\WritingToolsClean\\Windows_and_Linux\\myvenv\\Lib\\site-packages\\openai\\types\\beta\\realtime\\error_event.py',
   'PYMODULE'),
  ('openai.types.beta.realtime.input_audio_buffer_append_event',
   'c:\\Users\\<USER>\\Desktop\\Mes_projets\\WritingToolsClean\\Windows_and_Linux\\myvenv\\Lib\\site-packages\\openai\\types\\beta\\realtime\\input_audio_buffer_append_event.py',
   'PYMODULE'),
  ('openai.types.beta.realtime.input_audio_buffer_append_event_param',
   'c:\\Users\\<USER>\\Desktop\\Mes_projets\\WritingToolsClean\\Windows_and_Linux\\myvenv\\Lib\\site-packages\\openai\\types\\beta\\realtime\\input_audio_buffer_append_event_param.py',
   'PYMODULE'),
  ('openai.types.beta.realtime.input_audio_buffer_clear_event',
   'c:\\Users\\<USER>\\Desktop\\Mes_projets\\WritingToolsClean\\Windows_and_Linux\\myvenv\\Lib\\site-packages\\openai\\types\\beta\\realtime\\input_audio_buffer_clear_event.py',
   'PYMODULE'),
  ('openai.types.beta.realtime.input_audio_buffer_clear_event_param',
   'c:\\Users\\<USER>\\Desktop\\Mes_projets\\WritingToolsClean\\Windows_and_Linux\\myvenv\\Lib\\site-packages\\openai\\types\\beta\\realtime\\input_audio_buffer_clear_event_param.py',
   'PYMODULE'),
  ('openai.types.beta.realtime.input_audio_buffer_cleared_event',
   'c:\\Users\\<USER>\\Desktop\\Mes_projets\\WritingToolsClean\\Windows_and_Linux\\myvenv\\Lib\\site-packages\\openai\\types\\beta\\realtime\\input_audio_buffer_cleared_event.py',
   'PYMODULE'),
  ('openai.types.beta.realtime.input_audio_buffer_commit_event',
   'c:\\Users\\<USER>\\Desktop\\Mes_projets\\WritingToolsClean\\Windows_and_Linux\\myvenv\\Lib\\site-packages\\openai\\types\\beta\\realtime\\input_audio_buffer_commit_event.py',
   'PYMODULE'),
  ('openai.types.beta.realtime.input_audio_buffer_commit_event_param',
   'c:\\Users\\<USER>\\Desktop\\Mes_projets\\WritingToolsClean\\Windows_and_Linux\\myvenv\\Lib\\site-packages\\openai\\types\\beta\\realtime\\input_audio_buffer_commit_event_param.py',
   'PYMODULE'),
  ('openai.types.beta.realtime.input_audio_buffer_committed_event',
   'c:\\Users\\<USER>\\Desktop\\Mes_projets\\WritingToolsClean\\Windows_and_Linux\\myvenv\\Lib\\site-packages\\openai\\types\\beta\\realtime\\input_audio_buffer_committed_event.py',
   'PYMODULE'),
  ('openai.types.beta.realtime.input_audio_buffer_speech_started_event',
   'c:\\Users\\<USER>\\Desktop\\Mes_projets\\WritingToolsClean\\Windows_and_Linux\\myvenv\\Lib\\site-packages\\openai\\types\\beta\\realtime\\input_audio_buffer_speech_started_event.py',
   'PYMODULE'),
  ('openai.types.beta.realtime.input_audio_buffer_speech_stopped_event',
   'c:\\Users\\<USER>\\Desktop\\Mes_projets\\WritingToolsClean\\Windows_and_Linux\\myvenv\\Lib\\site-packages\\openai\\types\\beta\\realtime\\input_audio_buffer_speech_stopped_event.py',
   'PYMODULE'),
  ('openai.types.beta.realtime.rate_limits_updated_event',
   'c:\\Users\\<USER>\\Desktop\\Mes_projets\\WritingToolsClean\\Windows_and_Linux\\myvenv\\Lib\\site-packages\\openai\\types\\beta\\realtime\\rate_limits_updated_event.py',
   'PYMODULE'),
  ('openai.types.beta.realtime.realtime_client_event',
   'c:\\Users\\<USER>\\Desktop\\Mes_projets\\WritingToolsClean\\Windows_and_Linux\\myvenv\\Lib\\site-packages\\openai\\types\\beta\\realtime\\realtime_client_event.py',
   'PYMODULE'),
  ('openai.types.beta.realtime.realtime_client_event_param',
   'c:\\Users\\<USER>\\Desktop\\Mes_projets\\WritingToolsClean\\Windows_and_Linux\\myvenv\\Lib\\site-packages\\openai\\types\\beta\\realtime\\realtime_client_event_param.py',
   'PYMODULE'),
  ('openai.types.beta.realtime.realtime_connect_params',
   'c:\\Users\\<USER>\\Desktop\\Mes_projets\\WritingToolsClean\\Windows_and_Linux\\myvenv\\Lib\\site-packages\\openai\\types\\beta\\realtime\\realtime_connect_params.py',
   'PYMODULE'),
  ('openai.types.beta.realtime.realtime_response',
   'c:\\Users\\<USER>\\Desktop\\Mes_projets\\WritingToolsClean\\Windows_and_Linux\\myvenv\\Lib\\site-packages\\openai\\types\\beta\\realtime\\realtime_response.py',
   'PYMODULE'),
  ('openai.types.beta.realtime.realtime_response_status',
   'c:\\Users\\<USER>\\Desktop\\Mes_projets\\WritingToolsClean\\Windows_and_Linux\\myvenv\\Lib\\site-packages\\openai\\types\\beta\\realtime\\realtime_response_status.py',
   'PYMODULE'),
  ('openai.types.beta.realtime.realtime_response_usage',
   'c:\\Users\\<USER>\\Desktop\\Mes_projets\\WritingToolsClean\\Windows_and_Linux\\myvenv\\Lib\\site-packages\\openai\\types\\beta\\realtime\\realtime_response_usage.py',
   'PYMODULE'),
  ('openai.types.beta.realtime.realtime_server_event',
   'c:\\Users\\<USER>\\Desktop\\Mes_projets\\WritingToolsClean\\Windows_and_Linux\\myvenv\\Lib\\site-packages\\openai\\types\\beta\\realtime\\realtime_server_event.py',
   'PYMODULE'),
  ('openai.types.beta.realtime.response_audio_delta_event',
   'c:\\Users\\<USER>\\Desktop\\Mes_projets\\WritingToolsClean\\Windows_and_Linux\\myvenv\\Lib\\site-packages\\openai\\types\\beta\\realtime\\response_audio_delta_event.py',
   'PYMODULE'),
  ('openai.types.beta.realtime.response_audio_done_event',
   'c:\\Users\\<USER>\\Desktop\\Mes_projets\\WritingToolsClean\\Windows_and_Linux\\myvenv\\Lib\\site-packages\\openai\\types\\beta\\realtime\\response_audio_done_event.py',
   'PYMODULE'),
  ('openai.types.beta.realtime.response_audio_transcript_delta_event',
   'c:\\Users\\<USER>\\Desktop\\Mes_projets\\WritingToolsClean\\Windows_and_Linux\\myvenv\\Lib\\site-packages\\openai\\types\\beta\\realtime\\response_audio_transcript_delta_event.py',
   'PYMODULE'),
  ('openai.types.beta.realtime.response_audio_transcript_done_event',
   'c:\\Users\\<USER>\\Desktop\\Mes_projets\\WritingToolsClean\\Windows_and_Linux\\myvenv\\Lib\\site-packages\\openai\\types\\beta\\realtime\\response_audio_transcript_done_event.py',
   'PYMODULE'),
  ('openai.types.beta.realtime.response_cancel_event',
   'c:\\Users\\<USER>\\Desktop\\Mes_projets\\WritingToolsClean\\Windows_and_Linux\\myvenv\\Lib\\site-packages\\openai\\types\\beta\\realtime\\response_cancel_event.py',
   'PYMODULE'),
  ('openai.types.beta.realtime.response_cancel_event_param',
   'c:\\Users\\<USER>\\Desktop\\Mes_projets\\WritingToolsClean\\Windows_and_Linux\\myvenv\\Lib\\site-packages\\openai\\types\\beta\\realtime\\response_cancel_event_param.py',
   'PYMODULE'),
  ('openai.types.beta.realtime.response_content_part_added_event',
   'c:\\Users\\<USER>\\Desktop\\Mes_projets\\WritingToolsClean\\Windows_and_Linux\\myvenv\\Lib\\site-packages\\openai\\types\\beta\\realtime\\response_content_part_added_event.py',
   'PYMODULE'),
  ('openai.types.beta.realtime.response_content_part_done_event',
   'c:\\Users\\<USER>\\Desktop\\Mes_projets\\WritingToolsClean\\Windows_and_Linux\\myvenv\\Lib\\site-packages\\openai\\types\\beta\\realtime\\response_content_part_done_event.py',
   'PYMODULE'),
  ('openai.types.beta.realtime.response_create_event',
   'c:\\Users\\<USER>\\Desktop\\Mes_projets\\WritingToolsClean\\Windows_and_Linux\\myvenv\\Lib\\site-packages\\openai\\types\\beta\\realtime\\response_create_event.py',
   'PYMODULE'),
  ('openai.types.beta.realtime.response_create_event_param',
   'c:\\Users\\<USER>\\Desktop\\Mes_projets\\WritingToolsClean\\Windows_and_Linux\\myvenv\\Lib\\site-packages\\openai\\types\\beta\\realtime\\response_create_event_param.py',
   'PYMODULE'),
  ('openai.types.beta.realtime.response_created_event',
   'c:\\Users\\<USER>\\Desktop\\Mes_projets\\WritingToolsClean\\Windows_and_Linux\\myvenv\\Lib\\site-packages\\openai\\types\\beta\\realtime\\response_created_event.py',
   'PYMODULE'),
  ('openai.types.beta.realtime.response_done_event',
   'c:\\Users\\<USER>\\Desktop\\Mes_projets\\WritingToolsClean\\Windows_and_Linux\\myvenv\\Lib\\site-packages\\openai\\types\\beta\\realtime\\response_done_event.py',
   'PYMODULE'),
  ('openai.types.beta.realtime.response_function_call_arguments_delta_event',
   'c:\\Users\\<USER>\\Desktop\\Mes_projets\\WritingToolsClean\\Windows_and_Linux\\myvenv\\Lib\\site-packages\\openai\\types\\beta\\realtime\\response_function_call_arguments_delta_event.py',
   'PYMODULE'),
  ('openai.types.beta.realtime.response_function_call_arguments_done_event',
   'c:\\Users\\<USER>\\Desktop\\Mes_projets\\WritingToolsClean\\Windows_and_Linux\\myvenv\\Lib\\site-packages\\openai\\types\\beta\\realtime\\response_function_call_arguments_done_event.py',
   'PYMODULE'),
  ('openai.types.beta.realtime.response_output_item_added_event',
   'c:\\Users\\<USER>\\Desktop\\Mes_projets\\WritingToolsClean\\Windows_and_Linux\\myvenv\\Lib\\site-packages\\openai\\types\\beta\\realtime\\response_output_item_added_event.py',
   'PYMODULE'),
  ('openai.types.beta.realtime.response_output_item_done_event',
   'c:\\Users\\<USER>\\Desktop\\Mes_projets\\WritingToolsClean\\Windows_and_Linux\\myvenv\\Lib\\site-packages\\openai\\types\\beta\\realtime\\response_output_item_done_event.py',
   'PYMODULE'),
  ('openai.types.beta.realtime.response_text_delta_event',
   'c:\\Users\\<USER>\\Desktop\\Mes_projets\\WritingToolsClean\\Windows_and_Linux\\myvenv\\Lib\\site-packages\\openai\\types\\beta\\realtime\\response_text_delta_event.py',
   'PYMODULE'),
  ('openai.types.beta.realtime.response_text_done_event',
   'c:\\Users\\<USER>\\Desktop\\Mes_projets\\WritingToolsClean\\Windows_and_Linux\\myvenv\\Lib\\site-packages\\openai\\types\\beta\\realtime\\response_text_done_event.py',
   'PYMODULE'),
  ('openai.types.beta.realtime.session',
   'c:\\Users\\<USER>\\Desktop\\Mes_projets\\WritingToolsClean\\Windows_and_Linux\\myvenv\\Lib\\site-packages\\openai\\types\\beta\\realtime\\session.py',
   'PYMODULE'),
  ('openai.types.beta.realtime.session_create_params',
   'c:\\Users\\<USER>\\Desktop\\Mes_projets\\WritingToolsClean\\Windows_and_Linux\\myvenv\\Lib\\site-packages\\openai\\types\\beta\\realtime\\session_create_params.py',
   'PYMODULE'),
  ('openai.types.beta.realtime.session_create_response',
   'c:\\Users\\<USER>\\Desktop\\Mes_projets\\WritingToolsClean\\Windows_and_Linux\\myvenv\\Lib\\site-packages\\openai\\types\\beta\\realtime\\session_create_response.py',
   'PYMODULE'),
  ('openai.types.beta.realtime.session_created_event',
   'c:\\Users\\<USER>\\Desktop\\Mes_projets\\WritingToolsClean\\Windows_and_Linux\\myvenv\\Lib\\site-packages\\openai\\types\\beta\\realtime\\session_created_event.py',
   'PYMODULE'),
  ('openai.types.beta.realtime.session_update_event',
   'c:\\Users\\<USER>\\Desktop\\Mes_projets\\WritingToolsClean\\Windows_and_Linux\\myvenv\\Lib\\site-packages\\openai\\types\\beta\\realtime\\session_update_event.py',
   'PYMODULE'),
  ('openai.types.beta.realtime.session_update_event_param',
   'c:\\Users\\<USER>\\Desktop\\Mes_projets\\WritingToolsClean\\Windows_and_Linux\\myvenv\\Lib\\site-packages\\openai\\types\\beta\\realtime\\session_update_event_param.py',
   'PYMODULE'),
  ('openai.types.beta.realtime.session_updated_event',
   'c:\\Users\\<USER>\\Desktop\\Mes_projets\\WritingToolsClean\\Windows_and_Linux\\myvenv\\Lib\\site-packages\\openai\\types\\beta\\realtime\\session_updated_event.py',
   'PYMODULE'),
  ('openai.types.beta.realtime.transcription_session',
   'c:\\Users\\<USER>\\Desktop\\Mes_projets\\WritingToolsClean\\Windows_and_Linux\\myvenv\\Lib\\site-packages\\openai\\types\\beta\\realtime\\transcription_session.py',
   'PYMODULE'),
  ('openai.types.beta.realtime.transcription_session_create_params',
   'c:\\Users\\<USER>\\Desktop\\Mes_projets\\WritingToolsClean\\Windows_and_Linux\\myvenv\\Lib\\site-packages\\openai\\types\\beta\\realtime\\transcription_session_create_params.py',
   'PYMODULE'),
  ('openai.types.beta.realtime.transcription_session_update',
   'c:\\Users\\<USER>\\Desktop\\Mes_projets\\WritingToolsClean\\Windows_and_Linux\\myvenv\\Lib\\site-packages\\openai\\types\\beta\\realtime\\transcription_session_update.py',
   'PYMODULE'),
  ('openai.types.beta.realtime.transcription_session_update_param',
   'c:\\Users\\<USER>\\Desktop\\Mes_projets\\WritingToolsClean\\Windows_and_Linux\\myvenv\\Lib\\site-packages\\openai\\types\\beta\\realtime\\transcription_session_update_param.py',
   'PYMODULE'),
  ('openai.types.beta.realtime.transcription_session_updated_event',
   'c:\\Users\\<USER>\\Desktop\\Mes_projets\\WritingToolsClean\\Windows_and_Linux\\myvenv\\Lib\\site-packages\\openai\\types\\beta\\realtime\\transcription_session_updated_event.py',
   'PYMODULE'),
  ('openai.types.beta.thread',
   'c:\\Users\\<USER>\\Desktop\\Mes_projets\\WritingToolsClean\\Windows_and_Linux\\myvenv\\Lib\\site-packages\\openai\\types\\beta\\thread.py',
   'PYMODULE'),
  ('openai.types.beta.thread_create_and_run_params',
   'c:\\Users\\<USER>\\Desktop\\Mes_projets\\WritingToolsClean\\Windows_and_Linux\\myvenv\\Lib\\site-packages\\openai\\types\\beta\\thread_create_and_run_params.py',
   'PYMODULE'),
  ('openai.types.beta.thread_create_params',
   'c:\\Users\\<USER>\\Desktop\\Mes_projets\\WritingToolsClean\\Windows_and_Linux\\myvenv\\Lib\\site-packages\\openai\\types\\beta\\thread_create_params.py',
   'PYMODULE'),
  ('openai.types.beta.thread_deleted',
   'c:\\Users\\<USER>\\Desktop\\Mes_projets\\WritingToolsClean\\Windows_and_Linux\\myvenv\\Lib\\site-packages\\openai\\types\\beta\\thread_deleted.py',
   'PYMODULE'),
  ('openai.types.beta.thread_update_params',
   'c:\\Users\\<USER>\\Desktop\\Mes_projets\\WritingToolsClean\\Windows_and_Linux\\myvenv\\Lib\\site-packages\\openai\\types\\beta\\thread_update_params.py',
   'PYMODULE'),
  ('openai.types.beta.threads',
   'c:\\Users\\<USER>\\Desktop\\Mes_projets\\WritingToolsClean\\Windows_and_Linux\\myvenv\\Lib\\site-packages\\openai\\types\\beta\\threads\\__init__.py',
   'PYMODULE'),
  ('openai.types.beta.threads.annotation',
   'c:\\Users\\<USER>\\Desktop\\Mes_projets\\WritingToolsClean\\Windows_and_Linux\\myvenv\\Lib\\site-packages\\openai\\types\\beta\\threads\\annotation.py',
   'PYMODULE'),
  ('openai.types.beta.threads.annotation_delta',
   'c:\\Users\\<USER>\\Desktop\\Mes_projets\\WritingToolsClean\\Windows_and_Linux\\myvenv\\Lib\\site-packages\\openai\\types\\beta\\threads\\annotation_delta.py',
   'PYMODULE'),
  ('openai.types.beta.threads.file_citation_annotation',
   'c:\\Users\\<USER>\\Desktop\\Mes_projets\\WritingToolsClean\\Windows_and_Linux\\myvenv\\Lib\\site-packages\\openai\\types\\beta\\threads\\file_citation_annotation.py',
   'PYMODULE'),
  ('openai.types.beta.threads.file_citation_delta_annotation',
   'c:\\Users\\<USER>\\Desktop\\Mes_projets\\WritingToolsClean\\Windows_and_Linux\\myvenv\\Lib\\site-packages\\openai\\types\\beta\\threads\\file_citation_delta_annotation.py',
   'PYMODULE'),
  ('openai.types.beta.threads.file_path_annotation',
   'c:\\Users\\<USER>\\Desktop\\Mes_projets\\WritingToolsClean\\Windows_and_Linux\\myvenv\\Lib\\site-packages\\openai\\types\\beta\\threads\\file_path_annotation.py',
   'PYMODULE'),
  ('openai.types.beta.threads.file_path_delta_annotation',
   'c:\\Users\\<USER>\\Desktop\\Mes_projets\\WritingToolsClean\\Windows_and_Linux\\myvenv\\Lib\\site-packages\\openai\\types\\beta\\threads\\file_path_delta_annotation.py',
   'PYMODULE'),
  ('openai.types.beta.threads.image_file',
   'c:\\Users\\<USER>\\Desktop\\Mes_projets\\WritingToolsClean\\Windows_and_Linux\\myvenv\\Lib\\site-packages\\openai\\types\\beta\\threads\\image_file.py',
   'PYMODULE'),
  ('openai.types.beta.threads.image_file_content_block',
   'c:\\Users\\<USER>\\Desktop\\Mes_projets\\WritingToolsClean\\Windows_and_Linux\\myvenv\\Lib\\site-packages\\openai\\types\\beta\\threads\\image_file_content_block.py',
   'PYMODULE'),
  ('openai.types.beta.threads.image_file_content_block_param',
   'c:\\Users\\<USER>\\Desktop\\Mes_projets\\WritingToolsClean\\Windows_and_Linux\\myvenv\\Lib\\site-packages\\openai\\types\\beta\\threads\\image_file_content_block_param.py',
   'PYMODULE'),
  ('openai.types.beta.threads.image_file_delta',
   'c:\\Users\\<USER>\\Desktop\\Mes_projets\\WritingToolsClean\\Windows_and_Linux\\myvenv\\Lib\\site-packages\\openai\\types\\beta\\threads\\image_file_delta.py',
   'PYMODULE'),
  ('openai.types.beta.threads.image_file_delta_block',
   'c:\\Users\\<USER>\\Desktop\\Mes_projets\\WritingToolsClean\\Windows_and_Linux\\myvenv\\Lib\\site-packages\\openai\\types\\beta\\threads\\image_file_delta_block.py',
   'PYMODULE'),
  ('openai.types.beta.threads.image_file_param',
   'c:\\Users\\<USER>\\Desktop\\Mes_projets\\WritingToolsClean\\Windows_and_Linux\\myvenv\\Lib\\site-packages\\openai\\types\\beta\\threads\\image_file_param.py',
   'PYMODULE'),
  ('openai.types.beta.threads.image_url',
   'c:\\Users\\<USER>\\Desktop\\Mes_projets\\WritingToolsClean\\Windows_and_Linux\\myvenv\\Lib\\site-packages\\openai\\types\\beta\\threads\\image_url.py',
   'PYMODULE'),
  ('openai.types.beta.threads.image_url_content_block',
   'c:\\Users\\<USER>\\Desktop\\Mes_projets\\WritingToolsClean\\Windows_and_Linux\\myvenv\\Lib\\site-packages\\openai\\types\\beta\\threads\\image_url_content_block.py',
   'PYMODULE'),
  ('openai.types.beta.threads.image_url_content_block_param',
   'c:\\Users\\<USER>\\Desktop\\Mes_projets\\WritingToolsClean\\Windows_and_Linux\\myvenv\\Lib\\site-packages\\openai\\types\\beta\\threads\\image_url_content_block_param.py',
   'PYMODULE'),
  ('openai.types.beta.threads.image_url_delta',
   'c:\\Users\\<USER>\\Desktop\\Mes_projets\\WritingToolsClean\\Windows_and_Linux\\myvenv\\Lib\\site-packages\\openai\\types\\beta\\threads\\image_url_delta.py',
   'PYMODULE'),
  ('openai.types.beta.threads.image_url_delta_block',
   'c:\\Users\\<USER>\\Desktop\\Mes_projets\\WritingToolsClean\\Windows_and_Linux\\myvenv\\Lib\\site-packages\\openai\\types\\beta\\threads\\image_url_delta_block.py',
   'PYMODULE'),
  ('openai.types.beta.threads.image_url_param',
   'c:\\Users\\<USER>\\Desktop\\Mes_projets\\WritingToolsClean\\Windows_and_Linux\\myvenv\\Lib\\site-packages\\openai\\types\\beta\\threads\\image_url_param.py',
   'PYMODULE'),
  ('openai.types.beta.threads.message',
   'c:\\Users\\<USER>\\Desktop\\Mes_projets\\WritingToolsClean\\Windows_and_Linux\\myvenv\\Lib\\site-packages\\openai\\types\\beta\\threads\\message.py',
   'PYMODULE'),
  ('openai.types.beta.threads.message_content',
   'c:\\Users\\<USER>\\Desktop\\Mes_projets\\WritingToolsClean\\Windows_and_Linux\\myvenv\\Lib\\site-packages\\openai\\types\\beta\\threads\\message_content.py',
   'PYMODULE'),
  ('openai.types.beta.threads.message_content_delta',
   'c:\\Users\\<USER>\\Desktop\\Mes_projets\\WritingToolsClean\\Windows_and_Linux\\myvenv\\Lib\\site-packages\\openai\\types\\beta\\threads\\message_content_delta.py',
   'PYMODULE'),
  ('openai.types.beta.threads.message_content_part_param',
   'c:\\Users\\<USER>\\Desktop\\Mes_projets\\WritingToolsClean\\Windows_and_Linux\\myvenv\\Lib\\site-packages\\openai\\types\\beta\\threads\\message_content_part_param.py',
   'PYMODULE'),
  ('openai.types.beta.threads.message_create_params',
   'c:\\Users\\<USER>\\Desktop\\Mes_projets\\WritingToolsClean\\Windows_and_Linux\\myvenv\\Lib\\site-packages\\openai\\types\\beta\\threads\\message_create_params.py',
   'PYMODULE'),
  ('openai.types.beta.threads.message_deleted',
   'c:\\Users\\<USER>\\Desktop\\Mes_projets\\WritingToolsClean\\Windows_and_Linux\\myvenv\\Lib\\site-packages\\openai\\types\\beta\\threads\\message_deleted.py',
   'PYMODULE'),
  ('openai.types.beta.threads.message_delta',
   'c:\\Users\\<USER>\\Desktop\\Mes_projets\\WritingToolsClean\\Windows_and_Linux\\myvenv\\Lib\\site-packages\\openai\\types\\beta\\threads\\message_delta.py',
   'PYMODULE'),
  ('openai.types.beta.threads.message_delta_event',
   'c:\\Users\\<USER>\\Desktop\\Mes_projets\\WritingToolsClean\\Windows_and_Linux\\myvenv\\Lib\\site-packages\\openai\\types\\beta\\threads\\message_delta_event.py',
   'PYMODULE'),
  ('openai.types.beta.threads.message_list_params',
   'c:\\Users\\<USER>\\Desktop\\Mes_projets\\WritingToolsClean\\Windows_and_Linux\\myvenv\\Lib\\site-packages\\openai\\types\\beta\\threads\\message_list_params.py',
   'PYMODULE'),
  ('openai.types.beta.threads.message_update_params',
   'c:\\Users\\<USER>\\Desktop\\Mes_projets\\WritingToolsClean\\Windows_and_Linux\\myvenv\\Lib\\site-packages\\openai\\types\\beta\\threads\\message_update_params.py',
   'PYMODULE'),
  ('openai.types.beta.threads.refusal_content_block',
   'c:\\Users\\<USER>\\Desktop\\Mes_projets\\WritingToolsClean\\Windows_and_Linux\\myvenv\\Lib\\site-packages\\openai\\types\\beta\\threads\\refusal_content_block.py',
   'PYMODULE'),
  ('openai.types.beta.threads.refusal_delta_block',
   'c:\\Users\\<USER>\\Desktop\\Mes_projets\\WritingToolsClean\\Windows_and_Linux\\myvenv\\Lib\\site-packages\\openai\\types\\beta\\threads\\refusal_delta_block.py',
   'PYMODULE'),
  ('openai.types.beta.threads.required_action_function_tool_call',
   'c:\\Users\\<USER>\\Desktop\\Mes_projets\\WritingToolsClean\\Windows_and_Linux\\myvenv\\Lib\\site-packages\\openai\\types\\beta\\threads\\required_action_function_tool_call.py',
   'PYMODULE'),
  ('openai.types.beta.threads.run',
   'c:\\Users\\<USER>\\Desktop\\Mes_projets\\WritingToolsClean\\Windows_and_Linux\\myvenv\\Lib\\site-packages\\openai\\types\\beta\\threads\\run.py',
   'PYMODULE'),
  ('openai.types.beta.threads.run_create_params',
   'c:\\Users\\<USER>\\Desktop\\Mes_projets\\WritingToolsClean\\Windows_and_Linux\\myvenv\\Lib\\site-packages\\openai\\types\\beta\\threads\\run_create_params.py',
   'PYMODULE'),
  ('openai.types.beta.threads.run_list_params',
   'c:\\Users\\<USER>\\Desktop\\Mes_projets\\WritingToolsClean\\Windows_and_Linux\\myvenv\\Lib\\site-packages\\openai\\types\\beta\\threads\\run_list_params.py',
   'PYMODULE'),
  ('openai.types.beta.threads.run_status',
   'c:\\Users\\<USER>\\Desktop\\Mes_projets\\WritingToolsClean\\Windows_and_Linux\\myvenv\\Lib\\site-packages\\openai\\types\\beta\\threads\\run_status.py',
   'PYMODULE'),
  ('openai.types.beta.threads.run_submit_tool_outputs_params',
   'c:\\Users\\<USER>\\Desktop\\Mes_projets\\WritingToolsClean\\Windows_and_Linux\\myvenv\\Lib\\site-packages\\openai\\types\\beta\\threads\\run_submit_tool_outputs_params.py',
   'PYMODULE'),
  ('openai.types.beta.threads.run_update_params',
   'c:\\Users\\<USER>\\Desktop\\Mes_projets\\WritingToolsClean\\Windows_and_Linux\\myvenv\\Lib\\site-packages\\openai\\types\\beta\\threads\\run_update_params.py',
   'PYMODULE'),
  ('openai.types.beta.threads.runs',
   'c:\\Users\\<USER>\\Desktop\\Mes_projets\\WritingToolsClean\\Windows_and_Linux\\myvenv\\Lib\\site-packages\\openai\\types\\beta\\threads\\runs\\__init__.py',
   'PYMODULE'),
  ('openai.types.beta.threads.runs.code_interpreter_logs',
   'c:\\Users\\<USER>\\Desktop\\Mes_projets\\WritingToolsClean\\Windows_and_Linux\\myvenv\\Lib\\site-packages\\openai\\types\\beta\\threads\\runs\\code_interpreter_logs.py',
   'PYMODULE'),
  ('openai.types.beta.threads.runs.code_interpreter_output_image',
   'c:\\Users\\<USER>\\Desktop\\Mes_projets\\WritingToolsClean\\Windows_and_Linux\\myvenv\\Lib\\site-packages\\openai\\types\\beta\\threads\\runs\\code_interpreter_output_image.py',
   'PYMODULE'),
  ('openai.types.beta.threads.runs.code_interpreter_tool_call',
   'c:\\Users\\<USER>\\Desktop\\Mes_projets\\WritingToolsClean\\Windows_and_Linux\\myvenv\\Lib\\site-packages\\openai\\types\\beta\\threads\\runs\\code_interpreter_tool_call.py',
   'PYMODULE'),
  ('openai.types.beta.threads.runs.code_interpreter_tool_call_delta',
   'c:\\Users\\<USER>\\Desktop\\Mes_projets\\WritingToolsClean\\Windows_and_Linux\\myvenv\\Lib\\site-packages\\openai\\types\\beta\\threads\\runs\\code_interpreter_tool_call_delta.py',
   'PYMODULE'),
  ('openai.types.beta.threads.runs.file_search_tool_call',
   'c:\\Users\\<USER>\\Desktop\\Mes_projets\\WritingToolsClean\\Windows_and_Linux\\myvenv\\Lib\\site-packages\\openai\\types\\beta\\threads\\runs\\file_search_tool_call.py',
   'PYMODULE'),
  ('openai.types.beta.threads.runs.file_search_tool_call_delta',
   'c:\\Users\\<USER>\\Desktop\\Mes_projets\\WritingToolsClean\\Windows_and_Linux\\myvenv\\Lib\\site-packages\\openai\\types\\beta\\threads\\runs\\file_search_tool_call_delta.py',
   'PYMODULE'),
  ('openai.types.beta.threads.runs.function_tool_call',
   'c:\\Users\\<USER>\\Desktop\\Mes_projets\\WritingToolsClean\\Windows_and_Linux\\myvenv\\Lib\\site-packages\\openai\\types\\beta\\threads\\runs\\function_tool_call.py',
   'PYMODULE'),
  ('openai.types.beta.threads.runs.function_tool_call_delta',
   'c:\\Users\\<USER>\\Desktop\\Mes_projets\\WritingToolsClean\\Windows_and_Linux\\myvenv\\Lib\\site-packages\\openai\\types\\beta\\threads\\runs\\function_tool_call_delta.py',
   'PYMODULE'),
  ('openai.types.beta.threads.runs.message_creation_step_details',
   'c:\\Users\\<USER>\\Desktop\\Mes_projets\\WritingToolsClean\\Windows_and_Linux\\myvenv\\Lib\\site-packages\\openai\\types\\beta\\threads\\runs\\message_creation_step_details.py',
   'PYMODULE'),
  ('openai.types.beta.threads.runs.run_step',
   'c:\\Users\\<USER>\\Desktop\\Mes_projets\\WritingToolsClean\\Windows_and_Linux\\myvenv\\Lib\\site-packages\\openai\\types\\beta\\threads\\runs\\run_step.py',
   'PYMODULE'),
  ('openai.types.beta.threads.runs.run_step_delta',
   'c:\\Users\\<USER>\\Desktop\\Mes_projets\\WritingToolsClean\\Windows_and_Linux\\myvenv\\Lib\\site-packages\\openai\\types\\beta\\threads\\runs\\run_step_delta.py',
   'PYMODULE'),
  ('openai.types.beta.threads.runs.run_step_delta_event',
   'c:\\Users\\<USER>\\Desktop\\Mes_projets\\WritingToolsClean\\Windows_and_Linux\\myvenv\\Lib\\site-packages\\openai\\types\\beta\\threads\\runs\\run_step_delta_event.py',
   'PYMODULE'),
  ('openai.types.beta.threads.runs.run_step_delta_message_delta',
   'c:\\Users\\<USER>\\Desktop\\Mes_projets\\WritingToolsClean\\Windows_and_Linux\\myvenv\\Lib\\site-packages\\openai\\types\\beta\\threads\\runs\\run_step_delta_message_delta.py',
   'PYMODULE'),
  ('openai.types.beta.threads.runs.run_step_include',
   'c:\\Users\\<USER>\\Desktop\\Mes_projets\\WritingToolsClean\\Windows_and_Linux\\myvenv\\Lib\\site-packages\\openai\\types\\beta\\threads\\runs\\run_step_include.py',
   'PYMODULE'),
  ('openai.types.beta.threads.runs.step_list_params',
   'c:\\Users\\<USER>\\Desktop\\Mes_projets\\WritingToolsClean\\Windows_and_Linux\\myvenv\\Lib\\site-packages\\openai\\types\\beta\\threads\\runs\\step_list_params.py',
   'PYMODULE'),
  ('openai.types.beta.threads.runs.step_retrieve_params',
   'c:\\Users\\<USER>\\Desktop\\Mes_projets\\WritingToolsClean\\Windows_and_Linux\\myvenv\\Lib\\site-packages\\openai\\types\\beta\\threads\\runs\\step_retrieve_params.py',
   'PYMODULE'),
  ('openai.types.beta.threads.runs.tool_call',
   'c:\\Users\\<USER>\\Desktop\\Mes_projets\\WritingToolsClean\\Windows_and_Linux\\myvenv\\Lib\\site-packages\\openai\\types\\beta\\threads\\runs\\tool_call.py',
   'PYMODULE'),
  ('openai.types.beta.threads.runs.tool_call_delta',
   'c:\\Users\\<USER>\\Desktop\\Mes_projets\\WritingToolsClean\\Windows_and_Linux\\myvenv\\Lib\\site-packages\\openai\\types\\beta\\threads\\runs\\tool_call_delta.py',
   'PYMODULE'),
  ('openai.types.beta.threads.runs.tool_call_delta_object',
   'c:\\Users\\<USER>\\Desktop\\Mes_projets\\WritingToolsClean\\Windows_and_Linux\\myvenv\\Lib\\site-packages\\openai\\types\\beta\\threads\\runs\\tool_call_delta_object.py',
   'PYMODULE'),
  ('openai.types.beta.threads.runs.tool_calls_step_details',
   'c:\\Users\\<USER>\\Desktop\\Mes_projets\\WritingToolsClean\\Windows_and_Linux\\myvenv\\Lib\\site-packages\\openai\\types\\beta\\threads\\runs\\tool_calls_step_details.py',
   'PYMODULE'),
  ('openai.types.beta.threads.text',
   'c:\\Users\\<USER>\\Desktop\\Mes_projets\\WritingToolsClean\\Windows_and_Linux\\myvenv\\Lib\\site-packages\\openai\\types\\beta\\threads\\text.py',
   'PYMODULE'),
  ('openai.types.beta.threads.text_content_block',
   'c:\\Users\\<USER>\\Desktop\\Mes_projets\\WritingToolsClean\\Windows_and_Linux\\myvenv\\Lib\\site-packages\\openai\\types\\beta\\threads\\text_content_block.py',
   'PYMODULE'),
  ('openai.types.beta.threads.text_content_block_param',
   'c:\\Users\\<USER>\\Desktop\\Mes_projets\\WritingToolsClean\\Windows_and_Linux\\myvenv\\Lib\\site-packages\\openai\\types\\beta\\threads\\text_content_block_param.py',
   'PYMODULE'),
  ('openai.types.beta.threads.text_delta',
   'c:\\Users\\<USER>\\Desktop\\Mes_projets\\WritingToolsClean\\Windows_and_Linux\\myvenv\\Lib\\site-packages\\openai\\types\\beta\\threads\\text_delta.py',
   'PYMODULE'),
  ('openai.types.beta.threads.text_delta_block',
   'c:\\Users\\<USER>\\Desktop\\Mes_projets\\WritingToolsClean\\Windows_and_Linux\\myvenv\\Lib\\site-packages\\openai\\types\\beta\\threads\\text_delta_block.py',
   'PYMODULE'),
  ('openai.types.chat',
   'c:\\Users\\<USER>\\Desktop\\Mes_projets\\WritingToolsClean\\Windows_and_Linux\\myvenv\\Lib\\site-packages\\openai\\types\\chat\\__init__.py',
   'PYMODULE'),
  ('openai.types.chat.chat_completion',
   'c:\\Users\\<USER>\\Desktop\\Mes_projets\\WritingToolsClean\\Windows_and_Linux\\myvenv\\Lib\\site-packages\\openai\\types\\chat\\chat_completion.py',
   'PYMODULE'),
  ('openai.types.chat.chat_completion_assistant_message_param',
   'c:\\Users\\<USER>\\Desktop\\Mes_projets\\WritingToolsClean\\Windows_and_Linux\\myvenv\\Lib\\site-packages\\openai\\types\\chat\\chat_completion_assistant_message_param.py',
   'PYMODULE'),
  ('openai.types.chat.chat_completion_audio',
   'c:\\Users\\<USER>\\Desktop\\Mes_projets\\WritingToolsClean\\Windows_and_Linux\\myvenv\\Lib\\site-packages\\openai\\types\\chat\\chat_completion_audio.py',
   'PYMODULE'),
  ('openai.types.chat.chat_completion_audio_param',
   'c:\\Users\\<USER>\\Desktop\\Mes_projets\\WritingToolsClean\\Windows_and_Linux\\myvenv\\Lib\\site-packages\\openai\\types\\chat\\chat_completion_audio_param.py',
   'PYMODULE'),
  ('openai.types.chat.chat_completion_chunk',
   'c:\\Users\\<USER>\\Desktop\\Mes_projets\\WritingToolsClean\\Windows_and_Linux\\myvenv\\Lib\\site-packages\\openai\\types\\chat\\chat_completion_chunk.py',
   'PYMODULE'),
  ('openai.types.chat.chat_completion_content_part_image_param',
   'c:\\Users\\<USER>\\Desktop\\Mes_projets\\WritingToolsClean\\Windows_and_Linux\\myvenv\\Lib\\site-packages\\openai\\types\\chat\\chat_completion_content_part_image_param.py',
   'PYMODULE'),
  ('openai.types.chat.chat_completion_content_part_input_audio_param',
   'c:\\Users\\<USER>\\Desktop\\Mes_projets\\WritingToolsClean\\Windows_and_Linux\\myvenv\\Lib\\site-packages\\openai\\types\\chat\\chat_completion_content_part_input_audio_param.py',
   'PYMODULE'),
  ('openai.types.chat.chat_completion_content_part_param',
   'c:\\Users\\<USER>\\Desktop\\Mes_projets\\WritingToolsClean\\Windows_and_Linux\\myvenv\\Lib\\site-packages\\openai\\types\\chat\\chat_completion_content_part_param.py',
   'PYMODULE'),
  ('openai.types.chat.chat_completion_content_part_refusal_param',
   'c:\\Users\\<USER>\\Desktop\\Mes_projets\\WritingToolsClean\\Windows_and_Linux\\myvenv\\Lib\\site-packages\\openai\\types\\chat\\chat_completion_content_part_refusal_param.py',
   'PYMODULE'),
  ('openai.types.chat.chat_completion_content_part_text_param',
   'c:\\Users\\<USER>\\Desktop\\Mes_projets\\WritingToolsClean\\Windows_and_Linux\\myvenv\\Lib\\site-packages\\openai\\types\\chat\\chat_completion_content_part_text_param.py',
   'PYMODULE'),
  ('openai.types.chat.chat_completion_deleted',
   'c:\\Users\\<USER>\\Desktop\\Mes_projets\\WritingToolsClean\\Windows_and_Linux\\myvenv\\Lib\\site-packages\\openai\\types\\chat\\chat_completion_deleted.py',
   'PYMODULE'),
  ('openai.types.chat.chat_completion_developer_message_param',
   'c:\\Users\\<USER>\\Desktop\\Mes_projets\\WritingToolsClean\\Windows_and_Linux\\myvenv\\Lib\\site-packages\\openai\\types\\chat\\chat_completion_developer_message_param.py',
   'PYMODULE'),
  ('openai.types.chat.chat_completion_function_call_option_param',
   'c:\\Users\\<USER>\\Desktop\\Mes_projets\\WritingToolsClean\\Windows_and_Linux\\myvenv\\Lib\\site-packages\\openai\\types\\chat\\chat_completion_function_call_option_param.py',
   'PYMODULE'),
  ('openai.types.chat.chat_completion_function_message_param',
   'c:\\Users\\<USER>\\Desktop\\Mes_projets\\WritingToolsClean\\Windows_and_Linux\\myvenv\\Lib\\site-packages\\openai\\types\\chat\\chat_completion_function_message_param.py',
   'PYMODULE'),
  ('openai.types.chat.chat_completion_message',
   'c:\\Users\\<USER>\\Desktop\\Mes_projets\\WritingToolsClean\\Windows_and_Linux\\myvenv\\Lib\\site-packages\\openai\\types\\chat\\chat_completion_message.py',
   'PYMODULE'),
  ('openai.types.chat.chat_completion_message_param',
   'c:\\Users\\<USER>\\Desktop\\Mes_projets\\WritingToolsClean\\Windows_and_Linux\\myvenv\\Lib\\site-packages\\openai\\types\\chat\\chat_completion_message_param.py',
   'PYMODULE'),
  ('openai.types.chat.chat_completion_message_tool_call',
   'c:\\Users\\<USER>\\Desktop\\Mes_projets\\WritingToolsClean\\Windows_and_Linux\\myvenv\\Lib\\site-packages\\openai\\types\\chat\\chat_completion_message_tool_call.py',
   'PYMODULE'),
  ('openai.types.chat.chat_completion_message_tool_call_param',
   'c:\\Users\\<USER>\\Desktop\\Mes_projets\\WritingToolsClean\\Windows_and_Linux\\myvenv\\Lib\\site-packages\\openai\\types\\chat\\chat_completion_message_tool_call_param.py',
   'PYMODULE'),
  ('openai.types.chat.chat_completion_modality',
   'c:\\Users\\<USER>\\Desktop\\Mes_projets\\WritingToolsClean\\Windows_and_Linux\\myvenv\\Lib\\site-packages\\openai\\types\\chat\\chat_completion_modality.py',
   'PYMODULE'),
  ('openai.types.chat.chat_completion_named_tool_choice_param',
   'c:\\Users\\<USER>\\Desktop\\Mes_projets\\WritingToolsClean\\Windows_and_Linux\\myvenv\\Lib\\site-packages\\openai\\types\\chat\\chat_completion_named_tool_choice_param.py',
   'PYMODULE'),
  ('openai.types.chat.chat_completion_prediction_content_param',
   'c:\\Users\\<USER>\\Desktop\\Mes_projets\\WritingToolsClean\\Windows_and_Linux\\myvenv\\Lib\\site-packages\\openai\\types\\chat\\chat_completion_prediction_content_param.py',
   'PYMODULE'),
  ('openai.types.chat.chat_completion_reasoning_effort',
   'c:\\Users\\<USER>\\Desktop\\Mes_projets\\WritingToolsClean\\Windows_and_Linux\\myvenv\\Lib\\site-packages\\openai\\types\\chat\\chat_completion_reasoning_effort.py',
   'PYMODULE'),
  ('openai.types.chat.chat_completion_role',
   'c:\\Users\\<USER>\\Desktop\\Mes_projets\\WritingToolsClean\\Windows_and_Linux\\myvenv\\Lib\\site-packages\\openai\\types\\chat\\chat_completion_role.py',
   'PYMODULE'),
  ('openai.types.chat.chat_completion_store_message',
   'c:\\Users\\<USER>\\Desktop\\Mes_projets\\WritingToolsClean\\Windows_and_Linux\\myvenv\\Lib\\site-packages\\openai\\types\\chat\\chat_completion_store_message.py',
   'PYMODULE'),
  ('openai.types.chat.chat_completion_stream_options_param',
   'c:\\Users\\<USER>\\Desktop\\Mes_projets\\WritingToolsClean\\Windows_and_Linux\\myvenv\\Lib\\site-packages\\openai\\types\\chat\\chat_completion_stream_options_param.py',
   'PYMODULE'),
  ('openai.types.chat.chat_completion_system_message_param',
   'c:\\Users\\<USER>\\Desktop\\Mes_projets\\WritingToolsClean\\Windows_and_Linux\\myvenv\\Lib\\site-packages\\openai\\types\\chat\\chat_completion_system_message_param.py',
   'PYMODULE'),
  ('openai.types.chat.chat_completion_token_logprob',
   'c:\\Users\\<USER>\\Desktop\\Mes_projets\\WritingToolsClean\\Windows_and_Linux\\myvenv\\Lib\\site-packages\\openai\\types\\chat\\chat_completion_token_logprob.py',
   'PYMODULE'),
  ('openai.types.chat.chat_completion_tool',
   'c:\\Users\\<USER>\\Desktop\\Mes_projets\\WritingToolsClean\\Windows_and_Linux\\myvenv\\Lib\\site-packages\\openai\\types\\chat\\chat_completion_tool.py',
   'PYMODULE'),
  ('openai.types.chat.chat_completion_tool_choice_option_param',
   'c:\\Users\\<USER>\\Desktop\\Mes_projets\\WritingToolsClean\\Windows_and_Linux\\myvenv\\Lib\\site-packages\\openai\\types\\chat\\chat_completion_tool_choice_option_param.py',
   'PYMODULE'),
  ('openai.types.chat.chat_completion_tool_message_param',
   'c:\\Users\\<USER>\\Desktop\\Mes_projets\\WritingToolsClean\\Windows_and_Linux\\myvenv\\Lib\\site-packages\\openai\\types\\chat\\chat_completion_tool_message_param.py',
   'PYMODULE'),
  ('openai.types.chat.chat_completion_tool_param',
   'c:\\Users\\<USER>\\Desktop\\Mes_projets\\WritingToolsClean\\Windows_and_Linux\\myvenv\\Lib\\site-packages\\openai\\types\\chat\\chat_completion_tool_param.py',
   'PYMODULE'),
  ('openai.types.chat.chat_completion_user_message_param',
   'c:\\Users\\<USER>\\Desktop\\Mes_projets\\WritingToolsClean\\Windows_and_Linux\\myvenv\\Lib\\site-packages\\openai\\types\\chat\\chat_completion_user_message_param.py',
   'PYMODULE'),
  ('openai.types.chat.completion_create_params',
   'c:\\Users\\<USER>\\Desktop\\Mes_projets\\WritingToolsClean\\Windows_and_Linux\\myvenv\\Lib\\site-packages\\openai\\types\\chat\\completion_create_params.py',
   'PYMODULE'),
  ('openai.types.chat.completion_list_params',
   'c:\\Users\\<USER>\\Desktop\\Mes_projets\\WritingToolsClean\\Windows_and_Linux\\myvenv\\Lib\\site-packages\\openai\\types\\chat\\completion_list_params.py',
   'PYMODULE'),
  ('openai.types.chat.completion_update_params',
   'c:\\Users\\<USER>\\Desktop\\Mes_projets\\WritingToolsClean\\Windows_and_Linux\\myvenv\\Lib\\site-packages\\openai\\types\\chat\\completion_update_params.py',
   'PYMODULE'),
  ('openai.types.chat.completions',
   'c:\\Users\\<USER>\\Desktop\\Mes_projets\\WritingToolsClean\\Windows_and_Linux\\myvenv\\Lib\\site-packages\\openai\\types\\chat\\completions\\__init__.py',
   'PYMODULE'),
  ('openai.types.chat.completions.message_list_params',
   'c:\\Users\\<USER>\\Desktop\\Mes_projets\\WritingToolsClean\\Windows_and_Linux\\myvenv\\Lib\\site-packages\\openai\\types\\chat\\completions\\message_list_params.py',
   'PYMODULE'),
  ('openai.types.chat.parsed_chat_completion',
   'c:\\Users\\<USER>\\Desktop\\Mes_projets\\WritingToolsClean\\Windows_and_Linux\\myvenv\\Lib\\site-packages\\openai\\types\\chat\\parsed_chat_completion.py',
   'PYMODULE'),
  ('openai.types.chat.parsed_function_tool_call',
   'c:\\Users\\<USER>\\Desktop\\Mes_projets\\WritingToolsClean\\Windows_and_Linux\\myvenv\\Lib\\site-packages\\openai\\types\\chat\\parsed_function_tool_call.py',
   'PYMODULE'),
  ('openai.types.chat_model',
   'c:\\Users\\<USER>\\Desktop\\Mes_projets\\WritingToolsClean\\Windows_and_Linux\\myvenv\\Lib\\site-packages\\openai\\types\\chat_model.py',
   'PYMODULE'),
  ('openai.types.completion',
   'c:\\Users\\<USER>\\Desktop\\Mes_projets\\WritingToolsClean\\Windows_and_Linux\\myvenv\\Lib\\site-packages\\openai\\types\\completion.py',
   'PYMODULE'),
  ('openai.types.completion_choice',
   'c:\\Users\\<USER>\\Desktop\\Mes_projets\\WritingToolsClean\\Windows_and_Linux\\myvenv\\Lib\\site-packages\\openai\\types\\completion_choice.py',
   'PYMODULE'),
  ('openai.types.completion_create_params',
   'c:\\Users\\<USER>\\Desktop\\Mes_projets\\WritingToolsClean\\Windows_and_Linux\\myvenv\\Lib\\site-packages\\openai\\types\\completion_create_params.py',
   'PYMODULE'),
  ('openai.types.completion_usage',
   'c:\\Users\\<USER>\\Desktop\\Mes_projets\\WritingToolsClean\\Windows_and_Linux\\myvenv\\Lib\\site-packages\\openai\\types\\completion_usage.py',
   'PYMODULE'),
  ('openai.types.container_create_params',
   'c:\\Users\\<USER>\\Desktop\\Mes_projets\\WritingToolsClean\\Windows_and_Linux\\myvenv\\Lib\\site-packages\\openai\\types\\container_create_params.py',
   'PYMODULE'),
  ('openai.types.container_create_response',
   'c:\\Users\\<USER>\\Desktop\\Mes_projets\\WritingToolsClean\\Windows_and_Linux\\myvenv\\Lib\\site-packages\\openai\\types\\container_create_response.py',
   'PYMODULE'),
  ('openai.types.container_list_params',
   'c:\\Users\\<USER>\\Desktop\\Mes_projets\\WritingToolsClean\\Windows_and_Linux\\myvenv\\Lib\\site-packages\\openai\\types\\container_list_params.py',
   'PYMODULE'),
  ('openai.types.container_list_response',
   'c:\\Users\\<USER>\\Desktop\\Mes_projets\\WritingToolsClean\\Windows_and_Linux\\myvenv\\Lib\\site-packages\\openai\\types\\container_list_response.py',
   'PYMODULE'),
  ('openai.types.container_retrieve_response',
   'c:\\Users\\<USER>\\Desktop\\Mes_projets\\WritingToolsClean\\Windows_and_Linux\\myvenv\\Lib\\site-packages\\openai\\types\\container_retrieve_response.py',
   'PYMODULE'),
  ('openai.types.containers',
   'c:\\Users\\<USER>\\Desktop\\Mes_projets\\WritingToolsClean\\Windows_and_Linux\\myvenv\\Lib\\site-packages\\openai\\types\\containers\\__init__.py',
   'PYMODULE'),
  ('openai.types.containers.file_create_params',
   'c:\\Users\\<USER>\\Desktop\\Mes_projets\\WritingToolsClean\\Windows_and_Linux\\myvenv\\Lib\\site-packages\\openai\\types\\containers\\file_create_params.py',
   'PYMODULE'),
  ('openai.types.containers.file_create_response',
   'c:\\Users\\<USER>\\Desktop\\Mes_projets\\WritingToolsClean\\Windows_and_Linux\\myvenv\\Lib\\site-packages\\openai\\types\\containers\\file_create_response.py',
   'PYMODULE'),
  ('openai.types.containers.file_list_params',
   'c:\\Users\\<USER>\\Desktop\\Mes_projets\\WritingToolsClean\\Windows_and_Linux\\myvenv\\Lib\\site-packages\\openai\\types\\containers\\file_list_params.py',
   'PYMODULE'),
  ('openai.types.containers.file_list_response',
   'c:\\Users\\<USER>\\Desktop\\Mes_projets\\WritingToolsClean\\Windows_and_Linux\\myvenv\\Lib\\site-packages\\openai\\types\\containers\\file_list_response.py',
   'PYMODULE'),
  ('openai.types.containers.file_retrieve_response',
   'c:\\Users\\<USER>\\Desktop\\Mes_projets\\WritingToolsClean\\Windows_and_Linux\\myvenv\\Lib\\site-packages\\openai\\types\\containers\\file_retrieve_response.py',
   'PYMODULE'),
  ('openai.types.create_embedding_response',
   'c:\\Users\\<USER>\\Desktop\\Mes_projets\\WritingToolsClean\\Windows_and_Linux\\myvenv\\Lib\\site-packages\\openai\\types\\create_embedding_response.py',
   'PYMODULE'),
  ('openai.types.embedding',
   'c:\\Users\\<USER>\\Desktop\\Mes_projets\\WritingToolsClean\\Windows_and_Linux\\myvenv\\Lib\\site-packages\\openai\\types\\embedding.py',
   'PYMODULE'),
  ('openai.types.embedding_create_params',
   'c:\\Users\\<USER>\\Desktop\\Mes_projets\\WritingToolsClean\\Windows_and_Linux\\myvenv\\Lib\\site-packages\\openai\\types\\embedding_create_params.py',
   'PYMODULE'),
  ('openai.types.embedding_model',
   'c:\\Users\\<USER>\\Desktop\\Mes_projets\\WritingToolsClean\\Windows_and_Linux\\myvenv\\Lib\\site-packages\\openai\\types\\embedding_model.py',
   'PYMODULE'),
  ('openai.types.eval_create_params',
   'c:\\Users\\<USER>\\Desktop\\Mes_projets\\WritingToolsClean\\Windows_and_Linux\\myvenv\\Lib\\site-packages\\openai\\types\\eval_create_params.py',
   'PYMODULE'),
  ('openai.types.eval_create_response',
   'c:\\Users\\<USER>\\Desktop\\Mes_projets\\WritingToolsClean\\Windows_and_Linux\\myvenv\\Lib\\site-packages\\openai\\types\\eval_create_response.py',
   'PYMODULE'),
  ('openai.types.eval_custom_data_source_config',
   'c:\\Users\\<USER>\\Desktop\\Mes_projets\\WritingToolsClean\\Windows_and_Linux\\myvenv\\Lib\\site-packages\\openai\\types\\eval_custom_data_source_config.py',
   'PYMODULE'),
  ('openai.types.eval_delete_response',
   'c:\\Users\\<USER>\\Desktop\\Mes_projets\\WritingToolsClean\\Windows_and_Linux\\myvenv\\Lib\\site-packages\\openai\\types\\eval_delete_response.py',
   'PYMODULE'),
  ('openai.types.eval_list_params',
   'c:\\Users\\<USER>\\Desktop\\Mes_projets\\WritingToolsClean\\Windows_and_Linux\\myvenv\\Lib\\site-packages\\openai\\types\\eval_list_params.py',
   'PYMODULE'),
  ('openai.types.eval_list_response',
   'c:\\Users\\<USER>\\Desktop\\Mes_projets\\WritingToolsClean\\Windows_and_Linux\\myvenv\\Lib\\site-packages\\openai\\types\\eval_list_response.py',
   'PYMODULE'),
  ('openai.types.eval_retrieve_response',
   'c:\\Users\\<USER>\\Desktop\\Mes_projets\\WritingToolsClean\\Windows_and_Linux\\myvenv\\Lib\\site-packages\\openai\\types\\eval_retrieve_response.py',
   'PYMODULE'),
  ('openai.types.eval_stored_completions_data_source_config',
   'c:\\Users\\<USER>\\Desktop\\Mes_projets\\WritingToolsClean\\Windows_and_Linux\\myvenv\\Lib\\site-packages\\openai\\types\\eval_stored_completions_data_source_config.py',
   'PYMODULE'),
  ('openai.types.eval_update_params',
   'c:\\Users\\<USER>\\Desktop\\Mes_projets\\WritingToolsClean\\Windows_and_Linux\\myvenv\\Lib\\site-packages\\openai\\types\\eval_update_params.py',
   'PYMODULE'),
  ('openai.types.eval_update_response',
   'c:\\Users\\<USER>\\Desktop\\Mes_projets\\WritingToolsClean\\Windows_and_Linux\\myvenv\\Lib\\site-packages\\openai\\types\\eval_update_response.py',
   'PYMODULE'),
  ('openai.types.evals',
   'c:\\Users\\<USER>\\Desktop\\Mes_projets\\WritingToolsClean\\Windows_and_Linux\\myvenv\\Lib\\site-packages\\openai\\types\\evals\\__init__.py',
   'PYMODULE'),
  ('openai.types.evals.create_eval_completions_run_data_source',
   'c:\\Users\\<USER>\\Desktop\\Mes_projets\\WritingToolsClean\\Windows_and_Linux\\myvenv\\Lib\\site-packages\\openai\\types\\evals\\create_eval_completions_run_data_source.py',
   'PYMODULE'),
  ('openai.types.evals.create_eval_completions_run_data_source_param',
   'c:\\Users\\<USER>\\Desktop\\Mes_projets\\WritingToolsClean\\Windows_and_Linux\\myvenv\\Lib\\site-packages\\openai\\types\\evals\\create_eval_completions_run_data_source_param.py',
   'PYMODULE'),
  ('openai.types.evals.create_eval_jsonl_run_data_source',
   'c:\\Users\\<USER>\\Desktop\\Mes_projets\\WritingToolsClean\\Windows_and_Linux\\myvenv\\Lib\\site-packages\\openai\\types\\evals\\create_eval_jsonl_run_data_source.py',
   'PYMODULE'),
  ('openai.types.evals.create_eval_jsonl_run_data_source_param',
   'c:\\Users\\<USER>\\Desktop\\Mes_projets\\WritingToolsClean\\Windows_and_Linux\\myvenv\\Lib\\site-packages\\openai\\types\\evals\\create_eval_jsonl_run_data_source_param.py',
   'PYMODULE'),
  ('openai.types.evals.eval_api_error',
   'c:\\Users\\<USER>\\Desktop\\Mes_projets\\WritingToolsClean\\Windows_and_Linux\\myvenv\\Lib\\site-packages\\openai\\types\\evals\\eval_api_error.py',
   'PYMODULE'),
  ('openai.types.evals.run_cancel_response',
   'c:\\Users\\<USER>\\Desktop\\Mes_projets\\WritingToolsClean\\Windows_and_Linux\\myvenv\\Lib\\site-packages\\openai\\types\\evals\\run_cancel_response.py',
   'PYMODULE'),
  ('openai.types.evals.run_create_params',
   'c:\\Users\\<USER>\\Desktop\\Mes_projets\\WritingToolsClean\\Windows_and_Linux\\myvenv\\Lib\\site-packages\\openai\\types\\evals\\run_create_params.py',
   'PYMODULE'),
  ('openai.types.evals.run_create_response',
   'c:\\Users\\<USER>\\Desktop\\Mes_projets\\WritingToolsClean\\Windows_and_Linux\\myvenv\\Lib\\site-packages\\openai\\types\\evals\\run_create_response.py',
   'PYMODULE'),
  ('openai.types.evals.run_delete_response',
   'c:\\Users\\<USER>\\Desktop\\Mes_projets\\WritingToolsClean\\Windows_and_Linux\\myvenv\\Lib\\site-packages\\openai\\types\\evals\\run_delete_response.py',
   'PYMODULE'),
  ('openai.types.evals.run_list_params',
   'c:\\Users\\<USER>\\Desktop\\Mes_projets\\WritingToolsClean\\Windows_and_Linux\\myvenv\\Lib\\site-packages\\openai\\types\\evals\\run_list_params.py',
   'PYMODULE'),
  ('openai.types.evals.run_list_response',
   'c:\\Users\\<USER>\\Desktop\\Mes_projets\\WritingToolsClean\\Windows_and_Linux\\myvenv\\Lib\\site-packages\\openai\\types\\evals\\run_list_response.py',
   'PYMODULE'),
  ('openai.types.evals.run_retrieve_response',
   'c:\\Users\\<USER>\\Desktop\\Mes_projets\\WritingToolsClean\\Windows_and_Linux\\myvenv\\Lib\\site-packages\\openai\\types\\evals\\run_retrieve_response.py',
   'PYMODULE'),
  ('openai.types.evals.runs',
   'c:\\Users\\<USER>\\Desktop\\Mes_projets\\WritingToolsClean\\Windows_and_Linux\\myvenv\\Lib\\site-packages\\openai\\types\\evals\\runs\\__init__.py',
   'PYMODULE'),
  ('openai.types.evals.runs.output_item_list_params',
   'c:\\Users\\<USER>\\Desktop\\Mes_projets\\WritingToolsClean\\Windows_and_Linux\\myvenv\\Lib\\site-packages\\openai\\types\\evals\\runs\\output_item_list_params.py',
   'PYMODULE'),
  ('openai.types.evals.runs.output_item_list_response',
   'c:\\Users\\<USER>\\Desktop\\Mes_projets\\WritingToolsClean\\Windows_and_Linux\\myvenv\\Lib\\site-packages\\openai\\types\\evals\\runs\\output_item_list_response.py',
   'PYMODULE'),
  ('openai.types.evals.runs.output_item_retrieve_response',
   'c:\\Users\\<USER>\\Desktop\\Mes_projets\\WritingToolsClean\\Windows_and_Linux\\myvenv\\Lib\\site-packages\\openai\\types\\evals\\runs\\output_item_retrieve_response.py',
   'PYMODULE'),
  ('openai.types.file_chunking_strategy',
   'c:\\Users\\<USER>\\Desktop\\Mes_projets\\WritingToolsClean\\Windows_and_Linux\\myvenv\\Lib\\site-packages\\openai\\types\\file_chunking_strategy.py',
   'PYMODULE'),
  ('openai.types.file_chunking_strategy_param',
   'c:\\Users\\<USER>\\Desktop\\Mes_projets\\WritingToolsClean\\Windows_and_Linux\\myvenv\\Lib\\site-packages\\openai\\types\\file_chunking_strategy_param.py',
   'PYMODULE'),
  ('openai.types.file_content',
   'c:\\Users\\<USER>\\Desktop\\Mes_projets\\WritingToolsClean\\Windows_and_Linux\\myvenv\\Lib\\site-packages\\openai\\types\\file_content.py',
   'PYMODULE'),
  ('openai.types.file_create_params',
   'c:\\Users\\<USER>\\Desktop\\Mes_projets\\WritingToolsClean\\Windows_and_Linux\\myvenv\\Lib\\site-packages\\openai\\types\\file_create_params.py',
   'PYMODULE'),
  ('openai.types.file_deleted',
   'c:\\Users\\<USER>\\Desktop\\Mes_projets\\WritingToolsClean\\Windows_and_Linux\\myvenv\\Lib\\site-packages\\openai\\types\\file_deleted.py',
   'PYMODULE'),
  ('openai.types.file_list_params',
   'c:\\Users\\<USER>\\Desktop\\Mes_projets\\WritingToolsClean\\Windows_and_Linux\\myvenv\\Lib\\site-packages\\openai\\types\\file_list_params.py',
   'PYMODULE'),
  ('openai.types.file_object',
   'c:\\Users\\<USER>\\Desktop\\Mes_projets\\WritingToolsClean\\Windows_and_Linux\\myvenv\\Lib\\site-packages\\openai\\types\\file_object.py',
   'PYMODULE'),
  ('openai.types.file_purpose',
   'c:\\Users\\<USER>\\Desktop\\Mes_projets\\WritingToolsClean\\Windows_and_Linux\\myvenv\\Lib\\site-packages\\openai\\types\\file_purpose.py',
   'PYMODULE'),
  ('openai.types.fine_tuning',
   'c:\\Users\\<USER>\\Desktop\\Mes_projets\\WritingToolsClean\\Windows_and_Linux\\myvenv\\Lib\\site-packages\\openai\\types\\fine_tuning\\__init__.py',
   'PYMODULE'),
  ('openai.types.fine_tuning.alpha',
   'c:\\Users\\<USER>\\Desktop\\Mes_projets\\WritingToolsClean\\Windows_and_Linux\\myvenv\\Lib\\site-packages\\openai\\types\\fine_tuning\\alpha\\__init__.py',
   'PYMODULE'),
  ('openai.types.fine_tuning.alpha.grader_run_params',
   'c:\\Users\\<USER>\\Desktop\\Mes_projets\\WritingToolsClean\\Windows_and_Linux\\myvenv\\Lib\\site-packages\\openai\\types\\fine_tuning\\alpha\\grader_run_params.py',
   'PYMODULE'),
  ('openai.types.fine_tuning.alpha.grader_run_response',
   'c:\\Users\\<USER>\\Desktop\\Mes_projets\\WritingToolsClean\\Windows_and_Linux\\myvenv\\Lib\\site-packages\\openai\\types\\fine_tuning\\alpha\\grader_run_response.py',
   'PYMODULE'),
  ('openai.types.fine_tuning.alpha.grader_validate_params',
   'c:\\Users\\<USER>\\Desktop\\Mes_projets\\WritingToolsClean\\Windows_and_Linux\\myvenv\\Lib\\site-packages\\openai\\types\\fine_tuning\\alpha\\grader_validate_params.py',
   'PYMODULE'),
  ('openai.types.fine_tuning.alpha.grader_validate_response',
   'c:\\Users\\<USER>\\Desktop\\Mes_projets\\WritingToolsClean\\Windows_and_Linux\\myvenv\\Lib\\site-packages\\openai\\types\\fine_tuning\\alpha\\grader_validate_response.py',
   'PYMODULE'),
  ('openai.types.fine_tuning.checkpoints',
   'c:\\Users\\<USER>\\Desktop\\Mes_projets\\WritingToolsClean\\Windows_and_Linux\\myvenv\\Lib\\site-packages\\openai\\types\\fine_tuning\\checkpoints\\__init__.py',
   'PYMODULE'),
  ('openai.types.fine_tuning.checkpoints.permission_create_params',
   'c:\\Users\\<USER>\\Desktop\\Mes_projets\\WritingToolsClean\\Windows_and_Linux\\myvenv\\Lib\\site-packages\\openai\\types\\fine_tuning\\checkpoints\\permission_create_params.py',
   'PYMODULE'),
  ('openai.types.fine_tuning.checkpoints.permission_create_response',
   'c:\\Users\\<USER>\\Desktop\\Mes_projets\\WritingToolsClean\\Windows_and_Linux\\myvenv\\Lib\\site-packages\\openai\\types\\fine_tuning\\checkpoints\\permission_create_response.py',
   'PYMODULE'),
  ('openai.types.fine_tuning.checkpoints.permission_delete_response',
   'c:\\Users\\<USER>\\Desktop\\Mes_projets\\WritingToolsClean\\Windows_and_Linux\\myvenv\\Lib\\site-packages\\openai\\types\\fine_tuning\\checkpoints\\permission_delete_response.py',
   'PYMODULE'),
  ('openai.types.fine_tuning.checkpoints.permission_retrieve_params',
   'c:\\Users\\<USER>\\Desktop\\Mes_projets\\WritingToolsClean\\Windows_and_Linux\\myvenv\\Lib\\site-packages\\openai\\types\\fine_tuning\\checkpoints\\permission_retrieve_params.py',
   'PYMODULE'),
  ('openai.types.fine_tuning.checkpoints.permission_retrieve_response',
   'c:\\Users\\<USER>\\Desktop\\Mes_projets\\WritingToolsClean\\Windows_and_Linux\\myvenv\\Lib\\site-packages\\openai\\types\\fine_tuning\\checkpoints\\permission_retrieve_response.py',
   'PYMODULE'),
  ('openai.types.fine_tuning.dpo_hyperparameters',
   'c:\\Users\\<USER>\\Desktop\\Mes_projets\\WritingToolsClean\\Windows_and_Linux\\myvenv\\Lib\\site-packages\\openai\\types\\fine_tuning\\dpo_hyperparameters.py',
   'PYMODULE'),
  ('openai.types.fine_tuning.dpo_hyperparameters_param',
   'c:\\Users\\<USER>\\Desktop\\Mes_projets\\WritingToolsClean\\Windows_and_Linux\\myvenv\\Lib\\site-packages\\openai\\types\\fine_tuning\\dpo_hyperparameters_param.py',
   'PYMODULE'),
  ('openai.types.fine_tuning.dpo_method',
   'c:\\Users\\<USER>\\Desktop\\Mes_projets\\WritingToolsClean\\Windows_and_Linux\\myvenv\\Lib\\site-packages\\openai\\types\\fine_tuning\\dpo_method.py',
   'PYMODULE'),
  ('openai.types.fine_tuning.dpo_method_param',
   'c:\\Users\\<USER>\\Desktop\\Mes_projets\\WritingToolsClean\\Windows_and_Linux\\myvenv\\Lib\\site-packages\\openai\\types\\fine_tuning\\dpo_method_param.py',
   'PYMODULE'),
  ('openai.types.fine_tuning.fine_tuning_job',
   'c:\\Users\\<USER>\\Desktop\\Mes_projets\\WritingToolsClean\\Windows_and_Linux\\myvenv\\Lib\\site-packages\\openai\\types\\fine_tuning\\fine_tuning_job.py',
   'PYMODULE'),
  ('openai.types.fine_tuning.fine_tuning_job_event',
   'c:\\Users\\<USER>\\Desktop\\Mes_projets\\WritingToolsClean\\Windows_and_Linux\\myvenv\\Lib\\site-packages\\openai\\types\\fine_tuning\\fine_tuning_job_event.py',
   'PYMODULE'),
  ('openai.types.fine_tuning.fine_tuning_job_integration',
   'c:\\Users\\<USER>\\Desktop\\Mes_projets\\WritingToolsClean\\Windows_and_Linux\\myvenv\\Lib\\site-packages\\openai\\types\\fine_tuning\\fine_tuning_job_integration.py',
   'PYMODULE'),
  ('openai.types.fine_tuning.fine_tuning_job_wandb_integration',
   'c:\\Users\\<USER>\\Desktop\\Mes_projets\\WritingToolsClean\\Windows_and_Linux\\myvenv\\Lib\\site-packages\\openai\\types\\fine_tuning\\fine_tuning_job_wandb_integration.py',
   'PYMODULE'),
  ('openai.types.fine_tuning.fine_tuning_job_wandb_integration_object',
   'c:\\Users\\<USER>\\Desktop\\Mes_projets\\WritingToolsClean\\Windows_and_Linux\\myvenv\\Lib\\site-packages\\openai\\types\\fine_tuning\\fine_tuning_job_wandb_integration_object.py',
   'PYMODULE'),
  ('openai.types.fine_tuning.job_create_params',
   'c:\\Users\\<USER>\\Desktop\\Mes_projets\\WritingToolsClean\\Windows_and_Linux\\myvenv\\Lib\\site-packages\\openai\\types\\fine_tuning\\job_create_params.py',
   'PYMODULE'),
  ('openai.types.fine_tuning.job_list_events_params',
   'c:\\Users\\<USER>\\Desktop\\Mes_projets\\WritingToolsClean\\Windows_and_Linux\\myvenv\\Lib\\site-packages\\openai\\types\\fine_tuning\\job_list_events_params.py',
   'PYMODULE'),
  ('openai.types.fine_tuning.job_list_params',
   'c:\\Users\\<USER>\\Desktop\\Mes_projets\\WritingToolsClean\\Windows_and_Linux\\myvenv\\Lib\\site-packages\\openai\\types\\fine_tuning\\job_list_params.py',
   'PYMODULE'),
  ('openai.types.fine_tuning.jobs',
   'c:\\Users\\<USER>\\Desktop\\Mes_projets\\WritingToolsClean\\Windows_and_Linux\\myvenv\\Lib\\site-packages\\openai\\types\\fine_tuning\\jobs\\__init__.py',
   'PYMODULE'),
  ('openai.types.fine_tuning.jobs.checkpoint_list_params',
   'c:\\Users\\<USER>\\Desktop\\Mes_projets\\WritingToolsClean\\Windows_and_Linux\\myvenv\\Lib\\site-packages\\openai\\types\\fine_tuning\\jobs\\checkpoint_list_params.py',
   'PYMODULE'),
  ('openai.types.fine_tuning.jobs.fine_tuning_job_checkpoint',
   'c:\\Users\\<USER>\\Desktop\\Mes_projets\\WritingToolsClean\\Windows_and_Linux\\myvenv\\Lib\\site-packages\\openai\\types\\fine_tuning\\jobs\\fine_tuning_job_checkpoint.py',
   'PYMODULE'),
  ('openai.types.fine_tuning.reinforcement_hyperparameters',
   'c:\\Users\\<USER>\\Desktop\\Mes_projets\\WritingToolsClean\\Windows_and_Linux\\myvenv\\Lib\\site-packages\\openai\\types\\fine_tuning\\reinforcement_hyperparameters.py',
   'PYMODULE'),
  ('openai.types.fine_tuning.reinforcement_hyperparameters_param',
   'c:\\Users\\<USER>\\Desktop\\Mes_projets\\WritingToolsClean\\Windows_and_Linux\\myvenv\\Lib\\site-packages\\openai\\types\\fine_tuning\\reinforcement_hyperparameters_param.py',
   'PYMODULE'),
  ('openai.types.fine_tuning.reinforcement_method',
   'c:\\Users\\<USER>\\Desktop\\Mes_projets\\WritingToolsClean\\Windows_and_Linux\\myvenv\\Lib\\site-packages\\openai\\types\\fine_tuning\\reinforcement_method.py',
   'PYMODULE'),
  ('openai.types.fine_tuning.reinforcement_method_param',
   'c:\\Users\\<USER>\\Desktop\\Mes_projets\\WritingToolsClean\\Windows_and_Linux\\myvenv\\Lib\\site-packages\\openai\\types\\fine_tuning\\reinforcement_method_param.py',
   'PYMODULE'),
  ('openai.types.fine_tuning.supervised_hyperparameters',
   'c:\\Users\\<USER>\\Desktop\\Mes_projets\\WritingToolsClean\\Windows_and_Linux\\myvenv\\Lib\\site-packages\\openai\\types\\fine_tuning\\supervised_hyperparameters.py',
   'PYMODULE'),
  ('openai.types.fine_tuning.supervised_hyperparameters_param',
   'c:\\Users\\<USER>\\Desktop\\Mes_projets\\WritingToolsClean\\Windows_and_Linux\\myvenv\\Lib\\site-packages\\openai\\types\\fine_tuning\\supervised_hyperparameters_param.py',
   'PYMODULE'),
  ('openai.types.fine_tuning.supervised_method',
   'c:\\Users\\<USER>\\Desktop\\Mes_projets\\WritingToolsClean\\Windows_and_Linux\\myvenv\\Lib\\site-packages\\openai\\types\\fine_tuning\\supervised_method.py',
   'PYMODULE'),
  ('openai.types.fine_tuning.supervised_method_param',
   'c:\\Users\\<USER>\\Desktop\\Mes_projets\\WritingToolsClean\\Windows_and_Linux\\myvenv\\Lib\\site-packages\\openai\\types\\fine_tuning\\supervised_method_param.py',
   'PYMODULE'),
  ('openai.types.graders',
   'c:\\Users\\<USER>\\Desktop\\Mes_projets\\WritingToolsClean\\Windows_and_Linux\\myvenv\\Lib\\site-packages\\openai\\types\\graders\\__init__.py',
   'PYMODULE'),
  ('openai.types.graders.label_model_grader',
   'c:\\Users\\<USER>\\Desktop\\Mes_projets\\WritingToolsClean\\Windows_and_Linux\\myvenv\\Lib\\site-packages\\openai\\types\\graders\\label_model_grader.py',
   'PYMODULE'),
  ('openai.types.graders.label_model_grader_param',
   'c:\\Users\\<USER>\\Desktop\\Mes_projets\\WritingToolsClean\\Windows_and_Linux\\myvenv\\Lib\\site-packages\\openai\\types\\graders\\label_model_grader_param.py',
   'PYMODULE'),
  ('openai.types.graders.multi_grader',
   'c:\\Users\\<USER>\\Desktop\\Mes_projets\\WritingToolsClean\\Windows_and_Linux\\myvenv\\Lib\\site-packages\\openai\\types\\graders\\multi_grader.py',
   'PYMODULE'),
  ('openai.types.graders.multi_grader_param',
   'c:\\Users\\<USER>\\Desktop\\Mes_projets\\WritingToolsClean\\Windows_and_Linux\\myvenv\\Lib\\site-packages\\openai\\types\\graders\\multi_grader_param.py',
   'PYMODULE'),
  ('openai.types.graders.python_grader',
   'c:\\Users\\<USER>\\Desktop\\Mes_projets\\WritingToolsClean\\Windows_and_Linux\\myvenv\\Lib\\site-packages\\openai\\types\\graders\\python_grader.py',
   'PYMODULE'),
  ('openai.types.graders.python_grader_param',
   'c:\\Users\\<USER>\\Desktop\\Mes_projets\\WritingToolsClean\\Windows_and_Linux\\myvenv\\Lib\\site-packages\\openai\\types\\graders\\python_grader_param.py',
   'PYMODULE'),
  ('openai.types.graders.score_model_grader',
   'c:\\Users\\<USER>\\Desktop\\Mes_projets\\WritingToolsClean\\Windows_and_Linux\\myvenv\\Lib\\site-packages\\openai\\types\\graders\\score_model_grader.py',
   'PYMODULE'),
  ('openai.types.graders.score_model_grader_param',
   'c:\\Users\\<USER>\\Desktop\\Mes_projets\\WritingToolsClean\\Windows_and_Linux\\myvenv\\Lib\\site-packages\\openai\\types\\graders\\score_model_grader_param.py',
   'PYMODULE'),
  ('openai.types.graders.string_check_grader',
   'c:\\Users\\<USER>\\Desktop\\Mes_projets\\WritingToolsClean\\Windows_and_Linux\\myvenv\\Lib\\site-packages\\openai\\types\\graders\\string_check_grader.py',
   'PYMODULE'),
  ('openai.types.graders.string_check_grader_param',
   'c:\\Users\\<USER>\\Desktop\\Mes_projets\\WritingToolsClean\\Windows_and_Linux\\myvenv\\Lib\\site-packages\\openai\\types\\graders\\string_check_grader_param.py',
   'PYMODULE'),
  ('openai.types.graders.text_similarity_grader',
   'c:\\Users\\<USER>\\Desktop\\Mes_projets\\WritingToolsClean\\Windows_and_Linux\\myvenv\\Lib\\site-packages\\openai\\types\\graders\\text_similarity_grader.py',
   'PYMODULE'),
  ('openai.types.graders.text_similarity_grader_param',
   'c:\\Users\\<USER>\\Desktop\\Mes_projets\\WritingToolsClean\\Windows_and_Linux\\myvenv\\Lib\\site-packages\\openai\\types\\graders\\text_similarity_grader_param.py',
   'PYMODULE'),
  ('openai.types.image',
   'c:\\Users\\<USER>\\Desktop\\Mes_projets\\WritingToolsClean\\Windows_and_Linux\\myvenv\\Lib\\site-packages\\openai\\types\\image.py',
   'PYMODULE'),
  ('openai.types.image_create_variation_params',
   'c:\\Users\\<USER>\\Desktop\\Mes_projets\\WritingToolsClean\\Windows_and_Linux\\myvenv\\Lib\\site-packages\\openai\\types\\image_create_variation_params.py',
   'PYMODULE'),
  ('openai.types.image_edit_completed_event',
   'c:\\Users\\<USER>\\Desktop\\Mes_projets\\WritingToolsClean\\Windows_and_Linux\\myvenv\\Lib\\site-packages\\openai\\types\\image_edit_completed_event.py',
   'PYMODULE'),
  ('openai.types.image_edit_params',
   'c:\\Users\\<USER>\\Desktop\\Mes_projets\\WritingToolsClean\\Windows_and_Linux\\myvenv\\Lib\\site-packages\\openai\\types\\image_edit_params.py',
   'PYMODULE'),
  ('openai.types.image_edit_partial_image_event',
   'c:\\Users\\<USER>\\Desktop\\Mes_projets\\WritingToolsClean\\Windows_and_Linux\\myvenv\\Lib\\site-packages\\openai\\types\\image_edit_partial_image_event.py',
   'PYMODULE'),
  ('openai.types.image_edit_stream_event',
   'c:\\Users\\<USER>\\Desktop\\Mes_projets\\WritingToolsClean\\Windows_and_Linux\\myvenv\\Lib\\site-packages\\openai\\types\\image_edit_stream_event.py',
   'PYMODULE'),
  ('openai.types.image_gen_completed_event',
   'c:\\Users\\<USER>\\Desktop\\Mes_projets\\WritingToolsClean\\Windows_and_Linux\\myvenv\\Lib\\site-packages\\openai\\types\\image_gen_completed_event.py',
   'PYMODULE'),
  ('openai.types.image_gen_partial_image_event',
   'c:\\Users\\<USER>\\Desktop\\Mes_projets\\WritingToolsClean\\Windows_and_Linux\\myvenv\\Lib\\site-packages\\openai\\types\\image_gen_partial_image_event.py',
   'PYMODULE'),
  ('openai.types.image_gen_stream_event',
   'c:\\Users\\<USER>\\Desktop\\Mes_projets\\WritingToolsClean\\Windows_and_Linux\\myvenv\\Lib\\site-packages\\openai\\types\\image_gen_stream_event.py',
   'PYMODULE'),
  ('openai.types.image_generate_params',
   'c:\\Users\\<USER>\\Desktop\\Mes_projets\\WritingToolsClean\\Windows_and_Linux\\myvenv\\Lib\\site-packages\\openai\\types\\image_generate_params.py',
   'PYMODULE'),
  ('openai.types.image_model',
   'c:\\Users\\<USER>\\Desktop\\Mes_projets\\WritingToolsClean\\Windows_and_Linux\\myvenv\\Lib\\site-packages\\openai\\types\\image_model.py',
   'PYMODULE'),
  ('openai.types.images_response',
   'c:\\Users\\<USER>\\Desktop\\Mes_projets\\WritingToolsClean\\Windows_and_Linux\\myvenv\\Lib\\site-packages\\openai\\types\\images_response.py',
   'PYMODULE'),
  ('openai.types.model',
   'c:\\Users\\<USER>\\Desktop\\Mes_projets\\WritingToolsClean\\Windows_and_Linux\\myvenv\\Lib\\site-packages\\openai\\types\\model.py',
   'PYMODULE'),
  ('openai.types.model_deleted',
   'c:\\Users\\<USER>\\Desktop\\Mes_projets\\WritingToolsClean\\Windows_and_Linux\\myvenv\\Lib\\site-packages\\openai\\types\\model_deleted.py',
   'PYMODULE'),
  ('openai.types.moderation',
   'c:\\Users\\<USER>\\Desktop\\Mes_projets\\WritingToolsClean\\Windows_and_Linux\\myvenv\\Lib\\site-packages\\openai\\types\\moderation.py',
   'PYMODULE'),
  ('openai.types.moderation_create_params',
   'c:\\Users\\<USER>\\Desktop\\Mes_projets\\WritingToolsClean\\Windows_and_Linux\\myvenv\\Lib\\site-packages\\openai\\types\\moderation_create_params.py',
   'PYMODULE'),
  ('openai.types.moderation_create_response',
   'c:\\Users\\<USER>\\Desktop\\Mes_projets\\WritingToolsClean\\Windows_and_Linux\\myvenv\\Lib\\site-packages\\openai\\types\\moderation_create_response.py',
   'PYMODULE'),
  ('openai.types.moderation_image_url_input_param',
   'c:\\Users\\<USER>\\Desktop\\Mes_projets\\WritingToolsClean\\Windows_and_Linux\\myvenv\\Lib\\site-packages\\openai\\types\\moderation_image_url_input_param.py',
   'PYMODULE'),
  ('openai.types.moderation_model',
   'c:\\Users\\<USER>\\Desktop\\Mes_projets\\WritingToolsClean\\Windows_and_Linux\\myvenv\\Lib\\site-packages\\openai\\types\\moderation_model.py',
   'PYMODULE'),
  ('openai.types.moderation_multi_modal_input_param',
   'c:\\Users\\<USER>\\Desktop\\Mes_projets\\WritingToolsClean\\Windows_and_Linux\\myvenv\\Lib\\site-packages\\openai\\types\\moderation_multi_modal_input_param.py',
   'PYMODULE'),
  ('openai.types.moderation_text_input_param',
   'c:\\Users\\<USER>\\Desktop\\Mes_projets\\WritingToolsClean\\Windows_and_Linux\\myvenv\\Lib\\site-packages\\openai\\types\\moderation_text_input_param.py',
   'PYMODULE'),
  ('openai.types.other_file_chunking_strategy_object',
   'c:\\Users\\<USER>\\Desktop\\Mes_projets\\WritingToolsClean\\Windows_and_Linux\\myvenv\\Lib\\site-packages\\openai\\types\\other_file_chunking_strategy_object.py',
   'PYMODULE'),
  ('openai.types.responses',
   'c:\\Users\\<USER>\\Desktop\\Mes_projets\\WritingToolsClean\\Windows_and_Linux\\myvenv\\Lib\\site-packages\\openai\\types\\responses\\__init__.py',
   'PYMODULE'),
  ('openai.types.responses.computer_tool',
   'c:\\Users\\<USER>\\Desktop\\Mes_projets\\WritingToolsClean\\Windows_and_Linux\\myvenv\\Lib\\site-packages\\openai\\types\\responses\\computer_tool.py',
   'PYMODULE'),
  ('openai.types.responses.computer_tool_param',
   'c:\\Users\\<USER>\\Desktop\\Mes_projets\\WritingToolsClean\\Windows_and_Linux\\myvenv\\Lib\\site-packages\\openai\\types\\responses\\computer_tool_param.py',
   'PYMODULE'),
  ('openai.types.responses.easy_input_message',
   'c:\\Users\\<USER>\\Desktop\\Mes_projets\\WritingToolsClean\\Windows_and_Linux\\myvenv\\Lib\\site-packages\\openai\\types\\responses\\easy_input_message.py',
   'PYMODULE'),
  ('openai.types.responses.easy_input_message_param',
   'c:\\Users\\<USER>\\Desktop\\Mes_projets\\WritingToolsClean\\Windows_and_Linux\\myvenv\\Lib\\site-packages\\openai\\types\\responses\\easy_input_message_param.py',
   'PYMODULE'),
  ('openai.types.responses.file_search_tool',
   'c:\\Users\\<USER>\\Desktop\\Mes_projets\\WritingToolsClean\\Windows_and_Linux\\myvenv\\Lib\\site-packages\\openai\\types\\responses\\file_search_tool.py',
   'PYMODULE'),
  ('openai.types.responses.file_search_tool_param',
   'c:\\Users\\<USER>\\Desktop\\Mes_projets\\WritingToolsClean\\Windows_and_Linux\\myvenv\\Lib\\site-packages\\openai\\types\\responses\\file_search_tool_param.py',
   'PYMODULE'),
  ('openai.types.responses.function_tool',
   'c:\\Users\\<USER>\\Desktop\\Mes_projets\\WritingToolsClean\\Windows_and_Linux\\myvenv\\Lib\\site-packages\\openai\\types\\responses\\function_tool.py',
   'PYMODULE'),
  ('openai.types.responses.function_tool_param',
   'c:\\Users\\<USER>\\Desktop\\Mes_projets\\WritingToolsClean\\Windows_and_Linux\\myvenv\\Lib\\site-packages\\openai\\types\\responses\\function_tool_param.py',
   'PYMODULE'),
  ('openai.types.responses.input_item_list_params',
   'c:\\Users\\<USER>\\Desktop\\Mes_projets\\WritingToolsClean\\Windows_and_Linux\\myvenv\\Lib\\site-packages\\openai\\types\\responses\\input_item_list_params.py',
   'PYMODULE'),
  ('openai.types.responses.parsed_response',
   'c:\\Users\\<USER>\\Desktop\\Mes_projets\\WritingToolsClean\\Windows_and_Linux\\myvenv\\Lib\\site-packages\\openai\\types\\responses\\parsed_response.py',
   'PYMODULE'),
  ('openai.types.responses.response',
   'c:\\Users\\<USER>\\Desktop\\Mes_projets\\WritingToolsClean\\Windows_and_Linux\\myvenv\\Lib\\site-packages\\openai\\types\\responses\\response.py',
   'PYMODULE'),
  ('openai.types.responses.response_audio_delta_event',
   'c:\\Users\\<USER>\\Desktop\\Mes_projets\\WritingToolsClean\\Windows_and_Linux\\myvenv\\Lib\\site-packages\\openai\\types\\responses\\response_audio_delta_event.py',
   'PYMODULE'),
  ('openai.types.responses.response_audio_done_event',
   'c:\\Users\\<USER>\\Desktop\\Mes_projets\\WritingToolsClean\\Windows_and_Linux\\myvenv\\Lib\\site-packages\\openai\\types\\responses\\response_audio_done_event.py',
   'PYMODULE'),
  ('openai.types.responses.response_audio_transcript_delta_event',
   'c:\\Users\\<USER>\\Desktop\\Mes_projets\\WritingToolsClean\\Windows_and_Linux\\myvenv\\Lib\\site-packages\\openai\\types\\responses\\response_audio_transcript_delta_event.py',
   'PYMODULE'),
  ('openai.types.responses.response_audio_transcript_done_event',
   'c:\\Users\\<USER>\\Desktop\\Mes_projets\\WritingToolsClean\\Windows_and_Linux\\myvenv\\Lib\\site-packages\\openai\\types\\responses\\response_audio_transcript_done_event.py',
   'PYMODULE'),
  ('openai.types.responses.response_code_interpreter_call_code_delta_event',
   'c:\\Users\\<USER>\\Desktop\\Mes_projets\\WritingToolsClean\\Windows_and_Linux\\myvenv\\Lib\\site-packages\\openai\\types\\responses\\response_code_interpreter_call_code_delta_event.py',
   'PYMODULE'),
  ('openai.types.responses.response_code_interpreter_call_code_done_event',
   'c:\\Users\\<USER>\\Desktop\\Mes_projets\\WritingToolsClean\\Windows_and_Linux\\myvenv\\Lib\\site-packages\\openai\\types\\responses\\response_code_interpreter_call_code_done_event.py',
   'PYMODULE'),
  ('openai.types.responses.response_code_interpreter_call_completed_event',
   'c:\\Users\\<USER>\\Desktop\\Mes_projets\\WritingToolsClean\\Windows_and_Linux\\myvenv\\Lib\\site-packages\\openai\\types\\responses\\response_code_interpreter_call_completed_event.py',
   'PYMODULE'),
  ('openai.types.responses.response_code_interpreter_call_in_progress_event',
   'c:\\Users\\<USER>\\Desktop\\Mes_projets\\WritingToolsClean\\Windows_and_Linux\\myvenv\\Lib\\site-packages\\openai\\types\\responses\\response_code_interpreter_call_in_progress_event.py',
   'PYMODULE'),
  ('openai.types.responses.response_code_interpreter_call_interpreting_event',
   'c:\\Users\\<USER>\\Desktop\\Mes_projets\\WritingToolsClean\\Windows_and_Linux\\myvenv\\Lib\\site-packages\\openai\\types\\responses\\response_code_interpreter_call_interpreting_event.py',
   'PYMODULE'),
  ('openai.types.responses.response_code_interpreter_tool_call',
   'c:\\Users\\<USER>\\Desktop\\Mes_projets\\WritingToolsClean\\Windows_and_Linux\\myvenv\\Lib\\site-packages\\openai\\types\\responses\\response_code_interpreter_tool_call.py',
   'PYMODULE'),
  ('openai.types.responses.response_code_interpreter_tool_call_param',
   'c:\\Users\\<USER>\\Desktop\\Mes_projets\\WritingToolsClean\\Windows_and_Linux\\myvenv\\Lib\\site-packages\\openai\\types\\responses\\response_code_interpreter_tool_call_param.py',
   'PYMODULE'),
  ('openai.types.responses.response_completed_event',
   'c:\\Users\\<USER>\\Desktop\\Mes_projets\\WritingToolsClean\\Windows_and_Linux\\myvenv\\Lib\\site-packages\\openai\\types\\responses\\response_completed_event.py',
   'PYMODULE'),
  ('openai.types.responses.response_computer_tool_call',
   'c:\\Users\\<USER>\\Desktop\\Mes_projets\\WritingToolsClean\\Windows_and_Linux\\myvenv\\Lib\\site-packages\\openai\\types\\responses\\response_computer_tool_call.py',
   'PYMODULE'),
  ('openai.types.responses.response_computer_tool_call_output_item',
   'c:\\Users\\<USER>\\Desktop\\Mes_projets\\WritingToolsClean\\Windows_and_Linux\\myvenv\\Lib\\site-packages\\openai\\types\\responses\\response_computer_tool_call_output_item.py',
   'PYMODULE'),
  ('openai.types.responses.response_computer_tool_call_output_screenshot',
   'c:\\Users\\<USER>\\Desktop\\Mes_projets\\WritingToolsClean\\Windows_and_Linux\\myvenv\\Lib\\site-packages\\openai\\types\\responses\\response_computer_tool_call_output_screenshot.py',
   'PYMODULE'),
  ('openai.types.responses.response_computer_tool_call_output_screenshot_param',
   'c:\\Users\\<USER>\\Desktop\\Mes_projets\\WritingToolsClean\\Windows_and_Linux\\myvenv\\Lib\\site-packages\\openai\\types\\responses\\response_computer_tool_call_output_screenshot_param.py',
   'PYMODULE'),
  ('openai.types.responses.response_computer_tool_call_param',
   'c:\\Users\\<USER>\\Desktop\\Mes_projets\\WritingToolsClean\\Windows_and_Linux\\myvenv\\Lib\\site-packages\\openai\\types\\responses\\response_computer_tool_call_param.py',
   'PYMODULE'),
  ('openai.types.responses.response_content_part_added_event',
   'c:\\Users\\<USER>\\Desktop\\Mes_projets\\WritingToolsClean\\Windows_and_Linux\\myvenv\\Lib\\site-packages\\openai\\types\\responses\\response_content_part_added_event.py',
   'PYMODULE'),
  ('openai.types.responses.response_content_part_done_event',
   'c:\\Users\\<USER>\\Desktop\\Mes_projets\\WritingToolsClean\\Windows_and_Linux\\myvenv\\Lib\\site-packages\\openai\\types\\responses\\response_content_part_done_event.py',
   'PYMODULE'),
  ('openai.types.responses.response_create_params',
   'c:\\Users\\<USER>\\Desktop\\Mes_projets\\WritingToolsClean\\Windows_and_Linux\\myvenv\\Lib\\site-packages\\openai\\types\\responses\\response_create_params.py',
   'PYMODULE'),
  ('openai.types.responses.response_created_event',
   'c:\\Users\\<USER>\\Desktop\\Mes_projets\\WritingToolsClean\\Windows_and_Linux\\myvenv\\Lib\\site-packages\\openai\\types\\responses\\response_created_event.py',
   'PYMODULE'),
  ('openai.types.responses.response_error',
   'c:\\Users\\<USER>\\Desktop\\Mes_projets\\WritingToolsClean\\Windows_and_Linux\\myvenv\\Lib\\site-packages\\openai\\types\\responses\\response_error.py',
   'PYMODULE'),
  ('openai.types.responses.response_error_event',
   'c:\\Users\\<USER>\\Desktop\\Mes_projets\\WritingToolsClean\\Windows_and_Linux\\myvenv\\Lib\\site-packages\\openai\\types\\responses\\response_error_event.py',
   'PYMODULE'),
  ('openai.types.responses.response_failed_event',
   'c:\\Users\\<USER>\\Desktop\\Mes_projets\\WritingToolsClean\\Windows_and_Linux\\myvenv\\Lib\\site-packages\\openai\\types\\responses\\response_failed_event.py',
   'PYMODULE'),
  ('openai.types.responses.response_file_search_call_completed_event',
   'c:\\Users\\<USER>\\Desktop\\Mes_projets\\WritingToolsClean\\Windows_and_Linux\\myvenv\\Lib\\site-packages\\openai\\types\\responses\\response_file_search_call_completed_event.py',
   'PYMODULE'),
  ('openai.types.responses.response_file_search_call_in_progress_event',
   'c:\\Users\\<USER>\\Desktop\\Mes_projets\\WritingToolsClean\\Windows_and_Linux\\myvenv\\Lib\\site-packages\\openai\\types\\responses\\response_file_search_call_in_progress_event.py',
   'PYMODULE'),
  ('openai.types.responses.response_file_search_call_searching_event',
   'c:\\Users\\<USER>\\Desktop\\Mes_projets\\WritingToolsClean\\Windows_and_Linux\\myvenv\\Lib\\site-packages\\openai\\types\\responses\\response_file_search_call_searching_event.py',
   'PYMODULE'),
  ('openai.types.responses.response_file_search_tool_call',
   'c:\\Users\\<USER>\\Desktop\\Mes_projets\\WritingToolsClean\\Windows_and_Linux\\myvenv\\Lib\\site-packages\\openai\\types\\responses\\response_file_search_tool_call.py',
   'PYMODULE'),
  ('openai.types.responses.response_file_search_tool_call_param',
   'c:\\Users\\<USER>\\Desktop\\Mes_projets\\WritingToolsClean\\Windows_and_Linux\\myvenv\\Lib\\site-packages\\openai\\types\\responses\\response_file_search_tool_call_param.py',
   'PYMODULE'),
  ('openai.types.responses.response_format_text_config',
   'c:\\Users\\<USER>\\Desktop\\Mes_projets\\WritingToolsClean\\Windows_and_Linux\\myvenv\\Lib\\site-packages\\openai\\types\\responses\\response_format_text_config.py',
   'PYMODULE'),
  ('openai.types.responses.response_format_text_config_param',
   'c:\\Users\\<USER>\\Desktop\\Mes_projets\\WritingToolsClean\\Windows_and_Linux\\myvenv\\Lib\\site-packages\\openai\\types\\responses\\response_format_text_config_param.py',
   'PYMODULE'),
  ('openai.types.responses.response_format_text_json_schema_config',
   'c:\\Users\\<USER>\\Desktop\\Mes_projets\\WritingToolsClean\\Windows_and_Linux\\myvenv\\Lib\\site-packages\\openai\\types\\responses\\response_format_text_json_schema_config.py',
   'PYMODULE'),
  ('openai.types.responses.response_format_text_json_schema_config_param',
   'c:\\Users\\<USER>\\Desktop\\Mes_projets\\WritingToolsClean\\Windows_and_Linux\\myvenv\\Lib\\site-packages\\openai\\types\\responses\\response_format_text_json_schema_config_param.py',
   'PYMODULE'),
  ('openai.types.responses.response_function_call_arguments_delta_event',
   'c:\\Users\\<USER>\\Desktop\\Mes_projets\\WritingToolsClean\\Windows_and_Linux\\myvenv\\Lib\\site-packages\\openai\\types\\responses\\response_function_call_arguments_delta_event.py',
   'PYMODULE'),
  ('openai.types.responses.response_function_call_arguments_done_event',
   'c:\\Users\\<USER>\\Desktop\\Mes_projets\\WritingToolsClean\\Windows_and_Linux\\myvenv\\Lib\\site-packages\\openai\\types\\responses\\response_function_call_arguments_done_event.py',
   'PYMODULE'),
  ('openai.types.responses.response_function_tool_call',
   'c:\\Users\\<USER>\\Desktop\\Mes_projets\\WritingToolsClean\\Windows_and_Linux\\myvenv\\Lib\\site-packages\\openai\\types\\responses\\response_function_tool_call.py',
   'PYMODULE'),
  ('openai.types.responses.response_function_tool_call_item',
   'c:\\Users\\<USER>\\Desktop\\Mes_projets\\WritingToolsClean\\Windows_and_Linux\\myvenv\\Lib\\site-packages\\openai\\types\\responses\\response_function_tool_call_item.py',
   'PYMODULE'),
  ('openai.types.responses.response_function_tool_call_output_item',
   'c:\\Users\\<USER>\\Desktop\\Mes_projets\\WritingToolsClean\\Windows_and_Linux\\myvenv\\Lib\\site-packages\\openai\\types\\responses\\response_function_tool_call_output_item.py',
   'PYMODULE'),
  ('openai.types.responses.response_function_tool_call_param',
   'c:\\Users\\<USER>\\Desktop\\Mes_projets\\WritingToolsClean\\Windows_and_Linux\\myvenv\\Lib\\site-packages\\openai\\types\\responses\\response_function_tool_call_param.py',
   'PYMODULE'),
  ('openai.types.responses.response_function_web_search',
   'c:\\Users\\<USER>\\Desktop\\Mes_projets\\WritingToolsClean\\Windows_and_Linux\\myvenv\\Lib\\site-packages\\openai\\types\\responses\\response_function_web_search.py',
   'PYMODULE'),
  ('openai.types.responses.response_function_web_search_param',
   'c:\\Users\\<USER>\\Desktop\\Mes_projets\\WritingToolsClean\\Windows_and_Linux\\myvenv\\Lib\\site-packages\\openai\\types\\responses\\response_function_web_search_param.py',
   'PYMODULE'),
  ('openai.types.responses.response_image_gen_call_completed_event',
   'c:\\Users\\<USER>\\Desktop\\Mes_projets\\WritingToolsClean\\Windows_and_Linux\\myvenv\\Lib\\site-packages\\openai\\types\\responses\\response_image_gen_call_completed_event.py',
   'PYMODULE'),
  ('openai.types.responses.response_image_gen_call_generating_event',
   'c:\\Users\\<USER>\\Desktop\\Mes_projets\\WritingToolsClean\\Windows_and_Linux\\myvenv\\Lib\\site-packages\\openai\\types\\responses\\response_image_gen_call_generating_event.py',
   'PYMODULE'),
  ('openai.types.responses.response_image_gen_call_in_progress_event',
   'c:\\Users\\<USER>\\Desktop\\Mes_projets\\WritingToolsClean\\Windows_and_Linux\\myvenv\\Lib\\site-packages\\openai\\types\\responses\\response_image_gen_call_in_progress_event.py',
   'PYMODULE'),
  ('openai.types.responses.response_image_gen_call_partial_image_event',
   'c:\\Users\\<USER>\\Desktop\\Mes_projets\\WritingToolsClean\\Windows_and_Linux\\myvenv\\Lib\\site-packages\\openai\\types\\responses\\response_image_gen_call_partial_image_event.py',
   'PYMODULE'),
  ('openai.types.responses.response_in_progress_event',
   'c:\\Users\\<USER>\\Desktop\\Mes_projets\\WritingToolsClean\\Windows_and_Linux\\myvenv\\Lib\\site-packages\\openai\\types\\responses\\response_in_progress_event.py',
   'PYMODULE'),
  ('openai.types.responses.response_includable',
   'c:\\Users\\<USER>\\Desktop\\Mes_projets\\WritingToolsClean\\Windows_and_Linux\\myvenv\\Lib\\site-packages\\openai\\types\\responses\\response_includable.py',
   'PYMODULE'),
  ('openai.types.responses.response_incomplete_event',
   'c:\\Users\\<USER>\\Desktop\\Mes_projets\\WritingToolsClean\\Windows_and_Linux\\myvenv\\Lib\\site-packages\\openai\\types\\responses\\response_incomplete_event.py',
   'PYMODULE'),
  ('openai.types.responses.response_input_content',
   'c:\\Users\\<USER>\\Desktop\\Mes_projets\\WritingToolsClean\\Windows_and_Linux\\myvenv\\Lib\\site-packages\\openai\\types\\responses\\response_input_content.py',
   'PYMODULE'),
  ('openai.types.responses.response_input_content_param',
   'c:\\Users\\<USER>\\Desktop\\Mes_projets\\WritingToolsClean\\Windows_and_Linux\\myvenv\\Lib\\site-packages\\openai\\types\\responses\\response_input_content_param.py',
   'PYMODULE'),
  ('openai.types.responses.response_input_file',
   'c:\\Users\\<USER>\\Desktop\\Mes_projets\\WritingToolsClean\\Windows_and_Linux\\myvenv\\Lib\\site-packages\\openai\\types\\responses\\response_input_file.py',
   'PYMODULE'),
  ('openai.types.responses.response_input_file_param',
   'c:\\Users\\<USER>\\Desktop\\Mes_projets\\WritingToolsClean\\Windows_and_Linux\\myvenv\\Lib\\site-packages\\openai\\types\\responses\\response_input_file_param.py',
   'PYMODULE'),
  ('openai.types.responses.response_input_image',
   'c:\\Users\\<USER>\\Desktop\\Mes_projets\\WritingToolsClean\\Windows_and_Linux\\myvenv\\Lib\\site-packages\\openai\\types\\responses\\response_input_image.py',
   'PYMODULE'),
  ('openai.types.responses.response_input_image_param',
   'c:\\Users\\<USER>\\Desktop\\Mes_projets\\WritingToolsClean\\Windows_and_Linux\\myvenv\\Lib\\site-packages\\openai\\types\\responses\\response_input_image_param.py',
   'PYMODULE'),
  ('openai.types.responses.response_input_item',
   'c:\\Users\\<USER>\\Desktop\\Mes_projets\\WritingToolsClean\\Windows_and_Linux\\myvenv\\Lib\\site-packages\\openai\\types\\responses\\response_input_item.py',
   'PYMODULE'),
  ('openai.types.responses.response_input_item_param',
   'c:\\Users\\<USER>\\Desktop\\Mes_projets\\WritingToolsClean\\Windows_and_Linux\\myvenv\\Lib\\site-packages\\openai\\types\\responses\\response_input_item_param.py',
   'PYMODULE'),
  ('openai.types.responses.response_input_message_content_list',
   'c:\\Users\\<USER>\\Desktop\\Mes_projets\\WritingToolsClean\\Windows_and_Linux\\myvenv\\Lib\\site-packages\\openai\\types\\responses\\response_input_message_content_list.py',
   'PYMODULE'),
  ('openai.types.responses.response_input_message_content_list_param',
   'c:\\Users\\<USER>\\Desktop\\Mes_projets\\WritingToolsClean\\Windows_and_Linux\\myvenv\\Lib\\site-packages\\openai\\types\\responses\\response_input_message_content_list_param.py',
   'PYMODULE'),
  ('openai.types.responses.response_input_message_item',
   'c:\\Users\\<USER>\\Desktop\\Mes_projets\\WritingToolsClean\\Windows_and_Linux\\myvenv\\Lib\\site-packages\\openai\\types\\responses\\response_input_message_item.py',
   'PYMODULE'),
  ('openai.types.responses.response_input_param',
   'c:\\Users\\<USER>\\Desktop\\Mes_projets\\WritingToolsClean\\Windows_and_Linux\\myvenv\\Lib\\site-packages\\openai\\types\\responses\\response_input_param.py',
   'PYMODULE'),
  ('openai.types.responses.response_input_text',
   'c:\\Users\\<USER>\\Desktop\\Mes_projets\\WritingToolsClean\\Windows_and_Linux\\myvenv\\Lib\\site-packages\\openai\\types\\responses\\response_input_text.py',
   'PYMODULE'),
  ('openai.types.responses.response_input_text_param',
   'c:\\Users\\<USER>\\Desktop\\Mes_projets\\WritingToolsClean\\Windows_and_Linux\\myvenv\\Lib\\site-packages\\openai\\types\\responses\\response_input_text_param.py',
   'PYMODULE'),
  ('openai.types.responses.response_item',
   'c:\\Users\\<USER>\\Desktop\\Mes_projets\\WritingToolsClean\\Windows_and_Linux\\myvenv\\Lib\\site-packages\\openai\\types\\responses\\response_item.py',
   'PYMODULE'),
  ('openai.types.responses.response_item_list',
   'c:\\Users\\<USER>\\Desktop\\Mes_projets\\WritingToolsClean\\Windows_and_Linux\\myvenv\\Lib\\site-packages\\openai\\types\\responses\\response_item_list.py',
   'PYMODULE'),
  ('openai.types.responses.response_mcp_call_arguments_delta_event',
   'c:\\Users\\<USER>\\Desktop\\Mes_projets\\WritingToolsClean\\Windows_and_Linux\\myvenv\\Lib\\site-packages\\openai\\types\\responses\\response_mcp_call_arguments_delta_event.py',
   'PYMODULE'),
  ('openai.types.responses.response_mcp_call_arguments_done_event',
   'c:\\Users\\<USER>\\Desktop\\Mes_projets\\WritingToolsClean\\Windows_and_Linux\\myvenv\\Lib\\site-packages\\openai\\types\\responses\\response_mcp_call_arguments_done_event.py',
   'PYMODULE'),
  ('openai.types.responses.response_mcp_call_completed_event',
   'c:\\Users\\<USER>\\Desktop\\Mes_projets\\WritingToolsClean\\Windows_and_Linux\\myvenv\\Lib\\site-packages\\openai\\types\\responses\\response_mcp_call_completed_event.py',
   'PYMODULE'),
  ('openai.types.responses.response_mcp_call_failed_event',
   'c:\\Users\\<USER>\\Desktop\\Mes_projets\\WritingToolsClean\\Windows_and_Linux\\myvenv\\Lib\\site-packages\\openai\\types\\responses\\response_mcp_call_failed_event.py',
   'PYMODULE'),
  ('openai.types.responses.response_mcp_call_in_progress_event',
   'c:\\Users\\<USER>\\Desktop\\Mes_projets\\WritingToolsClean\\Windows_and_Linux\\myvenv\\Lib\\site-packages\\openai\\types\\responses\\response_mcp_call_in_progress_event.py',
   'PYMODULE'),
  ('openai.types.responses.response_mcp_list_tools_completed_event',
   'c:\\Users\\<USER>\\Desktop\\Mes_projets\\WritingToolsClean\\Windows_and_Linux\\myvenv\\Lib\\site-packages\\openai\\types\\responses\\response_mcp_list_tools_completed_event.py',
   'PYMODULE'),
  ('openai.types.responses.response_mcp_list_tools_failed_event',
   'c:\\Users\\<USER>\\Desktop\\Mes_projets\\WritingToolsClean\\Windows_and_Linux\\myvenv\\Lib\\site-packages\\openai\\types\\responses\\response_mcp_list_tools_failed_event.py',
   'PYMODULE'),
  ('openai.types.responses.response_mcp_list_tools_in_progress_event',
   'c:\\Users\\<USER>\\Desktop\\Mes_projets\\WritingToolsClean\\Windows_and_Linux\\myvenv\\Lib\\site-packages\\openai\\types\\responses\\response_mcp_list_tools_in_progress_event.py',
   'PYMODULE'),
  ('openai.types.responses.response_output_item',
   'c:\\Users\\<USER>\\Desktop\\Mes_projets\\WritingToolsClean\\Windows_and_Linux\\myvenv\\Lib\\site-packages\\openai\\types\\responses\\response_output_item.py',
   'PYMODULE'),
  ('openai.types.responses.response_output_item_added_event',
   'c:\\Users\\<USER>\\Desktop\\Mes_projets\\WritingToolsClean\\Windows_and_Linux\\myvenv\\Lib\\site-packages\\openai\\types\\responses\\response_output_item_added_event.py',
   'PYMODULE'),
  ('openai.types.responses.response_output_item_done_event',
   'c:\\Users\\<USER>\\Desktop\\Mes_projets\\WritingToolsClean\\Windows_and_Linux\\myvenv\\Lib\\site-packages\\openai\\types\\responses\\response_output_item_done_event.py',
   'PYMODULE'),
  ('openai.types.responses.response_output_message',
   'c:\\Users\\<USER>\\Desktop\\Mes_projets\\WritingToolsClean\\Windows_and_Linux\\myvenv\\Lib\\site-packages\\openai\\types\\responses\\response_output_message.py',
   'PYMODULE'),
  ('openai.types.responses.response_output_message_param',
   'c:\\Users\\<USER>\\Desktop\\Mes_projets\\WritingToolsClean\\Windows_and_Linux\\myvenv\\Lib\\site-packages\\openai\\types\\responses\\response_output_message_param.py',
   'PYMODULE'),
  ('openai.types.responses.response_output_refusal',
   'c:\\Users\\<USER>\\Desktop\\Mes_projets\\WritingToolsClean\\Windows_and_Linux\\myvenv\\Lib\\site-packages\\openai\\types\\responses\\response_output_refusal.py',
   'PYMODULE'),
  ('openai.types.responses.response_output_refusal_param',
   'c:\\Users\\<USER>\\Desktop\\Mes_projets\\WritingToolsClean\\Windows_and_Linux\\myvenv\\Lib\\site-packages\\openai\\types\\responses\\response_output_refusal_param.py',
   'PYMODULE'),
  ('openai.types.responses.response_output_text',
   'c:\\Users\\<USER>\\Desktop\\Mes_projets\\WritingToolsClean\\Windows_and_Linux\\myvenv\\Lib\\site-packages\\openai\\types\\responses\\response_output_text.py',
   'PYMODULE'),
  ('openai.types.responses.response_output_text_annotation_added_event',
   'c:\\Users\\<USER>\\Desktop\\Mes_projets\\WritingToolsClean\\Windows_and_Linux\\myvenv\\Lib\\site-packages\\openai\\types\\responses\\response_output_text_annotation_added_event.py',
   'PYMODULE'),
  ('openai.types.responses.response_output_text_param',
   'c:\\Users\\<USER>\\Desktop\\Mes_projets\\WritingToolsClean\\Windows_and_Linux\\myvenv\\Lib\\site-packages\\openai\\types\\responses\\response_output_text_param.py',
   'PYMODULE'),
  ('openai.types.responses.response_prompt',
   'c:\\Users\\<USER>\\Desktop\\Mes_projets\\WritingToolsClean\\Windows_and_Linux\\myvenv\\Lib\\site-packages\\openai\\types\\responses\\response_prompt.py',
   'PYMODULE'),
  ('openai.types.responses.response_prompt_param',
   'c:\\Users\\<USER>\\Desktop\\Mes_projets\\WritingToolsClean\\Windows_and_Linux\\myvenv\\Lib\\site-packages\\openai\\types\\responses\\response_prompt_param.py',
   'PYMODULE'),
  ('openai.types.responses.response_queued_event',
   'c:\\Users\\<USER>\\Desktop\\Mes_projets\\WritingToolsClean\\Windows_and_Linux\\myvenv\\Lib\\site-packages\\openai\\types\\responses\\response_queued_event.py',
   'PYMODULE'),
  ('openai.types.responses.response_reasoning_item',
   'c:\\Users\\<USER>\\Desktop\\Mes_projets\\WritingToolsClean\\Windows_and_Linux\\myvenv\\Lib\\site-packages\\openai\\types\\responses\\response_reasoning_item.py',
   'PYMODULE'),
  ('openai.types.responses.response_reasoning_item_param',
   'c:\\Users\\<USER>\\Desktop\\Mes_projets\\WritingToolsClean\\Windows_and_Linux\\myvenv\\Lib\\site-packages\\openai\\types\\responses\\response_reasoning_item_param.py',
   'PYMODULE'),
  ('openai.types.responses.response_reasoning_summary_delta_event',
   'c:\\Users\\<USER>\\Desktop\\Mes_projets\\WritingToolsClean\\Windows_and_Linux\\myvenv\\Lib\\site-packages\\openai\\types\\responses\\response_reasoning_summary_delta_event.py',
   'PYMODULE'),
  ('openai.types.responses.response_reasoning_summary_done_event',
   'c:\\Users\\<USER>\\Desktop\\Mes_projets\\WritingToolsClean\\Windows_and_Linux\\myvenv\\Lib\\site-packages\\openai\\types\\responses\\response_reasoning_summary_done_event.py',
   'PYMODULE'),
  ('openai.types.responses.response_reasoning_summary_part_added_event',
   'c:\\Users\\<USER>\\Desktop\\Mes_projets\\WritingToolsClean\\Windows_and_Linux\\myvenv\\Lib\\site-packages\\openai\\types\\responses\\response_reasoning_summary_part_added_event.py',
   'PYMODULE'),
  ('openai.types.responses.response_reasoning_summary_part_done_event',
   'c:\\Users\\<USER>\\Desktop\\Mes_projets\\WritingToolsClean\\Windows_and_Linux\\myvenv\\Lib\\site-packages\\openai\\types\\responses\\response_reasoning_summary_part_done_event.py',
   'PYMODULE'),
  ('openai.types.responses.response_reasoning_summary_text_delta_event',
   'c:\\Users\\<USER>\\Desktop\\Mes_projets\\WritingToolsClean\\Windows_and_Linux\\myvenv\\Lib\\site-packages\\openai\\types\\responses\\response_reasoning_summary_text_delta_event.py',
   'PYMODULE'),
  ('openai.types.responses.response_reasoning_summary_text_done_event',
   'c:\\Users\\<USER>\\Desktop\\Mes_projets\\WritingToolsClean\\Windows_and_Linux\\myvenv\\Lib\\site-packages\\openai\\types\\responses\\response_reasoning_summary_text_done_event.py',
   'PYMODULE'),
  ('openai.types.responses.response_refusal_delta_event',
   'c:\\Users\\<USER>\\Desktop\\Mes_projets\\WritingToolsClean\\Windows_and_Linux\\myvenv\\Lib\\site-packages\\openai\\types\\responses\\response_refusal_delta_event.py',
   'PYMODULE'),
  ('openai.types.responses.response_refusal_done_event',
   'c:\\Users\\<USER>\\Desktop\\Mes_projets\\WritingToolsClean\\Windows_and_Linux\\myvenv\\Lib\\site-packages\\openai\\types\\responses\\response_refusal_done_event.py',
   'PYMODULE'),
  ('openai.types.responses.response_retrieve_params',
   'c:\\Users\\<USER>\\Desktop\\Mes_projets\\WritingToolsClean\\Windows_and_Linux\\myvenv\\Lib\\site-packages\\openai\\types\\responses\\response_retrieve_params.py',
   'PYMODULE'),
  ('openai.types.responses.response_status',
   'c:\\Users\\<USER>\\Desktop\\Mes_projets\\WritingToolsClean\\Windows_and_Linux\\myvenv\\Lib\\site-packages\\openai\\types\\responses\\response_status.py',
   'PYMODULE'),
  ('openai.types.responses.response_stream_event',
   'c:\\Users\\<USER>\\Desktop\\Mes_projets\\WritingToolsClean\\Windows_and_Linux\\myvenv\\Lib\\site-packages\\openai\\types\\responses\\response_stream_event.py',
   'PYMODULE'),
  ('openai.types.responses.response_text_config',
   'c:\\Users\\<USER>\\Desktop\\Mes_projets\\WritingToolsClean\\Windows_and_Linux\\myvenv\\Lib\\site-packages\\openai\\types\\responses\\response_text_config.py',
   'PYMODULE'),
  ('openai.types.responses.response_text_config_param',
   'c:\\Users\\<USER>\\Desktop\\Mes_projets\\WritingToolsClean\\Windows_and_Linux\\myvenv\\Lib\\site-packages\\openai\\types\\responses\\response_text_config_param.py',
   'PYMODULE'),
  ('openai.types.responses.response_text_delta_event',
   'c:\\Users\\<USER>\\Desktop\\Mes_projets\\WritingToolsClean\\Windows_and_Linux\\myvenv\\Lib\\site-packages\\openai\\types\\responses\\response_text_delta_event.py',
   'PYMODULE'),
  ('openai.types.responses.response_text_done_event',
   'c:\\Users\\<USER>\\Desktop\\Mes_projets\\WritingToolsClean\\Windows_and_Linux\\myvenv\\Lib\\site-packages\\openai\\types\\responses\\response_text_done_event.py',
   'PYMODULE'),
  ('openai.types.responses.response_usage',
   'c:\\Users\\<USER>\\Desktop\\Mes_projets\\WritingToolsClean\\Windows_and_Linux\\myvenv\\Lib\\site-packages\\openai\\types\\responses\\response_usage.py',
   'PYMODULE'),
  ('openai.types.responses.response_web_search_call_completed_event',
   'c:\\Users\\<USER>\\Desktop\\Mes_projets\\WritingToolsClean\\Windows_and_Linux\\myvenv\\Lib\\site-packages\\openai\\types\\responses\\response_web_search_call_completed_event.py',
   'PYMODULE'),
  ('openai.types.responses.response_web_search_call_in_progress_event',
   'c:\\Users\\<USER>\\Desktop\\Mes_projets\\WritingToolsClean\\Windows_and_Linux\\myvenv\\Lib\\site-packages\\openai\\types\\responses\\response_web_search_call_in_progress_event.py',
   'PYMODULE'),
  ('openai.types.responses.response_web_search_call_searching_event',
   'c:\\Users\\<USER>\\Desktop\\Mes_projets\\WritingToolsClean\\Windows_and_Linux\\myvenv\\Lib\\site-packages\\openai\\types\\responses\\response_web_search_call_searching_event.py',
   'PYMODULE'),
  ('openai.types.responses.tool',
   'c:\\Users\\<USER>\\Desktop\\Mes_projets\\WritingToolsClean\\Windows_and_Linux\\myvenv\\Lib\\site-packages\\openai\\types\\responses\\tool.py',
   'PYMODULE'),
  ('openai.types.responses.tool_choice_function',
   'c:\\Users\\<USER>\\Desktop\\Mes_projets\\WritingToolsClean\\Windows_and_Linux\\myvenv\\Lib\\site-packages\\openai\\types\\responses\\tool_choice_function.py',
   'PYMODULE'),
  ('openai.types.responses.tool_choice_function_param',
   'c:\\Users\\<USER>\\Desktop\\Mes_projets\\WritingToolsClean\\Windows_and_Linux\\myvenv\\Lib\\site-packages\\openai\\types\\responses\\tool_choice_function_param.py',
   'PYMODULE'),
  ('openai.types.responses.tool_choice_mcp',
   'c:\\Users\\<USER>\\Desktop\\Mes_projets\\WritingToolsClean\\Windows_and_Linux\\myvenv\\Lib\\site-packages\\openai\\types\\responses\\tool_choice_mcp.py',
   'PYMODULE'),
  ('openai.types.responses.tool_choice_mcp_param',
   'c:\\Users\\<USER>\\Desktop\\Mes_projets\\WritingToolsClean\\Windows_and_Linux\\myvenv\\Lib\\site-packages\\openai\\types\\responses\\tool_choice_mcp_param.py',
   'PYMODULE'),
  ('openai.types.responses.tool_choice_options',
   'c:\\Users\\<USER>\\Desktop\\Mes_projets\\WritingToolsClean\\Windows_and_Linux\\myvenv\\Lib\\site-packages\\openai\\types\\responses\\tool_choice_options.py',
   'PYMODULE'),
  ('openai.types.responses.tool_choice_types',
   'c:\\Users\\<USER>\\Desktop\\Mes_projets\\WritingToolsClean\\Windows_and_Linux\\myvenv\\Lib\\site-packages\\openai\\types\\responses\\tool_choice_types.py',
   'PYMODULE'),
  ('openai.types.responses.tool_choice_types_param',
   'c:\\Users\\<USER>\\Desktop\\Mes_projets\\WritingToolsClean\\Windows_and_Linux\\myvenv\\Lib\\site-packages\\openai\\types\\responses\\tool_choice_types_param.py',
   'PYMODULE'),
  ('openai.types.responses.tool_param',
   'c:\\Users\\<USER>\\Desktop\\Mes_projets\\WritingToolsClean\\Windows_and_Linux\\myvenv\\Lib\\site-packages\\openai\\types\\responses\\tool_param.py',
   'PYMODULE'),
  ('openai.types.responses.web_search_tool',
   'c:\\Users\\<USER>\\Desktop\\Mes_projets\\WritingToolsClean\\Windows_and_Linux\\myvenv\\Lib\\site-packages\\openai\\types\\responses\\web_search_tool.py',
   'PYMODULE'),
  ('openai.types.responses.web_search_tool_param',
   'c:\\Users\\<USER>\\Desktop\\Mes_projets\\WritingToolsClean\\Windows_and_Linux\\myvenv\\Lib\\site-packages\\openai\\types\\responses\\web_search_tool_param.py',
   'PYMODULE'),
  ('openai.types.shared',
   'c:\\Users\\<USER>\\Desktop\\Mes_projets\\WritingToolsClean\\Windows_and_Linux\\myvenv\\Lib\\site-packages\\openai\\types\\shared\\__init__.py',
   'PYMODULE'),
  ('openai.types.shared.all_models',
   'c:\\Users\\<USER>\\Desktop\\Mes_projets\\WritingToolsClean\\Windows_and_Linux\\myvenv\\Lib\\site-packages\\openai\\types\\shared\\all_models.py',
   'PYMODULE'),
  ('openai.types.shared.chat_model',
   'c:\\Users\\<USER>\\Desktop\\Mes_projets\\WritingToolsClean\\Windows_and_Linux\\myvenv\\Lib\\site-packages\\openai\\types\\shared\\chat_model.py',
   'PYMODULE'),
  ('openai.types.shared.comparison_filter',
   'c:\\Users\\<USER>\\Desktop\\Mes_projets\\WritingToolsClean\\Windows_and_Linux\\myvenv\\Lib\\site-packages\\openai\\types\\shared\\comparison_filter.py',
   'PYMODULE'),
  ('openai.types.shared.compound_filter',
   'c:\\Users\\<USER>\\Desktop\\Mes_projets\\WritingToolsClean\\Windows_and_Linux\\myvenv\\Lib\\site-packages\\openai\\types\\shared\\compound_filter.py',
   'PYMODULE'),
  ('openai.types.shared.error_object',
   'c:\\Users\\<USER>\\Desktop\\Mes_projets\\WritingToolsClean\\Windows_and_Linux\\myvenv\\Lib\\site-packages\\openai\\types\\shared\\error_object.py',
   'PYMODULE'),
  ('openai.types.shared.function_definition',
   'c:\\Users\\<USER>\\Desktop\\Mes_projets\\WritingToolsClean\\Windows_and_Linux\\myvenv\\Lib\\site-packages\\openai\\types\\shared\\function_definition.py',
   'PYMODULE'),
  ('openai.types.shared.function_parameters',
   'c:\\Users\\<USER>\\Desktop\\Mes_projets\\WritingToolsClean\\Windows_and_Linux\\myvenv\\Lib\\site-packages\\openai\\types\\shared\\function_parameters.py',
   'PYMODULE'),
  ('openai.types.shared.metadata',
   'c:\\Users\\<USER>\\Desktop\\Mes_projets\\WritingToolsClean\\Windows_and_Linux\\myvenv\\Lib\\site-packages\\openai\\types\\shared\\metadata.py',
   'PYMODULE'),
  ('openai.types.shared.reasoning',
   'c:\\Users\\<USER>\\Desktop\\Mes_projets\\WritingToolsClean\\Windows_and_Linux\\myvenv\\Lib\\site-packages\\openai\\types\\shared\\reasoning.py',
   'PYMODULE'),
  ('openai.types.shared.reasoning_effort',
   'c:\\Users\\<USER>\\Desktop\\Mes_projets\\WritingToolsClean\\Windows_and_Linux\\myvenv\\Lib\\site-packages\\openai\\types\\shared\\reasoning_effort.py',
   'PYMODULE'),
  ('openai.types.shared.response_format_json_object',
   'c:\\Users\\<USER>\\Desktop\\Mes_projets\\WritingToolsClean\\Windows_and_Linux\\myvenv\\Lib\\site-packages\\openai\\types\\shared\\response_format_json_object.py',
   'PYMODULE'),
  ('openai.types.shared.response_format_json_schema',
   'c:\\Users\\<USER>\\Desktop\\Mes_projets\\WritingToolsClean\\Windows_and_Linux\\myvenv\\Lib\\site-packages\\openai\\types\\shared\\response_format_json_schema.py',
   'PYMODULE'),
  ('openai.types.shared.response_format_text',
   'c:\\Users\\<USER>\\Desktop\\Mes_projets\\WritingToolsClean\\Windows_and_Linux\\myvenv\\Lib\\site-packages\\openai\\types\\shared\\response_format_text.py',
   'PYMODULE'),
  ('openai.types.shared.responses_model',
   'c:\\Users\\<USER>\\Desktop\\Mes_projets\\WritingToolsClean\\Windows_and_Linux\\myvenv\\Lib\\site-packages\\openai\\types\\shared\\responses_model.py',
   'PYMODULE'),
  ('openai.types.shared_params',
   'c:\\Users\\<USER>\\Desktop\\Mes_projets\\WritingToolsClean\\Windows_and_Linux\\myvenv\\Lib\\site-packages\\openai\\types\\shared_params\\__init__.py',
   'PYMODULE'),
  ('openai.types.shared_params.chat_model',
   'c:\\Users\\<USER>\\Desktop\\Mes_projets\\WritingToolsClean\\Windows_and_Linux\\myvenv\\Lib\\site-packages\\openai\\types\\shared_params\\chat_model.py',
   'PYMODULE'),
  ('openai.types.shared_params.comparison_filter',
   'c:\\Users\\<USER>\\Desktop\\Mes_projets\\WritingToolsClean\\Windows_and_Linux\\myvenv\\Lib\\site-packages\\openai\\types\\shared_params\\comparison_filter.py',
   'PYMODULE'),
  ('openai.types.shared_params.compound_filter',
   'c:\\Users\\<USER>\\Desktop\\Mes_projets\\WritingToolsClean\\Windows_and_Linux\\myvenv\\Lib\\site-packages\\openai\\types\\shared_params\\compound_filter.py',
   'PYMODULE'),
  ('openai.types.shared_params.function_definition',
   'c:\\Users\\<USER>\\Desktop\\Mes_projets\\WritingToolsClean\\Windows_and_Linux\\myvenv\\Lib\\site-packages\\openai\\types\\shared_params\\function_definition.py',
   'PYMODULE'),
  ('openai.types.shared_params.function_parameters',
   'c:\\Users\\<USER>\\Desktop\\Mes_projets\\WritingToolsClean\\Windows_and_Linux\\myvenv\\Lib\\site-packages\\openai\\types\\shared_params\\function_parameters.py',
   'PYMODULE'),
  ('openai.types.shared_params.metadata',
   'c:\\Users\\<USER>\\Desktop\\Mes_projets\\WritingToolsClean\\Windows_and_Linux\\myvenv\\Lib\\site-packages\\openai\\types\\shared_params\\metadata.py',
   'PYMODULE'),
  ('openai.types.shared_params.reasoning',
   'c:\\Users\\<USER>\\Desktop\\Mes_projets\\WritingToolsClean\\Windows_and_Linux\\myvenv\\Lib\\site-packages\\openai\\types\\shared_params\\reasoning.py',
   'PYMODULE'),
  ('openai.types.shared_params.reasoning_effort',
   'c:\\Users\\<USER>\\Desktop\\Mes_projets\\WritingToolsClean\\Windows_and_Linux\\myvenv\\Lib\\site-packages\\openai\\types\\shared_params\\reasoning_effort.py',
   'PYMODULE'),
  ('openai.types.shared_params.response_format_json_object',
   'c:\\Users\\<USER>\\Desktop\\Mes_projets\\WritingToolsClean\\Windows_and_Linux\\myvenv\\Lib\\site-packages\\openai\\types\\shared_params\\response_format_json_object.py',
   'PYMODULE'),
  ('openai.types.shared_params.response_format_json_schema',
   'c:\\Users\\<USER>\\Desktop\\Mes_projets\\WritingToolsClean\\Windows_and_Linux\\myvenv\\Lib\\site-packages\\openai\\types\\shared_params\\response_format_json_schema.py',
   'PYMODULE'),
  ('openai.types.shared_params.response_format_text',
   'c:\\Users\\<USER>\\Desktop\\Mes_projets\\WritingToolsClean\\Windows_and_Linux\\myvenv\\Lib\\site-packages\\openai\\types\\shared_params\\response_format_text.py',
   'PYMODULE'),
  ('openai.types.shared_params.responses_model',
   'c:\\Users\\<USER>\\Desktop\\Mes_projets\\WritingToolsClean\\Windows_and_Linux\\myvenv\\Lib\\site-packages\\openai\\types\\shared_params\\responses_model.py',
   'PYMODULE'),
  ('openai.types.static_file_chunking_strategy',
   'c:\\Users\\<USER>\\Desktop\\Mes_projets\\WritingToolsClean\\Windows_and_Linux\\myvenv\\Lib\\site-packages\\openai\\types\\static_file_chunking_strategy.py',
   'PYMODULE'),
  ('openai.types.static_file_chunking_strategy_object',
   'c:\\Users\\<USER>\\Desktop\\Mes_projets\\WritingToolsClean\\Windows_and_Linux\\myvenv\\Lib\\site-packages\\openai\\types\\static_file_chunking_strategy_object.py',
   'PYMODULE'),
  ('openai.types.static_file_chunking_strategy_object_param',
   'c:\\Users\\<USER>\\Desktop\\Mes_projets\\WritingToolsClean\\Windows_and_Linux\\myvenv\\Lib\\site-packages\\openai\\types\\static_file_chunking_strategy_object_param.py',
   'PYMODULE'),
  ('openai.types.static_file_chunking_strategy_param',
   'c:\\Users\\<USER>\\Desktop\\Mes_projets\\WritingToolsClean\\Windows_and_Linux\\myvenv\\Lib\\site-packages\\openai\\types\\static_file_chunking_strategy_param.py',
   'PYMODULE'),
  ('openai.types.upload',
   'c:\\Users\\<USER>\\Desktop\\Mes_projets\\WritingToolsClean\\Windows_and_Linux\\myvenv\\Lib\\site-packages\\openai\\types\\upload.py',
   'PYMODULE'),
  ('openai.types.upload_complete_params',
   'c:\\Users\\<USER>\\Desktop\\Mes_projets\\WritingToolsClean\\Windows_and_Linux\\myvenv\\Lib\\site-packages\\openai\\types\\upload_complete_params.py',
   'PYMODULE'),
  ('openai.types.upload_create_params',
   'c:\\Users\\<USER>\\Desktop\\Mes_projets\\WritingToolsClean\\Windows_and_Linux\\myvenv\\Lib\\site-packages\\openai\\types\\upload_create_params.py',
   'PYMODULE'),
  ('openai.types.uploads',
   'c:\\Users\\<USER>\\Desktop\\Mes_projets\\WritingToolsClean\\Windows_and_Linux\\myvenv\\Lib\\site-packages\\openai\\types\\uploads\\__init__.py',
   'PYMODULE'),
  ('openai.types.uploads.part_create_params',
   'c:\\Users\\<USER>\\Desktop\\Mes_projets\\WritingToolsClean\\Windows_and_Linux\\myvenv\\Lib\\site-packages\\openai\\types\\uploads\\part_create_params.py',
   'PYMODULE'),
  ('openai.types.uploads.upload_part',
   'c:\\Users\\<USER>\\Desktop\\Mes_projets\\WritingToolsClean\\Windows_and_Linux\\myvenv\\Lib\\site-packages\\openai\\types\\uploads\\upload_part.py',
   'PYMODULE'),
  ('openai.types.vector_store',
   'c:\\Users\\<USER>\\Desktop\\Mes_projets\\WritingToolsClean\\Windows_and_Linux\\myvenv\\Lib\\site-packages\\openai\\types\\vector_store.py',
   'PYMODULE'),
  ('openai.types.vector_store_create_params',
   'c:\\Users\\<USER>\\Desktop\\Mes_projets\\WritingToolsClean\\Windows_and_Linux\\myvenv\\Lib\\site-packages\\openai\\types\\vector_store_create_params.py',
   'PYMODULE'),
  ('openai.types.vector_store_deleted',
   'c:\\Users\\<USER>\\Desktop\\Mes_projets\\WritingToolsClean\\Windows_and_Linux\\myvenv\\Lib\\site-packages\\openai\\types\\vector_store_deleted.py',
   'PYMODULE'),
  ('openai.types.vector_store_list_params',
   'c:\\Users\\<USER>\\Desktop\\Mes_projets\\WritingToolsClean\\Windows_and_Linux\\myvenv\\Lib\\site-packages\\openai\\types\\vector_store_list_params.py',
   'PYMODULE'),
  ('openai.types.vector_store_search_params',
   'c:\\Users\\<USER>\\Desktop\\Mes_projets\\WritingToolsClean\\Windows_and_Linux\\myvenv\\Lib\\site-packages\\openai\\types\\vector_store_search_params.py',
   'PYMODULE'),
  ('openai.types.vector_store_search_response',
   'c:\\Users\\<USER>\\Desktop\\Mes_projets\\WritingToolsClean\\Windows_and_Linux\\myvenv\\Lib\\site-packages\\openai\\types\\vector_store_search_response.py',
   'PYMODULE'),
  ('openai.types.vector_store_update_params',
   'c:\\Users\\<USER>\\Desktop\\Mes_projets\\WritingToolsClean\\Windows_and_Linux\\myvenv\\Lib\\site-packages\\openai\\types\\vector_store_update_params.py',
   'PYMODULE'),
  ('openai.types.vector_stores',
   'c:\\Users\\<USER>\\Desktop\\Mes_projets\\WritingToolsClean\\Windows_and_Linux\\myvenv\\Lib\\site-packages\\openai\\types\\vector_stores\\__init__.py',
   'PYMODULE'),
  ('openai.types.vector_stores.file_batch_create_params',
   'c:\\Users\\<USER>\\Desktop\\Mes_projets\\WritingToolsClean\\Windows_and_Linux\\myvenv\\Lib\\site-packages\\openai\\types\\vector_stores\\file_batch_create_params.py',
   'PYMODULE'),
  ('openai.types.vector_stores.file_batch_list_files_params',
   'c:\\Users\\<USER>\\Desktop\\Mes_projets\\WritingToolsClean\\Windows_and_Linux\\myvenv\\Lib\\site-packages\\openai\\types\\vector_stores\\file_batch_list_files_params.py',
   'PYMODULE'),
  ('openai.types.vector_stores.file_content_response',
   'c:\\Users\\<USER>\\Desktop\\Mes_projets\\WritingToolsClean\\Windows_and_Linux\\myvenv\\Lib\\site-packages\\openai\\types\\vector_stores\\file_content_response.py',
   'PYMODULE'),
  ('openai.types.vector_stores.file_create_params',
   'c:\\Users\\<USER>\\Desktop\\Mes_projets\\WritingToolsClean\\Windows_and_Linux\\myvenv\\Lib\\site-packages\\openai\\types\\vector_stores\\file_create_params.py',
   'PYMODULE'),
  ('openai.types.vector_stores.file_list_params',
   'c:\\Users\\<USER>\\Desktop\\Mes_projets\\WritingToolsClean\\Windows_and_Linux\\myvenv\\Lib\\site-packages\\openai\\types\\vector_stores\\file_list_params.py',
   'PYMODULE'),
  ('openai.types.vector_stores.file_update_params',
   'c:\\Users\\<USER>\\Desktop\\Mes_projets\\WritingToolsClean\\Windows_and_Linux\\myvenv\\Lib\\site-packages\\openai\\types\\vector_stores\\file_update_params.py',
   'PYMODULE'),
  ('openai.types.vector_stores.vector_store_file',
   'c:\\Users\\<USER>\\Desktop\\Mes_projets\\WritingToolsClean\\Windows_and_Linux\\myvenv\\Lib\\site-packages\\openai\\types\\vector_stores\\vector_store_file.py',
   'PYMODULE'),
  ('openai.types.vector_stores.vector_store_file_batch',
   'c:\\Users\\<USER>\\Desktop\\Mes_projets\\WritingToolsClean\\Windows_and_Linux\\myvenv\\Lib\\site-packages\\openai\\types\\vector_stores\\vector_store_file_batch.py',
   'PYMODULE'),
  ('openai.types.vector_stores.vector_store_file_deleted',
   'c:\\Users\\<USER>\\Desktop\\Mes_projets\\WritingToolsClean\\Windows_and_Linux\\myvenv\\Lib\\site-packages\\openai\\types\\vector_stores\\vector_store_file_deleted.py',
   'PYMODULE'),
  ('openai.types.webhooks',
   'c:\\Users\\<USER>\\Desktop\\Mes_projets\\WritingToolsClean\\Windows_and_Linux\\myvenv\\Lib\\site-packages\\openai\\types\\webhooks\\__init__.py',
   'PYMODULE'),
  ('openai.types.webhooks.batch_cancelled_webhook_event',
   'c:\\Users\\<USER>\\Desktop\\Mes_projets\\WritingToolsClean\\Windows_and_Linux\\myvenv\\Lib\\site-packages\\openai\\types\\webhooks\\batch_cancelled_webhook_event.py',
   'PYMODULE'),
  ('openai.types.webhooks.batch_completed_webhook_event',
   'c:\\Users\\<USER>\\Desktop\\Mes_projets\\WritingToolsClean\\Windows_and_Linux\\myvenv\\Lib\\site-packages\\openai\\types\\webhooks\\batch_completed_webhook_event.py',
   'PYMODULE'),
  ('openai.types.webhooks.batch_expired_webhook_event',
   'c:\\Users\\<USER>\\Desktop\\Mes_projets\\WritingToolsClean\\Windows_and_Linux\\myvenv\\Lib\\site-packages\\openai\\types\\webhooks\\batch_expired_webhook_event.py',
   'PYMODULE'),
  ('openai.types.webhooks.batch_failed_webhook_event',
   'c:\\Users\\<USER>\\Desktop\\Mes_projets\\WritingToolsClean\\Windows_and_Linux\\myvenv\\Lib\\site-packages\\openai\\types\\webhooks\\batch_failed_webhook_event.py',
   'PYMODULE'),
  ('openai.types.webhooks.eval_run_canceled_webhook_event',
   'c:\\Users\\<USER>\\Desktop\\Mes_projets\\WritingToolsClean\\Windows_and_Linux\\myvenv\\Lib\\site-packages\\openai\\types\\webhooks\\eval_run_canceled_webhook_event.py',
   'PYMODULE'),
  ('openai.types.webhooks.eval_run_failed_webhook_event',
   'c:\\Users\\<USER>\\Desktop\\Mes_projets\\WritingToolsClean\\Windows_and_Linux\\myvenv\\Lib\\site-packages\\openai\\types\\webhooks\\eval_run_failed_webhook_event.py',
   'PYMODULE'),
  ('openai.types.webhooks.eval_run_succeeded_webhook_event',
   'c:\\Users\\<USER>\\Desktop\\Mes_projets\\WritingToolsClean\\Windows_and_Linux\\myvenv\\Lib\\site-packages\\openai\\types\\webhooks\\eval_run_succeeded_webhook_event.py',
   'PYMODULE'),
  ('openai.types.webhooks.fine_tuning_job_cancelled_webhook_event',
   'c:\\Users\\<USER>\\Desktop\\Mes_projets\\WritingToolsClean\\Windows_and_Linux\\myvenv\\Lib\\site-packages\\openai\\types\\webhooks\\fine_tuning_job_cancelled_webhook_event.py',
   'PYMODULE'),
  ('openai.types.webhooks.fine_tuning_job_failed_webhook_event',
   'c:\\Users\\<USER>\\Desktop\\Mes_projets\\WritingToolsClean\\Windows_and_Linux\\myvenv\\Lib\\site-packages\\openai\\types\\webhooks\\fine_tuning_job_failed_webhook_event.py',
   'PYMODULE'),
  ('openai.types.webhooks.fine_tuning_job_succeeded_webhook_event',
   'c:\\Users\\<USER>\\Desktop\\Mes_projets\\WritingToolsClean\\Windows_and_Linux\\myvenv\\Lib\\site-packages\\openai\\types\\webhooks\\fine_tuning_job_succeeded_webhook_event.py',
   'PYMODULE'),
  ('openai.types.webhooks.response_cancelled_webhook_event',
   'c:\\Users\\<USER>\\Desktop\\Mes_projets\\WritingToolsClean\\Windows_and_Linux\\myvenv\\Lib\\site-packages\\openai\\types\\webhooks\\response_cancelled_webhook_event.py',
   'PYMODULE'),
  ('openai.types.webhooks.response_completed_webhook_event',
   'c:\\Users\\<USER>\\Desktop\\Mes_projets\\WritingToolsClean\\Windows_and_Linux\\myvenv\\Lib\\site-packages\\openai\\types\\webhooks\\response_completed_webhook_event.py',
   'PYMODULE'),
  ('openai.types.webhooks.response_failed_webhook_event',
   'c:\\Users\\<USER>\\Desktop\\Mes_projets\\WritingToolsClean\\Windows_and_Linux\\myvenv\\Lib\\site-packages\\openai\\types\\webhooks\\response_failed_webhook_event.py',
   'PYMODULE'),
  ('openai.types.webhooks.response_incomplete_webhook_event',
   'c:\\Users\\<USER>\\Desktop\\Mes_projets\\WritingToolsClean\\Windows_and_Linux\\myvenv\\Lib\\site-packages\\openai\\types\\webhooks\\response_incomplete_webhook_event.py',
   'PYMODULE'),
  ('openai.types.webhooks.unwrap_webhook_event',
   'c:\\Users\\<USER>\\Desktop\\Mes_projets\\WritingToolsClean\\Windows_and_Linux\\myvenv\\Lib\\site-packages\\openai\\types\\webhooks\\unwrap_webhook_event.py',
   'PYMODULE'),
  ('openai.types.websocket_connection_options',
   'c:\\Users\\<USER>\\Desktop\\Mes_projets\\WritingToolsClean\\Windows_and_Linux\\myvenv\\Lib\\site-packages\\openai\\types\\websocket_connection_options.py',
   'PYMODULE'),
  ('openai.version',
   'c:\\Users\\<USER>\\Desktop\\Mes_projets\\WritingToolsClean\\Windows_and_Linux\\myvenv\\Lib\\site-packages\\openai\\version.py',
   'PYMODULE'),
  ('packaging',
   'c:\\Users\\<USER>\\Desktop\\Mes_projets\\WritingToolsClean\\Windows_and_Linux\\myvenv\\Lib\\site-packages\\packaging\\__init__.py',
   'PYMODULE'),
  ('packaging._elffile',
   'c:\\Users\\<USER>\\Desktop\\Mes_projets\\WritingToolsClean\\Windows_and_Linux\\myvenv\\Lib\\site-packages\\packaging\\_elffile.py',
   'PYMODULE'),
  ('packaging._manylinux',
   'c:\\Users\\<USER>\\Desktop\\Mes_projets\\WritingToolsClean\\Windows_and_Linux\\myvenv\\Lib\\site-packages\\packaging\\_manylinux.py',
   'PYMODULE'),
  ('packaging._musllinux',
   'c:\\Users\\<USER>\\Desktop\\Mes_projets\\WritingToolsClean\\Windows_and_Linux\\myvenv\\Lib\\site-packages\\packaging\\_musllinux.py',
   'PYMODULE'),
  ('packaging._parser',
   'c:\\Users\\<USER>\\Desktop\\Mes_projets\\WritingToolsClean\\Windows_and_Linux\\myvenv\\Lib\\site-packages\\packaging\\_parser.py',
   'PYMODULE'),
  ('packaging._structures',
   'c:\\Users\\<USER>\\Desktop\\Mes_projets\\WritingToolsClean\\Windows_and_Linux\\myvenv\\Lib\\site-packages\\packaging\\_structures.py',
   'PYMODULE'),
  ('packaging._tokenizer',
   'c:\\Users\\<USER>\\Desktop\\Mes_projets\\WritingToolsClean\\Windows_and_Linux\\myvenv\\Lib\\site-packages\\packaging\\_tokenizer.py',
   'PYMODULE'),
  ('packaging.licenses',
   'c:\\Users\\<USER>\\Desktop\\Mes_projets\\WritingToolsClean\\Windows_and_Linux\\myvenv\\Lib\\site-packages\\packaging\\licenses\\__init__.py',
   'PYMODULE'),
  ('packaging.licenses._spdx',
   'c:\\Users\\<USER>\\Desktop\\Mes_projets\\WritingToolsClean\\Windows_and_Linux\\myvenv\\Lib\\site-packages\\packaging\\licenses\\_spdx.py',
   'PYMODULE'),
  ('packaging.markers',
   'c:\\Users\\<USER>\\Desktop\\Mes_projets\\WritingToolsClean\\Windows_and_Linux\\myvenv\\Lib\\site-packages\\packaging\\markers.py',
   'PYMODULE'),
  ('packaging.requirements',
   'c:\\Users\\<USER>\\Desktop\\Mes_projets\\WritingToolsClean\\Windows_and_Linux\\myvenv\\Lib\\site-packages\\packaging\\requirements.py',
   'PYMODULE'),
  ('packaging.specifiers',
   'c:\\Users\\<USER>\\Desktop\\Mes_projets\\WritingToolsClean\\Windows_and_Linux\\myvenv\\Lib\\site-packages\\packaging\\specifiers.py',
   'PYMODULE'),
  ('packaging.tags',
   'c:\\Users\\<USER>\\Desktop\\Mes_projets\\WritingToolsClean\\Windows_and_Linux\\myvenv\\Lib\\site-packages\\packaging\\tags.py',
   'PYMODULE'),
  ('packaging.utils',
   'c:\\Users\\<USER>\\Desktop\\Mes_projets\\WritingToolsClean\\Windows_and_Linux\\myvenv\\Lib\\site-packages\\packaging\\utils.py',
   'PYMODULE'),
  ('packaging.version',
   'c:\\Users\\<USER>\\Desktop\\Mes_projets\\WritingToolsClean\\Windows_and_Linux\\myvenv\\Lib\\site-packages\\packaging\\version.py',
   'PYMODULE'),
  ('pathlib',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\pathlib.py',
   'PYMODULE'),
  ('pdb',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\pdb.py',
   'PYMODULE'),
  ('pickle',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\pickle.py',
   'PYMODULE'),
  ('pkgutil',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\pkgutil.py',
   'PYMODULE'),
  ('platform',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\platform.py',
   'PYMODULE'),
  ('pprint',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\pprint.py',
   'PYMODULE'),
  ('proto',
   'c:\\Users\\<USER>\\Desktop\\Mes_projets\\WritingToolsClean\\Windows_and_Linux\\myvenv\\Lib\\site-packages\\proto\\__init__.py',
   'PYMODULE'),
  ('proto._file_info',
   'c:\\Users\\<USER>\\Desktop\\Mes_projets\\WritingToolsClean\\Windows_and_Linux\\myvenv\\Lib\\site-packages\\proto\\_file_info.py',
   'PYMODULE'),
  ('proto._package_info',
   'c:\\Users\\<USER>\\Desktop\\Mes_projets\\WritingToolsClean\\Windows_and_Linux\\myvenv\\Lib\\site-packages\\proto\\_package_info.py',
   'PYMODULE'),
  ('proto.datetime_helpers',
   'c:\\Users\\<USER>\\Desktop\\Mes_projets\\WritingToolsClean\\Windows_and_Linux\\myvenv\\Lib\\site-packages\\proto\\datetime_helpers.py',
   'PYMODULE'),
  ('proto.enums',
   'c:\\Users\\<USER>\\Desktop\\Mes_projets\\WritingToolsClean\\Windows_and_Linux\\myvenv\\Lib\\site-packages\\proto\\enums.py',
   'PYMODULE'),
  ('proto.fields',
   'c:\\Users\\<USER>\\Desktop\\Mes_projets\\WritingToolsClean\\Windows_and_Linux\\myvenv\\Lib\\site-packages\\proto\\fields.py',
   'PYMODULE'),
  ('proto.marshal',
   'c:\\Users\\<USER>\\Desktop\\Mes_projets\\WritingToolsClean\\Windows_and_Linux\\myvenv\\Lib\\site-packages\\proto\\marshal\\__init__.py',
   'PYMODULE'),
  ('proto.marshal.collections',
   'c:\\Users\\<USER>\\Desktop\\Mes_projets\\WritingToolsClean\\Windows_and_Linux\\myvenv\\Lib\\site-packages\\proto\\marshal\\collections\\__init__.py',
   'PYMODULE'),
  ('proto.marshal.collections.maps',
   'c:\\Users\\<USER>\\Desktop\\Mes_projets\\WritingToolsClean\\Windows_and_Linux\\myvenv\\Lib\\site-packages\\proto\\marshal\\collections\\maps.py',
   'PYMODULE'),
  ('proto.marshal.collections.repeated',
   'c:\\Users\\<USER>\\Desktop\\Mes_projets\\WritingToolsClean\\Windows_and_Linux\\myvenv\\Lib\\site-packages\\proto\\marshal\\collections\\repeated.py',
   'PYMODULE'),
  ('proto.marshal.compat',
   'c:\\Users\\<USER>\\Desktop\\Mes_projets\\WritingToolsClean\\Windows_and_Linux\\myvenv\\Lib\\site-packages\\proto\\marshal\\compat.py',
   'PYMODULE'),
  ('proto.marshal.marshal',
   'c:\\Users\\<USER>\\Desktop\\Mes_projets\\WritingToolsClean\\Windows_and_Linux\\myvenv\\Lib\\site-packages\\proto\\marshal\\marshal.py',
   'PYMODULE'),
  ('proto.marshal.rules',
   'c:\\Users\\<USER>\\Desktop\\Mes_projets\\WritingToolsClean\\Windows_and_Linux\\myvenv\\Lib\\site-packages\\proto\\marshal\\rules\\__init__.py',
   'PYMODULE'),
  ('proto.marshal.rules.bytes',
   'c:\\Users\\<USER>\\Desktop\\Mes_projets\\WritingToolsClean\\Windows_and_Linux\\myvenv\\Lib\\site-packages\\proto\\marshal\\rules\\bytes.py',
   'PYMODULE'),
  ('proto.marshal.rules.dates',
   'c:\\Users\\<USER>\\Desktop\\Mes_projets\\WritingToolsClean\\Windows_and_Linux\\myvenv\\Lib\\site-packages\\proto\\marshal\\rules\\dates.py',
   'PYMODULE'),
  ('proto.marshal.rules.enums',
   'c:\\Users\\<USER>\\Desktop\\Mes_projets\\WritingToolsClean\\Windows_and_Linux\\myvenv\\Lib\\site-packages\\proto\\marshal\\rules\\enums.py',
   'PYMODULE'),
  ('proto.marshal.rules.field_mask',
   'c:\\Users\\<USER>\\Desktop\\Mes_projets\\WritingToolsClean\\Windows_and_Linux\\myvenv\\Lib\\site-packages\\proto\\marshal\\rules\\field_mask.py',
   'PYMODULE'),
  ('proto.marshal.rules.message',
   'c:\\Users\\<USER>\\Desktop\\Mes_projets\\WritingToolsClean\\Windows_and_Linux\\myvenv\\Lib\\site-packages\\proto\\marshal\\rules\\message.py',
   'PYMODULE'),
  ('proto.marshal.rules.stringy_numbers',
   'c:\\Users\\<USER>\\Desktop\\Mes_projets\\WritingToolsClean\\Windows_and_Linux\\myvenv\\Lib\\site-packages\\proto\\marshal\\rules\\stringy_numbers.py',
   'PYMODULE'),
  ('proto.marshal.rules.struct',
   'c:\\Users\\<USER>\\Desktop\\Mes_projets\\WritingToolsClean\\Windows_and_Linux\\myvenv\\Lib\\site-packages\\proto\\marshal\\rules\\struct.py',
   'PYMODULE'),
  ('proto.marshal.rules.wrappers',
   'c:\\Users\\<USER>\\Desktop\\Mes_projets\\WritingToolsClean\\Windows_and_Linux\\myvenv\\Lib\\site-packages\\proto\\marshal\\rules\\wrappers.py',
   'PYMODULE'),
  ('proto.message',
   'c:\\Users\\<USER>\\Desktop\\Mes_projets\\WritingToolsClean\\Windows_and_Linux\\myvenv\\Lib\\site-packages\\proto\\message.py',
   'PYMODULE'),
  ('proto.modules',
   'c:\\Users\\<USER>\\Desktop\\Mes_projets\\WritingToolsClean\\Windows_and_Linux\\myvenv\\Lib\\site-packages\\proto\\modules.py',
   'PYMODULE'),
  ('proto.primitives',
   'c:\\Users\\<USER>\\Desktop\\Mes_projets\\WritingToolsClean\\Windows_and_Linux\\myvenv\\Lib\\site-packages\\proto\\primitives.py',
   'PYMODULE'),
  ('proto.utils',
   'c:\\Users\\<USER>\\Desktop\\Mes_projets\\WritingToolsClean\\Windows_and_Linux\\myvenv\\Lib\\site-packages\\proto\\utils.py',
   'PYMODULE'),
  ('proto.version',
   'c:\\Users\\<USER>\\Desktop\\Mes_projets\\WritingToolsClean\\Windows_and_Linux\\myvenv\\Lib\\site-packages\\proto\\version.py',
   'PYMODULE'),
  ('py_compile',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\py_compile.py',
   'PYMODULE'),
  ('pyasn1',
   'c:\\Users\\<USER>\\Desktop\\Mes_projets\\WritingToolsClean\\Windows_and_Linux\\myvenv\\Lib\\site-packages\\pyasn1\\__init__.py',
   'PYMODULE'),
  ('pyasn1.codec',
   'c:\\Users\\<USER>\\Desktop\\Mes_projets\\WritingToolsClean\\Windows_and_Linux\\myvenv\\Lib\\site-packages\\pyasn1\\codec\\__init__.py',
   'PYMODULE'),
  ('pyasn1.codec.ber',
   'c:\\Users\\<USER>\\Desktop\\Mes_projets\\WritingToolsClean\\Windows_and_Linux\\myvenv\\Lib\\site-packages\\pyasn1\\codec\\ber\\__init__.py',
   'PYMODULE'),
  ('pyasn1.codec.ber.decoder',
   'c:\\Users\\<USER>\\Desktop\\Mes_projets\\WritingToolsClean\\Windows_and_Linux\\myvenv\\Lib\\site-packages\\pyasn1\\codec\\ber\\decoder.py',
   'PYMODULE'),
  ('pyasn1.codec.ber.encoder',
   'c:\\Users\\<USER>\\Desktop\\Mes_projets\\WritingToolsClean\\Windows_and_Linux\\myvenv\\Lib\\site-packages\\pyasn1\\codec\\ber\\encoder.py',
   'PYMODULE'),
  ('pyasn1.codec.ber.eoo',
   'c:\\Users\\<USER>\\Desktop\\Mes_projets\\WritingToolsClean\\Windows_and_Linux\\myvenv\\Lib\\site-packages\\pyasn1\\codec\\ber\\eoo.py',
   'PYMODULE'),
  ('pyasn1.codec.cer',
   'c:\\Users\\<USER>\\Desktop\\Mes_projets\\WritingToolsClean\\Windows_and_Linux\\myvenv\\Lib\\site-packages\\pyasn1\\codec\\cer\\__init__.py',
   'PYMODULE'),
  ('pyasn1.codec.cer.decoder',
   'c:\\Users\\<USER>\\Desktop\\Mes_projets\\WritingToolsClean\\Windows_and_Linux\\myvenv\\Lib\\site-packages\\pyasn1\\codec\\cer\\decoder.py',
   'PYMODULE'),
  ('pyasn1.codec.cer.encoder',
   'c:\\Users\\<USER>\\Desktop\\Mes_projets\\WritingToolsClean\\Windows_and_Linux\\myvenv\\Lib\\site-packages\\pyasn1\\codec\\cer\\encoder.py',
   'PYMODULE'),
  ('pyasn1.codec.der',
   'c:\\Users\\<USER>\\Desktop\\Mes_projets\\WritingToolsClean\\Windows_and_Linux\\myvenv\\Lib\\site-packages\\pyasn1\\codec\\der\\__init__.py',
   'PYMODULE'),
  ('pyasn1.codec.der.decoder',
   'c:\\Users\\<USER>\\Desktop\\Mes_projets\\WritingToolsClean\\Windows_and_Linux\\myvenv\\Lib\\site-packages\\pyasn1\\codec\\der\\decoder.py',
   'PYMODULE'),
  ('pyasn1.codec.der.encoder',
   'c:\\Users\\<USER>\\Desktop\\Mes_projets\\WritingToolsClean\\Windows_and_Linux\\myvenv\\Lib\\site-packages\\pyasn1\\codec\\der\\encoder.py',
   'PYMODULE'),
  ('pyasn1.codec.streaming',
   'c:\\Users\\<USER>\\Desktop\\Mes_projets\\WritingToolsClean\\Windows_and_Linux\\myvenv\\Lib\\site-packages\\pyasn1\\codec\\streaming.py',
   'PYMODULE'),
  ('pyasn1.compat',
   'c:\\Users\\<USER>\\Desktop\\Mes_projets\\WritingToolsClean\\Windows_and_Linux\\myvenv\\Lib\\site-packages\\pyasn1\\compat\\__init__.py',
   'PYMODULE'),
  ('pyasn1.compat.integer',
   'c:\\Users\\<USER>\\Desktop\\Mes_projets\\WritingToolsClean\\Windows_and_Linux\\myvenv\\Lib\\site-packages\\pyasn1\\compat\\integer.py',
   'PYMODULE'),
  ('pyasn1.debug',
   'c:\\Users\\<USER>\\Desktop\\Mes_projets\\WritingToolsClean\\Windows_and_Linux\\myvenv\\Lib\\site-packages\\pyasn1\\debug.py',
   'PYMODULE'),
  ('pyasn1.error',
   'c:\\Users\\<USER>\\Desktop\\Mes_projets\\WritingToolsClean\\Windows_and_Linux\\myvenv\\Lib\\site-packages\\pyasn1\\error.py',
   'PYMODULE'),
  ('pyasn1.type',
   'c:\\Users\\<USER>\\Desktop\\Mes_projets\\WritingToolsClean\\Windows_and_Linux\\myvenv\\Lib\\site-packages\\pyasn1\\type\\__init__.py',
   'PYMODULE'),
  ('pyasn1.type.base',
   'c:\\Users\\<USER>\\Desktop\\Mes_projets\\WritingToolsClean\\Windows_and_Linux\\myvenv\\Lib\\site-packages\\pyasn1\\type\\base.py',
   'PYMODULE'),
  ('pyasn1.type.char',
   'c:\\Users\\<USER>\\Desktop\\Mes_projets\\WritingToolsClean\\Windows_and_Linux\\myvenv\\Lib\\site-packages\\pyasn1\\type\\char.py',
   'PYMODULE'),
  ('pyasn1.type.constraint',
   'c:\\Users\\<USER>\\Desktop\\Mes_projets\\WritingToolsClean\\Windows_and_Linux\\myvenv\\Lib\\site-packages\\pyasn1\\type\\constraint.py',
   'PYMODULE'),
  ('pyasn1.type.error',
   'c:\\Users\\<USER>\\Desktop\\Mes_projets\\WritingToolsClean\\Windows_and_Linux\\myvenv\\Lib\\site-packages\\pyasn1\\type\\error.py',
   'PYMODULE'),
  ('pyasn1.type.namedtype',
   'c:\\Users\\<USER>\\Desktop\\Mes_projets\\WritingToolsClean\\Windows_and_Linux\\myvenv\\Lib\\site-packages\\pyasn1\\type\\namedtype.py',
   'PYMODULE'),
  ('pyasn1.type.namedval',
   'c:\\Users\\<USER>\\Desktop\\Mes_projets\\WritingToolsClean\\Windows_and_Linux\\myvenv\\Lib\\site-packages\\pyasn1\\type\\namedval.py',
   'PYMODULE'),
  ('pyasn1.type.opentype',
   'c:\\Users\\<USER>\\Desktop\\Mes_projets\\WritingToolsClean\\Windows_and_Linux\\myvenv\\Lib\\site-packages\\pyasn1\\type\\opentype.py',
   'PYMODULE'),
  ('pyasn1.type.tag',
   'c:\\Users\\<USER>\\Desktop\\Mes_projets\\WritingToolsClean\\Windows_and_Linux\\myvenv\\Lib\\site-packages\\pyasn1\\type\\tag.py',
   'PYMODULE'),
  ('pyasn1.type.tagmap',
   'c:\\Users\\<USER>\\Desktop\\Mes_projets\\WritingToolsClean\\Windows_and_Linux\\myvenv\\Lib\\site-packages\\pyasn1\\type\\tagmap.py',
   'PYMODULE'),
  ('pyasn1.type.univ',
   'c:\\Users\\<USER>\\Desktop\\Mes_projets\\WritingToolsClean\\Windows_and_Linux\\myvenv\\Lib\\site-packages\\pyasn1\\type\\univ.py',
   'PYMODULE'),
  ('pyasn1.type.useful',
   'c:\\Users\\<USER>\\Desktop\\Mes_projets\\WritingToolsClean\\Windows_and_Linux\\myvenv\\Lib\\site-packages\\pyasn1\\type\\useful.py',
   'PYMODULE'),
  ('pyasn1_modules',
   'c:\\Users\\<USER>\\Desktop\\Mes_projets\\WritingToolsClean\\Windows_and_Linux\\myvenv\\Lib\\site-packages\\pyasn1_modules\\__init__.py',
   'PYMODULE'),
  ('pyasn1_modules.pem',
   'c:\\Users\\<USER>\\Desktop\\Mes_projets\\WritingToolsClean\\Windows_and_Linux\\myvenv\\Lib\\site-packages\\pyasn1_modules\\pem.py',
   'PYMODULE'),
  ('pyasn1_modules.rfc2251',
   'c:\\Users\\<USER>\\Desktop\\Mes_projets\\WritingToolsClean\\Windows_and_Linux\\myvenv\\Lib\\site-packages\\pyasn1_modules\\rfc2251.py',
   'PYMODULE'),
  ('pyasn1_modules.rfc2459',
   'c:\\Users\\<USER>\\Desktop\\Mes_projets\\WritingToolsClean\\Windows_and_Linux\\myvenv\\Lib\\site-packages\\pyasn1_modules\\rfc2459.py',
   'PYMODULE'),
  ('pyasn1_modules.rfc5208',
   'c:\\Users\\<USER>\\Desktop\\Mes_projets\\WritingToolsClean\\Windows_and_Linux\\myvenv\\Lib\\site-packages\\pyasn1_modules\\rfc5208.py',
   'PYMODULE'),
  ('pydantic',
   'c:\\Users\\<USER>\\Desktop\\Mes_projets\\WritingToolsClean\\Windows_and_Linux\\myvenv\\Lib\\site-packages\\pydantic\\__init__.py',
   'PYMODULE'),
  ('pydantic._internal',
   'c:\\Users\\<USER>\\Desktop\\Mes_projets\\WritingToolsClean\\Windows_and_Linux\\myvenv\\Lib\\site-packages\\pydantic\\_internal\\__init__.py',
   'PYMODULE'),
  ('pydantic._internal._config',
   'c:\\Users\\<USER>\\Desktop\\Mes_projets\\WritingToolsClean\\Windows_and_Linux\\myvenv\\Lib\\site-packages\\pydantic\\_internal\\_config.py',
   'PYMODULE'),
  ('pydantic._internal._core_metadata',
   'c:\\Users\\<USER>\\Desktop\\Mes_projets\\WritingToolsClean\\Windows_and_Linux\\myvenv\\Lib\\site-packages\\pydantic\\_internal\\_core_metadata.py',
   'PYMODULE'),
  ('pydantic._internal._core_utils',
   'c:\\Users\\<USER>\\Desktop\\Mes_projets\\WritingToolsClean\\Windows_and_Linux\\myvenv\\Lib\\site-packages\\pydantic\\_internal\\_core_utils.py',
   'PYMODULE'),
  ('pydantic._internal._dataclasses',
   'c:\\Users\\<USER>\\Desktop\\Mes_projets\\WritingToolsClean\\Windows_and_Linux\\myvenv\\Lib\\site-packages\\pydantic\\_internal\\_dataclasses.py',
   'PYMODULE'),
  ('pydantic._internal._decorators',
   'c:\\Users\\<USER>\\Desktop\\Mes_projets\\WritingToolsClean\\Windows_and_Linux\\myvenv\\Lib\\site-packages\\pydantic\\_internal\\_decorators.py',
   'PYMODULE'),
  ('pydantic._internal._decorators_v1',
   'c:\\Users\\<USER>\\Desktop\\Mes_projets\\WritingToolsClean\\Windows_and_Linux\\myvenv\\Lib\\site-packages\\pydantic\\_internal\\_decorators_v1.py',
   'PYMODULE'),
  ('pydantic._internal._discriminated_union',
   'c:\\Users\\<USER>\\Desktop\\Mes_projets\\WritingToolsClean\\Windows_and_Linux\\myvenv\\Lib\\site-packages\\pydantic\\_internal\\_discriminated_union.py',
   'PYMODULE'),
  ('pydantic._internal._docs_extraction',
   'c:\\Users\\<USER>\\Desktop\\Mes_projets\\WritingToolsClean\\Windows_and_Linux\\myvenv\\Lib\\site-packages\\pydantic\\_internal\\_docs_extraction.py',
   'PYMODULE'),
  ('pydantic._internal._fields',
   'c:\\Users\\<USER>\\Desktop\\Mes_projets\\WritingToolsClean\\Windows_and_Linux\\myvenv\\Lib\\site-packages\\pydantic\\_internal\\_fields.py',
   'PYMODULE'),
  ('pydantic._internal._forward_ref',
   'c:\\Users\\<USER>\\Desktop\\Mes_projets\\WritingToolsClean\\Windows_and_Linux\\myvenv\\Lib\\site-packages\\pydantic\\_internal\\_forward_ref.py',
   'PYMODULE'),
  ('pydantic._internal._generate_schema',
   'c:\\Users\\<USER>\\Desktop\\Mes_projets\\WritingToolsClean\\Windows_and_Linux\\myvenv\\Lib\\site-packages\\pydantic\\_internal\\_generate_schema.py',
   'PYMODULE'),
  ('pydantic._internal._generics',
   'c:\\Users\\<USER>\\Desktop\\Mes_projets\\WritingToolsClean\\Windows_and_Linux\\myvenv\\Lib\\site-packages\\pydantic\\_internal\\_generics.py',
   'PYMODULE'),
  ('pydantic._internal._git',
   'c:\\Users\\<USER>\\Desktop\\Mes_projets\\WritingToolsClean\\Windows_and_Linux\\myvenv\\Lib\\site-packages\\pydantic\\_internal\\_git.py',
   'PYMODULE'),
  ('pydantic._internal._import_utils',
   'c:\\Users\\<USER>\\Desktop\\Mes_projets\\WritingToolsClean\\Windows_and_Linux\\myvenv\\Lib\\site-packages\\pydantic\\_internal\\_import_utils.py',
   'PYMODULE'),
  ('pydantic._internal._internal_dataclass',
   'c:\\Users\\<USER>\\Desktop\\Mes_projets\\WritingToolsClean\\Windows_and_Linux\\myvenv\\Lib\\site-packages\\pydantic\\_internal\\_internal_dataclass.py',
   'PYMODULE'),
  ('pydantic._internal._known_annotated_metadata',
   'c:\\Users\\<USER>\\Desktop\\Mes_projets\\WritingToolsClean\\Windows_and_Linux\\myvenv\\Lib\\site-packages\\pydantic\\_internal\\_known_annotated_metadata.py',
   'PYMODULE'),
  ('pydantic._internal._mock_val_ser',
   'c:\\Users\\<USER>\\Desktop\\Mes_projets\\WritingToolsClean\\Windows_and_Linux\\myvenv\\Lib\\site-packages\\pydantic\\_internal\\_mock_val_ser.py',
   'PYMODULE'),
  ('pydantic._internal._model_construction',
   'c:\\Users\\<USER>\\Desktop\\Mes_projets\\WritingToolsClean\\Windows_and_Linux\\myvenv\\Lib\\site-packages\\pydantic\\_internal\\_model_construction.py',
   'PYMODULE'),
  ('pydantic._internal._namespace_utils',
   'c:\\Users\\<USER>\\Desktop\\Mes_projets\\WritingToolsClean\\Windows_and_Linux\\myvenv\\Lib\\site-packages\\pydantic\\_internal\\_namespace_utils.py',
   'PYMODULE'),
  ('pydantic._internal._repr',
   'c:\\Users\\<USER>\\Desktop\\Mes_projets\\WritingToolsClean\\Windows_and_Linux\\myvenv\\Lib\\site-packages\\pydantic\\_internal\\_repr.py',
   'PYMODULE'),
  ('pydantic._internal._schema_gather',
   'c:\\Users\\<USER>\\Desktop\\Mes_projets\\WritingToolsClean\\Windows_and_Linux\\myvenv\\Lib\\site-packages\\pydantic\\_internal\\_schema_gather.py',
   'PYMODULE'),
  ('pydantic._internal._schema_generation_shared',
   'c:\\Users\\<USER>\\Desktop\\Mes_projets\\WritingToolsClean\\Windows_and_Linux\\myvenv\\Lib\\site-packages\\pydantic\\_internal\\_schema_generation_shared.py',
   'PYMODULE'),
  ('pydantic._internal._serializers',
   'c:\\Users\\<USER>\\Desktop\\Mes_projets\\WritingToolsClean\\Windows_and_Linux\\myvenv\\Lib\\site-packages\\pydantic\\_internal\\_serializers.py',
   'PYMODULE'),
  ('pydantic._internal._signature',
   'c:\\Users\\<USER>\\Desktop\\Mes_projets\\WritingToolsClean\\Windows_and_Linux\\myvenv\\Lib\\site-packages\\pydantic\\_internal\\_signature.py',
   'PYMODULE'),
  ('pydantic._internal._typing_extra',
   'c:\\Users\\<USER>\\Desktop\\Mes_projets\\WritingToolsClean\\Windows_and_Linux\\myvenv\\Lib\\site-packages\\pydantic\\_internal\\_typing_extra.py',
   'PYMODULE'),
  ('pydantic._internal._utils',
   'c:\\Users\\<USER>\\Desktop\\Mes_projets\\WritingToolsClean\\Windows_and_Linux\\myvenv\\Lib\\site-packages\\pydantic\\_internal\\_utils.py',
   'PYMODULE'),
  ('pydantic._internal._validate_call',
   'c:\\Users\\<USER>\\Desktop\\Mes_projets\\WritingToolsClean\\Windows_and_Linux\\myvenv\\Lib\\site-packages\\pydantic\\_internal\\_validate_call.py',
   'PYMODULE'),
  ('pydantic._internal._validators',
   'c:\\Users\\<USER>\\Desktop\\Mes_projets\\WritingToolsClean\\Windows_and_Linux\\myvenv\\Lib\\site-packages\\pydantic\\_internal\\_validators.py',
   'PYMODULE'),
  ('pydantic._migration',
   'c:\\Users\\<USER>\\Desktop\\Mes_projets\\WritingToolsClean\\Windows_and_Linux\\myvenv\\Lib\\site-packages\\pydantic\\_migration.py',
   'PYMODULE'),
  ('pydantic.alias_generators',
   'c:\\Users\\<USER>\\Desktop\\Mes_projets\\WritingToolsClean\\Windows_and_Linux\\myvenv\\Lib\\site-packages\\pydantic\\alias_generators.py',
   'PYMODULE'),
  ('pydantic.aliases',
   'c:\\Users\\<USER>\\Desktop\\Mes_projets\\WritingToolsClean\\Windows_and_Linux\\myvenv\\Lib\\site-packages\\pydantic\\aliases.py',
   'PYMODULE'),
  ('pydantic.annotated_handlers',
   'c:\\Users\\<USER>\\Desktop\\Mes_projets\\WritingToolsClean\\Windows_and_Linux\\myvenv\\Lib\\site-packages\\pydantic\\annotated_handlers.py',
   'PYMODULE'),
  ('pydantic.class_validators',
   'c:\\Users\\<USER>\\Desktop\\Mes_projets\\WritingToolsClean\\Windows_and_Linux\\myvenv\\Lib\\site-packages\\pydantic\\class_validators.py',
   'PYMODULE'),
  ('pydantic.color',
   'c:\\Users\\<USER>\\Desktop\\Mes_projets\\WritingToolsClean\\Windows_and_Linux\\myvenv\\Lib\\site-packages\\pydantic\\color.py',
   'PYMODULE'),
  ('pydantic.config',
   'c:\\Users\\<USER>\\Desktop\\Mes_projets\\WritingToolsClean\\Windows_and_Linux\\myvenv\\Lib\\site-packages\\pydantic\\config.py',
   'PYMODULE'),
  ('pydantic.dataclasses',
   'c:\\Users\\<USER>\\Desktop\\Mes_projets\\WritingToolsClean\\Windows_and_Linux\\myvenv\\Lib\\site-packages\\pydantic\\dataclasses.py',
   'PYMODULE'),
  ('pydantic.datetime_parse',
   'c:\\Users\\<USER>\\Desktop\\Mes_projets\\WritingToolsClean\\Windows_and_Linux\\myvenv\\Lib\\site-packages\\pydantic\\datetime_parse.py',
   'PYMODULE'),
  ('pydantic.decorator',
   'c:\\Users\\<USER>\\Desktop\\Mes_projets\\WritingToolsClean\\Windows_and_Linux\\myvenv\\Lib\\site-packages\\pydantic\\decorator.py',
   'PYMODULE'),
  ('pydantic.deprecated',
   'c:\\Users\\<USER>\\Desktop\\Mes_projets\\WritingToolsClean\\Windows_and_Linux\\myvenv\\Lib\\site-packages\\pydantic\\deprecated\\__init__.py',
   'PYMODULE'),
  ('pydantic.deprecated.class_validators',
   'c:\\Users\\<USER>\\Desktop\\Mes_projets\\WritingToolsClean\\Windows_and_Linux\\myvenv\\Lib\\site-packages\\pydantic\\deprecated\\class_validators.py',
   'PYMODULE'),
  ('pydantic.deprecated.config',
   'c:\\Users\\<USER>\\Desktop\\Mes_projets\\WritingToolsClean\\Windows_and_Linux\\myvenv\\Lib\\site-packages\\pydantic\\deprecated\\config.py',
   'PYMODULE'),
  ('pydantic.deprecated.copy_internals',
   'c:\\Users\\<USER>\\Desktop\\Mes_projets\\WritingToolsClean\\Windows_and_Linux\\myvenv\\Lib\\site-packages\\pydantic\\deprecated\\copy_internals.py',
   'PYMODULE'),
  ('pydantic.deprecated.decorator',
   'c:\\Users\\<USER>\\Desktop\\Mes_projets\\WritingToolsClean\\Windows_and_Linux\\myvenv\\Lib\\site-packages\\pydantic\\deprecated\\decorator.py',
   'PYMODULE'),
  ('pydantic.deprecated.json',
   'c:\\Users\\<USER>\\Desktop\\Mes_projets\\WritingToolsClean\\Windows_and_Linux\\myvenv\\Lib\\site-packages\\pydantic\\deprecated\\json.py',
   'PYMODULE'),
  ('pydantic.deprecated.parse',
   'c:\\Users\\<USER>\\Desktop\\Mes_projets\\WritingToolsClean\\Windows_and_Linux\\myvenv\\Lib\\site-packages\\pydantic\\deprecated\\parse.py',
   'PYMODULE'),
  ('pydantic.deprecated.tools',
   'c:\\Users\\<USER>\\Desktop\\Mes_projets\\WritingToolsClean\\Windows_and_Linux\\myvenv\\Lib\\site-packages\\pydantic\\deprecated\\tools.py',
   'PYMODULE'),
  ('pydantic.env_settings',
   'c:\\Users\\<USER>\\Desktop\\Mes_projets\\WritingToolsClean\\Windows_and_Linux\\myvenv\\Lib\\site-packages\\pydantic\\env_settings.py',
   'PYMODULE'),
  ('pydantic.error_wrappers',
   'c:\\Users\\<USER>\\Desktop\\Mes_projets\\WritingToolsClean\\Windows_and_Linux\\myvenv\\Lib\\site-packages\\pydantic\\error_wrappers.py',
   'PYMODULE'),
  ('pydantic.errors',
   'c:\\Users\\<USER>\\Desktop\\Mes_projets\\WritingToolsClean\\Windows_and_Linux\\myvenv\\Lib\\site-packages\\pydantic\\errors.py',
   'PYMODULE'),
  ('pydantic.experimental',
   'c:\\Users\\<USER>\\Desktop\\Mes_projets\\WritingToolsClean\\Windows_and_Linux\\myvenv\\Lib\\site-packages\\pydantic\\experimental\\__init__.py',
   'PYMODULE'),
  ('pydantic.experimental.arguments_schema',
   'c:\\Users\\<USER>\\Desktop\\Mes_projets\\WritingToolsClean\\Windows_and_Linux\\myvenv\\Lib\\site-packages\\pydantic\\experimental\\arguments_schema.py',
   'PYMODULE'),
  ('pydantic.experimental.pipeline',
   'c:\\Users\\<USER>\\Desktop\\Mes_projets\\WritingToolsClean\\Windows_and_Linux\\myvenv\\Lib\\site-packages\\pydantic\\experimental\\pipeline.py',
   'PYMODULE'),
  ('pydantic.fields',
   'c:\\Users\\<USER>\\Desktop\\Mes_projets\\WritingToolsClean\\Windows_and_Linux\\myvenv\\Lib\\site-packages\\pydantic\\fields.py',
   'PYMODULE'),
  ('pydantic.functional_serializers',
   'c:\\Users\\<USER>\\Desktop\\Mes_projets\\WritingToolsClean\\Windows_and_Linux\\myvenv\\Lib\\site-packages\\pydantic\\functional_serializers.py',
   'PYMODULE'),
  ('pydantic.functional_validators',
   'c:\\Users\\<USER>\\Desktop\\Mes_projets\\WritingToolsClean\\Windows_and_Linux\\myvenv\\Lib\\site-packages\\pydantic\\functional_validators.py',
   'PYMODULE'),
  ('pydantic.generics',
   'c:\\Users\\<USER>\\Desktop\\Mes_projets\\WritingToolsClean\\Windows_and_Linux\\myvenv\\Lib\\site-packages\\pydantic\\generics.py',
   'PYMODULE'),
  ('pydantic.json',
   'c:\\Users\\<USER>\\Desktop\\Mes_projets\\WritingToolsClean\\Windows_and_Linux\\myvenv\\Lib\\site-packages\\pydantic\\json.py',
   'PYMODULE'),
  ('pydantic.json_schema',
   'c:\\Users\\<USER>\\Desktop\\Mes_projets\\WritingToolsClean\\Windows_and_Linux\\myvenv\\Lib\\site-packages\\pydantic\\json_schema.py',
   'PYMODULE'),
  ('pydantic.main',
   'c:\\Users\\<USER>\\Desktop\\Mes_projets\\WritingToolsClean\\Windows_and_Linux\\myvenv\\Lib\\site-packages\\pydantic\\main.py',
   'PYMODULE'),
  ('pydantic.mypy',
   'c:\\Users\\<USER>\\Desktop\\Mes_projets\\WritingToolsClean\\Windows_and_Linux\\myvenv\\Lib\\site-packages\\pydantic\\mypy.py',
   'PYMODULE'),
  ('pydantic.networks',
   'c:\\Users\\<USER>\\Desktop\\Mes_projets\\WritingToolsClean\\Windows_and_Linux\\myvenv\\Lib\\site-packages\\pydantic\\networks.py',
   'PYMODULE'),
  ('pydantic.parse',
   'c:\\Users\\<USER>\\Desktop\\Mes_projets\\WritingToolsClean\\Windows_and_Linux\\myvenv\\Lib\\site-packages\\pydantic\\parse.py',
   'PYMODULE'),
  ('pydantic.plugin',
   'c:\\Users\\<USER>\\Desktop\\Mes_projets\\WritingToolsClean\\Windows_and_Linux\\myvenv\\Lib\\site-packages\\pydantic\\plugin\\__init__.py',
   'PYMODULE'),
  ('pydantic.plugin._loader',
   'c:\\Users\\<USER>\\Desktop\\Mes_projets\\WritingToolsClean\\Windows_and_Linux\\myvenv\\Lib\\site-packages\\pydantic\\plugin\\_loader.py',
   'PYMODULE'),
  ('pydantic.plugin._schema_validator',
   'c:\\Users\\<USER>\\Desktop\\Mes_projets\\WritingToolsClean\\Windows_and_Linux\\myvenv\\Lib\\site-packages\\pydantic\\plugin\\_schema_validator.py',
   'PYMODULE'),
  ('pydantic.root_model',
   'c:\\Users\\<USER>\\Desktop\\Mes_projets\\WritingToolsClean\\Windows_and_Linux\\myvenv\\Lib\\site-packages\\pydantic\\root_model.py',
   'PYMODULE'),
  ('pydantic.schema',
   'c:\\Users\\<USER>\\Desktop\\Mes_projets\\WritingToolsClean\\Windows_and_Linux\\myvenv\\Lib\\site-packages\\pydantic\\schema.py',
   'PYMODULE'),
  ('pydantic.tools',
   'c:\\Users\\<USER>\\Desktop\\Mes_projets\\WritingToolsClean\\Windows_and_Linux\\myvenv\\Lib\\site-packages\\pydantic\\tools.py',
   'PYMODULE'),
  ('pydantic.type_adapter',
   'c:\\Users\\<USER>\\Desktop\\Mes_projets\\WritingToolsClean\\Windows_and_Linux\\myvenv\\Lib\\site-packages\\pydantic\\type_adapter.py',
   'PYMODULE'),
  ('pydantic.types',
   'c:\\Users\\<USER>\\Desktop\\Mes_projets\\WritingToolsClean\\Windows_and_Linux\\myvenv\\Lib\\site-packages\\pydantic\\types.py',
   'PYMODULE'),
  ('pydantic.typing',
   'c:\\Users\\<USER>\\Desktop\\Mes_projets\\WritingToolsClean\\Windows_and_Linux\\myvenv\\Lib\\site-packages\\pydantic\\typing.py',
   'PYMODULE'),
  ('pydantic.utils',
   'c:\\Users\\<USER>\\Desktop\\Mes_projets\\WritingToolsClean\\Windows_and_Linux\\myvenv\\Lib\\site-packages\\pydantic\\utils.py',
   'PYMODULE'),
  ('pydantic.v1',
   'c:\\Users\\<USER>\\Desktop\\Mes_projets\\WritingToolsClean\\Windows_and_Linux\\myvenv\\Lib\\site-packages\\pydantic\\v1\\__init__.py',
   'PYMODULE'),
  ('pydantic.v1._hypothesis_plugin',
   'c:\\Users\\<USER>\\Desktop\\Mes_projets\\WritingToolsClean\\Windows_and_Linux\\myvenv\\Lib\\site-packages\\pydantic\\v1\\_hypothesis_plugin.py',
   'PYMODULE'),
  ('pydantic.v1.annotated_types',
   'c:\\Users\\<USER>\\Desktop\\Mes_projets\\WritingToolsClean\\Windows_and_Linux\\myvenv\\Lib\\site-packages\\pydantic\\v1\\annotated_types.py',
   'PYMODULE'),
  ('pydantic.v1.class_validators',
   'c:\\Users\\<USER>\\Desktop\\Mes_projets\\WritingToolsClean\\Windows_and_Linux\\myvenv\\Lib\\site-packages\\pydantic\\v1\\class_validators.py',
   'PYMODULE'),
  ('pydantic.v1.color',
   'c:\\Users\\<USER>\\Desktop\\Mes_projets\\WritingToolsClean\\Windows_and_Linux\\myvenv\\Lib\\site-packages\\pydantic\\v1\\color.py',
   'PYMODULE'),
  ('pydantic.v1.config',
   'c:\\Users\\<USER>\\Desktop\\Mes_projets\\WritingToolsClean\\Windows_and_Linux\\myvenv\\Lib\\site-packages\\pydantic\\v1\\config.py',
   'PYMODULE'),
  ('pydantic.v1.dataclasses',
   'c:\\Users\\<USER>\\Desktop\\Mes_projets\\WritingToolsClean\\Windows_and_Linux\\myvenv\\Lib\\site-packages\\pydantic\\v1\\dataclasses.py',
   'PYMODULE'),
  ('pydantic.v1.datetime_parse',
   'c:\\Users\\<USER>\\Desktop\\Mes_projets\\WritingToolsClean\\Windows_and_Linux\\myvenv\\Lib\\site-packages\\pydantic\\v1\\datetime_parse.py',
   'PYMODULE'),
  ('pydantic.v1.decorator',
   'c:\\Users\\<USER>\\Desktop\\Mes_projets\\WritingToolsClean\\Windows_and_Linux\\myvenv\\Lib\\site-packages\\pydantic\\v1\\decorator.py',
   'PYMODULE'),
  ('pydantic.v1.env_settings',
   'c:\\Users\\<USER>\\Desktop\\Mes_projets\\WritingToolsClean\\Windows_and_Linux\\myvenv\\Lib\\site-packages\\pydantic\\v1\\env_settings.py',
   'PYMODULE'),
  ('pydantic.v1.error_wrappers',
   'c:\\Users\\<USER>\\Desktop\\Mes_projets\\WritingToolsClean\\Windows_and_Linux\\myvenv\\Lib\\site-packages\\pydantic\\v1\\error_wrappers.py',
   'PYMODULE'),
  ('pydantic.v1.errors',
   'c:\\Users\\<USER>\\Desktop\\Mes_projets\\WritingToolsClean\\Windows_and_Linux\\myvenv\\Lib\\site-packages\\pydantic\\v1\\errors.py',
   'PYMODULE'),
  ('pydantic.v1.fields',
   'c:\\Users\\<USER>\\Desktop\\Mes_projets\\WritingToolsClean\\Windows_and_Linux\\myvenv\\Lib\\site-packages\\pydantic\\v1\\fields.py',
   'PYMODULE'),
  ('pydantic.v1.generics',
   'c:\\Users\\<USER>\\Desktop\\Mes_projets\\WritingToolsClean\\Windows_and_Linux\\myvenv\\Lib\\site-packages\\pydantic\\v1\\generics.py',
   'PYMODULE'),
  ('pydantic.v1.json',
   'c:\\Users\\<USER>\\Desktop\\Mes_projets\\WritingToolsClean\\Windows_and_Linux\\myvenv\\Lib\\site-packages\\pydantic\\v1\\json.py',
   'PYMODULE'),
  ('pydantic.v1.main',
   'c:\\Users\\<USER>\\Desktop\\Mes_projets\\WritingToolsClean\\Windows_and_Linux\\myvenv\\Lib\\site-packages\\pydantic\\v1\\main.py',
   'PYMODULE'),
  ('pydantic.v1.mypy',
   'c:\\Users\\<USER>\\Desktop\\Mes_projets\\WritingToolsClean\\Windows_and_Linux\\myvenv\\Lib\\site-packages\\pydantic\\v1\\mypy.py',
   'PYMODULE'),
  ('pydantic.v1.networks',
   'c:\\Users\\<USER>\\Desktop\\Mes_projets\\WritingToolsClean\\Windows_and_Linux\\myvenv\\Lib\\site-packages\\pydantic\\v1\\networks.py',
   'PYMODULE'),
  ('pydantic.v1.parse',
   'c:\\Users\\<USER>\\Desktop\\Mes_projets\\WritingToolsClean\\Windows_and_Linux\\myvenv\\Lib\\site-packages\\pydantic\\v1\\parse.py',
   'PYMODULE'),
  ('pydantic.v1.schema',
   'c:\\Users\\<USER>\\Desktop\\Mes_projets\\WritingToolsClean\\Windows_and_Linux\\myvenv\\Lib\\site-packages\\pydantic\\v1\\schema.py',
   'PYMODULE'),
  ('pydantic.v1.tools',
   'c:\\Users\\<USER>\\Desktop\\Mes_projets\\WritingToolsClean\\Windows_and_Linux\\myvenv\\Lib\\site-packages\\pydantic\\v1\\tools.py',
   'PYMODULE'),
  ('pydantic.v1.types',
   'c:\\Users\\<USER>\\Desktop\\Mes_projets\\WritingToolsClean\\Windows_and_Linux\\myvenv\\Lib\\site-packages\\pydantic\\v1\\types.py',
   'PYMODULE'),
  ('pydantic.v1.typing',
   'c:\\Users\\<USER>\\Desktop\\Mes_projets\\WritingToolsClean\\Windows_and_Linux\\myvenv\\Lib\\site-packages\\pydantic\\v1\\typing.py',
   'PYMODULE'),
  ('pydantic.v1.utils',
   'c:\\Users\\<USER>\\Desktop\\Mes_projets\\WritingToolsClean\\Windows_and_Linux\\myvenv\\Lib\\site-packages\\pydantic\\v1\\utils.py',
   'PYMODULE'),
  ('pydantic.v1.validators',
   'c:\\Users\\<USER>\\Desktop\\Mes_projets\\WritingToolsClean\\Windows_and_Linux\\myvenv\\Lib\\site-packages\\pydantic\\v1\\validators.py',
   'PYMODULE'),
  ('pydantic.v1.version',
   'c:\\Users\\<USER>\\Desktop\\Mes_projets\\WritingToolsClean\\Windows_and_Linux\\myvenv\\Lib\\site-packages\\pydantic\\v1\\version.py',
   'PYMODULE'),
  ('pydantic.validate_call_decorator',
   'c:\\Users\\<USER>\\Desktop\\Mes_projets\\WritingToolsClean\\Windows_and_Linux\\myvenv\\Lib\\site-packages\\pydantic\\validate_call_decorator.py',
   'PYMODULE'),
  ('pydantic.validators',
   'c:\\Users\\<USER>\\Desktop\\Mes_projets\\WritingToolsClean\\Windows_and_Linux\\myvenv\\Lib\\site-packages\\pydantic\\validators.py',
   'PYMODULE'),
  ('pydantic.version',
   'c:\\Users\\<USER>\\Desktop\\Mes_projets\\WritingToolsClean\\Windows_and_Linux\\myvenv\\Lib\\site-packages\\pydantic\\version.py',
   'PYMODULE'),
  ('pydantic.warnings',
   'c:\\Users\\<USER>\\Desktop\\Mes_projets\\WritingToolsClean\\Windows_and_Linux\\myvenv\\Lib\\site-packages\\pydantic\\warnings.py',
   'PYMODULE'),
  ('pydantic_core',
   'c:\\Users\\<USER>\\Desktop\\Mes_projets\\WritingToolsClean\\Windows_and_Linux\\myvenv\\Lib\\site-packages\\pydantic_core\\__init__.py',
   'PYMODULE'),
  ('pydantic_core.core_schema',
   'c:\\Users\\<USER>\\Desktop\\Mes_projets\\WritingToolsClean\\Windows_and_Linux\\myvenv\\Lib\\site-packages\\pydantic_core\\core_schema.py',
   'PYMODULE'),
  ('pydoc',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\pydoc.py',
   'PYMODULE'),
  ('pydoc_data',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\pydoc_data\\__init__.py',
   'PYMODULE'),
  ('pydoc_data.topics',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\pydoc_data\\topics.py',
   'PYMODULE'),
  ('pynput',
   'c:\\Users\\<USER>\\Desktop\\Mes_projets\\WritingToolsClean\\Windows_and_Linux\\myvenv\\Lib\\site-packages\\pynput\\__init__.py',
   'PYMODULE'),
  ('pynput._info',
   'c:\\Users\\<USER>\\Desktop\\Mes_projets\\WritingToolsClean\\Windows_and_Linux\\myvenv\\Lib\\site-packages\\pynput\\_info.py',
   'PYMODULE'),
  ('pynput._util',
   'c:\\Users\\<USER>\\Desktop\\Mes_projets\\WritingToolsClean\\Windows_and_Linux\\myvenv\\Lib\\site-packages\\pynput\\_util\\__init__.py',
   'PYMODULE'),
  ('pynput._util.darwin',
   'c:\\Users\\<USER>\\Desktop\\Mes_projets\\WritingToolsClean\\Windows_and_Linux\\myvenv\\Lib\\site-packages\\pynput\\_util\\darwin.py',
   'PYMODULE'),
  ('pynput._util.darwin_vks',
   'c:\\Users\\<USER>\\Desktop\\Mes_projets\\WritingToolsClean\\Windows_and_Linux\\myvenv\\Lib\\site-packages\\pynput\\_util\\darwin_vks.py',
   'PYMODULE'),
  ('pynput._util.uinput',
   'c:\\Users\\<USER>\\Desktop\\Mes_projets\\WritingToolsClean\\Windows_and_Linux\\myvenv\\Lib\\site-packages\\pynput\\_util\\uinput.py',
   'PYMODULE'),
  ('pynput._util.win32',
   'c:\\Users\\<USER>\\Desktop\\Mes_projets\\WritingToolsClean\\Windows_and_Linux\\myvenv\\Lib\\site-packages\\pynput\\_util\\win32.py',
   'PYMODULE'),
  ('pynput._util.win32_vks',
   'c:\\Users\\<USER>\\Desktop\\Mes_projets\\WritingToolsClean\\Windows_and_Linux\\myvenv\\Lib\\site-packages\\pynput\\_util\\win32_vks.py',
   'PYMODULE'),
  ('pynput._util.xorg',
   'c:\\Users\\<USER>\\Desktop\\Mes_projets\\WritingToolsClean\\Windows_and_Linux\\myvenv\\Lib\\site-packages\\pynput\\_util\\xorg.py',
   'PYMODULE'),
  ('pynput._util.xorg_keysyms',
   'c:\\Users\\<USER>\\Desktop\\Mes_projets\\WritingToolsClean\\Windows_and_Linux\\myvenv\\Lib\\site-packages\\pynput\\_util\\xorg_keysyms.py',
   'PYMODULE'),
  ('pynput.keyboard',
   'c:\\Users\\<USER>\\Desktop\\Mes_projets\\WritingToolsClean\\Windows_and_Linux\\myvenv\\Lib\\site-packages\\pynput\\keyboard\\__init__.py',
   'PYMODULE'),
  ('pynput.keyboard._base',
   'c:\\Users\\<USER>\\Desktop\\Mes_projets\\WritingToolsClean\\Windows_and_Linux\\myvenv\\Lib\\site-packages\\pynput\\keyboard\\_base.py',
   'PYMODULE'),
  ('pynput.keyboard._darwin',
   'c:\\Users\\<USER>\\Desktop\\Mes_projets\\WritingToolsClean\\Windows_and_Linux\\myvenv\\Lib\\site-packages\\pynput\\keyboard\\_darwin.py',
   'PYMODULE'),
  ('pynput.keyboard._dummy',
   'c:\\Users\\<USER>\\Desktop\\Mes_projets\\WritingToolsClean\\Windows_and_Linux\\myvenv\\Lib\\site-packages\\pynput\\keyboard\\_dummy.py',
   'PYMODULE'),
  ('pynput.keyboard._uinput',
   'c:\\Users\\<USER>\\Desktop\\Mes_projets\\WritingToolsClean\\Windows_and_Linux\\myvenv\\Lib\\site-packages\\pynput\\keyboard\\_uinput.py',
   'PYMODULE'),
  ('pynput.keyboard._win32',
   'c:\\Users\\<USER>\\Desktop\\Mes_projets\\WritingToolsClean\\Windows_and_Linux\\myvenv\\Lib\\site-packages\\pynput\\keyboard\\_win32.py',
   'PYMODULE'),
  ('pynput.keyboard._xorg',
   'c:\\Users\\<USER>\\Desktop\\Mes_projets\\WritingToolsClean\\Windows_and_Linux\\myvenv\\Lib\\site-packages\\pynput\\keyboard\\_xorg.py',
   'PYMODULE'),
  ('pynput.mouse',
   'c:\\Users\\<USER>\\Desktop\\Mes_projets\\WritingToolsClean\\Windows_and_Linux\\myvenv\\Lib\\site-packages\\pynput\\mouse\\__init__.py',
   'PYMODULE'),
  ('pynput.mouse._base',
   'c:\\Users\\<USER>\\Desktop\\Mes_projets\\WritingToolsClean\\Windows_and_Linux\\myvenv\\Lib\\site-packages\\pynput\\mouse\\_base.py',
   'PYMODULE'),
  ('pynput.mouse._darwin',
   'c:\\Users\\<USER>\\Desktop\\Mes_projets\\WritingToolsClean\\Windows_and_Linux\\myvenv\\Lib\\site-packages\\pynput\\mouse\\_darwin.py',
   'PYMODULE'),
  ('pynput.mouse._dummy',
   'c:\\Users\\<USER>\\Desktop\\Mes_projets\\WritingToolsClean\\Windows_and_Linux\\myvenv\\Lib\\site-packages\\pynput\\mouse\\_dummy.py',
   'PYMODULE'),
  ('pynput.mouse._win32',
   'c:\\Users\\<USER>\\Desktop\\Mes_projets\\WritingToolsClean\\Windows_and_Linux\\myvenv\\Lib\\site-packages\\pynput\\mouse\\_win32.py',
   'PYMODULE'),
  ('pynput.mouse._xorg',
   'c:\\Users\\<USER>\\Desktop\\Mes_projets\\WritingToolsClean\\Windows_and_Linux\\myvenv\\Lib\\site-packages\\pynput\\mouse\\_xorg.py',
   'PYMODULE'),
  ('pyparsing',
   'c:\\Users\\<USER>\\Desktop\\Mes_projets\\WritingToolsClean\\Windows_and_Linux\\myvenv\\Lib\\site-packages\\pyparsing\\__init__.py',
   'PYMODULE'),
  ('pyparsing.actions',
   'c:\\Users\\<USER>\\Desktop\\Mes_projets\\WritingToolsClean\\Windows_and_Linux\\myvenv\\Lib\\site-packages\\pyparsing\\actions.py',
   'PYMODULE'),
  ('pyparsing.common',
   'c:\\Users\\<USER>\\Desktop\\Mes_projets\\WritingToolsClean\\Windows_and_Linux\\myvenv\\Lib\\site-packages\\pyparsing\\common.py',
   'PYMODULE'),
  ('pyparsing.core',
   'c:\\Users\\<USER>\\Desktop\\Mes_projets\\WritingToolsClean\\Windows_and_Linux\\myvenv\\Lib\\site-packages\\pyparsing\\core.py',
   'PYMODULE'),
  ('pyparsing.diagram',
   'c:\\Users\\<USER>\\Desktop\\Mes_projets\\WritingToolsClean\\Windows_and_Linux\\myvenv\\Lib\\site-packages\\pyparsing\\diagram\\__init__.py',
   'PYMODULE'),
  ('pyparsing.exceptions',
   'c:\\Users\\<USER>\\Desktop\\Mes_projets\\WritingToolsClean\\Windows_and_Linux\\myvenv\\Lib\\site-packages\\pyparsing\\exceptions.py',
   'PYMODULE'),
  ('pyparsing.helpers',
   'c:\\Users\\<USER>\\Desktop\\Mes_projets\\WritingToolsClean\\Windows_and_Linux\\myvenv\\Lib\\site-packages\\pyparsing\\helpers.py',
   'PYMODULE'),
  ('pyparsing.results',
   'c:\\Users\\<USER>\\Desktop\\Mes_projets\\WritingToolsClean\\Windows_and_Linux\\myvenv\\Lib\\site-packages\\pyparsing\\results.py',
   'PYMODULE'),
  ('pyparsing.testing',
   'c:\\Users\\<USER>\\Desktop\\Mes_projets\\WritingToolsClean\\Windows_and_Linux\\myvenv\\Lib\\site-packages\\pyparsing\\testing.py',
   'PYMODULE'),
  ('pyparsing.unicode',
   'c:\\Users\\<USER>\\Desktop\\Mes_projets\\WritingToolsClean\\Windows_and_Linux\\myvenv\\Lib\\site-packages\\pyparsing\\unicode.py',
   'PYMODULE'),
  ('pyparsing.util',
   'c:\\Users\\<USER>\\Desktop\\Mes_projets\\WritingToolsClean\\Windows_and_Linux\\myvenv\\Lib\\site-packages\\pyparsing\\util.py',
   'PYMODULE'),
  ('pyperclip',
   'c:\\Users\\<USER>\\Desktop\\Mes_projets\\WritingToolsClean\\Windows_and_Linux\\myvenv\\Lib\\site-packages\\pyperclip\\__init__.py',
   'PYMODULE'),
  ('queue',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\queue.py',
   'PYMODULE'),
  ('quopri',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\quopri.py',
   'PYMODULE'),
  ('random',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\random.py',
   'PYMODULE'),
  ('requests',
   'c:\\Users\\<USER>\\Desktop\\Mes_projets\\WritingToolsClean\\Windows_and_Linux\\myvenv\\Lib\\site-packages\\requests\\__init__.py',
   'PYMODULE'),
  ('requests.__version__',
   'c:\\Users\\<USER>\\Desktop\\Mes_projets\\WritingToolsClean\\Windows_and_Linux\\myvenv\\Lib\\site-packages\\requests\\__version__.py',
   'PYMODULE'),
  ('requests._internal_utils',
   'c:\\Users\\<USER>\\Desktop\\Mes_projets\\WritingToolsClean\\Windows_and_Linux\\myvenv\\Lib\\site-packages\\requests\\_internal_utils.py',
   'PYMODULE'),
  ('requests.adapters',
   'c:\\Users\\<USER>\\Desktop\\Mes_projets\\WritingToolsClean\\Windows_and_Linux\\myvenv\\Lib\\site-packages\\requests\\adapters.py',
   'PYMODULE'),
  ('requests.api',
   'c:\\Users\\<USER>\\Desktop\\Mes_projets\\WritingToolsClean\\Windows_and_Linux\\myvenv\\Lib\\site-packages\\requests\\api.py',
   'PYMODULE'),
  ('requests.auth',
   'c:\\Users\\<USER>\\Desktop\\Mes_projets\\WritingToolsClean\\Windows_and_Linux\\myvenv\\Lib\\site-packages\\requests\\auth.py',
   'PYMODULE'),
  ('requests.certs',
   'c:\\Users\\<USER>\\Desktop\\Mes_projets\\WritingToolsClean\\Windows_and_Linux\\myvenv\\Lib\\site-packages\\requests\\certs.py',
   'PYMODULE'),
  ('requests.compat',
   'c:\\Users\\<USER>\\Desktop\\Mes_projets\\WritingToolsClean\\Windows_and_Linux\\myvenv\\Lib\\site-packages\\requests\\compat.py',
   'PYMODULE'),
  ('requests.cookies',
   'c:\\Users\\<USER>\\Desktop\\Mes_projets\\WritingToolsClean\\Windows_and_Linux\\myvenv\\Lib\\site-packages\\requests\\cookies.py',
   'PYMODULE'),
  ('requests.exceptions',
   'c:\\Users\\<USER>\\Desktop\\Mes_projets\\WritingToolsClean\\Windows_and_Linux\\myvenv\\Lib\\site-packages\\requests\\exceptions.py',
   'PYMODULE'),
  ('requests.hooks',
   'c:\\Users\\<USER>\\Desktop\\Mes_projets\\WritingToolsClean\\Windows_and_Linux\\myvenv\\Lib\\site-packages\\requests\\hooks.py',
   'PYMODULE'),
  ('requests.models',
   'c:\\Users\\<USER>\\Desktop\\Mes_projets\\WritingToolsClean\\Windows_and_Linux\\myvenv\\Lib\\site-packages\\requests\\models.py',
   'PYMODULE'),
  ('requests.packages',
   'c:\\Users\\<USER>\\Desktop\\Mes_projets\\WritingToolsClean\\Windows_and_Linux\\myvenv\\Lib\\site-packages\\requests\\packages.py',
   'PYMODULE'),
  ('requests.sessions',
   'c:\\Users\\<USER>\\Desktop\\Mes_projets\\WritingToolsClean\\Windows_and_Linux\\myvenv\\Lib\\site-packages\\requests\\sessions.py',
   'PYMODULE'),
  ('requests.status_codes',
   'c:\\Users\\<USER>\\Desktop\\Mes_projets\\WritingToolsClean\\Windows_and_Linux\\myvenv\\Lib\\site-packages\\requests\\status_codes.py',
   'PYMODULE'),
  ('requests.structures',
   'c:\\Users\\<USER>\\Desktop\\Mes_projets\\WritingToolsClean\\Windows_and_Linux\\myvenv\\Lib\\site-packages\\requests\\structures.py',
   'PYMODULE'),
  ('requests.utils',
   'c:\\Users\\<USER>\\Desktop\\Mes_projets\\WritingToolsClean\\Windows_and_Linux\\myvenv\\Lib\\site-packages\\requests\\utils.py',
   'PYMODULE'),
  ('rlcompleter',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\rlcompleter.py',
   'PYMODULE'),
  ('rsa',
   'c:\\Users\\<USER>\\Desktop\\Mes_projets\\WritingToolsClean\\Windows_and_Linux\\myvenv\\Lib\\site-packages\\rsa\\__init__.py',
   'PYMODULE'),
  ('rsa.asn1',
   'c:\\Users\\<USER>\\Desktop\\Mes_projets\\WritingToolsClean\\Windows_and_Linux\\myvenv\\Lib\\site-packages\\rsa\\asn1.py',
   'PYMODULE'),
  ('rsa.common',
   'c:\\Users\\<USER>\\Desktop\\Mes_projets\\WritingToolsClean\\Windows_and_Linux\\myvenv\\Lib\\site-packages\\rsa\\common.py',
   'PYMODULE'),
  ('rsa.core',
   'c:\\Users\\<USER>\\Desktop\\Mes_projets\\WritingToolsClean\\Windows_and_Linux\\myvenv\\Lib\\site-packages\\rsa\\core.py',
   'PYMODULE'),
  ('rsa.key',
   'c:\\Users\\<USER>\\Desktop\\Mes_projets\\WritingToolsClean\\Windows_and_Linux\\myvenv\\Lib\\site-packages\\rsa\\key.py',
   'PYMODULE'),
  ('rsa.parallel',
   'c:\\Users\\<USER>\\Desktop\\Mes_projets\\WritingToolsClean\\Windows_and_Linux\\myvenv\\Lib\\site-packages\\rsa\\parallel.py',
   'PYMODULE'),
  ('rsa.pem',
   'c:\\Users\\<USER>\\Desktop\\Mes_projets\\WritingToolsClean\\Windows_and_Linux\\myvenv\\Lib\\site-packages\\rsa\\pem.py',
   'PYMODULE'),
  ('rsa.pkcs1',
   'c:\\Users\\<USER>\\Desktop\\Mes_projets\\WritingToolsClean\\Windows_and_Linux\\myvenv\\Lib\\site-packages\\rsa\\pkcs1.py',
   'PYMODULE'),
  ('rsa.prime',
   'c:\\Users\\<USER>\\Desktop\\Mes_projets\\WritingToolsClean\\Windows_and_Linux\\myvenv\\Lib\\site-packages\\rsa\\prime.py',
   'PYMODULE'),
  ('rsa.randnum',
   'c:\\Users\\<USER>\\Desktop\\Mes_projets\\WritingToolsClean\\Windows_and_Linux\\myvenv\\Lib\\site-packages\\rsa\\randnum.py',
   'PYMODULE'),
  ('rsa.transform',
   'c:\\Users\\<USER>\\Desktop\\Mes_projets\\WritingToolsClean\\Windows_and_Linux\\myvenv\\Lib\\site-packages\\rsa\\transform.py',
   'PYMODULE'),
  ('runpy',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\runpy.py',
   'PYMODULE'),
  ('secrets',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\secrets.py',
   'PYMODULE'),
  ('selectors',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\selectors.py',
   'PYMODULE'),
  ('setuptools',
   'c:\\Users\\<USER>\\Desktop\\Mes_projets\\WritingToolsClean\\Windows_and_Linux\\myvenv\\Lib\\site-packages\\setuptools\\__init__.py',
   'PYMODULE'),
  ('setuptools._core_metadata',
   'c:\\Users\\<USER>\\Desktop\\Mes_projets\\WritingToolsClean\\Windows_and_Linux\\myvenv\\Lib\\site-packages\\setuptools\\_core_metadata.py',
   'PYMODULE'),
  ('setuptools._discovery',
   'c:\\Users\\<USER>\\Desktop\\Mes_projets\\WritingToolsClean\\Windows_and_Linux\\myvenv\\Lib\\site-packages\\setuptools\\_discovery.py',
   'PYMODULE'),
  ('setuptools._distutils',
   'c:\\Users\\<USER>\\Desktop\\Mes_projets\\WritingToolsClean\\Windows_and_Linux\\myvenv\\Lib\\site-packages\\setuptools\\_distutils\\__init__.py',
   'PYMODULE'),
  ('setuptools._distutils._log',
   'c:\\Users\\<USER>\\Desktop\\Mes_projets\\WritingToolsClean\\Windows_and_Linux\\myvenv\\Lib\\site-packages\\setuptools\\_distutils\\_log.py',
   'PYMODULE'),
  ('setuptools._distutils._modified',
   'c:\\Users\\<USER>\\Desktop\\Mes_projets\\WritingToolsClean\\Windows_and_Linux\\myvenv\\Lib\\site-packages\\setuptools\\_distutils\\_modified.py',
   'PYMODULE'),
  ('setuptools._distutils._msvccompiler',
   'c:\\Users\\<USER>\\Desktop\\Mes_projets\\WritingToolsClean\\Windows_and_Linux\\myvenv\\Lib\\site-packages\\setuptools\\_distutils\\_msvccompiler.py',
   'PYMODULE'),
  ('setuptools._distutils.archive_util',
   'c:\\Users\\<USER>\\Desktop\\Mes_projets\\WritingToolsClean\\Windows_and_Linux\\myvenv\\Lib\\site-packages\\setuptools\\_distutils\\archive_util.py',
   'PYMODULE'),
  ('setuptools._distutils.ccompiler',
   'c:\\Users\\<USER>\\Desktop\\Mes_projets\\WritingToolsClean\\Windows_and_Linux\\myvenv\\Lib\\site-packages\\setuptools\\_distutils\\ccompiler.py',
   'PYMODULE'),
  ('setuptools._distutils.cmd',
   'c:\\Users\\<USER>\\Desktop\\Mes_projets\\WritingToolsClean\\Windows_and_Linux\\myvenv\\Lib\\site-packages\\setuptools\\_distutils\\cmd.py',
   'PYMODULE'),
  ('setuptools._distutils.command',
   'c:\\Users\\<USER>\\Desktop\\Mes_projets\\WritingToolsClean\\Windows_and_Linux\\myvenv\\Lib\\site-packages\\setuptools\\_distutils\\command\\__init__.py',
   'PYMODULE'),
  ('setuptools._distutils.command.bdist',
   'c:\\Users\\<USER>\\Desktop\\Mes_projets\\WritingToolsClean\\Windows_and_Linux\\myvenv\\Lib\\site-packages\\setuptools\\_distutils\\command\\bdist.py',
   'PYMODULE'),
  ('setuptools._distutils.command.build',
   'c:\\Users\\<USER>\\Desktop\\Mes_projets\\WritingToolsClean\\Windows_and_Linux\\myvenv\\Lib\\site-packages\\setuptools\\_distutils\\command\\build.py',
   'PYMODULE'),
  ('setuptools._distutils.command.build_ext',
   'c:\\Users\\<USER>\\Desktop\\Mes_projets\\WritingToolsClean\\Windows_and_Linux\\myvenv\\Lib\\site-packages\\setuptools\\_distutils\\command\\build_ext.py',
   'PYMODULE'),
  ('setuptools._distutils.command.sdist',
   'c:\\Users\\<USER>\\Desktop\\Mes_projets\\WritingToolsClean\\Windows_and_Linux\\myvenv\\Lib\\site-packages\\setuptools\\_distutils\\command\\sdist.py',
   'PYMODULE'),
  ('setuptools._distutils.compat',
   'c:\\Users\\<USER>\\Desktop\\Mes_projets\\WritingToolsClean\\Windows_and_Linux\\myvenv\\Lib\\site-packages\\setuptools\\_distutils\\compat\\__init__.py',
   'PYMODULE'),
  ('setuptools._distutils.compat.numpy',
   'c:\\Users\\<USER>\\Desktop\\Mes_projets\\WritingToolsClean\\Windows_and_Linux\\myvenv\\Lib\\site-packages\\setuptools\\_distutils\\compat\\numpy.py',
   'PYMODULE'),
  ('setuptools._distutils.compat.py39',
   'c:\\Users\\<USER>\\Desktop\\Mes_projets\\WritingToolsClean\\Windows_and_Linux\\myvenv\\Lib\\site-packages\\setuptools\\_distutils\\compat\\py39.py',
   'PYMODULE'),
  ('setuptools._distutils.compilers', '-', 'PYMODULE'),
  ('setuptools._distutils.compilers.C', '-', 'PYMODULE'),
  ('setuptools._distutils.compilers.C.base',
   'c:\\Users\\<USER>\\Desktop\\Mes_projets\\WritingToolsClean\\Windows_and_Linux\\myvenv\\Lib\\site-packages\\setuptools\\_distutils\\compilers\\C\\base.py',
   'PYMODULE'),
  ('setuptools._distutils.compilers.C.errors',
   'c:\\Users\\<USER>\\Desktop\\Mes_projets\\WritingToolsClean\\Windows_and_Linux\\myvenv\\Lib\\site-packages\\setuptools\\_distutils\\compilers\\C\\errors.py',
   'PYMODULE'),
  ('setuptools._distutils.compilers.C.msvc',
   'c:\\Users\\<USER>\\Desktop\\Mes_projets\\WritingToolsClean\\Windows_and_Linux\\myvenv\\Lib\\site-packages\\setuptools\\_distutils\\compilers\\C\\msvc.py',
   'PYMODULE'),
  ('setuptools._distutils.core',
   'c:\\Users\\<USER>\\Desktop\\Mes_projets\\WritingToolsClean\\Windows_and_Linux\\myvenv\\Lib\\site-packages\\setuptools\\_distutils\\core.py',
   'PYMODULE'),
  ('setuptools._distutils.debug',
   'c:\\Users\\<USER>\\Desktop\\Mes_projets\\WritingToolsClean\\Windows_and_Linux\\myvenv\\Lib\\site-packages\\setuptools\\_distutils\\debug.py',
   'PYMODULE'),
  ('setuptools._distutils.dir_util',
   'c:\\Users\\<USER>\\Desktop\\Mes_projets\\WritingToolsClean\\Windows_and_Linux\\myvenv\\Lib\\site-packages\\setuptools\\_distutils\\dir_util.py',
   'PYMODULE'),
  ('setuptools._distutils.dist',
   'c:\\Users\\<USER>\\Desktop\\Mes_projets\\WritingToolsClean\\Windows_and_Linux\\myvenv\\Lib\\site-packages\\setuptools\\_distutils\\dist.py',
   'PYMODULE'),
  ('setuptools._distutils.errors',
   'c:\\Users\\<USER>\\Desktop\\Mes_projets\\WritingToolsClean\\Windows_and_Linux\\myvenv\\Lib\\site-packages\\setuptools\\_distutils\\errors.py',
   'PYMODULE'),
  ('setuptools._distutils.extension',
   'c:\\Users\\<USER>\\Desktop\\Mes_projets\\WritingToolsClean\\Windows_and_Linux\\myvenv\\Lib\\site-packages\\setuptools\\_distutils\\extension.py',
   'PYMODULE'),
  ('setuptools._distutils.fancy_getopt',
   'c:\\Users\\<USER>\\Desktop\\Mes_projets\\WritingToolsClean\\Windows_and_Linux\\myvenv\\Lib\\site-packages\\setuptools\\_distutils\\fancy_getopt.py',
   'PYMODULE'),
  ('setuptools._distutils.file_util',
   'c:\\Users\\<USER>\\Desktop\\Mes_projets\\WritingToolsClean\\Windows_and_Linux\\myvenv\\Lib\\site-packages\\setuptools\\_distutils\\file_util.py',
   'PYMODULE'),
  ('setuptools._distutils.filelist',
   'c:\\Users\\<USER>\\Desktop\\Mes_projets\\WritingToolsClean\\Windows_and_Linux\\myvenv\\Lib\\site-packages\\setuptools\\_distutils\\filelist.py',
   'PYMODULE'),
  ('setuptools._distutils.log',
   'c:\\Users\\<USER>\\Desktop\\Mes_projets\\WritingToolsClean\\Windows_and_Linux\\myvenv\\Lib\\site-packages\\setuptools\\_distutils\\log.py',
   'PYMODULE'),
  ('setuptools._distutils.spawn',
   'c:\\Users\\<USER>\\Desktop\\Mes_projets\\WritingToolsClean\\Windows_and_Linux\\myvenv\\Lib\\site-packages\\setuptools\\_distutils\\spawn.py',
   'PYMODULE'),
  ('setuptools._distutils.sysconfig',
   'c:\\Users\\<USER>\\Desktop\\Mes_projets\\WritingToolsClean\\Windows_and_Linux\\myvenv\\Lib\\site-packages\\setuptools\\_distutils\\sysconfig.py',
   'PYMODULE'),
  ('setuptools._distutils.text_file',
   'c:\\Users\\<USER>\\Desktop\\Mes_projets\\WritingToolsClean\\Windows_and_Linux\\myvenv\\Lib\\site-packages\\setuptools\\_distutils\\text_file.py',
   'PYMODULE'),
  ('setuptools._distutils.util',
   'c:\\Users\\<USER>\\Desktop\\Mes_projets\\WritingToolsClean\\Windows_and_Linux\\myvenv\\Lib\\site-packages\\setuptools\\_distutils\\util.py',
   'PYMODULE'),
  ('setuptools._distutils.version',
   'c:\\Users\\<USER>\\Desktop\\Mes_projets\\WritingToolsClean\\Windows_and_Linux\\myvenv\\Lib\\site-packages\\setuptools\\_distutils\\version.py',
   'PYMODULE'),
  ('setuptools._distutils.versionpredicate',
   'c:\\Users\\<USER>\\Desktop\\Mes_projets\\WritingToolsClean\\Windows_and_Linux\\myvenv\\Lib\\site-packages\\setuptools\\_distutils\\versionpredicate.py',
   'PYMODULE'),
  ('setuptools._entry_points',
   'c:\\Users\\<USER>\\Desktop\\Mes_projets\\WritingToolsClean\\Windows_and_Linux\\myvenv\\Lib\\site-packages\\setuptools\\_entry_points.py',
   'PYMODULE'),
  ('setuptools._imp',
   'c:\\Users\\<USER>\\Desktop\\Mes_projets\\WritingToolsClean\\Windows_and_Linux\\myvenv\\Lib\\site-packages\\setuptools\\_imp.py',
   'PYMODULE'),
  ('setuptools._importlib',
   'c:\\Users\\<USER>\\Desktop\\Mes_projets\\WritingToolsClean\\Windows_and_Linux\\myvenv\\Lib\\site-packages\\setuptools\\_importlib.py',
   'PYMODULE'),
  ('setuptools._itertools',
   'c:\\Users\\<USER>\\Desktop\\Mes_projets\\WritingToolsClean\\Windows_and_Linux\\myvenv\\Lib\\site-packages\\setuptools\\_itertools.py',
   'PYMODULE'),
  ('setuptools._normalization',
   'c:\\Users\\<USER>\\Desktop\\Mes_projets\\WritingToolsClean\\Windows_and_Linux\\myvenv\\Lib\\site-packages\\setuptools\\_normalization.py',
   'PYMODULE'),
  ('setuptools._path',
   'c:\\Users\\<USER>\\Desktop\\Mes_projets\\WritingToolsClean\\Windows_and_Linux\\myvenv\\Lib\\site-packages\\setuptools\\_path.py',
   'PYMODULE'),
  ('setuptools._reqs',
   'c:\\Users\\<USER>\\Desktop\\Mes_projets\\WritingToolsClean\\Windows_and_Linux\\myvenv\\Lib\\site-packages\\setuptools\\_reqs.py',
   'PYMODULE'),
  ('setuptools._shutil',
   'c:\\Users\\<USER>\\Desktop\\Mes_projets\\WritingToolsClean\\Windows_and_Linux\\myvenv\\Lib\\site-packages\\setuptools\\_shutil.py',
   'PYMODULE'),
  ('setuptools._static',
   'c:\\Users\\<USER>\\Desktop\\Mes_projets\\WritingToolsClean\\Windows_and_Linux\\myvenv\\Lib\\site-packages\\setuptools\\_static.py',
   'PYMODULE'),
  ('setuptools._vendor', '-', 'PYMODULE'),
  ('setuptools._vendor.backports',
   'c:\\Users\\<USER>\\Desktop\\Mes_projets\\WritingToolsClean\\Windows_and_Linux\\myvenv\\Lib\\site-packages\\setuptools\\_vendor\\backports\\__init__.py',
   'PYMODULE'),
  ('setuptools._vendor.backports.tarfile',
   'c:\\Users\\<USER>\\Desktop\\Mes_projets\\WritingToolsClean\\Windows_and_Linux\\myvenv\\Lib\\site-packages\\setuptools\\_vendor\\backports\\tarfile\\__init__.py',
   'PYMODULE'),
  ('setuptools._vendor.backports.tarfile.compat',
   'c:\\Users\\<USER>\\Desktop\\Mes_projets\\WritingToolsClean\\Windows_and_Linux\\myvenv\\Lib\\site-packages\\setuptools\\_vendor\\backports\\tarfile\\compat\\__init__.py',
   'PYMODULE'),
  ('setuptools._vendor.backports.tarfile.compat.py38',
   'c:\\Users\\<USER>\\Desktop\\Mes_projets\\WritingToolsClean\\Windows_and_Linux\\myvenv\\Lib\\site-packages\\setuptools\\_vendor\\backports\\tarfile\\compat\\py38.py',
   'PYMODULE'),
  ('setuptools._vendor.importlib_metadata',
   'c:\\Users\\<USER>\\Desktop\\Mes_projets\\WritingToolsClean\\Windows_and_Linux\\myvenv\\Lib\\site-packages\\setuptools\\_vendor\\importlib_metadata\\__init__.py',
   'PYMODULE'),
  ('setuptools._vendor.importlib_metadata._adapters',
   'c:\\Users\\<USER>\\Desktop\\Mes_projets\\WritingToolsClean\\Windows_and_Linux\\myvenv\\Lib\\site-packages\\setuptools\\_vendor\\importlib_metadata\\_adapters.py',
   'PYMODULE'),
  ('setuptools._vendor.importlib_metadata._collections',
   'c:\\Users\\<USER>\\Desktop\\Mes_projets\\WritingToolsClean\\Windows_and_Linux\\myvenv\\Lib\\site-packages\\setuptools\\_vendor\\importlib_metadata\\_collections.py',
   'PYMODULE'),
  ('setuptools._vendor.importlib_metadata._compat',
   'c:\\Users\\<USER>\\Desktop\\Mes_projets\\WritingToolsClean\\Windows_and_Linux\\myvenv\\Lib\\site-packages\\setuptools\\_vendor\\importlib_metadata\\_compat.py',
   'PYMODULE'),
  ('setuptools._vendor.importlib_metadata._functools',
   'c:\\Users\\<USER>\\Desktop\\Mes_projets\\WritingToolsClean\\Windows_and_Linux\\myvenv\\Lib\\site-packages\\setuptools\\_vendor\\importlib_metadata\\_functools.py',
   'PYMODULE'),
  ('setuptools._vendor.importlib_metadata._itertools',
   'c:\\Users\\<USER>\\Desktop\\Mes_projets\\WritingToolsClean\\Windows_and_Linux\\myvenv\\Lib\\site-packages\\setuptools\\_vendor\\importlib_metadata\\_itertools.py',
   'PYMODULE'),
  ('setuptools._vendor.importlib_metadata._meta',
   'c:\\Users\\<USER>\\Desktop\\Mes_projets\\WritingToolsClean\\Windows_and_Linux\\myvenv\\Lib\\site-packages\\setuptools\\_vendor\\importlib_metadata\\_meta.py',
   'PYMODULE'),
  ('setuptools._vendor.importlib_metadata._text',
   'c:\\Users\\<USER>\\Desktop\\Mes_projets\\WritingToolsClean\\Windows_and_Linux\\myvenv\\Lib\\site-packages\\setuptools\\_vendor\\importlib_metadata\\_text.py',
   'PYMODULE'),
  ('setuptools._vendor.importlib_metadata.compat',
   'c:\\Users\\<USER>\\Desktop\\Mes_projets\\WritingToolsClean\\Windows_and_Linux\\myvenv\\Lib\\site-packages\\setuptools\\_vendor\\importlib_metadata\\compat\\__init__.py',
   'PYMODULE'),
  ('setuptools._vendor.importlib_metadata.compat.py311',
   'c:\\Users\\<USER>\\Desktop\\Mes_projets\\WritingToolsClean\\Windows_and_Linux\\myvenv\\Lib\\site-packages\\setuptools\\_vendor\\importlib_metadata\\compat\\py311.py',
   'PYMODULE'),
  ('setuptools._vendor.importlib_metadata.compat.py39',
   'c:\\Users\\<USER>\\Desktop\\Mes_projets\\WritingToolsClean\\Windows_and_Linux\\myvenv\\Lib\\site-packages\\setuptools\\_vendor\\importlib_metadata\\compat\\py39.py',
   'PYMODULE'),
  ('setuptools._vendor.jaraco', '-', 'PYMODULE'),
  ('setuptools._vendor.jaraco.context',
   'c:\\Users\\<USER>\\Desktop\\Mes_projets\\WritingToolsClean\\Windows_and_Linux\\myvenv\\Lib\\site-packages\\setuptools\\_vendor\\jaraco\\context.py',
   'PYMODULE'),
  ('setuptools._vendor.jaraco.functools',
   'c:\\Users\\<USER>\\Desktop\\Mes_projets\\WritingToolsClean\\Windows_and_Linux\\myvenv\\Lib\\site-packages\\setuptools\\_vendor\\jaraco\\functools\\__init__.py',
   'PYMODULE'),
  ('setuptools._vendor.jaraco.text',
   'c:\\Users\\<USER>\\Desktop\\Mes_projets\\WritingToolsClean\\Windows_and_Linux\\myvenv\\Lib\\site-packages\\setuptools\\_vendor\\jaraco\\text\\__init__.py',
   'PYMODULE'),
  ('setuptools._vendor.more_itertools',
   'c:\\Users\\<USER>\\Desktop\\Mes_projets\\WritingToolsClean\\Windows_and_Linux\\myvenv\\Lib\\site-packages\\setuptools\\_vendor\\more_itertools\\__init__.py',
   'PYMODULE'),
  ('setuptools._vendor.more_itertools.more',
   'c:\\Users\\<USER>\\Desktop\\Mes_projets\\WritingToolsClean\\Windows_and_Linux\\myvenv\\Lib\\site-packages\\setuptools\\_vendor\\more_itertools\\more.py',
   'PYMODULE'),
  ('setuptools._vendor.more_itertools.recipes',
   'c:\\Users\\<USER>\\Desktop\\Mes_projets\\WritingToolsClean\\Windows_and_Linux\\myvenv\\Lib\\site-packages\\setuptools\\_vendor\\more_itertools\\recipes.py',
   'PYMODULE'),
  ('setuptools._vendor.packaging',
   'c:\\Users\\<USER>\\Desktop\\Mes_projets\\WritingToolsClean\\Windows_and_Linux\\myvenv\\Lib\\site-packages\\setuptools\\_vendor\\packaging\\__init__.py',
   'PYMODULE'),
  ('setuptools._vendor.packaging._elffile',
   'c:\\Users\\<USER>\\Desktop\\Mes_projets\\WritingToolsClean\\Windows_and_Linux\\myvenv\\Lib\\site-packages\\setuptools\\_vendor\\packaging\\_elffile.py',
   'PYMODULE'),
  ('setuptools._vendor.packaging._manylinux',
   'c:\\Users\\<USER>\\Desktop\\Mes_projets\\WritingToolsClean\\Windows_and_Linux\\myvenv\\Lib\\site-packages\\setuptools\\_vendor\\packaging\\_manylinux.py',
   'PYMODULE'),
  ('setuptools._vendor.packaging._musllinux',
   'c:\\Users\\<USER>\\Desktop\\Mes_projets\\WritingToolsClean\\Windows_and_Linux\\myvenv\\Lib\\site-packages\\setuptools\\_vendor\\packaging\\_musllinux.py',
   'PYMODULE'),
  ('setuptools._vendor.packaging._parser',
   'c:\\Users\\<USER>\\Desktop\\Mes_projets\\WritingToolsClean\\Windows_and_Linux\\myvenv\\Lib\\site-packages\\setuptools\\_vendor\\packaging\\_parser.py',
   'PYMODULE'),
  ('setuptools._vendor.packaging._structures',
   'c:\\Users\\<USER>\\Desktop\\Mes_projets\\WritingToolsClean\\Windows_and_Linux\\myvenv\\Lib\\site-packages\\setuptools\\_vendor\\packaging\\_structures.py',
   'PYMODULE'),
  ('setuptools._vendor.packaging._tokenizer',
   'c:\\Users\\<USER>\\Desktop\\Mes_projets\\WritingToolsClean\\Windows_and_Linux\\myvenv\\Lib\\site-packages\\setuptools\\_vendor\\packaging\\_tokenizer.py',
   'PYMODULE'),
  ('setuptools._vendor.packaging.markers',
   'c:\\Users\\<USER>\\Desktop\\Mes_projets\\WritingToolsClean\\Windows_and_Linux\\myvenv\\Lib\\site-packages\\setuptools\\_vendor\\packaging\\markers.py',
   'PYMODULE'),
  ('setuptools._vendor.packaging.requirements',
   'c:\\Users\\<USER>\\Desktop\\Mes_projets\\WritingToolsClean\\Windows_and_Linux\\myvenv\\Lib\\site-packages\\setuptools\\_vendor\\packaging\\requirements.py',
   'PYMODULE'),
  ('setuptools._vendor.packaging.specifiers',
   'c:\\Users\\<USER>\\Desktop\\Mes_projets\\WritingToolsClean\\Windows_and_Linux\\myvenv\\Lib\\site-packages\\setuptools\\_vendor\\packaging\\specifiers.py',
   'PYMODULE'),
  ('setuptools._vendor.packaging.tags',
   'c:\\Users\\<USER>\\Desktop\\Mes_projets\\WritingToolsClean\\Windows_and_Linux\\myvenv\\Lib\\site-packages\\setuptools\\_vendor\\packaging\\tags.py',
   'PYMODULE'),
  ('setuptools._vendor.packaging.utils',
   'c:\\Users\\<USER>\\Desktop\\Mes_projets\\WritingToolsClean\\Windows_and_Linux\\myvenv\\Lib\\site-packages\\setuptools\\_vendor\\packaging\\utils.py',
   'PYMODULE'),
  ('setuptools._vendor.packaging.version',
   'c:\\Users\\<USER>\\Desktop\\Mes_projets\\WritingToolsClean\\Windows_and_Linux\\myvenv\\Lib\\site-packages\\setuptools\\_vendor\\packaging\\version.py',
   'PYMODULE'),
  ('setuptools._vendor.tomli',
   'c:\\Users\\<USER>\\Desktop\\Mes_projets\\WritingToolsClean\\Windows_and_Linux\\myvenv\\Lib\\site-packages\\setuptools\\_vendor\\tomli\\__init__.py',
   'PYMODULE'),
  ('setuptools._vendor.tomli._parser',
   'c:\\Users\\<USER>\\Desktop\\Mes_projets\\WritingToolsClean\\Windows_and_Linux\\myvenv\\Lib\\site-packages\\setuptools\\_vendor\\tomli\\_parser.py',
   'PYMODULE'),
  ('setuptools._vendor.tomli._re',
   'c:\\Users\\<USER>\\Desktop\\Mes_projets\\WritingToolsClean\\Windows_and_Linux\\myvenv\\Lib\\site-packages\\setuptools\\_vendor\\tomli\\_re.py',
   'PYMODULE'),
  ('setuptools._vendor.tomli._types',
   'c:\\Users\\<USER>\\Desktop\\Mes_projets\\WritingToolsClean\\Windows_and_Linux\\myvenv\\Lib\\site-packages\\setuptools\\_vendor\\tomli\\_types.py',
   'PYMODULE'),
  ('setuptools._vendor.wheel',
   'c:\\Users\\<USER>\\Desktop\\Mes_projets\\WritingToolsClean\\Windows_and_Linux\\myvenv\\Lib\\site-packages\\setuptools\\_vendor\\wheel\\__init__.py',
   'PYMODULE'),
  ('setuptools._vendor.wheel.cli',
   'c:\\Users\\<USER>\\Desktop\\Mes_projets\\WritingToolsClean\\Windows_and_Linux\\myvenv\\Lib\\site-packages\\setuptools\\_vendor\\wheel\\cli\\__init__.py',
   'PYMODULE'),
  ('setuptools._vendor.wheel.cli.convert',
   'c:\\Users\\<USER>\\Desktop\\Mes_projets\\WritingToolsClean\\Windows_and_Linux\\myvenv\\Lib\\site-packages\\setuptools\\_vendor\\wheel\\cli\\convert.py',
   'PYMODULE'),
  ('setuptools._vendor.wheel.cli.pack',
   'c:\\Users\\<USER>\\Desktop\\Mes_projets\\WritingToolsClean\\Windows_and_Linux\\myvenv\\Lib\\site-packages\\setuptools\\_vendor\\wheel\\cli\\pack.py',
   'PYMODULE'),
  ('setuptools._vendor.wheel.cli.tags',
   'c:\\Users\\<USER>\\Desktop\\Mes_projets\\WritingToolsClean\\Windows_and_Linux\\myvenv\\Lib\\site-packages\\setuptools\\_vendor\\wheel\\cli\\tags.py',
   'PYMODULE'),
  ('setuptools._vendor.wheel.cli.unpack',
   'c:\\Users\\<USER>\\Desktop\\Mes_projets\\WritingToolsClean\\Windows_and_Linux\\myvenv\\Lib\\site-packages\\setuptools\\_vendor\\wheel\\cli\\unpack.py',
   'PYMODULE'),
  ('setuptools._vendor.wheel.macosx_libfile',
   'c:\\Users\\<USER>\\Desktop\\Mes_projets\\WritingToolsClean\\Windows_and_Linux\\myvenv\\Lib\\site-packages\\setuptools\\_vendor\\wheel\\macosx_libfile.py',
   'PYMODULE'),
  ('setuptools._vendor.wheel.metadata',
   'c:\\Users\\<USER>\\Desktop\\Mes_projets\\WritingToolsClean\\Windows_and_Linux\\myvenv\\Lib\\site-packages\\setuptools\\_vendor\\wheel\\metadata.py',
   'PYMODULE'),
  ('setuptools._vendor.wheel.util',
   'c:\\Users\\<USER>\\Desktop\\Mes_projets\\WritingToolsClean\\Windows_and_Linux\\myvenv\\Lib\\site-packages\\setuptools\\_vendor\\wheel\\util.py',
   'PYMODULE'),
  ('setuptools._vendor.wheel.vendored',
   'c:\\Users\\<USER>\\Desktop\\Mes_projets\\WritingToolsClean\\Windows_and_Linux\\myvenv\\Lib\\site-packages\\setuptools\\_vendor\\wheel\\vendored\\__init__.py',
   'PYMODULE'),
  ('setuptools._vendor.wheel.vendored.packaging',
   'c:\\Users\\<USER>\\Desktop\\Mes_projets\\WritingToolsClean\\Windows_and_Linux\\myvenv\\Lib\\site-packages\\setuptools\\_vendor\\wheel\\vendored\\packaging\\__init__.py',
   'PYMODULE'),
  ('setuptools._vendor.wheel.vendored.packaging._elffile',
   'c:\\Users\\<USER>\\Desktop\\Mes_projets\\WritingToolsClean\\Windows_and_Linux\\myvenv\\Lib\\site-packages\\setuptools\\_vendor\\wheel\\vendored\\packaging\\_elffile.py',
   'PYMODULE'),
  ('setuptools._vendor.wheel.vendored.packaging._manylinux',
   'c:\\Users\\<USER>\\Desktop\\Mes_projets\\WritingToolsClean\\Windows_and_Linux\\myvenv\\Lib\\site-packages\\setuptools\\_vendor\\wheel\\vendored\\packaging\\_manylinux.py',
   'PYMODULE'),
  ('setuptools._vendor.wheel.vendored.packaging._musllinux',
   'c:\\Users\\<USER>\\Desktop\\Mes_projets\\WritingToolsClean\\Windows_and_Linux\\myvenv\\Lib\\site-packages\\setuptools\\_vendor\\wheel\\vendored\\packaging\\_musllinux.py',
   'PYMODULE'),
  ('setuptools._vendor.wheel.vendored.packaging._parser',
   'c:\\Users\\<USER>\\Desktop\\Mes_projets\\WritingToolsClean\\Windows_and_Linux\\myvenv\\Lib\\site-packages\\setuptools\\_vendor\\wheel\\vendored\\packaging\\_parser.py',
   'PYMODULE'),
  ('setuptools._vendor.wheel.vendored.packaging._structures',
   'c:\\Users\\<USER>\\Desktop\\Mes_projets\\WritingToolsClean\\Windows_and_Linux\\myvenv\\Lib\\site-packages\\setuptools\\_vendor\\wheel\\vendored\\packaging\\_structures.py',
   'PYMODULE'),
  ('setuptools._vendor.wheel.vendored.packaging._tokenizer',
   'c:\\Users\\<USER>\\Desktop\\Mes_projets\\WritingToolsClean\\Windows_and_Linux\\myvenv\\Lib\\site-packages\\setuptools\\_vendor\\wheel\\vendored\\packaging\\_tokenizer.py',
   'PYMODULE'),
  ('setuptools._vendor.wheel.vendored.packaging.markers',
   'c:\\Users\\<USER>\\Desktop\\Mes_projets\\WritingToolsClean\\Windows_and_Linux\\myvenv\\Lib\\site-packages\\setuptools\\_vendor\\wheel\\vendored\\packaging\\markers.py',
   'PYMODULE'),
  ('setuptools._vendor.wheel.vendored.packaging.requirements',
   'c:\\Users\\<USER>\\Desktop\\Mes_projets\\WritingToolsClean\\Windows_and_Linux\\myvenv\\Lib\\site-packages\\setuptools\\_vendor\\wheel\\vendored\\packaging\\requirements.py',
   'PYMODULE'),
  ('setuptools._vendor.wheel.vendored.packaging.specifiers',
   'c:\\Users\\<USER>\\Desktop\\Mes_projets\\WritingToolsClean\\Windows_and_Linux\\myvenv\\Lib\\site-packages\\setuptools\\_vendor\\wheel\\vendored\\packaging\\specifiers.py',
   'PYMODULE'),
  ('setuptools._vendor.wheel.vendored.packaging.tags',
   'c:\\Users\\<USER>\\Desktop\\Mes_projets\\WritingToolsClean\\Windows_and_Linux\\myvenv\\Lib\\site-packages\\setuptools\\_vendor\\wheel\\vendored\\packaging\\tags.py',
   'PYMODULE'),
  ('setuptools._vendor.wheel.vendored.packaging.utils',
   'c:\\Users\\<USER>\\Desktop\\Mes_projets\\WritingToolsClean\\Windows_and_Linux\\myvenv\\Lib\\site-packages\\setuptools\\_vendor\\wheel\\vendored\\packaging\\utils.py',
   'PYMODULE'),
  ('setuptools._vendor.wheel.vendored.packaging.version',
   'c:\\Users\\<USER>\\Desktop\\Mes_projets\\WritingToolsClean\\Windows_and_Linux\\myvenv\\Lib\\site-packages\\setuptools\\_vendor\\wheel\\vendored\\packaging\\version.py',
   'PYMODULE'),
  ('setuptools._vendor.wheel.wheelfile',
   'c:\\Users\\<USER>\\Desktop\\Mes_projets\\WritingToolsClean\\Windows_and_Linux\\myvenv\\Lib\\site-packages\\setuptools\\_vendor\\wheel\\wheelfile.py',
   'PYMODULE'),
  ('setuptools._vendor.zipp',
   'c:\\Users\\<USER>\\Desktop\\Mes_projets\\WritingToolsClean\\Windows_and_Linux\\myvenv\\Lib\\site-packages\\setuptools\\_vendor\\zipp\\__init__.py',
   'PYMODULE'),
  ('setuptools._vendor.zipp.compat',
   'c:\\Users\\<USER>\\Desktop\\Mes_projets\\WritingToolsClean\\Windows_and_Linux\\myvenv\\Lib\\site-packages\\setuptools\\_vendor\\zipp\\compat\\__init__.py',
   'PYMODULE'),
  ('setuptools._vendor.zipp.compat.py310',
   'c:\\Users\\<USER>\\Desktop\\Mes_projets\\WritingToolsClean\\Windows_and_Linux\\myvenv\\Lib\\site-packages\\setuptools\\_vendor\\zipp\\compat\\py310.py',
   'PYMODULE'),
  ('setuptools._vendor.zipp.glob',
   'c:\\Users\\<USER>\\Desktop\\Mes_projets\\WritingToolsClean\\Windows_and_Linux\\myvenv\\Lib\\site-packages\\setuptools\\_vendor\\zipp\\glob.py',
   'PYMODULE'),
  ('setuptools.archive_util',
   'c:\\Users\\<USER>\\Desktop\\Mes_projets\\WritingToolsClean\\Windows_and_Linux\\myvenv\\Lib\\site-packages\\setuptools\\archive_util.py',
   'PYMODULE'),
  ('setuptools.command',
   'c:\\Users\\<USER>\\Desktop\\Mes_projets\\WritingToolsClean\\Windows_and_Linux\\myvenv\\Lib\\site-packages\\setuptools\\command\\__init__.py',
   'PYMODULE'),
  ('setuptools.command._requirestxt',
   'c:\\Users\\<USER>\\Desktop\\Mes_projets\\WritingToolsClean\\Windows_and_Linux\\myvenv\\Lib\\site-packages\\setuptools\\command\\_requirestxt.py',
   'PYMODULE'),
  ('setuptools.command.bdist_egg',
   'c:\\Users\\<USER>\\Desktop\\Mes_projets\\WritingToolsClean\\Windows_and_Linux\\myvenv\\Lib\\site-packages\\setuptools\\command\\bdist_egg.py',
   'PYMODULE'),
  ('setuptools.command.bdist_wheel',
   'c:\\Users\\<USER>\\Desktop\\Mes_projets\\WritingToolsClean\\Windows_and_Linux\\myvenv\\Lib\\site-packages\\setuptools\\command\\bdist_wheel.py',
   'PYMODULE'),
  ('setuptools.command.build',
   'c:\\Users\\<USER>\\Desktop\\Mes_projets\\WritingToolsClean\\Windows_and_Linux\\myvenv\\Lib\\site-packages\\setuptools\\command\\build.py',
   'PYMODULE'),
  ('setuptools.command.egg_info',
   'c:\\Users\\<USER>\\Desktop\\Mes_projets\\WritingToolsClean\\Windows_and_Linux\\myvenv\\Lib\\site-packages\\setuptools\\command\\egg_info.py',
   'PYMODULE'),
  ('setuptools.command.sdist',
   'c:\\Users\\<USER>\\Desktop\\Mes_projets\\WritingToolsClean\\Windows_and_Linux\\myvenv\\Lib\\site-packages\\setuptools\\command\\sdist.py',
   'PYMODULE'),
  ('setuptools.command.setopt',
   'c:\\Users\\<USER>\\Desktop\\Mes_projets\\WritingToolsClean\\Windows_and_Linux\\myvenv\\Lib\\site-packages\\setuptools\\command\\setopt.py',
   'PYMODULE'),
  ('setuptools.compat',
   'c:\\Users\\<USER>\\Desktop\\Mes_projets\\WritingToolsClean\\Windows_and_Linux\\myvenv\\Lib\\site-packages\\setuptools\\compat\\__init__.py',
   'PYMODULE'),
  ('setuptools.compat.py310',
   'c:\\Users\\<USER>\\Desktop\\Mes_projets\\WritingToolsClean\\Windows_and_Linux\\myvenv\\Lib\\site-packages\\setuptools\\compat\\py310.py',
   'PYMODULE'),
  ('setuptools.compat.py311',
   'c:\\Users\\<USER>\\Desktop\\Mes_projets\\WritingToolsClean\\Windows_and_Linux\\myvenv\\Lib\\site-packages\\setuptools\\compat\\py311.py',
   'PYMODULE'),
  ('setuptools.compat.py39',
   'c:\\Users\\<USER>\\Desktop\\Mes_projets\\WritingToolsClean\\Windows_and_Linux\\myvenv\\Lib\\site-packages\\setuptools\\compat\\py39.py',
   'PYMODULE'),
  ('setuptools.config',
   'c:\\Users\\<USER>\\Desktop\\Mes_projets\\WritingToolsClean\\Windows_and_Linux\\myvenv\\Lib\\site-packages\\setuptools\\config\\__init__.py',
   'PYMODULE'),
  ('setuptools.config._apply_pyprojecttoml',
   'c:\\Users\\<USER>\\Desktop\\Mes_projets\\WritingToolsClean\\Windows_and_Linux\\myvenv\\Lib\\site-packages\\setuptools\\config\\_apply_pyprojecttoml.py',
   'PYMODULE'),
  ('setuptools.config._validate_pyproject',
   'c:\\Users\\<USER>\\Desktop\\Mes_projets\\WritingToolsClean\\Windows_and_Linux\\myvenv\\Lib\\site-packages\\setuptools\\config\\_validate_pyproject\\__init__.py',
   'PYMODULE'),
  ('setuptools.config._validate_pyproject.error_reporting',
   'c:\\Users\\<USER>\\Desktop\\Mes_projets\\WritingToolsClean\\Windows_and_Linux\\myvenv\\Lib\\site-packages\\setuptools\\config\\_validate_pyproject\\error_reporting.py',
   'PYMODULE'),
  ('setuptools.config._validate_pyproject.extra_validations',
   'c:\\Users\\<USER>\\Desktop\\Mes_projets\\WritingToolsClean\\Windows_and_Linux\\myvenv\\Lib\\site-packages\\setuptools\\config\\_validate_pyproject\\extra_validations.py',
   'PYMODULE'),
  ('setuptools.config._validate_pyproject.fastjsonschema_exceptions',
   'c:\\Users\\<USER>\\Desktop\\Mes_projets\\WritingToolsClean\\Windows_and_Linux\\myvenv\\Lib\\site-packages\\setuptools\\config\\_validate_pyproject\\fastjsonschema_exceptions.py',
   'PYMODULE'),
  ('setuptools.config._validate_pyproject.fastjsonschema_validations',
   'c:\\Users\\<USER>\\Desktop\\Mes_projets\\WritingToolsClean\\Windows_and_Linux\\myvenv\\Lib\\site-packages\\setuptools\\config\\_validate_pyproject\\fastjsonschema_validations.py',
   'PYMODULE'),
  ('setuptools.config._validate_pyproject.formats',
   'c:\\Users\\<USER>\\Desktop\\Mes_projets\\WritingToolsClean\\Windows_and_Linux\\myvenv\\Lib\\site-packages\\setuptools\\config\\_validate_pyproject\\formats.py',
   'PYMODULE'),
  ('setuptools.config.expand',
   'c:\\Users\\<USER>\\Desktop\\Mes_projets\\WritingToolsClean\\Windows_and_Linux\\myvenv\\Lib\\site-packages\\setuptools\\config\\expand.py',
   'PYMODULE'),
  ('setuptools.config.pyprojecttoml',
   'c:\\Users\\<USER>\\Desktop\\Mes_projets\\WritingToolsClean\\Windows_and_Linux\\myvenv\\Lib\\site-packages\\setuptools\\config\\pyprojecttoml.py',
   'PYMODULE'),
  ('setuptools.config.setupcfg',
   'c:\\Users\\<USER>\\Desktop\\Mes_projets\\WritingToolsClean\\Windows_and_Linux\\myvenv\\Lib\\site-packages\\setuptools\\config\\setupcfg.py',
   'PYMODULE'),
  ('setuptools.depends',
   'c:\\Users\\<USER>\\Desktop\\Mes_projets\\WritingToolsClean\\Windows_and_Linux\\myvenv\\Lib\\site-packages\\setuptools\\depends.py',
   'PYMODULE'),
  ('setuptools.discovery',
   'c:\\Users\\<USER>\\Desktop\\Mes_projets\\WritingToolsClean\\Windows_and_Linux\\myvenv\\Lib\\site-packages\\setuptools\\discovery.py',
   'PYMODULE'),
  ('setuptools.dist',
   'c:\\Users\\<USER>\\Desktop\\Mes_projets\\WritingToolsClean\\Windows_and_Linux\\myvenv\\Lib\\site-packages\\setuptools\\dist.py',
   'PYMODULE'),
  ('setuptools.errors',
   'c:\\Users\\<USER>\\Desktop\\Mes_projets\\WritingToolsClean\\Windows_and_Linux\\myvenv\\Lib\\site-packages\\setuptools\\errors.py',
   'PYMODULE'),
  ('setuptools.extension',
   'c:\\Users\\<USER>\\Desktop\\Mes_projets\\WritingToolsClean\\Windows_and_Linux\\myvenv\\Lib\\site-packages\\setuptools\\extension.py',
   'PYMODULE'),
  ('setuptools.glob',
   'c:\\Users\\<USER>\\Desktop\\Mes_projets\\WritingToolsClean\\Windows_and_Linux\\myvenv\\Lib\\site-packages\\setuptools\\glob.py',
   'PYMODULE'),
  ('setuptools.installer',
   'c:\\Users\\<USER>\\Desktop\\Mes_projets\\WritingToolsClean\\Windows_and_Linux\\myvenv\\Lib\\site-packages\\setuptools\\installer.py',
   'PYMODULE'),
  ('setuptools.logging',
   'c:\\Users\\<USER>\\Desktop\\Mes_projets\\WritingToolsClean\\Windows_and_Linux\\myvenv\\Lib\\site-packages\\setuptools\\logging.py',
   'PYMODULE'),
  ('setuptools.monkey',
   'c:\\Users\\<USER>\\Desktop\\Mes_projets\\WritingToolsClean\\Windows_and_Linux\\myvenv\\Lib\\site-packages\\setuptools\\monkey.py',
   'PYMODULE'),
  ('setuptools.msvc',
   'c:\\Users\\<USER>\\Desktop\\Mes_projets\\WritingToolsClean\\Windows_and_Linux\\myvenv\\Lib\\site-packages\\setuptools\\msvc.py',
   'PYMODULE'),
  ('setuptools.unicode_utils',
   'c:\\Users\\<USER>\\Desktop\\Mes_projets\\WritingToolsClean\\Windows_and_Linux\\myvenv\\Lib\\site-packages\\setuptools\\unicode_utils.py',
   'PYMODULE'),
  ('setuptools.version',
   'c:\\Users\\<USER>\\Desktop\\Mes_projets\\WritingToolsClean\\Windows_and_Linux\\myvenv\\Lib\\site-packages\\setuptools\\version.py',
   'PYMODULE'),
  ('setuptools.warnings',
   'c:\\Users\\<USER>\\Desktop\\Mes_projets\\WritingToolsClean\\Windows_and_Linux\\myvenv\\Lib\\site-packages\\setuptools\\warnings.py',
   'PYMODULE'),
  ('setuptools.wheel',
   'c:\\Users\\<USER>\\Desktop\\Mes_projets\\WritingToolsClean\\Windows_and_Linux\\myvenv\\Lib\\site-packages\\setuptools\\wheel.py',
   'PYMODULE'),
  ('setuptools.windows_support',
   'c:\\Users\\<USER>\\Desktop\\Mes_projets\\WritingToolsClean\\Windows_and_Linux\\myvenv\\Lib\\site-packages\\setuptools\\windows_support.py',
   'PYMODULE'),
  ('shiboken6',
   'c:\\Users\\<USER>\\Desktop\\Mes_projets\\WritingToolsClean\\Windows_and_Linux\\myvenv\\Lib\\site-packages\\shiboken6\\__init__.py',
   'PYMODULE'),
  ('shlex',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\shlex.py',
   'PYMODULE'),
  ('shutil',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\shutil.py',
   'PYMODULE'),
  ('signal',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\signal.py',
   'PYMODULE'),
  ('site',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\site.py',
   'PYMODULE'),
  ('six',
   'c:\\Users\\<USER>\\Desktop\\Mes_projets\\WritingToolsClean\\Windows_and_Linux\\myvenv\\Lib\\site-packages\\six.py',
   'PYMODULE'),
  ('sniffio',
   'c:\\Users\\<USER>\\Desktop\\Mes_projets\\WritingToolsClean\\Windows_and_Linux\\myvenv\\Lib\\site-packages\\sniffio\\__init__.py',
   'PYMODULE'),
  ('sniffio._impl',
   'c:\\Users\\<USER>\\Desktop\\Mes_projets\\WritingToolsClean\\Windows_and_Linux\\myvenv\\Lib\\site-packages\\sniffio\\_impl.py',
   'PYMODULE'),
  ('sniffio._version',
   'c:\\Users\\<USER>\\Desktop\\Mes_projets\\WritingToolsClean\\Windows_and_Linux\\myvenv\\Lib\\site-packages\\sniffio\\_version.py',
   'PYMODULE'),
  ('socket',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\socket.py',
   'PYMODULE'),
  ('socketserver',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\socketserver.py',
   'PYMODULE'),
  ('ssl',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\ssl.py',
   'PYMODULE'),
  ('statistics',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\statistics.py',
   'PYMODULE'),
  ('string',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\string.py',
   'PYMODULE'),
  ('stringprep',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\stringprep.py',
   'PYMODULE'),
  ('subprocess',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\subprocess.py',
   'PYMODULE'),
  ('sysconfig',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\sysconfig.py',
   'PYMODULE'),
  ('tarfile',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\tarfile.py',
   'PYMODULE'),
  ('tempfile',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\tempfile.py',
   'PYMODULE'),
  ('textwrap',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\textwrap.py',
   'PYMODULE'),
  ('threading',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\threading.py',
   'PYMODULE'),
  ('token',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\token.py',
   'PYMODULE'),
  ('tokenize',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\tokenize.py',
   'PYMODULE'),
  ('tomllib',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\tomllib\\__init__.py',
   'PYMODULE'),
  ('tomllib._parser',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\tomllib\\_parser.py',
   'PYMODULE'),
  ('tomllib._re',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\tomllib\\_re.py',
   'PYMODULE'),
  ('tomllib._types',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\tomllib\\_types.py',
   'PYMODULE'),
  ('tqdm',
   'c:\\Users\\<USER>\\Desktop\\Mes_projets\\WritingToolsClean\\Windows_and_Linux\\myvenv\\Lib\\site-packages\\tqdm\\__init__.py',
   'PYMODULE'),
  ('tqdm._dist_ver',
   'c:\\Users\\<USER>\\Desktop\\Mes_projets\\WritingToolsClean\\Windows_and_Linux\\myvenv\\Lib\\site-packages\\tqdm\\_dist_ver.py',
   'PYMODULE'),
  ('tqdm._monitor',
   'c:\\Users\\<USER>\\Desktop\\Mes_projets\\WritingToolsClean\\Windows_and_Linux\\myvenv\\Lib\\site-packages\\tqdm\\_monitor.py',
   'PYMODULE'),
  ('tqdm._tqdm_pandas',
   'c:\\Users\\<USER>\\Desktop\\Mes_projets\\WritingToolsClean\\Windows_and_Linux\\myvenv\\Lib\\site-packages\\tqdm\\_tqdm_pandas.py',
   'PYMODULE'),
  ('tqdm.asyncio',
   'c:\\Users\\<USER>\\Desktop\\Mes_projets\\WritingToolsClean\\Windows_and_Linux\\myvenv\\Lib\\site-packages\\tqdm\\asyncio.py',
   'PYMODULE'),
  ('tqdm.auto',
   'c:\\Users\\<USER>\\Desktop\\Mes_projets\\WritingToolsClean\\Windows_and_Linux\\myvenv\\Lib\\site-packages\\tqdm\\auto.py',
   'PYMODULE'),
  ('tqdm.autonotebook',
   'c:\\Users\\<USER>\\Desktop\\Mes_projets\\WritingToolsClean\\Windows_and_Linux\\myvenv\\Lib\\site-packages\\tqdm\\autonotebook.py',
   'PYMODULE'),
  ('tqdm.cli',
   'c:\\Users\\<USER>\\Desktop\\Mes_projets\\WritingToolsClean\\Windows_and_Linux\\myvenv\\Lib\\site-packages\\tqdm\\cli.py',
   'PYMODULE'),
  ('tqdm.gui',
   'c:\\Users\\<USER>\\Desktop\\Mes_projets\\WritingToolsClean\\Windows_and_Linux\\myvenv\\Lib\\site-packages\\tqdm\\gui.py',
   'PYMODULE'),
  ('tqdm.notebook',
   'c:\\Users\\<USER>\\Desktop\\Mes_projets\\WritingToolsClean\\Windows_and_Linux\\myvenv\\Lib\\site-packages\\tqdm\\notebook.py',
   'PYMODULE'),
  ('tqdm.std',
   'c:\\Users\\<USER>\\Desktop\\Mes_projets\\WritingToolsClean\\Windows_and_Linux\\myvenv\\Lib\\site-packages\\tqdm\\std.py',
   'PYMODULE'),
  ('tqdm.utils',
   'c:\\Users\\<USER>\\Desktop\\Mes_projets\\WritingToolsClean\\Windows_and_Linux\\myvenv\\Lib\\site-packages\\tqdm\\utils.py',
   'PYMODULE'),
  ('tqdm.version',
   'c:\\Users\\<USER>\\Desktop\\Mes_projets\\WritingToolsClean\\Windows_and_Linux\\myvenv\\Lib\\site-packages\\tqdm\\version.py',
   'PYMODULE'),
  ('tracemalloc',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\tracemalloc.py',
   'PYMODULE'),
  ('tty',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\tty.py',
   'PYMODULE'),
  ('typing',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\typing.py',
   'PYMODULE'),
  ('typing_extensions',
   'c:\\Users\\<USER>\\Desktop\\Mes_projets\\WritingToolsClean\\Windows_and_Linux\\myvenv\\Lib\\site-packages\\typing_extensions.py',
   'PYMODULE'),
  ('typing_inspection',
   'c:\\Users\\<USER>\\Desktop\\Mes_projets\\WritingToolsClean\\Windows_and_Linux\\myvenv\\Lib\\site-packages\\typing_inspection\\__init__.py',
   'PYMODULE'),
  ('typing_inspection.introspection',
   'c:\\Users\\<USER>\\Desktop\\Mes_projets\\WritingToolsClean\\Windows_and_Linux\\myvenv\\Lib\\site-packages\\typing_inspection\\introspection.py',
   'PYMODULE'),
  ('typing_inspection.typing_objects',
   'c:\\Users\\<USER>\\Desktop\\Mes_projets\\WritingToolsClean\\Windows_and_Linux\\myvenv\\Lib\\site-packages\\typing_inspection\\typing_objects.py',
   'PYMODULE'),
  ('ui',
   'c:\\Users\\<USER>\\Desktop\\Mes_projets\\WritingToolsClean\\Windows_and_Linux\\ui\\__init__.py',
   'PYMODULE'),
  ('ui.AboutWindow',
   'c:\\Users\\<USER>\\Desktop\\Mes_projets\\WritingToolsClean\\Windows_and_Linux\\ui\\AboutWindow.py',
   'PYMODULE'),
  ('ui.AutostartManager',
   'c:\\Users\\<USER>\\Desktop\\Mes_projets\\WritingToolsClean\\Windows_and_Linux\\ui\\AutostartManager.py',
   'PYMODULE'),
  ('ui.CustomPopupWindow',
   'c:\\Users\\<USER>\\Desktop\\Mes_projets\\WritingToolsClean\\Windows_and_Linux\\ui\\CustomPopupWindow.py',
   'PYMODULE'),
  ('ui.OnboardingWindow',
   'c:\\Users\\<USER>\\Desktop\\Mes_projets\\WritingToolsClean\\Windows_and_Linux\\ui\\OnboardingWindow.py',
   'PYMODULE'),
  ('ui.ResponseWindow',
   'c:\\Users\\<USER>\\Desktop\\Mes_projets\\WritingToolsClean\\Windows_and_Linux\\ui\\ResponseWindow.py',
   'PYMODULE'),
  ('ui.SettingsWindow',
   'c:\\Users\\<USER>\\Desktop\\Mes_projets\\WritingToolsClean\\Windows_and_Linux\\ui\\SettingsWindow.py',
   'PYMODULE'),
  ('ui.UIUtils',
   'c:\\Users\\<USER>\\Desktop\\Mes_projets\\WritingToolsClean\\Windows_and_Linux\\ui\\UIUtils.py',
   'PYMODULE'),
  ('update_checker',
   'c:\\Users\\<USER>\\Desktop\\Mes_projets\\WritingToolsClean\\Windows_and_Linux\\update_checker.py',
   'PYMODULE'),
  ('uritemplate',
   'c:\\Users\\<USER>\\Desktop\\Mes_projets\\WritingToolsClean\\Windows_and_Linux\\myvenv\\Lib\\site-packages\\uritemplate\\__init__.py',
   'PYMODULE'),
  ('uritemplate.api',
   'c:\\Users\\<USER>\\Desktop\\Mes_projets\\WritingToolsClean\\Windows_and_Linux\\myvenv\\Lib\\site-packages\\uritemplate\\api.py',
   'PYMODULE'),
  ('uritemplate.orderedset',
   'c:\\Users\\<USER>\\Desktop\\Mes_projets\\WritingToolsClean\\Windows_and_Linux\\myvenv\\Lib\\site-packages\\uritemplate\\orderedset.py',
   'PYMODULE'),
  ('uritemplate.template',
   'c:\\Users\\<USER>\\Desktop\\Mes_projets\\WritingToolsClean\\Windows_and_Linux\\myvenv\\Lib\\site-packages\\uritemplate\\template.py',
   'PYMODULE'),
  ('uritemplate.variable',
   'c:\\Users\\<USER>\\Desktop\\Mes_projets\\WritingToolsClean\\Windows_and_Linux\\myvenv\\Lib\\site-packages\\uritemplate\\variable.py',
   'PYMODULE'),
  ('urllib',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\urllib\\__init__.py',
   'PYMODULE'),
  ('urllib.error',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\urllib\\error.py',
   'PYMODULE'),
  ('urllib.parse',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\urllib\\parse.py',
   'PYMODULE'),
  ('urllib.request',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\urllib\\request.py',
   'PYMODULE'),
  ('urllib.response',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\urllib\\response.py',
   'PYMODULE'),
  ('urllib3',
   'c:\\Users\\<USER>\\Desktop\\Mes_projets\\WritingToolsClean\\Windows_and_Linux\\myvenv\\Lib\\site-packages\\urllib3\\__init__.py',
   'PYMODULE'),
  ('urllib3._base_connection',
   'c:\\Users\\<USER>\\Desktop\\Mes_projets\\WritingToolsClean\\Windows_and_Linux\\myvenv\\Lib\\site-packages\\urllib3\\_base_connection.py',
   'PYMODULE'),
  ('urllib3._collections',
   'c:\\Users\\<USER>\\Desktop\\Mes_projets\\WritingToolsClean\\Windows_and_Linux\\myvenv\\Lib\\site-packages\\urllib3\\_collections.py',
   'PYMODULE'),
  ('urllib3._request_methods',
   'c:\\Users\\<USER>\\Desktop\\Mes_projets\\WritingToolsClean\\Windows_and_Linux\\myvenv\\Lib\\site-packages\\urllib3\\_request_methods.py',
   'PYMODULE'),
  ('urllib3._version',
   'c:\\Users\\<USER>\\Desktop\\Mes_projets\\WritingToolsClean\\Windows_and_Linux\\myvenv\\Lib\\site-packages\\urllib3\\_version.py',
   'PYMODULE'),
  ('urllib3.connection',
   'c:\\Users\\<USER>\\Desktop\\Mes_projets\\WritingToolsClean\\Windows_and_Linux\\myvenv\\Lib\\site-packages\\urllib3\\connection.py',
   'PYMODULE'),
  ('urllib3.connectionpool',
   'c:\\Users\\<USER>\\Desktop\\Mes_projets\\WritingToolsClean\\Windows_and_Linux\\myvenv\\Lib\\site-packages\\urllib3\\connectionpool.py',
   'PYMODULE'),
  ('urllib3.contrib',
   'c:\\Users\\<USER>\\Desktop\\Mes_projets\\WritingToolsClean\\Windows_and_Linux\\myvenv\\Lib\\site-packages\\urllib3\\contrib\\__init__.py',
   'PYMODULE'),
  ('urllib3.contrib.emscripten',
   'c:\\Users\\<USER>\\Desktop\\Mes_projets\\WritingToolsClean\\Windows_and_Linux\\myvenv\\Lib\\site-packages\\urllib3\\contrib\\emscripten\\__init__.py',
   'PYMODULE'),
  ('urllib3.contrib.emscripten.connection',
   'c:\\Users\\<USER>\\Desktop\\Mes_projets\\WritingToolsClean\\Windows_and_Linux\\myvenv\\Lib\\site-packages\\urllib3\\contrib\\emscripten\\connection.py',
   'PYMODULE'),
  ('urllib3.contrib.emscripten.fetch',
   'c:\\Users\\<USER>\\Desktop\\Mes_projets\\WritingToolsClean\\Windows_and_Linux\\myvenv\\Lib\\site-packages\\urllib3\\contrib\\emscripten\\fetch.py',
   'PYMODULE'),
  ('urllib3.contrib.emscripten.request',
   'c:\\Users\\<USER>\\Desktop\\Mes_projets\\WritingToolsClean\\Windows_and_Linux\\myvenv\\Lib\\site-packages\\urllib3\\contrib\\emscripten\\request.py',
   'PYMODULE'),
  ('urllib3.contrib.emscripten.response',
   'c:\\Users\\<USER>\\Desktop\\Mes_projets\\WritingToolsClean\\Windows_and_Linux\\myvenv\\Lib\\site-packages\\urllib3\\contrib\\emscripten\\response.py',
   'PYMODULE'),
  ('urllib3.contrib.pyopenssl',
   'c:\\Users\\<USER>\\Desktop\\Mes_projets\\WritingToolsClean\\Windows_and_Linux\\myvenv\\Lib\\site-packages\\urllib3\\contrib\\pyopenssl.py',
   'PYMODULE'),
  ('urllib3.contrib.socks',
   'c:\\Users\\<USER>\\Desktop\\Mes_projets\\WritingToolsClean\\Windows_and_Linux\\myvenv\\Lib\\site-packages\\urllib3\\contrib\\socks.py',
   'PYMODULE'),
  ('urllib3.exceptions',
   'c:\\Users\\<USER>\\Desktop\\Mes_projets\\WritingToolsClean\\Windows_and_Linux\\myvenv\\Lib\\site-packages\\urllib3\\exceptions.py',
   'PYMODULE'),
  ('urllib3.fields',
   'c:\\Users\\<USER>\\Desktop\\Mes_projets\\WritingToolsClean\\Windows_and_Linux\\myvenv\\Lib\\site-packages\\urllib3\\fields.py',
   'PYMODULE'),
  ('urllib3.filepost',
   'c:\\Users\\<USER>\\Desktop\\Mes_projets\\WritingToolsClean\\Windows_and_Linux\\myvenv\\Lib\\site-packages\\urllib3\\filepost.py',
   'PYMODULE'),
  ('urllib3.http2',
   'c:\\Users\\<USER>\\Desktop\\Mes_projets\\WritingToolsClean\\Windows_and_Linux\\myvenv\\Lib\\site-packages\\urllib3\\http2\\__init__.py',
   'PYMODULE'),
  ('urllib3.http2.connection',
   'c:\\Users\\<USER>\\Desktop\\Mes_projets\\WritingToolsClean\\Windows_and_Linux\\myvenv\\Lib\\site-packages\\urllib3\\http2\\connection.py',
   'PYMODULE'),
  ('urllib3.http2.probe',
   'c:\\Users\\<USER>\\Desktop\\Mes_projets\\WritingToolsClean\\Windows_and_Linux\\myvenv\\Lib\\site-packages\\urllib3\\http2\\probe.py',
   'PYMODULE'),
  ('urllib3.poolmanager',
   'c:\\Users\\<USER>\\Desktop\\Mes_projets\\WritingToolsClean\\Windows_and_Linux\\myvenv\\Lib\\site-packages\\urllib3\\poolmanager.py',
   'PYMODULE'),
  ('urllib3.response',
   'c:\\Users\\<USER>\\Desktop\\Mes_projets\\WritingToolsClean\\Windows_and_Linux\\myvenv\\Lib\\site-packages\\urllib3\\response.py',
   'PYMODULE'),
  ('urllib3.util',
   'c:\\Users\\<USER>\\Desktop\\Mes_projets\\WritingToolsClean\\Windows_and_Linux\\myvenv\\Lib\\site-packages\\urllib3\\util\\__init__.py',
   'PYMODULE'),
  ('urllib3.util.connection',
   'c:\\Users\\<USER>\\Desktop\\Mes_projets\\WritingToolsClean\\Windows_and_Linux\\myvenv\\Lib\\site-packages\\urllib3\\util\\connection.py',
   'PYMODULE'),
  ('urllib3.util.proxy',
   'c:\\Users\\<USER>\\Desktop\\Mes_projets\\WritingToolsClean\\Windows_and_Linux\\myvenv\\Lib\\site-packages\\urllib3\\util\\proxy.py',
   'PYMODULE'),
  ('urllib3.util.request',
   'c:\\Users\\<USER>\\Desktop\\Mes_projets\\WritingToolsClean\\Windows_and_Linux\\myvenv\\Lib\\site-packages\\urllib3\\util\\request.py',
   'PYMODULE'),
  ('urllib3.util.response',
   'c:\\Users\\<USER>\\Desktop\\Mes_projets\\WritingToolsClean\\Windows_and_Linux\\myvenv\\Lib\\site-packages\\urllib3\\util\\response.py',
   'PYMODULE'),
  ('urllib3.util.retry',
   'c:\\Users\\<USER>\\Desktop\\Mes_projets\\WritingToolsClean\\Windows_and_Linux\\myvenv\\Lib\\site-packages\\urllib3\\util\\retry.py',
   'PYMODULE'),
  ('urllib3.util.ssl_',
   'c:\\Users\\<USER>\\Desktop\\Mes_projets\\WritingToolsClean\\Windows_and_Linux\\myvenv\\Lib\\site-packages\\urllib3\\util\\ssl_.py',
   'PYMODULE'),
  ('urllib3.util.ssl_match_hostname',
   'c:\\Users\\<USER>\\Desktop\\Mes_projets\\WritingToolsClean\\Windows_and_Linux\\myvenv\\Lib\\site-packages\\urllib3\\util\\ssl_match_hostname.py',
   'PYMODULE'),
  ('urllib3.util.ssltransport',
   'c:\\Users\\<USER>\\Desktop\\Mes_projets\\WritingToolsClean\\Windows_and_Linux\\myvenv\\Lib\\site-packages\\urllib3\\util\\ssltransport.py',
   'PYMODULE'),
  ('urllib3.util.timeout',
   'c:\\Users\\<USER>\\Desktop\\Mes_projets\\WritingToolsClean\\Windows_and_Linux\\myvenv\\Lib\\site-packages\\urllib3\\util\\timeout.py',
   'PYMODULE'),
  ('urllib3.util.url',
   'c:\\Users\\<USER>\\Desktop\\Mes_projets\\WritingToolsClean\\Windows_and_Linux\\myvenv\\Lib\\site-packages\\urllib3\\util\\url.py',
   'PYMODULE'),
  ('urllib3.util.util',
   'c:\\Users\\<USER>\\Desktop\\Mes_projets\\WritingToolsClean\\Windows_and_Linux\\myvenv\\Lib\\site-packages\\urllib3\\util\\util.py',
   'PYMODULE'),
  ('urllib3.util.wait',
   'c:\\Users\\<USER>\\Desktop\\Mes_projets\\WritingToolsClean\\Windows_and_Linux\\myvenv\\Lib\\site-packages\\urllib3\\util\\wait.py',
   'PYMODULE'),
  ('uuid',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\uuid.py',
   'PYMODULE'),
  ('webbrowser',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\webbrowser.py',
   'PYMODULE'),
  ('xml',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\xml\\__init__.py',
   'PYMODULE'),
  ('xml.parsers',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\xml\\parsers\\__init__.py',
   'PYMODULE'),
  ('xml.parsers.expat',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\xml\\parsers\\expat.py',
   'PYMODULE'),
  ('xml.sax',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\xml\\sax\\__init__.py',
   'PYMODULE'),
  ('xml.sax._exceptions',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\xml\\sax\\_exceptions.py',
   'PYMODULE'),
  ('xml.sax.expatreader',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\xml\\sax\\expatreader.py',
   'PYMODULE'),
  ('xml.sax.handler',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\xml\\sax\\handler.py',
   'PYMODULE'),
  ('xml.sax.saxutils',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\xml\\sax\\saxutils.py',
   'PYMODULE'),
  ('xml.sax.xmlreader',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\xml\\sax\\xmlreader.py',
   'PYMODULE'),
  ('xmlrpc',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\xmlrpc\\__init__.py',
   'PYMODULE'),
  ('xmlrpc.client',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\xmlrpc\\client.py',
   'PYMODULE'),
  ('zipfile',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\zipfile\\__init__.py',
   'PYMODULE'),
  ('zipfile._path',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\zipfile\\_path\\__init__.py',
   'PYMODULE'),
  ('zipfile._path.glob',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\zipfile\\_path\\glob.py',
   'PYMODULE'),
  ('zipimport',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\zipimport.py',
   'PYMODULE'),
  ('zoneinfo',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\zoneinfo\\__init__.py',
   'PYMODULE'),
  ('zoneinfo._common',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\zoneinfo\\_common.py',
   'PYMODULE'),
  ('zoneinfo._tzpath',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\zoneinfo\\_tzpath.py',
   'PYMODULE'),
  ('zoneinfo._zoneinfo',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\zoneinfo\\_zoneinfo.py',
   'PYMODULE')])
