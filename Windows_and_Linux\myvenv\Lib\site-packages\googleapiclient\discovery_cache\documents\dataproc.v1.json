{"auth": {"oauth2": {"scopes": {"https://www.googleapis.com/auth/cloud-platform": {"description": "See, edit, configure, and delete your Google Cloud data and see the email address for your Google Account."}}}}, "basePath": "", "baseUrl": "https://dataproc.googleapis.com/", "batchPath": "batch", "canonicalName": "Dataproc", "description": "Manages Hadoop-based clusters and jobs on Google Cloud Platform.", "discoveryVersion": "v1", "documentationLink": "https://cloud.google.com/dataproc/", "endpoints": [{"description": "Regional Endpoint", "endpointUrl": "https://dataproc.europe-west3.rep.googleapis.com/", "location": "europe-west3"}, {"description": "Regional Endpoint", "endpointUrl": "https://dataproc.europe-west9.rep.googleapis.com/", "location": "europe-west9"}, {"description": "Regional Endpoint", "endpointUrl": "https://dataproc.us-central1.rep.googleapis.com/", "location": "us-central1"}, {"description": "Regional Endpoint", "endpointUrl": "https://dataproc.us-central2.rep.googleapis.com/", "location": "us-central2"}, {"description": "Regional Endpoint", "endpointUrl": "https://dataproc.us-east1.rep.googleapis.com/", "location": "us-east1"}, {"description": "Regional Endpoint", "endpointUrl": "https://dataproc.us-east4.rep.googleapis.com/", "location": "us-east4"}, {"description": "Regional Endpoint", "endpointUrl": "https://dataproc.us-east5.rep.googleapis.com/", "location": "us-east5"}, {"description": "Regional Endpoint", "endpointUrl": "https://dataproc.us-east7.rep.googleapis.com/", "location": "us-east7"}, {"description": "Regional Endpoint", "endpointUrl": "https://dataproc.us-south1.rep.googleapis.com/", "location": "us-south1"}, {"description": "Regional Endpoint", "endpointUrl": "https://dataproc.us-west1.rep.googleapis.com/", "location": "us-west1"}, {"description": "Regional Endpoint", "endpointUrl": "https://dataproc.us-west2.rep.googleapis.com/", "location": "us-west2"}, {"description": "Regional Endpoint", "endpointUrl": "https://dataproc.us-west3.rep.googleapis.com/", "location": "us-west3"}, {"description": "Regional Endpoint", "endpointUrl": "https://dataproc.us-west8.rep.googleapis.com/", "location": "us-west8"}, {"description": "Regional Endpoint", "endpointUrl": "https://dataproc.europe-west8.rep.googleapis.com/", "location": "europe-west8"}, {"description": "Regional Endpoint", "endpointUrl": "https://dataproc.me-central2.rep.googleapis.com/", "location": "me-central2"}], "fullyEncodeReservedExpansion": true, "icons": {"x16": "http://www.google.com/images/icons/product/search-16.gif", "x32": "http://www.google.com/images/icons/product/search-32.gif"}, "id": "dataproc:v1", "kind": "discovery#restDescription", "mtlsRootUrl": "https://dataproc.mtls.googleapis.com/", "name": "dataproc", "ownerDomain": "google.com", "ownerName": "Google", "parameters": {"$.xgafv": {"description": "V1 error format.", "enum": ["1", "2"], "enumDescriptions": ["v1 error format", "v2 error format"], "location": "query", "type": "string"}, "access_token": {"description": "OAuth access token.", "location": "query", "type": "string"}, "alt": {"default": "json", "description": "Data format for response.", "enum": ["json", "media", "proto"], "enumDescriptions": ["Responses with Content-Type of application/json", "Media download with context-dependent Content-Type", "Responses with Content-Type of application/x-protobuf"], "location": "query", "type": "string"}, "callback": {"description": "JSONP", "location": "query", "type": "string"}, "fields": {"description": "Selector specifying which fields to include in a partial response.", "location": "query", "type": "string"}, "key": {"description": "API key. Your API key identifies your project and provides you with API access, quota, and reports. Required unless you provide an OAuth 2.0 token.", "location": "query", "type": "string"}, "oauth_token": {"description": "OAuth 2.0 token for the current user.", "location": "query", "type": "string"}, "prettyPrint": {"default": "true", "description": "Returns response with indentations and line breaks.", "location": "query", "type": "boolean"}, "quotaUser": {"description": "Available to use for quota purposes for server-side applications. Can be any arbitrary string assigned to a user, but should not exceed 40 characters.", "location": "query", "type": "string"}, "uploadType": {"description": "Legacy upload protocol for media (e.g. \"media\", \"multipart\").", "location": "query", "type": "string"}, "upload_protocol": {"description": "Upload protocol for media (e.g. \"raw\", \"multipart\").", "location": "query", "type": "string"}}, "protocol": "rest", "resources": {"projects": {"resources": {"locations": {"resources": {"autoscalingPolicies": {"methods": {"create": {"description": "Creates new autoscaling policy.", "flatPath": "v1/projects/{projectsId}/locations/{locationsId}/autoscalingPolicies", "httpMethod": "POST", "id": "dataproc.projects.locations.autoscalingPolicies.create", "parameterOrder": ["parent"], "parameters": {"parent": {"description": "Required. The \"resource name\" of the region or location, as described in https://cloud.google.com/apis/design/resource_names. For projects.regions.autoscalingPolicies.create, the resource name of the region has the following format: projects/{project_id}/regions/{region} For projects.locations.autoscalingPolicies.create, the resource name of the location has the following format: projects/{project_id}/locations/{location}", "location": "path", "pattern": "^projects/[^/]+/locations/[^/]+$", "required": true, "type": "string"}}, "path": "v1/{+parent}/autoscalingPolicies", "request": {"$ref": "AutoscalingPolicy"}, "response": {"$ref": "AutoscalingPolicy"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform"]}, "delete": {"description": "Deletes an autoscaling policy. It is an error to delete an autoscaling policy that is in use by one or more clusters.", "flatPath": "v1/projects/{projectsId}/locations/{locationsId}/autoscalingPolicies/{autoscalingPoliciesId}", "httpMethod": "DELETE", "id": "dataproc.projects.locations.autoscalingPolicies.delete", "parameterOrder": ["name"], "parameters": {"name": {"description": "Required. The \"resource name\" of the autoscaling policy, as described in https://cloud.google.com/apis/design/resource_names. For projects.regions.autoscalingPolicies.delete, the resource name of the policy has the following format: projects/{project_id}/regions/{region}/autoscalingPolicies/{policy_id} For projects.locations.autoscalingPolicies.delete, the resource name of the policy has the following format: projects/{project_id}/locations/{location}/autoscalingPolicies/{policy_id}", "location": "path", "pattern": "^projects/[^/]+/locations/[^/]+/autoscalingPolicies/[^/]+$", "required": true, "type": "string"}}, "path": "v1/{+name}", "response": {"$ref": "Empty"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform"]}, "get": {"description": "Retrieves autoscaling policy.", "flatPath": "v1/projects/{projectsId}/locations/{locationsId}/autoscalingPolicies/{autoscalingPoliciesId}", "httpMethod": "GET", "id": "dataproc.projects.locations.autoscalingPolicies.get", "parameterOrder": ["name"], "parameters": {"name": {"description": "Required. The \"resource name\" of the autoscaling policy, as described in https://cloud.google.com/apis/design/resource_names. For projects.regions.autoscalingPolicies.get, the resource name of the policy has the following format: projects/{project_id}/regions/{region}/autoscalingPolicies/{policy_id} For projects.locations.autoscalingPolicies.get, the resource name of the policy has the following format: projects/{project_id}/locations/{location}/autoscalingPolicies/{policy_id}", "location": "path", "pattern": "^projects/[^/]+/locations/[^/]+/autoscalingPolicies/[^/]+$", "required": true, "type": "string"}}, "path": "v1/{+name}", "response": {"$ref": "AutoscalingPolicy"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform"]}, "getIamPolicy": {"description": "Gets the access control policy for a resource. Returns an empty policy if the resource exists and does not have a policy set.", "flatPath": "v1/projects/{projectsId}/locations/{locationsId}/autoscalingPolicies/{autoscalingPoliciesId}:getIamPolicy", "httpMethod": "POST", "id": "dataproc.projects.locations.autoscalingPolicies.getIamPolicy", "parameterOrder": ["resource"], "parameters": {"resource": {"description": "REQUIRED: The resource for which the policy is being requested. See Resource names (https://cloud.google.com/apis/design/resource_names) for the appropriate value for this field.", "location": "path", "pattern": "^projects/[^/]+/locations/[^/]+/autoscalingPolicies/[^/]+$", "required": true, "type": "string"}}, "path": "v1/{+resource}:getIamPolicy", "request": {"$ref": "GetIamPolicyRequest"}, "response": {"$ref": "Policy"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform"]}, "list": {"description": "Lists autoscaling policies in the project.", "flatPath": "v1/projects/{projectsId}/locations/{locationsId}/autoscalingPolicies", "httpMethod": "GET", "id": "dataproc.projects.locations.autoscalingPolicies.list", "parameterOrder": ["parent"], "parameters": {"pageSize": {"description": "Optional. The maximum number of results to return in each response. Must be less than or equal to 1000. Defaults to 100.", "format": "int32", "location": "query", "type": "integer"}, "pageToken": {"description": "Optional. The page token, returned by a previous call, to request the next page of results.", "location": "query", "type": "string"}, "parent": {"description": "Required. The \"resource name\" of the region or location, as described in https://cloud.google.com/apis/design/resource_names. For projects.regions.autoscalingPolicies.list, the resource name of the region has the following format: projects/{project_id}/regions/{region} For projects.locations.autoscalingPolicies.list, the resource name of the location has the following format: projects/{project_id}/locations/{location}", "location": "path", "pattern": "^projects/[^/]+/locations/[^/]+$", "required": true, "type": "string"}}, "path": "v1/{+parent}/autoscalingPolicies", "response": {"$ref": "ListAutoscalingPoliciesResponse"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform"]}, "setIamPolicy": {"description": "Sets the access control policy on the specified resource. Replaces any existing policy.Can return NOT_FOUND, INVALID_ARGUMENT, and PERMISSION_DENIED errors.", "flatPath": "v1/projects/{projectsId}/locations/{locationsId}/autoscalingPolicies/{autoscalingPoliciesId}:setIamPolicy", "httpMethod": "POST", "id": "dataproc.projects.locations.autoscalingPolicies.setIamPolicy", "parameterOrder": ["resource"], "parameters": {"resource": {"description": "REQUIRED: The resource for which the policy is being specified. See Resource names (https://cloud.google.com/apis/design/resource_names) for the appropriate value for this field.", "location": "path", "pattern": "^projects/[^/]+/locations/[^/]+/autoscalingPolicies/[^/]+$", "required": true, "type": "string"}}, "path": "v1/{+resource}:setIamPolicy", "request": {"$ref": "SetIamPolicyRequest"}, "response": {"$ref": "Policy"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform"]}, "testIamPermissions": {"description": "Returns permissions that a caller has on the specified resource. If the resource does not exist, this will return an empty set of permissions, not a NOT_FOUND error.Note: This operation is designed to be used for building permission-aware UIs and command-line tools, not for authorization checking. This operation may \"fail open\" without warning.", "flatPath": "v1/projects/{projectsId}/locations/{locationsId}/autoscalingPolicies/{autoscalingPoliciesId}:testIamPermissions", "httpMethod": "POST", "id": "dataproc.projects.locations.autoscalingPolicies.testIamPermissions", "parameterOrder": ["resource"], "parameters": {"resource": {"description": "REQUIRED: The resource for which the policy detail is being requested. See Resource names (https://cloud.google.com/apis/design/resource_names) for the appropriate value for this field.", "location": "path", "pattern": "^projects/[^/]+/locations/[^/]+/autoscalingPolicies/[^/]+$", "required": true, "type": "string"}}, "path": "v1/{+resource}:testIamPermissions", "request": {"$ref": "TestIamPermissionsRequest"}, "response": {"$ref": "TestIamPermissionsResponse"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform"]}, "update": {"description": "Updates (replaces) autoscaling policy.Disabled check for update_mask, because all updates will be full replacements.", "flatPath": "v1/projects/{projectsId}/locations/{locationsId}/autoscalingPolicies/{autoscalingPoliciesId}", "httpMethod": "PUT", "id": "dataproc.projects.locations.autoscalingPolicies.update", "parameterOrder": ["name"], "parameters": {"name": {"description": "Output only. The \"resource name\" of the autoscaling policy, as described in https://cloud.google.com/apis/design/resource_names. For projects.regions.autoscalingPolicies, the resource name of the policy has the following format: projects/{project_id}/regions/{region}/autoscalingPolicies/{policy_id} For projects.locations.autoscalingPolicies, the resource name of the policy has the following format: projects/{project_id}/locations/{location}/autoscalingPolicies/{policy_id}", "location": "path", "pattern": "^projects/[^/]+/locations/[^/]+/autoscalingPolicies/[^/]+$", "required": true, "type": "string"}}, "path": "v1/{+name}", "request": {"$ref": "AutoscalingPolicy"}, "response": {"$ref": "AutoscalingPolicy"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform"]}}}, "batches": {"methods": {"analyze": {"description": "Analyze a Batch for possible recommendations and insights.", "flatPath": "v1/projects/{projectsId}/locations/{locationsId}/batches/{batchesId}:analyze", "httpMethod": "POST", "id": "dataproc.projects.locations.batches.analyze", "parameterOrder": ["name"], "parameters": {"name": {"description": "Required. The fully qualified name of the batch to analyze in the format \"projects/PROJECT_ID/locations/DATAPROC_REGION/batches/BATCH_ID\"", "location": "path", "pattern": "^projects/[^/]+/locations/[^/]+/batches/[^/]+$", "required": true, "type": "string"}}, "path": "v1/{+name}:analyze", "request": {"$ref": "AnalyzeBatchRequest"}, "response": {"$ref": "Operation"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform"]}, "create": {"description": "Creates a batch workload that executes asynchronously.", "flatPath": "v1/projects/{projectsId}/locations/{locationsId}/batches", "httpMethod": "POST", "id": "dataproc.projects.locations.batches.create", "parameterOrder": ["parent"], "parameters": {"batchId": {"description": "Optional. The ID to use for the batch, which will become the final component of the batch's resource name.This value must be 4-63 characters. Valid characters are /[a-z][0-9]-/.", "location": "query", "type": "string"}, "parent": {"description": "Required. The parent resource where this batch will be created.", "location": "path", "pattern": "^projects/[^/]+/locations/[^/]+$", "required": true, "type": "string"}, "requestId": {"description": "Optional. A unique ID used to identify the request. If the service receives two CreateBatchRequest (https://cloud.google.com/dataproc/docs/reference/rpc/google.cloud.dataproc.v1#google.cloud.dataproc.v1.CreateBatchRequest)s with the same request_id, the second request is ignored and the Operation that corresponds to the first Batch created and stored in the backend is returned.Recommendation: Set this value to a UUID (https://en.wikipedia.org/wiki/Universally_unique_identifier).The value must contain only letters (a-z, A-Z), numbers (0-9), underscores (_), and hyphens (-). The maximum length is 40 characters.", "location": "query", "type": "string"}}, "path": "v1/{+parent}/batches", "request": {"$ref": "<PERSON><PERSON>"}, "response": {"$ref": "Operation"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform"]}, "delete": {"description": "Deletes the batch workload resource. If the batch is not in a CANCELLED, SUCCEEDED or FAILED State, the delete operation fails and the response returns FAILED_PRECONDITION.", "flatPath": "v1/projects/{projectsId}/locations/{locationsId}/batches/{batchesId}", "httpMethod": "DELETE", "id": "dataproc.projects.locations.batches.delete", "parameterOrder": ["name"], "parameters": {"name": {"description": "Required. The fully qualified name of the batch to retrieve in the format \"projects/PROJECT_ID/locations/DATAPROC_REGION/batches/BATCH_ID\"", "location": "path", "pattern": "^projects/[^/]+/locations/[^/]+/batches/[^/]+$", "required": true, "type": "string"}}, "path": "v1/{+name}", "response": {"$ref": "Empty"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform"]}, "get": {"description": "Gets the batch workload resource representation.", "flatPath": "v1/projects/{projectsId}/locations/{locationsId}/batches/{batchesId}", "httpMethod": "GET", "id": "dataproc.projects.locations.batches.get", "parameterOrder": ["name"], "parameters": {"name": {"description": "Required. The fully qualified name of the batch to retrieve in the format \"projects/PROJECT_ID/locations/DATAPROC_REGION/batches/BATCH_ID\"", "location": "path", "pattern": "^projects/[^/]+/locations/[^/]+/batches/[^/]+$", "required": true, "type": "string"}}, "path": "v1/{+name}", "response": {"$ref": "<PERSON><PERSON>"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform"]}, "list": {"description": "Lists batch workloads.", "flatPath": "v1/projects/{projectsId}/locations/{locationsId}/batches", "httpMethod": "GET", "id": "dataproc.projects.locations.batches.list", "parameterOrder": ["parent"], "parameters": {"filter": {"description": "Optional. A filter for the batches to return in the response.A filter is a logical expression constraining the values of various fields in each batch resource. Filters are case sensitive, and may contain multiple clauses combined with logical operators (AND/OR). Supported fields are batch_id, batch_uuid, state, create_time, and labels.e.g. state = RUNNING and create_time < \"2023-01-01T00:00:00Z\" filters for batches in state RUNNING that were created before 2023-01-01. state = RUNNING and labels.environment=production filters for batches in state in a RUNNING state that have a production environment label.See https://google.aip.dev/assets/misc/ebnf-filtering.txt for a detailed description of the filter syntax and a list of supported comparisons.", "location": "query", "type": "string"}, "orderBy": {"description": "Optional. Field(s) on which to sort the list of batches.Currently the only supported sort orders are unspecified (empty) and create_time desc to sort by most recently created batches first.See https://google.aip.dev/132#ordering for more details.", "location": "query", "type": "string"}, "pageSize": {"description": "Optional. The maximum number of batches to return in each response. The service may return fewer than this value. The default page size is 20; the maximum page size is 1000.", "format": "int32", "location": "query", "type": "integer"}, "pageToken": {"description": "Optional. A page token received from a previous ListBatches call. Provide this token to retrieve the subsequent page.", "location": "query", "type": "string"}, "parent": {"description": "Required. The parent, which owns this collection of batches.", "location": "path", "pattern": "^projects/[^/]+/locations/[^/]+$", "required": true, "type": "string"}}, "path": "v1/{+parent}/batches", "response": {"$ref": "ListBatchesResponse"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform"]}}, "resources": {"sparkApplications": {"methods": {"access": {"description": "Obtain high level information corresponding to a single Spark Application.", "flatPath": "v1/projects/{projectsId}/locations/{locationsId}/batches/{batchesId}/sparkApplications/{sparkApplicationsId}:access", "httpMethod": "GET", "id": "dataproc.projects.locations.batches.sparkApplications.access", "parameterOrder": ["name"], "parameters": {"name": {"description": "Required. The fully qualified name of the batch to retrieve in the format \"projects/PROJECT_ID/locations/DATAPROC_REGION/batches/BATCH_ID/sparkApplications/APPLICATION_ID\"", "location": "path", "pattern": "^projects/[^/]+/locations/[^/]+/batches/[^/]+/sparkApplications/[^/]+$", "required": true, "type": "string"}, "parent": {"description": "Required. Parent (Batch) resource reference.", "location": "query", "type": "string"}}, "path": "v1/{+name}:access", "response": {"$ref": "AccessSparkApplicationResponse"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform"]}, "accessEnvironmentInfo": {"description": "Obtain environment details for a Spark Application", "flatPath": "v1/projects/{projectsId}/locations/{locationsId}/batches/{batchesId}/sparkApplications/{sparkApplicationsId}:accessEnvironmentInfo", "httpMethod": "GET", "id": "dataproc.projects.locations.batches.sparkApplications.accessEnvironmentInfo", "parameterOrder": ["name"], "parameters": {"name": {"description": "Required. The fully qualified name of the batch to retrieve in the format \"projects/PROJECT_ID/locations/DATAPROC_REGION/batches/BATCH_ID/sparkApplications/APPLICATION_ID\"", "location": "path", "pattern": "^projects/[^/]+/locations/[^/]+/batches/[^/]+/sparkApplications/[^/]+$", "required": true, "type": "string"}, "parent": {"description": "Required. Parent (Batch) resource reference.", "location": "query", "type": "string"}}, "path": "v1/{+name}:accessEnvironmentInfo", "response": {"$ref": "AccessSparkApplicationEnvironmentInfoResponse"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform"]}, "accessJob": {"description": "Obtain data corresponding to a spark job for a Spark Application.", "flatPath": "v1/projects/{projectsId}/locations/{locationsId}/batches/{batchesId}/sparkApplications/{sparkApplicationsId}:accessJob", "httpMethod": "GET", "id": "dataproc.projects.locations.batches.sparkApplications.accessJob", "parameterOrder": ["name"], "parameters": {"jobId": {"description": "Required. Job ID to fetch data for.", "format": "int64", "location": "query", "type": "string"}, "name": {"description": "Required. The fully qualified name of the batch to retrieve in the format \"projects/PROJECT_ID/locations/DATAPROC_REGION/batches/BATCH_ID/sparkApplications/APPLICATION_ID\"", "location": "path", "pattern": "^projects/[^/]+/locations/[^/]+/batches/[^/]+/sparkApplications/[^/]+$", "required": true, "type": "string"}, "parent": {"description": "Required. Parent (Batch) resource reference.", "location": "query", "type": "string"}}, "path": "v1/{+name}:accessJob", "response": {"$ref": "AccessSparkApplicationJobResponse"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform"]}, "accessSqlPlan": {"description": "Obtain Spark Plan Graph for a Spark Application SQL execution. Limits the number of clusters returned as part of the graph to 10000.", "flatPath": "v1/projects/{projectsId}/locations/{locationsId}/batches/{batchesId}/sparkApplications/{sparkApplicationsId}:accessSqlPlan", "httpMethod": "GET", "id": "dataproc.projects.locations.batches.sparkApplications.accessSqlPlan", "parameterOrder": ["name"], "parameters": {"executionId": {"description": "Required. Execution ID", "format": "int64", "location": "query", "type": "string"}, "name": {"description": "Required. The fully qualified name of the batch to retrieve in the format \"projects/PROJECT_ID/locations/DATAPROC_REGION/batches/BATCH_ID/sparkApplications/APPLICATION_ID\"", "location": "path", "pattern": "^projects/[^/]+/locations/[^/]+/batches/[^/]+/sparkApplications/[^/]+$", "required": true, "type": "string"}, "parent": {"description": "Required. Parent (Batch) resource reference.", "location": "query", "type": "string"}}, "path": "v1/{+name}:accessSqlPlan", "response": {"$ref": "AccessSparkApplicationSqlSparkPlanGraphResponse"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform"]}, "accessSqlQuery": {"description": "Obtain data corresponding to a particular SQL Query for a Spark Application.", "flatPath": "v1/projects/{projectsId}/locations/{locationsId}/batches/{batchesId}/sparkApplications/{sparkApplicationsId}:accessSqlQuery", "httpMethod": "GET", "id": "dataproc.projects.locations.batches.sparkApplications.accessSqlQuery", "parameterOrder": ["name"], "parameters": {"details": {"description": "Optional. Lists/ hides details of Spark plan nodes. True is set to list and false to hide.", "location": "query", "type": "boolean"}, "executionId": {"description": "Required. Execution ID", "format": "int64", "location": "query", "type": "string"}, "name": {"description": "Required. The fully qualified name of the batch to retrieve in the format \"projects/PROJECT_ID/locations/DATAPROC_REGION/batches/BATCH_ID/sparkApplications/APPLICATION_ID\"", "location": "path", "pattern": "^projects/[^/]+/locations/[^/]+/batches/[^/]+/sparkApplications/[^/]+$", "required": true, "type": "string"}, "parent": {"description": "Required. Parent (Batch) resource reference.", "location": "query", "type": "string"}, "planDescription": {"description": "Optional. Enables/ disables physical plan description on demand", "location": "query", "type": "boolean"}}, "path": "v1/{+name}:accessSqlQuery", "response": {"$ref": "AccessSparkApplicationSqlQueryResponse"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform"]}, "accessStageAttempt": {"description": "Obtain data corresponding to a spark stage attempt for a Spark Application.", "flatPath": "v1/projects/{projectsId}/locations/{locationsId}/batches/{batchesId}/sparkApplications/{sparkApplicationsId}:accessStageAttempt", "httpMethod": "GET", "id": "dataproc.projects.locations.batches.sparkApplications.accessStageAttempt", "parameterOrder": ["name"], "parameters": {"name": {"description": "Required. The fully qualified name of the batch to retrieve in the format \"projects/PROJECT_ID/locations/DATAPROC_REGION/batches/BATCH_ID/sparkApplications/APPLICATION_ID\"", "location": "path", "pattern": "^projects/[^/]+/locations/[^/]+/batches/[^/]+/sparkApplications/[^/]+$", "required": true, "type": "string"}, "parent": {"description": "Required. Parent (Batch) resource reference.", "location": "query", "type": "string"}, "stageAttemptId": {"description": "Required. Stage Attempt ID", "format": "int32", "location": "query", "type": "integer"}, "stageId": {"description": "Required. Stage ID", "format": "int64", "location": "query", "type": "string"}, "summaryMetricsMask": {"description": "Optional. The list of summary metrics fields to include. Empty list will default to skip all summary metrics fields. Example, if the response should include TaskQuantileMetrics, the request should have task_quantile_metrics in summary_metrics_mask field", "format": "google-fieldmask", "location": "query", "type": "string"}}, "path": "v1/{+name}:accessStageAttempt", "response": {"$ref": "AccessSparkApplicationStageAttemptResponse"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform"]}, "accessStageRddGraph": {"description": "Obtain RDD operation graph for a Spark Application Stage. Limits the number of clusters returned as part of the graph to 10000.", "flatPath": "v1/projects/{projectsId}/locations/{locationsId}/batches/{batchesId}/sparkApplications/{sparkApplicationsId}:accessStageRddGraph", "httpMethod": "GET", "id": "dataproc.projects.locations.batches.sparkApplications.accessStageRddGraph", "parameterOrder": ["name"], "parameters": {"name": {"description": "Required. The fully qualified name of the batch to retrieve in the format \"projects/PROJECT_ID/locations/DATAPROC_REGION/batches/BATCH_ID/sparkApplications/APPLICATION_ID\"", "location": "path", "pattern": "^projects/[^/]+/locations/[^/]+/batches/[^/]+/sparkApplications/[^/]+$", "required": true, "type": "string"}, "parent": {"description": "Required. Parent (Batch) resource reference.", "location": "query", "type": "string"}, "stageId": {"description": "Required. Stage ID", "format": "int64", "location": "query", "type": "string"}}, "path": "v1/{+name}:accessStageRddGraph", "response": {"$ref": "AccessSparkApplicationStageRddOperationGraphResponse"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform"]}, "search": {"description": "Obtain high level information and list of Spark Applications corresponding to a batch", "flatPath": "v1/projects/{projectsId}/locations/{locationsId}/batches/{batchesId}/sparkApplications:search", "httpMethod": "GET", "id": "dataproc.projects.locations.batches.sparkApplications.search", "parameterOrder": ["parent"], "parameters": {"applicationStatus": {"description": "Optional. Search only applications in the chosen state.", "enum": ["APPLICATION_STATUS_UNSPECIFIED", "APPLICATION_STATUS_RUNNING", "APPLICATION_STATUS_COMPLETED"], "enumDescriptions": ["", "", ""], "location": "query", "type": "string"}, "maxEndTime": {"description": "Optional. Latest end timestamp to list.", "format": "google-datetime", "location": "query", "type": "string"}, "maxTime": {"description": "Optional. Latest start timestamp to list.", "format": "google-datetime", "location": "query", "type": "string"}, "minEndTime": {"description": "Optional. Earliest end timestamp to list.", "format": "google-datetime", "location": "query", "type": "string"}, "minTime": {"description": "Optional. Earliest start timestamp to list.", "format": "google-datetime", "location": "query", "type": "string"}, "pageSize": {"description": "Optional. Maximum number of applications to return in each response. The service may return fewer than this. The default page size is 10; the maximum page size is 100.", "format": "int32", "location": "query", "type": "integer"}, "pageToken": {"description": "Optional. A page token received from a previous SearchSparkApplications call. Provide this token to retrieve the subsequent page.", "location": "query", "type": "string"}, "parent": {"description": "Required. The fully qualified name of the batch to retrieve in the format \"projects/PROJECT_ID/locations/DATAPROC_REGION/batches/BATCH_ID\"", "location": "path", "pattern": "^projects/[^/]+/locations/[^/]+/batches/[^/]+$", "required": true, "type": "string"}}, "path": "v1/{+parent}/sparkApplications:search", "response": {"$ref": "SearchSparkApplicationsResponse"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform"]}, "searchExecutorStageSummary": {"description": "Obtain executor summary with respect to a spark stage attempt.", "flatPath": "v1/projects/{projectsId}/locations/{locationsId}/batches/{batchesId}/sparkApplications/{sparkApplicationsId}:searchExecutorStageSummary", "httpMethod": "GET", "id": "dataproc.projects.locations.batches.sparkApplications.searchExecutorStageSummary", "parameterOrder": ["name"], "parameters": {"name": {"description": "Required. The fully qualified name of the batch to retrieve in the format \"projects/PROJECT_ID/locations/DATAPROC_REGION/batches/BATCH_ID/sparkApplications/APPLICATION_ID\"", "location": "path", "pattern": "^projects/[^/]+/locations/[^/]+/batches/[^/]+/sparkApplications/[^/]+$", "required": true, "type": "string"}, "pageSize": {"description": "Optional. Maximum number of executors to return in each response. The service may return fewer than this. The default page size is 10; the maximum page size is 100.", "format": "int32", "location": "query", "type": "integer"}, "pageToken": {"description": "Optional. A page token received from a previous AccessSparkApplicationExecutorsList call. Provide this token to retrieve the subsequent page.", "location": "query", "type": "string"}, "parent": {"description": "Required. Parent (Batch) resource reference.", "location": "query", "type": "string"}, "stageAttemptId": {"description": "Required. Stage Attempt ID", "format": "int32", "location": "query", "type": "integer"}, "stageId": {"description": "Required. Stage ID", "format": "int64", "location": "query", "type": "string"}}, "path": "v1/{+name}:searchExecutorStageSummary", "response": {"$ref": "SearchSparkApplicationExecutorStageSummaryResponse"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform"]}, "searchExecutors": {"description": "Obtain data corresponding to executors for a Spark Application.", "flatPath": "v1/projects/{projectsId}/locations/{locationsId}/batches/{batchesId}/sparkApplications/{sparkApplicationsId}:searchExecutors", "httpMethod": "GET", "id": "dataproc.projects.locations.batches.sparkApplications.searchExecutors", "parameterOrder": ["name"], "parameters": {"executorStatus": {"description": "Optional. Filter to select whether active/ dead or all executors should be selected.", "enum": ["EXECUTOR_STATUS_UNSPECIFIED", "EXECUTOR_STATUS_ACTIVE", "EXECUTOR_STATUS_DEAD"], "enumDescriptions": ["", "", ""], "location": "query", "type": "string"}, "name": {"description": "Required. The fully qualified name of the batch to retrieve in the format \"projects/PROJECT_ID/locations/DATAPROC_REGION/batches/BATCH_ID/sparkApplications/APPLICATION_ID\"", "location": "path", "pattern": "^projects/[^/]+/locations/[^/]+/batches/[^/]+/sparkApplications/[^/]+$", "required": true, "type": "string"}, "pageSize": {"description": "Optional. Maximum number of executors to return in each response. The service may return fewer than this. The default page size is 10; the maximum page size is 100.", "format": "int32", "location": "query", "type": "integer"}, "pageToken": {"description": "Optional. A page token received from a previous AccessSparkApplicationExecutorsList call. Provide this token to retrieve the subsequent page.", "location": "query", "type": "string"}, "parent": {"description": "Required. Parent (Batch) resource reference.", "location": "query", "type": "string"}}, "path": "v1/{+name}:searchExecutors", "response": {"$ref": "SearchSparkApplicationExecutorsResponse"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform"]}, "searchJobs": {"description": "Obtain list of spark jobs corresponding to a Spark Application.", "flatPath": "v1/projects/{projectsId}/locations/{locationsId}/batches/{batchesId}/sparkApplications/{sparkApplicationsId}:searchJobs", "httpMethod": "GET", "id": "dataproc.projects.locations.batches.sparkApplications.searchJobs", "parameterOrder": ["name"], "parameters": {"jobStatus": {"description": "Optional. List only jobs in the specific state.", "enum": ["JOB_EXECUTION_STATUS_UNSPECIFIED", "JOB_EXECUTION_STATUS_RUNNING", "JOB_EXECUTION_STATUS_SUCCEEDED", "JOB_EXECUTION_STATUS_FAILED", "JOB_EXECUTION_STATUS_UNKNOWN"], "enumDescriptions": ["", "", "", "", ""], "location": "query", "type": "string"}, "name": {"description": "Required. The fully qualified name of the batch to retrieve in the format \"projects/PROJECT_ID/locations/DATAPROC_REGION/batches/BATCH_ID/sparkApplications/APPLICATION_ID\"", "location": "path", "pattern": "^projects/[^/]+/locations/[^/]+/batches/[^/]+/sparkApplications/[^/]+$", "required": true, "type": "string"}, "pageSize": {"description": "Optional. Maximum number of jobs to return in each response. The service may return fewer than this. The default page size is 10; the maximum page size is 100.", "format": "int32", "location": "query", "type": "integer"}, "pageToken": {"description": "Optional. A page token received from a previous SearchSparkApplicationJobs call. Provide this token to retrieve the subsequent page.", "location": "query", "type": "string"}, "parent": {"description": "Required. Parent (Batch) resource reference.", "location": "query", "type": "string"}}, "path": "v1/{+name}:searchJobs", "response": {"$ref": "SearchSparkApplicationJobsResponse"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform"]}, "searchSqlQueries": {"description": "Obtain data corresponding to SQL Queries for a Spark Application.", "flatPath": "v1/projects/{projectsId}/locations/{locationsId}/batches/{batchesId}/sparkApplications/{sparkApplicationsId}:searchSqlQueries", "httpMethod": "GET", "id": "dataproc.projects.locations.batches.sparkApplications.searchSqlQueries", "parameterOrder": ["name"], "parameters": {"details": {"description": "Optional. Lists/ hides details of Spark plan nodes. True is set to list and false to hide.", "location": "query", "type": "boolean"}, "name": {"description": "Required. The fully qualified name of the batch to retrieve in the format \"projects/PROJECT_ID/locations/DATAPROC_REGION/batches/BATCH_ID/sparkApplications/APPLICATION_ID\"", "location": "path", "pattern": "^projects/[^/]+/locations/[^/]+/batches/[^/]+/sparkApplications/[^/]+$", "required": true, "type": "string"}, "pageSize": {"description": "Optional. Maximum number of queries to return in each response. The service may return fewer than this. The default page size is 10; the maximum page size is 100.", "format": "int32", "location": "query", "type": "integer"}, "pageToken": {"description": "Optional. A page token received from a previous SearchSparkApplicationSqlQueries call. Provide this token to retrieve the subsequent page.", "location": "query", "type": "string"}, "parent": {"description": "Required. Parent (Batch) resource reference.", "location": "query", "type": "string"}, "planDescription": {"description": "Optional. Enables/ disables physical plan description on demand", "location": "query", "type": "boolean"}}, "path": "v1/{+name}:searchSqlQueries", "response": {"$ref": "SearchSparkApplicationSqlQueriesResponse"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform"]}, "searchStageAttemptTasks": {"description": "Obtain data corresponding to tasks for a spark stage attempt for a Spark Application.", "flatPath": "v1/projects/{projectsId}/locations/{locationsId}/batches/{batchesId}/sparkApplications/{sparkApplicationsId}:searchStageAttemptTasks", "httpMethod": "GET", "id": "dataproc.projects.locations.batches.sparkApplications.searchStageAttemptTasks", "parameterOrder": ["name"], "parameters": {"name": {"description": "Required. The fully qualified name of the batch to retrieve in the format \"projects/PROJECT_ID/locations/DATAPROC_REGION/batches/BATCH_ID/sparkApplications/APPLICATION_ID\"", "location": "path", "pattern": "^projects/[^/]+/locations/[^/]+/batches/[^/]+/sparkApplications/[^/]+$", "required": true, "type": "string"}, "pageSize": {"description": "Optional. Maximum number of tasks to return in each response. The service may return fewer than this. The default page size is 10; the maximum page size is 100.", "format": "int32", "location": "query", "type": "integer"}, "pageToken": {"description": "Optional. A page token received from a previous ListSparkApplicationStageAttemptTasks call. Provide this token to retrieve the subsequent page.", "location": "query", "type": "string"}, "parent": {"description": "Required. Parent (Batch) resource reference.", "location": "query", "type": "string"}, "sortRuntime": {"description": "Optional. Sort the tasks by runtime.", "location": "query", "type": "boolean"}, "stageAttemptId": {"description": "Optional. Stage Attempt ID", "format": "int32", "location": "query", "type": "integer"}, "stageId": {"description": "Optional. Stage ID", "format": "int64", "location": "query", "type": "string"}, "taskStatus": {"description": "Optional. List only tasks in the state.", "enum": ["TASK_STATUS_UNSPECIFIED", "TASK_STATUS_RUNNING", "TASK_STATUS_SUCCESS", "TASK_STATUS_FAILED", "TASK_STATUS_KILLED", "TASK_STATUS_PENDING"], "enumDescriptions": ["", "", "", "", "", ""], "location": "query", "type": "string"}}, "path": "v1/{+name}:searchStageAttemptTasks", "response": {"$ref": "SearchSparkApplicationStageAttemptTasksResponse"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform"]}, "searchStageAttempts": {"description": "Obtain data corresponding to a spark stage attempts for a Spark Application.", "flatPath": "v1/projects/{projectsId}/locations/{locationsId}/batches/{batchesId}/sparkApplications/{sparkApplicationsId}:searchStageAttempts", "httpMethod": "GET", "id": "dataproc.projects.locations.batches.sparkApplications.searchStageAttempts", "parameterOrder": ["name"], "parameters": {"name": {"description": "Required. The fully qualified name of the batch to retrieve in the format \"projects/PROJECT_ID/locations/DATAPROC_REGION/batches/BATCH_ID/sparkApplications/APPLICATION_ID\"", "location": "path", "pattern": "^projects/[^/]+/locations/[^/]+/batches/[^/]+/sparkApplications/[^/]+$", "required": true, "type": "string"}, "pageSize": {"description": "Optional. Maximum number of stage attempts (paging based on stage_attempt_id) to return in each response. The service may return fewer than this. The default page size is 10; the maximum page size is 100.", "format": "int32", "location": "query", "type": "integer"}, "pageToken": {"description": "Optional. A page token received from a previous SearchSparkApplicationStageAttempts call. Provide this token to retrieve the subsequent page.", "location": "query", "type": "string"}, "parent": {"description": "Required. Parent (Batch) resource reference.", "location": "query", "type": "string"}, "stageId": {"description": "Required. Stage ID for which attempts are to be fetched", "format": "int64", "location": "query", "type": "string"}, "summaryMetricsMask": {"description": "Optional. The list of summary metrics fields to include. Empty list will default to skip all summary metrics fields. Example, if the response should include TaskQuantileMetrics, the request should have task_quantile_metrics in summary_metrics_mask field", "format": "google-fieldmask", "location": "query", "type": "string"}}, "path": "v1/{+name}:searchStageAttempts", "response": {"$ref": "SearchSparkApplicationStageAttemptsResponse"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform"]}, "searchStages": {"description": "Obtain data corresponding to stages for a Spark Application.", "flatPath": "v1/projects/{projectsId}/locations/{locationsId}/batches/{batchesId}/sparkApplications/{sparkApplicationsId}:searchStages", "httpMethod": "GET", "id": "dataproc.projects.locations.batches.sparkApplications.searchStages", "parameterOrder": ["name"], "parameters": {"name": {"description": "Required. The fully qualified name of the batch to retrieve in the format \"projects/PROJECT_ID/locations/DATAPROC_REGION/batches/BATCH_ID/sparkApplications/APPLICATION_ID\"", "location": "path", "pattern": "^projects/[^/]+/locations/[^/]+/batches/[^/]+/sparkApplications/[^/]+$", "required": true, "type": "string"}, "pageSize": {"description": "Optional. Maximum number of stages (paging based on stage_id) to return in each response. The service may return fewer than this. The default page size is 10; the maximum page size is 100.", "format": "int32", "location": "query", "type": "integer"}, "pageToken": {"description": "Optional. A page token received from a previous FetchSparkApplicationStagesList call. Provide this token to retrieve the subsequent page.", "location": "query", "type": "string"}, "parent": {"description": "Required. Parent (Batch) resource reference.", "location": "query", "type": "string"}, "stageStatus": {"description": "Optional. List only stages in the given state.", "enum": ["STAGE_STATUS_UNSPECIFIED", "STAGE_STATUS_ACTIVE", "STAGE_STATUS_COMPLETE", "STAGE_STATUS_FAILED", "STAGE_STATUS_PENDING", "STAGE_STATUS_SKIPPED"], "enumDescriptions": ["", "", "", "", "", ""], "location": "query", "type": "string"}, "summaryMetricsMask": {"description": "Optional. The list of summary metrics fields to include. Empty list will default to skip all summary metrics fields. Example, if the response should include TaskQuantileMetrics, the request should have task_quantile_metrics in summary_metrics_mask field", "format": "google-fieldmask", "location": "query", "type": "string"}}, "path": "v1/{+name}:searchStages", "response": {"$ref": "SearchSparkApplicationStagesResponse"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform"]}, "summarizeExecutors": {"description": "Obtain summary of Executor Summary for a Spark Application", "flatPath": "v1/projects/{projectsId}/locations/{locationsId}/batches/{batchesId}/sparkApplications/{sparkApplicationsId}:summarizeExecutors", "httpMethod": "GET", "id": "dataproc.projects.locations.batches.sparkApplications.summarizeExecutors", "parameterOrder": ["name"], "parameters": {"name": {"description": "Required. The fully qualified name of the batch to retrieve in the format \"projects/PROJECT_ID/locations/DATAPROC_REGION/batches/BATCH_ID/sparkApplications/APPLICATION_ID\"", "location": "path", "pattern": "^projects/[^/]+/locations/[^/]+/batches/[^/]+/sparkApplications/[^/]+$", "required": true, "type": "string"}, "parent": {"description": "Required. Parent (Batch) resource reference.", "location": "query", "type": "string"}}, "path": "v1/{+name}:summarizeExecutors", "response": {"$ref": "SummarizeSparkApplicationExecutorsResponse"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform"]}, "summarizeJobs": {"description": "Obtain summary of Jobs for a Spark Application", "flatPath": "v1/projects/{projectsId}/locations/{locationsId}/batches/{batchesId}/sparkApplications/{sparkApplicationsId}:summarizeJobs", "httpMethod": "GET", "id": "dataproc.projects.locations.batches.sparkApplications.summarizeJobs", "parameterOrder": ["name"], "parameters": {"name": {"description": "Required. The fully qualified name of the batch to retrieve in the format \"projects/PROJECT_ID/locations/DATAPROC_REGION/batches/BATCH_ID/sparkApplications/APPLICATION_ID\"", "location": "path", "pattern": "^projects/[^/]+/locations/[^/]+/batches/[^/]+/sparkApplications/[^/]+$", "required": true, "type": "string"}, "parent": {"description": "Required. Parent (Batch) resource reference.", "location": "query", "type": "string"}}, "path": "v1/{+name}:summarizeJobs", "response": {"$ref": "SummarizeSparkApplicationJobsResponse"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform"]}, "summarizeStageAttemptTasks": {"description": "Obtain summary of Tasks for a Spark Application Stage Attempt", "flatPath": "v1/projects/{projectsId}/locations/{locationsId}/batches/{batchesId}/sparkApplications/{sparkApplicationsId}:summarizeStageAttemptTasks", "httpMethod": "GET", "id": "dataproc.projects.locations.batches.sparkApplications.summarizeStageAttemptTasks", "parameterOrder": ["name"], "parameters": {"name": {"description": "Required. The fully qualified name of the batch to retrieve in the format \"projects/PROJECT_ID/locations/DATAPROC_REGION/batches/BATCH_ID/sparkApplications/APPLICATION_ID\"", "location": "path", "pattern": "^projects/[^/]+/locations/[^/]+/batches/[^/]+/sparkApplications/[^/]+$", "required": true, "type": "string"}, "parent": {"description": "Required. Parent (Batch) resource reference.", "location": "query", "type": "string"}, "stageAttemptId": {"description": "Required. Stage Attempt ID", "format": "int32", "location": "query", "type": "integer"}, "stageId": {"description": "Required. Stage ID", "format": "int64", "location": "query", "type": "string"}}, "path": "v1/{+name}:summarizeStageAttemptTasks", "response": {"$ref": "SummarizeSparkApplicationStageAttemptTasksResponse"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform"]}, "summarizeStages": {"description": "Obtain summary of Stages for a Spark Application", "flatPath": "v1/projects/{projectsId}/locations/{locationsId}/batches/{batchesId}/sparkApplications/{sparkApplicationsId}:summarizeStages", "httpMethod": "GET", "id": "dataproc.projects.locations.batches.sparkApplications.summarizeStages", "parameterOrder": ["name"], "parameters": {"name": {"description": "Required. The fully qualified name of the batch to retrieve in the format \"projects/PROJECT_ID/locations/DATAPROC_REGION/batches/BATCH_ID/sparkApplications/APPLICATION_ID\"", "location": "path", "pattern": "^projects/[^/]+/locations/[^/]+/batches/[^/]+/sparkApplications/[^/]+$", "required": true, "type": "string"}, "parent": {"description": "Required. Parent (Batch) resource reference.", "location": "query", "type": "string"}}, "path": "v1/{+name}:summarizeStages", "response": {"$ref": "SummarizeSparkApplicationStagesResponse"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform"]}, "write": {"description": "Write wrapper objects from dataplane to spanner", "flatPath": "v1/projects/{projectsId}/locations/{locationsId}/batches/{batchesId}/sparkApplications/{sparkApplicationsId}:write", "httpMethod": "POST", "id": "dataproc.projects.locations.batches.sparkApplications.write", "parameterOrder": ["name"], "parameters": {"name": {"description": "Required. The fully qualified name of the spark application to write data about in the format \"projects/PROJECT_ID/locations/DATAPROC_REGION/batches/BATCH_ID/sparkApplications/APPLICATION_ID\"", "location": "path", "pattern": "^projects/[^/]+/locations/[^/]+/batches/[^/]+/sparkApplications/[^/]+$", "required": true, "type": "string"}}, "path": "v1/{+name}:write", "request": {"$ref": "WriteSparkApplicationContextRequest"}, "response": {"$ref": "WriteSparkApplicationContextResponse"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform"]}}}}}, "operations": {"methods": {"cancel": {"description": "Starts asynchronous cancellation on a long-running operation. The server makes a best effort to cancel the operation, but success is not guaranteed. If the server doesn't support this method, it returns google.rpc.Code.UNIMPLEMENTED. Clients can use Operations.GetOperation or other methods to check whether the cancellation succeeded or whether the operation completed despite cancellation. On successful cancellation, the operation is not deleted; instead, it becomes an operation with an Operation.error value with a google.rpc.Status.code of 1, corresponding to Code.CANCELLED.", "flatPath": "v1/projects/{projectsId}/locations/{locationsId}/operations/{operationsId}:cancel", "httpMethod": "POST", "id": "dataproc.projects.locations.operations.cancel", "parameterOrder": ["name"], "parameters": {"name": {"description": "The name of the operation resource to be cancelled.", "location": "path", "pattern": "^projects/[^/]+/locations/[^/]+/operations/[^/]+$", "required": true, "type": "string"}}, "path": "v1/{+name}:cancel", "response": {"$ref": "Empty"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform"]}, "delete": {"description": "Deletes a long-running operation. This method indicates that the client is no longer interested in the operation result. It does not cancel the operation. If the server doesn't support this method, it returns google.rpc.Code.UNIMPLEMENTED.", "flatPath": "v1/projects/{projectsId}/locations/{locationsId}/operations/{operationsId}", "httpMethod": "DELETE", "id": "dataproc.projects.locations.operations.delete", "parameterOrder": ["name"], "parameters": {"name": {"description": "The name of the operation resource to be deleted.", "location": "path", "pattern": "^projects/[^/]+/locations/[^/]+/operations/[^/]+$", "required": true, "type": "string"}}, "path": "v1/{+name}", "response": {"$ref": "Empty"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform"]}, "get": {"description": "Gets the latest state of a long-running operation. Clients can use this method to poll the operation result at intervals as recommended by the API service.", "flatPath": "v1/projects/{projectsId}/locations/{locationsId}/operations/{operationsId}", "httpMethod": "GET", "id": "dataproc.projects.locations.operations.get", "parameterOrder": ["name"], "parameters": {"name": {"description": "The name of the operation resource.", "location": "path", "pattern": "^projects/[^/]+/locations/[^/]+/operations/[^/]+$", "required": true, "type": "string"}}, "path": "v1/{+name}", "response": {"$ref": "Operation"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform"]}, "list": {"description": "Lists operations that match the specified filter in the request. If the server doesn't support this method, it returns UNIMPLEMENTED.", "flatPath": "v1/projects/{projectsId}/locations/{locationsId}/operations", "httpMethod": "GET", "id": "dataproc.projects.locations.operations.list", "parameterOrder": ["name"], "parameters": {"filter": {"description": "The standard list filter.", "location": "query", "type": "string"}, "name": {"description": "The name of the operation's parent resource.", "location": "path", "pattern": "^projects/[^/]+/locations/[^/]+/operations$", "required": true, "type": "string"}, "pageSize": {"description": "The standard list page size.", "format": "int32", "location": "query", "type": "integer"}, "pageToken": {"description": "The standard list page token.", "location": "query", "type": "string"}}, "path": "v1/{+name}", "response": {"$ref": "ListOperationsResponse"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform"]}}}, "sessionTemplates": {"methods": {"create": {"description": "Create a session template synchronously.", "flatPath": "v1/projects/{projectsId}/locations/{locationsId}/sessionTemplates", "httpMethod": "POST", "id": "dataproc.projects.locations.sessionTemplates.create", "parameterOrder": ["parent"], "parameters": {"parent": {"description": "Required. The parent resource where this session template will be created.", "location": "path", "pattern": "^projects/[^/]+/locations/[^/]+$", "required": true, "type": "string"}}, "path": "v1/{+parent}/sessionTemplates", "request": {"$ref": "SessionTemplate"}, "response": {"$ref": "SessionTemplate"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform"]}, "delete": {"description": "Deletes a session template.", "flatPath": "v1/projects/{projectsId}/locations/{locationsId}/sessionTemplates/{sessionTemplatesId}", "httpMethod": "DELETE", "id": "dataproc.projects.locations.sessionTemplates.delete", "parameterOrder": ["name"], "parameters": {"name": {"description": "Required. The name of the session template resource to delete.", "location": "path", "pattern": "^projects/[^/]+/locations/[^/]+/sessionTemplates/[^/]+$", "required": true, "type": "string"}}, "path": "v1/{+name}", "response": {"$ref": "Empty"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform"]}, "get": {"description": "Gets the resource representation for a session template.", "flatPath": "v1/projects/{projectsId}/locations/{locationsId}/sessionTemplates/{sessionTemplatesId}", "httpMethod": "GET", "id": "dataproc.projects.locations.sessionTemplates.get", "parameterOrder": ["name"], "parameters": {"name": {"description": "Required. The name of the session template to retrieve.", "location": "path", "pattern": "^projects/[^/]+/locations/[^/]+/sessionTemplates/[^/]+$", "required": true, "type": "string"}}, "path": "v1/{+name}", "response": {"$ref": "SessionTemplate"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform"]}, "list": {"description": "Lists session templates.", "flatPath": "v1/projects/{projectsId}/locations/{locationsId}/sessionTemplates", "httpMethod": "GET", "id": "dataproc.projects.locations.sessionTemplates.list", "parameterOrder": ["parent"], "parameters": {"filter": {"description": "Optional. A filter for the session templates to return in the response. Filters are case sensitive and have the following syntax:field = value AND field = value ...", "location": "query", "type": "string"}, "pageSize": {"description": "Optional. The maximum number of sessions to return in each response. The service may return fewer than this value.", "format": "int32", "location": "query", "type": "integer"}, "pageToken": {"description": "Optional. A page token received from a previous ListSessions call. Provide this token to retrieve the subsequent page.", "location": "query", "type": "string"}, "parent": {"description": "Required. The parent that owns this collection of session templates.", "location": "path", "pattern": "^projects/[^/]+/locations/[^/]+$", "required": true, "type": "string"}}, "path": "v1/{+parent}/sessionTemplates", "response": {"$ref": "ListSessionTemplatesResponse"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform"]}, "patch": {"description": "Updates the session template synchronously.", "flatPath": "v1/projects/{projectsId}/locations/{locationsId}/sessionTemplates/{sessionTemplatesId}", "httpMethod": "PATCH", "id": "dataproc.projects.locations.sessionTemplates.patch", "parameterOrder": ["name"], "parameters": {"name": {"description": "Required. The resource name of the session template.", "location": "path", "pattern": "^projects/[^/]+/locations/[^/]+/sessionTemplates/[^/]+$", "required": true, "type": "string"}}, "path": "v1/{+name}", "request": {"$ref": "SessionTemplate"}, "response": {"$ref": "SessionTemplate"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform"]}}}, "sessions": {"methods": {"create": {"description": "Create an interactive session asynchronously.", "flatPath": "v1/projects/{projectsId}/locations/{locationsId}/sessions", "httpMethod": "POST", "id": "dataproc.projects.locations.sessions.create", "parameterOrder": ["parent"], "parameters": {"parent": {"description": "Required. The parent resource where this session will be created.", "location": "path", "pattern": "^projects/[^/]+/locations/[^/]+$", "required": true, "type": "string"}, "requestId": {"description": "Optional. A unique ID used to identify the request. If the service receives two CreateSessionRequests (https://cloud.google.com/dataproc/docs/reference/rpc/google.cloud.dataproc.v1#google.cloud.dataproc.v1.CreateSessionRequest)s with the same ID, the second request is ignored, and the first Session is created and stored in the backend.Recommendation: Set this value to a UUID (https://en.wikipedia.org/wiki/Universally_unique_identifier).The value must contain only letters (a-z, A-Z), numbers (0-9), underscores (_), and hyphens (-). The maximum length is 40 characters.", "location": "query", "type": "string"}, "sessionId": {"description": "Required. The ID to use for the session, which becomes the final component of the session's resource name.This value must be 4-63 characters. Valid characters are /a-z-/.", "location": "query", "type": "string"}}, "path": "v1/{+parent}/sessions", "request": {"$ref": "Session"}, "response": {"$ref": "Operation"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform"]}, "delete": {"description": "Deletes the interactive session resource. If the session is not in terminal state, it is terminated, and then deleted.", "flatPath": "v1/projects/{projectsId}/locations/{locationsId}/sessions/{sessionsId}", "httpMethod": "DELETE", "id": "dataproc.projects.locations.sessions.delete", "parameterOrder": ["name"], "parameters": {"name": {"description": "Required. The name of the session resource to delete.", "location": "path", "pattern": "^projects/[^/]+/locations/[^/]+/sessions/[^/]+$", "required": true, "type": "string"}, "requestId": {"description": "Optional. A unique ID used to identify the request. If the service receives two DeleteSessionRequest (https://cloud.google.com/dataproc/docs/reference/rpc/google.cloud.dataproc.v1#google.cloud.dataproc.v1.DeleteSessionRequest)s with the same ID, the second request is ignored.Recommendation: Set this value to a UUID (https://en.wikipedia.org/wiki/Universally_unique_identifier).The value must contain only letters (a-z, A-Z), numbers (0-9), underscores (_), and hyphens (-). The maximum length is 40 characters.", "location": "query", "type": "string"}}, "path": "v1/{+name}", "response": {"$ref": "Operation"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform"]}, "get": {"description": "Gets the resource representation for an interactive session.", "flatPath": "v1/projects/{projectsId}/locations/{locationsId}/sessions/{sessionsId}", "httpMethod": "GET", "id": "dataproc.projects.locations.sessions.get", "parameterOrder": ["name"], "parameters": {"name": {"description": "Required. The name of the session to retrieve.", "location": "path", "pattern": "^projects/[^/]+/locations/[^/]+/sessions/[^/]+$", "required": true, "type": "string"}}, "path": "v1/{+name}", "response": {"$ref": "Session"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform"]}, "list": {"description": "Lists interactive sessions.", "flatPath": "v1/projects/{projectsId}/locations/{locationsId}/sessions", "httpMethod": "GET", "id": "dataproc.projects.locations.sessions.list", "parameterOrder": ["parent"], "parameters": {"filter": {"description": "Optional. A filter for the sessions to return in the response.A filter is a logical expression constraining the values of various fields in each session resource. Filters are case sensitive, and may contain multiple clauses combined with logical operators (AND, OR). Supported fields are session_id, session_uuid, state, create_time, and labels.Example: state = ACTIVE and create_time < \"2023-01-01T00:00:00Z\" is a filter for sessions in an ACTIVE state that were created before 2023-01-01. state = ACTIVE and labels.environment=production is a filter for sessions in an ACTIVE state that have a production environment label.See https://google.aip.dev/assets/misc/ebnf-filtering.txt for a detailed description of the filter syntax and a list of supported comparators.", "location": "query", "type": "string"}, "pageSize": {"description": "Optional. The maximum number of sessions to return in each response. The service may return fewer than this value.", "format": "int32", "location": "query", "type": "integer"}, "pageToken": {"description": "Optional. A page token received from a previous ListSessions call. Provide this token to retrieve the subsequent page.", "location": "query", "type": "string"}, "parent": {"description": "Required. The parent, which owns this collection of sessions.", "location": "path", "pattern": "^projects/[^/]+/locations/[^/]+$", "required": true, "type": "string"}}, "path": "v1/{+parent}/sessions", "response": {"$ref": "ListSessionsResponse"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform"]}, "terminate": {"description": "Terminates the interactive session.", "flatPath": "v1/projects/{projectsId}/locations/{locationsId}/sessions/{sessionsId}:terminate", "httpMethod": "POST", "id": "dataproc.projects.locations.sessions.terminate", "parameterOrder": ["name"], "parameters": {"name": {"description": "Required. The name of the session resource to terminate.", "location": "path", "pattern": "^projects/[^/]+/locations/[^/]+/sessions/[^/]+$", "required": true, "type": "string"}}, "path": "v1/{+name}:terminate", "request": {"$ref": "TerminateSessionRequest"}, "response": {"$ref": "Operation"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform"]}}, "resources": {"sparkApplications": {"methods": {"access": {"description": "Obtain high level information corresponding to a single Spark Application.", "flatPath": "v1/projects/{projectsId}/locations/{locationsId}/sessions/{sessionsId}/sparkApplications/{sparkApplicationsId}:access", "httpMethod": "GET", "id": "dataproc.projects.locations.sessions.sparkApplications.access", "parameterOrder": ["name"], "parameters": {"name": {"description": "Required. The fully qualified name of the session to retrieve in the format \"projects/PROJECT_ID/locations/DATAPROC_REGION/sessions/SESSION_ID/sparkApplications/APPLICATION_ID\"", "location": "path", "pattern": "^projects/[^/]+/locations/[^/]+/sessions/[^/]+/sparkApplications/[^/]+$", "required": true, "type": "string"}, "parent": {"description": "Required. <PERSON><PERSON> (Session) resource reference.", "location": "query", "type": "string"}}, "path": "v1/{+name}:access", "response": {"$ref": "AccessSessionSparkApplicationResponse"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform"]}, "accessEnvironmentInfo": {"description": "Obtain environment details for a Spark Application", "flatPath": "v1/projects/{projectsId}/locations/{locationsId}/sessions/{sessionsId}/sparkApplications/{sparkApplicationsId}:accessEnvironmentInfo", "httpMethod": "GET", "id": "dataproc.projects.locations.sessions.sparkApplications.accessEnvironmentInfo", "parameterOrder": ["name"], "parameters": {"name": {"description": "Required. The fully qualified name of the session to retrieve in the format \"projects/PROJECT_ID/locations/DATAPROC_REGION/sessions/SESSION_ID/sparkApplications/APPLICATION_ID\"", "location": "path", "pattern": "^projects/[^/]+/locations/[^/]+/sessions/[^/]+/sparkApplications/[^/]+$", "required": true, "type": "string"}, "parent": {"description": "Required. <PERSON><PERSON> (Session) resource reference.", "location": "query", "type": "string"}}, "path": "v1/{+name}:accessEnvironmentInfo", "response": {"$ref": "AccessSessionSparkApplicationEnvironmentInfoResponse"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform"]}, "accessJob": {"description": "Obtain data corresponding to a spark job for a Spark Application.", "flatPath": "v1/projects/{projectsId}/locations/{locationsId}/sessions/{sessionsId}/sparkApplications/{sparkApplicationsId}:accessJob", "httpMethod": "GET", "id": "dataproc.projects.locations.sessions.sparkApplications.accessJob", "parameterOrder": ["name"], "parameters": {"jobId": {"description": "Required. Job ID to fetch data for.", "format": "int64", "location": "query", "type": "string"}, "name": {"description": "Required. The fully qualified name of the session to retrieve in the format \"projects/PROJECT_ID/locations/DATAPROC_REGION/sessions/SESSION_ID/sparkApplications/APPLICATION_ID\"", "location": "path", "pattern": "^projects/[^/]+/locations/[^/]+/sessions/[^/]+/sparkApplications/[^/]+$", "required": true, "type": "string"}, "parent": {"description": "Required. <PERSON><PERSON> (Session) resource reference.", "location": "query", "type": "string"}}, "path": "v1/{+name}:accessJob", "response": {"$ref": "AccessSessionSparkApplicationJobResponse"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform"]}, "accessSqlPlan": {"description": "Obtain Spark Plan Graph for a Spark Application SQL execution. Limits the number of clusters returned as part of the graph to 10000.", "flatPath": "v1/projects/{projectsId}/locations/{locationsId}/sessions/{sessionsId}/sparkApplications/{sparkApplicationsId}:accessSqlPlan", "httpMethod": "GET", "id": "dataproc.projects.locations.sessions.sparkApplications.accessSqlPlan", "parameterOrder": ["name"], "parameters": {"executionId": {"description": "Required. Execution ID", "format": "int64", "location": "query", "type": "string"}, "name": {"description": "Required. The fully qualified name of the session to retrieve in the format \"projects/PROJECT_ID/locations/DATAPROC_REGION/sessions/SESSION_ID/sparkApplications/APPLICATION_ID\"", "location": "path", "pattern": "^projects/[^/]+/locations/[^/]+/sessions/[^/]+/sparkApplications/[^/]+$", "required": true, "type": "string"}, "parent": {"description": "Required. <PERSON><PERSON> (Session) resource reference.", "location": "query", "type": "string"}}, "path": "v1/{+name}:accessSqlPlan", "response": {"$ref": "AccessSessionSparkApplicationSqlSparkPlanGraphResponse"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform"]}, "accessSqlQuery": {"description": "Obtain data corresponding to a particular SQL Query for a Spark Application.", "flatPath": "v1/projects/{projectsId}/locations/{locationsId}/sessions/{sessionsId}/sparkApplications/{sparkApplicationsId}:accessSqlQuery", "httpMethod": "GET", "id": "dataproc.projects.locations.sessions.sparkApplications.accessSqlQuery", "parameterOrder": ["name"], "parameters": {"details": {"description": "Optional. Lists/ hides details of Spark plan nodes. True is set to list and false to hide.", "location": "query", "type": "boolean"}, "executionId": {"description": "Required. Execution ID", "format": "int64", "location": "query", "type": "string"}, "name": {"description": "Required. The fully qualified name of the session to retrieve in the format \"projects/PROJECT_ID/locations/DATAPROC_REGION/sessions/SESSION_ID/sparkApplications/APPLICATION_ID\"", "location": "path", "pattern": "^projects/[^/]+/locations/[^/]+/sessions/[^/]+/sparkApplications/[^/]+$", "required": true, "type": "string"}, "parent": {"description": "Required. <PERSON><PERSON> (Session) resource reference.", "location": "query", "type": "string"}, "planDescription": {"description": "Optional. Enables/ disables physical plan description on demand", "location": "query", "type": "boolean"}}, "path": "v1/{+name}:accessSqlQuery", "response": {"$ref": "AccessSessionSparkApplicationSqlQueryResponse"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform"]}, "accessStageAttempt": {"description": "Obtain data corresponding to a spark stage attempt for a Spark Application.", "flatPath": "v1/projects/{projectsId}/locations/{locationsId}/sessions/{sessionsId}/sparkApplications/{sparkApplicationsId}:accessStageAttempt", "httpMethod": "GET", "id": "dataproc.projects.locations.sessions.sparkApplications.accessStageAttempt", "parameterOrder": ["name"], "parameters": {"name": {"description": "Required. The fully qualified name of the session to retrieve in the format \"projects/PROJECT_ID/locations/DATAPROC_REGION/sessions/SESSION_ID/sparkApplications/APPLICATION_ID\"", "location": "path", "pattern": "^projects/[^/]+/locations/[^/]+/sessions/[^/]+/sparkApplications/[^/]+$", "required": true, "type": "string"}, "parent": {"description": "Required. <PERSON><PERSON> (Session) resource reference.", "location": "query", "type": "string"}, "stageAttemptId": {"description": "Required. Stage Attempt ID", "format": "int32", "location": "query", "type": "integer"}, "stageId": {"description": "Required. Stage ID", "format": "int64", "location": "query", "type": "string"}, "summaryMetricsMask": {"description": "Optional. The list of summary metrics fields to include. Empty list will default to skip all summary metrics fields. Example, if the response should include TaskQuantileMetrics, the request should have task_quantile_metrics in summary_metrics_mask field", "format": "google-fieldmask", "location": "query", "type": "string"}}, "path": "v1/{+name}:accessStageAttempt", "response": {"$ref": "AccessSessionSparkApplicationStageAttemptResponse"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform"]}, "accessStageRddGraph": {"description": "Obtain RDD operation graph for a Spark Application Stage. Limits the number of clusters returned as part of the graph to 10000.", "flatPath": "v1/projects/{projectsId}/locations/{locationsId}/sessions/{sessionsId}/sparkApplications/{sparkApplicationsId}:accessStageRddGraph", "httpMethod": "GET", "id": "dataproc.projects.locations.sessions.sparkApplications.accessStageRddGraph", "parameterOrder": ["name"], "parameters": {"name": {"description": "Required. The fully qualified name of the session to retrieve in the format \"projects/PROJECT_ID/locations/DATAPROC_REGION/sessions/SESSION_ID/sparkApplications/APPLICATION_ID\"", "location": "path", "pattern": "^projects/[^/]+/locations/[^/]+/sessions/[^/]+/sparkApplications/[^/]+$", "required": true, "type": "string"}, "parent": {"description": "Required. <PERSON><PERSON> (Session) resource reference.", "location": "query", "type": "string"}, "stageId": {"description": "Required. Stage ID", "format": "int64", "location": "query", "type": "string"}}, "path": "v1/{+name}:accessStageRddGraph", "response": {"$ref": "AccessSessionSparkApplicationStageRddOperationGraphResponse"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform"]}, "search": {"description": "Obtain high level information and list of Spark Applications corresponding to a batch", "flatPath": "v1/projects/{projectsId}/locations/{locationsId}/sessions/{sessionsId}/sparkApplications:search", "httpMethod": "GET", "id": "dataproc.projects.locations.sessions.sparkApplications.search", "parameterOrder": ["parent"], "parameters": {"applicationStatus": {"description": "Optional. Search only applications in the chosen state.", "enum": ["APPLICATION_STATUS_UNSPECIFIED", "APPLICATION_STATUS_RUNNING", "APPLICATION_STATUS_COMPLETED"], "enumDescriptions": ["", "", ""], "location": "query", "type": "string"}, "maxEndTime": {"description": "Optional. Latest end timestamp to list.", "format": "google-datetime", "location": "query", "type": "string"}, "maxTime": {"description": "Optional. Latest start timestamp to list.", "format": "google-datetime", "location": "query", "type": "string"}, "minEndTime": {"description": "Optional. Earliest end timestamp to list.", "format": "google-datetime", "location": "query", "type": "string"}, "minTime": {"description": "Optional. Earliest start timestamp to list.", "format": "google-datetime", "location": "query", "type": "string"}, "pageSize": {"description": "Optional. Maximum number of applications to return in each response. The service may return fewer than this. The default page size is 10; the maximum page size is 100.", "format": "int32", "location": "query", "type": "integer"}, "pageToken": {"description": "Optional. A page token received from a previous SearchSessionSparkApplications call. Provide this token to retrieve the subsequent page.", "location": "query", "type": "string"}, "parent": {"description": "Required. The fully qualified name of the session to retrieve in the format \"projects/PROJECT_ID/locations/DATAPROC_REGION/sessions/SESSION_ID\"", "location": "path", "pattern": "^projects/[^/]+/locations/[^/]+/sessions/[^/]+$", "required": true, "type": "string"}}, "path": "v1/{+parent}/sparkApplications:search", "response": {"$ref": "SearchSessionSparkApplicationsResponse"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform"]}, "searchExecutorStageSummary": {"description": "Obtain executor summary with respect to a spark stage attempt.", "flatPath": "v1/projects/{projectsId}/locations/{locationsId}/sessions/{sessionsId}/sparkApplications/{sparkApplicationsId}:searchExecutorStageSummary", "httpMethod": "GET", "id": "dataproc.projects.locations.sessions.sparkApplications.searchExecutorStageSummary", "parameterOrder": ["name"], "parameters": {"name": {"description": "Required. The fully qualified name of the session to retrieve in the format \"projects/PROJECT_ID/locations/DATAPROC_REGION/sessions/SESSION_ID/sparkApplications/APPLICATION_ID\"", "location": "path", "pattern": "^projects/[^/]+/locations/[^/]+/sessions/[^/]+/sparkApplications/[^/]+$", "required": true, "type": "string"}, "pageSize": {"description": "Optional. Maximum number of executors to return in each response. The service may return fewer than this. The default page size is 10; the maximum page size is 100.", "format": "int32", "location": "query", "type": "integer"}, "pageToken": {"description": "Optional. A page token received from a previous SearchSessionSparkApplicationExecutorStageSummary call. Provide this token to retrieve the subsequent page.", "location": "query", "type": "string"}, "parent": {"description": "Required. <PERSON><PERSON> (Session) resource reference.", "location": "query", "type": "string"}, "stageAttemptId": {"description": "Required. Stage Attempt ID", "format": "int32", "location": "query", "type": "integer"}, "stageId": {"description": "Required. Stage ID", "format": "int64", "location": "query", "type": "string"}}, "path": "v1/{+name}:searchExecutorStageSummary", "response": {"$ref": "SearchSessionSparkApplicationExecutorStageSummaryResponse"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform"]}, "searchExecutors": {"description": "Obtain data corresponding to executors for a Spark Application.", "flatPath": "v1/projects/{projectsId}/locations/{locationsId}/sessions/{sessionsId}/sparkApplications/{sparkApplicationsId}:searchExecutors", "httpMethod": "GET", "id": "dataproc.projects.locations.sessions.sparkApplications.searchExecutors", "parameterOrder": ["name"], "parameters": {"executorStatus": {"description": "Optional. Filter to select whether active/ dead or all executors should be selected.", "enum": ["EXECUTOR_STATUS_UNSPECIFIED", "EXECUTOR_STATUS_ACTIVE", "EXECUTOR_STATUS_DEAD"], "enumDescriptions": ["", "", ""], "location": "query", "type": "string"}, "name": {"description": "Required. The fully qualified name of the session to retrieve in the format \"projects/PROJECT_ID/locations/DATAPROC_REGION/sessions/SESSION_ID/sparkApplications/APPLICATION_ID\"", "location": "path", "pattern": "^projects/[^/]+/locations/[^/]+/sessions/[^/]+/sparkApplications/[^/]+$", "required": true, "type": "string"}, "pageSize": {"description": "Optional. Maximum number of executors to return in each response. The service may return fewer than this. The default page size is 10; the maximum page size is 100.", "format": "int32", "location": "query", "type": "integer"}, "pageToken": {"description": "Optional. A page token received from a previous SearchSessionSparkApplicationExecutors call. Provide this token to retrieve the subsequent page.", "location": "query", "type": "string"}, "parent": {"description": "Required. <PERSON><PERSON> (Session) resource reference.", "location": "query", "type": "string"}}, "path": "v1/{+name}:searchExecutors", "response": {"$ref": "SearchSessionSparkApplicationExecutorsResponse"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform"]}, "searchJobs": {"description": "Obtain list of spark jobs corresponding to a Spark Application.", "flatPath": "v1/projects/{projectsId}/locations/{locationsId}/sessions/{sessionsId}/sparkApplications/{sparkApplicationsId}:searchJobs", "httpMethod": "GET", "id": "dataproc.projects.locations.sessions.sparkApplications.searchJobs", "parameterOrder": ["name"], "parameters": {"jobIds": {"description": "Optional. List of Job IDs to filter by if provided.", "format": "int64", "location": "query", "repeated": true, "type": "string"}, "jobStatus": {"description": "Optional. List only jobs in the specific state.", "enum": ["JOB_EXECUTION_STATUS_UNSPECIFIED", "JOB_EXECUTION_STATUS_RUNNING", "JOB_EXECUTION_STATUS_SUCCEEDED", "JOB_EXECUTION_STATUS_FAILED", "JOB_EXECUTION_STATUS_UNKNOWN"], "enumDescriptions": ["", "", "", "", ""], "location": "query", "type": "string"}, "name": {"description": "Required. The fully qualified name of the session to retrieve in the format \"projects/PROJECT_ID/locations/DATAPROC_REGION/sessions/SESSION_ID/sparkApplications/APPLICATION_ID\"", "location": "path", "pattern": "^projects/[^/]+/locations/[^/]+/sessions/[^/]+/sparkApplications/[^/]+$", "required": true, "type": "string"}, "pageSize": {"description": "Optional. Maximum number of jobs to return in each response. The service may return fewer than this. The default page size is 10; the maximum page size is 100.", "format": "int32", "location": "query", "type": "integer"}, "pageToken": {"description": "Optional. A page token received from a previous SearchSessionSparkApplicationJobs call. Provide this token to retrieve the subsequent page.", "location": "query", "type": "string"}, "parent": {"description": "Required. <PERSON><PERSON> (Session) resource reference.", "location": "query", "type": "string"}}, "path": "v1/{+name}:searchJobs", "response": {"$ref": "SearchSessionSparkApplicationJobsResponse"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform"]}, "searchSqlQueries": {"description": "Obtain data corresponding to SQL Queries for a Spark Application.", "flatPath": "v1/projects/{projectsId}/locations/{locationsId}/sessions/{sessionsId}/sparkApplications/{sparkApplicationsId}:searchSqlQueries", "httpMethod": "GET", "id": "dataproc.projects.locations.sessions.sparkApplications.searchSqlQueries", "parameterOrder": ["name"], "parameters": {"details": {"description": "Optional. Lists/ hides details of Spark plan nodes. True is set to list and false to hide.", "location": "query", "type": "boolean"}, "name": {"description": "Required. The fully qualified name of the session to retrieve in the format \"projects/PROJECT_ID/locations/DATAPROC_REGION/sessions/SESSION_ID/sparkApplications/APPLICATION_ID\"", "location": "path", "pattern": "^projects/[^/]+/locations/[^/]+/sessions/[^/]+/sparkApplications/[^/]+$", "required": true, "type": "string"}, "operationIds": {"description": "Optional. List of Spark Connect operation IDs to filter by if provided.", "location": "query", "repeated": true, "type": "string"}, "pageSize": {"description": "Optional. Maximum number of queries to return in each response. The service may return fewer than this. The default page size is 10; the maximum page size is 100.", "format": "int32", "location": "query", "type": "integer"}, "pageToken": {"description": "Optional. A page token received from a previous SearchSessionSparkApplicationSqlQueries call. Provide this token to retrieve the subsequent page.", "location": "query", "type": "string"}, "parent": {"description": "Required. <PERSON><PERSON> (Session) resource reference.", "location": "query", "type": "string"}, "planDescription": {"description": "Optional. Enables/ disables physical plan description on demand", "location": "query", "type": "boolean"}}, "path": "v1/{+name}:searchSqlQueries", "response": {"$ref": "SearchSessionSparkApplicationSqlQueriesResponse"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform"]}, "searchStageAttemptTasks": {"description": "Obtain data corresponding to tasks for a spark stage attempt for a Spark Application.", "flatPath": "v1/projects/{projectsId}/locations/{locationsId}/sessions/{sessionsId}/sparkApplications/{sparkApplicationsId}:searchStageAttemptTasks", "httpMethod": "GET", "id": "dataproc.projects.locations.sessions.sparkApplications.searchStageAttemptTasks", "parameterOrder": ["name"], "parameters": {"name": {"description": "Required. The fully qualified name of the session to retrieve in the format \"projects/PROJECT_ID/locations/DATAPROC_REGION/sessions/SESSION_ID/sparkApplications/APPLICATION_ID\"", "location": "path", "pattern": "^projects/[^/]+/locations/[^/]+/sessions/[^/]+/sparkApplications/[^/]+$", "required": true, "type": "string"}, "pageSize": {"description": "Optional. Maximum number of tasks to return in each response. The service may return fewer than this. The default page size is 10; the maximum page size is 100.", "format": "int32", "location": "query", "type": "integer"}, "pageToken": {"description": "Optional. A page token received from a previous SearchSessionSparkApplicationStageAttemptTasks call. Provide this token to retrieve the subsequent page.", "location": "query", "type": "string"}, "parent": {"description": "Required. <PERSON><PERSON> (Session) resource reference.", "location": "query", "type": "string"}, "sortRuntime": {"description": "Optional. Sort the tasks by runtime.", "location": "query", "type": "boolean"}, "stageAttemptId": {"description": "Optional. Stage Attempt ID", "format": "int32", "location": "query", "type": "integer"}, "stageId": {"description": "Optional. Stage ID", "format": "int64", "location": "query", "type": "string"}, "taskStatus": {"description": "Optional. List only tasks in the state.", "enum": ["TASK_STATUS_UNSPECIFIED", "TASK_STATUS_RUNNING", "TASK_STATUS_SUCCESS", "TASK_STATUS_FAILED", "TASK_STATUS_KILLED", "TASK_STATUS_PENDING"], "enumDescriptions": ["", "", "", "", "", ""], "location": "query", "type": "string"}}, "path": "v1/{+name}:searchStageAttemptTasks", "response": {"$ref": "SearchSessionSparkApplicationStageAttemptTasksResponse"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform"]}, "searchStageAttempts": {"description": "Obtain data corresponding to a spark stage attempts for a Spark Application.", "flatPath": "v1/projects/{projectsId}/locations/{locationsId}/sessions/{sessionsId}/sparkApplications/{sparkApplicationsId}:searchStageAttempts", "httpMethod": "GET", "id": "dataproc.projects.locations.sessions.sparkApplications.searchStageAttempts", "parameterOrder": ["name"], "parameters": {"name": {"description": "Required. The fully qualified name of the session to retrieve in the format \"projects/PROJECT_ID/locations/DATAPROC_REGION/sessions/SESSION_ID/sparkApplications/APPLICATION_ID\"", "location": "path", "pattern": "^projects/[^/]+/locations/[^/]+/sessions/[^/]+/sparkApplications/[^/]+$", "required": true, "type": "string"}, "pageSize": {"description": "Optional. Maximum number of stage attempts (paging based on stage_attempt_id) to return in each response. The service may return fewer than this. The default page size is 10; the maximum page size is 100.", "format": "int32", "location": "query", "type": "integer"}, "pageToken": {"description": "Optional. A page token received from a previous SearchSessionSparkApplicationStageAttempts call. Provide this token to retrieve the subsequent page.", "location": "query", "type": "string"}, "parent": {"description": "Required. <PERSON><PERSON> (Session) resource reference.", "location": "query", "type": "string"}, "stageId": {"description": "Required. Stage ID for which attempts are to be fetched", "format": "int64", "location": "query", "type": "string"}, "summaryMetricsMask": {"description": "Optional. The list of summary metrics fields to include. Empty list will default to skip all summary metrics fields. Example, if the response should include TaskQuantileMetrics, the request should have task_quantile_metrics in summary_metrics_mask field", "format": "google-fieldmask", "location": "query", "type": "string"}}, "path": "v1/{+name}:searchStageAttempts", "response": {"$ref": "SearchSessionSparkApplicationStageAttemptsResponse"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform"]}, "searchStages": {"description": "Obtain data corresponding to stages for a Spark Application.", "flatPath": "v1/projects/{projectsId}/locations/{locationsId}/sessions/{sessionsId}/sparkApplications/{sparkApplicationsId}:searchStages", "httpMethod": "GET", "id": "dataproc.projects.locations.sessions.sparkApplications.searchStages", "parameterOrder": ["name"], "parameters": {"name": {"description": "Required. The fully qualified name of the session to retrieve in the format \"projects/PROJECT_ID/locations/DATAPROC_REGION/sessions/SESSION_ID/sparkApplications/APPLICATION_ID\"", "location": "path", "pattern": "^projects/[^/]+/locations/[^/]+/sessions/[^/]+/sparkApplications/[^/]+$", "required": true, "type": "string"}, "pageSize": {"description": "Optional. Maximum number of stages (paging based on stage_id) to return in each response. The service may return fewer than this. The default page size is 10; the maximum page size is 100.", "format": "int32", "location": "query", "type": "integer"}, "pageToken": {"description": "Optional. A page token received from a previous SearchSessionSparkApplicationStages call. Provide this token to retrieve the subsequent page.", "location": "query", "type": "string"}, "parent": {"description": "Required. <PERSON><PERSON> (Session) resource reference.", "location": "query", "type": "string"}, "stageIds": {"description": "Optional. List of Stage IDs to filter by if provided.", "format": "int64", "location": "query", "repeated": true, "type": "string"}, "stageStatus": {"description": "Optional. List only stages in the given state.", "enum": ["STAGE_STATUS_UNSPECIFIED", "STAGE_STATUS_ACTIVE", "STAGE_STATUS_COMPLETE", "STAGE_STATUS_FAILED", "STAGE_STATUS_PENDING", "STAGE_STATUS_SKIPPED"], "enumDescriptions": ["", "", "", "", "", ""], "location": "query", "type": "string"}, "summaryMetricsMask": {"description": "Optional. The list of summary metrics fields to include. Empty list will default to skip all summary metrics fields. Example, if the response should include TaskQuantileMetrics, the request should have task_quantile_metrics in summary_metrics_mask field", "format": "google-fieldmask", "location": "query", "type": "string"}}, "path": "v1/{+name}:searchStages", "response": {"$ref": "SearchSessionSparkApplicationStagesResponse"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform"]}, "summarizeExecutors": {"description": "Obtain summary of Executor Summary for a Spark Application", "flatPath": "v1/projects/{projectsId}/locations/{locationsId}/sessions/{sessionsId}/sparkApplications/{sparkApplicationsId}:summarizeExecutors", "httpMethod": "GET", "id": "dataproc.projects.locations.sessions.sparkApplications.summarizeExecutors", "parameterOrder": ["name"], "parameters": {"name": {"description": "Required. The fully qualified name of the session to retrieve in the format \"projects/PROJECT_ID/locations/DATAPROC_REGION/sessions/SESSION_ID/sparkApplications/APPLICATION_ID\"", "location": "path", "pattern": "^projects/[^/]+/locations/[^/]+/sessions/[^/]+/sparkApplications/[^/]+$", "required": true, "type": "string"}, "parent": {"description": "Required. <PERSON><PERSON> (Session) resource reference.", "location": "query", "type": "string"}}, "path": "v1/{+name}:summarizeExecutors", "response": {"$ref": "SummarizeSessionSparkApplicationExecutorsResponse"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform"]}, "summarizeJobs": {"description": "Obtain summary of Jobs for a Spark Application", "flatPath": "v1/projects/{projectsId}/locations/{locationsId}/sessions/{sessionsId}/sparkApplications/{sparkApplicationsId}:summarizeJobs", "httpMethod": "GET", "id": "dataproc.projects.locations.sessions.sparkApplications.summarizeJobs", "parameterOrder": ["name"], "parameters": {"jobIds": {"description": "Optional. List of Job IDs to filter by if provided.", "format": "int64", "location": "query", "repeated": true, "type": "string"}, "name": {"description": "Required. The fully qualified name of the session to retrieve in the format \"projects/PROJECT_ID/locations/DATAPROC_REGION/sessions/SESSION_ID/sparkApplications/APPLICATION_ID\"", "location": "path", "pattern": "^projects/[^/]+/locations/[^/]+/sessions/[^/]+/sparkApplications/[^/]+$", "required": true, "type": "string"}, "parent": {"description": "Required. <PERSON><PERSON> (Session) resource reference.", "location": "query", "type": "string"}}, "path": "v1/{+name}:summarizeJobs", "response": {"$ref": "SummarizeSessionSparkApplicationJobsResponse"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform"]}, "summarizeStageAttemptTasks": {"description": "Obtain summary of Tasks for a Spark Application Stage Attempt", "flatPath": "v1/projects/{projectsId}/locations/{locationsId}/sessions/{sessionsId}/sparkApplications/{sparkApplicationsId}:summarizeStageAttemptTasks", "httpMethod": "GET", "id": "dataproc.projects.locations.sessions.sparkApplications.summarizeStageAttemptTasks", "parameterOrder": ["name"], "parameters": {"name": {"description": "Required. The fully qualified name of the session to retrieve in the format \"projects/PROJECT_ID/locations/DATAPROC_REGION/sessions/SESSION_ID/sparkApplications/APPLICATION_ID\"", "location": "path", "pattern": "^projects/[^/]+/locations/[^/]+/sessions/[^/]+/sparkApplications/[^/]+$", "required": true, "type": "string"}, "parent": {"description": "Required. <PERSON><PERSON> (Session) resource reference.", "location": "query", "type": "string"}, "stageAttemptId": {"description": "Required. Stage Attempt ID", "format": "int32", "location": "query", "type": "integer"}, "stageId": {"description": "Required. Stage ID", "format": "int64", "location": "query", "type": "string"}}, "path": "v1/{+name}:summarizeStageAttemptTasks", "response": {"$ref": "SummarizeSessionSparkApplicationStageAttemptTasksResponse"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform"]}, "summarizeStages": {"description": "Obtain summary of Stages for a Spark Application", "flatPath": "v1/projects/{projectsId}/locations/{locationsId}/sessions/{sessionsId}/sparkApplications/{sparkApplicationsId}:summarizeStages", "httpMethod": "GET", "id": "dataproc.projects.locations.sessions.sparkApplications.summarizeStages", "parameterOrder": ["name"], "parameters": {"name": {"description": "Required. The fully qualified name of the session to retrieve in the format \"projects/PROJECT_ID/locations/DATAPROC_REGION/sessions/SESSION_ID/sparkApplications/APPLICATION_ID\"", "location": "path", "pattern": "^projects/[^/]+/locations/[^/]+/sessions/[^/]+/sparkApplications/[^/]+$", "required": true, "type": "string"}, "parent": {"description": "Required. <PERSON><PERSON> (Session) resource reference.", "location": "query", "type": "string"}, "stageIds": {"description": "Optional. List of Stage IDs to filter by if provided.", "format": "int64", "location": "query", "repeated": true, "type": "string"}}, "path": "v1/{+name}:summarizeStages", "response": {"$ref": "SummarizeSessionSparkApplicationStagesResponse"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform"]}, "write": {"description": "Write wrapper objects from dataplane to spanner", "flatPath": "v1/projects/{projectsId}/locations/{locationsId}/sessions/{sessionsId}/sparkApplications/{sparkApplicationsId}:write", "httpMethod": "POST", "id": "dataproc.projects.locations.sessions.sparkApplications.write", "parameterOrder": ["name"], "parameters": {"name": {"description": "Required. The fully qualified name of the spark application to write data about in the format \"projects/PROJECT_ID/locations/DATAPROC_REGION/sessions/SESSION_ID/sparkApplications/APPLICATION_ID\"", "location": "path", "pattern": "^projects/[^/]+/locations/[^/]+/sessions/[^/]+/sparkApplications/[^/]+$", "required": true, "type": "string"}}, "path": "v1/{+name}:write", "request": {"$ref": "WriteSessionSparkApplicationContextRequest"}, "response": {"$ref": "WriteSessionSparkApplicationContextResponse"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform"]}}}}}, "workflowTemplates": {"methods": {"create": {"description": "Creates new workflow template.", "flatPath": "v1/projects/{projectsId}/locations/{locationsId}/workflowTemplates", "httpMethod": "POST", "id": "dataproc.projects.locations.workflowTemplates.create", "parameterOrder": ["parent"], "parameters": {"parent": {"description": "Required. The resource name of the region or location, as described in https://cloud.google.com/apis/design/resource_names. For projects.regions.workflowTemplates.create, the resource name of the region has the following format: projects/{project_id}/regions/{region} For projects.locations.workflowTemplates.create, the resource name of the location has the following format: projects/{project_id}/locations/{location}", "location": "path", "pattern": "^projects/[^/]+/locations/[^/]+$", "required": true, "type": "string"}}, "path": "v1/{+parent}/workflowTemplates", "request": {"$ref": "WorkflowTemplate"}, "response": {"$ref": "WorkflowTemplate"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform"]}, "delete": {"description": "Deletes a workflow template. It does not cancel in-progress workflows.", "flatPath": "v1/projects/{projectsId}/locations/{locationsId}/workflowTemplates/{workflowTemplatesId}", "httpMethod": "DELETE", "id": "dataproc.projects.locations.workflowTemplates.delete", "parameterOrder": ["name"], "parameters": {"name": {"description": "Required. The resource name of the workflow template, as described in https://cloud.google.com/apis/design/resource_names. For projects.regions.workflowTemplates.delete, the resource name of the template has the following format: projects/{project_id}/regions/{region}/workflowTemplates/{template_id} For projects.locations.workflowTemplates.instantiate, the resource name of the template has the following format: projects/{project_id}/locations/{location}/workflowTemplates/{template_id}", "location": "path", "pattern": "^projects/[^/]+/locations/[^/]+/workflowTemplates/[^/]+$", "required": true, "type": "string"}, "version": {"description": "Optional. The version of workflow template to delete. If specified, will only delete the template if the current server version matches specified version.", "format": "int32", "location": "query", "type": "integer"}}, "path": "v1/{+name}", "response": {"$ref": "Empty"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform"]}, "get": {"description": "Retrieves the latest workflow template.Can retrieve previously instantiated template by specifying optional version parameter.", "flatPath": "v1/projects/{projectsId}/locations/{locationsId}/workflowTemplates/{workflowTemplatesId}", "httpMethod": "GET", "id": "dataproc.projects.locations.workflowTemplates.get", "parameterOrder": ["name"], "parameters": {"name": {"description": "Required. The resource name of the workflow template, as described in https://cloud.google.com/apis/design/resource_names. For projects.regions.workflowTemplates.get, the resource name of the template has the following format: projects/{project_id}/regions/{region}/workflowTemplates/{template_id} For projects.locations.workflowTemplates.get, the resource name of the template has the following format: projects/{project_id}/locations/{location}/workflowTemplates/{template_id}", "location": "path", "pattern": "^projects/[^/]+/locations/[^/]+/workflowTemplates/[^/]+$", "required": true, "type": "string"}, "version": {"description": "Optional. The version of workflow template to retrieve. Only previously instantiated versions can be retrieved.If unspecified, retrieves the current version.", "format": "int32", "location": "query", "type": "integer"}}, "path": "v1/{+name}", "response": {"$ref": "WorkflowTemplate"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform"]}, "getIamPolicy": {"description": "Gets the access control policy for a resource. Returns an empty policy if the resource exists and does not have a policy set.", "flatPath": "v1/projects/{projectsId}/locations/{locationsId}/workflowTemplates/{workflowTemplatesId}:getIamPolicy", "httpMethod": "POST", "id": "dataproc.projects.locations.workflowTemplates.getIamPolicy", "parameterOrder": ["resource"], "parameters": {"resource": {"description": "REQUIRED: The resource for which the policy is being requested. See Resource names (https://cloud.google.com/apis/design/resource_names) for the appropriate value for this field.", "location": "path", "pattern": "^projects/[^/]+/locations/[^/]+/workflowTemplates/[^/]+$", "required": true, "type": "string"}}, "path": "v1/{+resource}:getIamPolicy", "request": {"$ref": "GetIamPolicyRequest"}, "response": {"$ref": "Policy"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform"]}, "instantiate": {"description": "Instantiates a template and begins execution.The returned Operation can be used to track execution of workflow by polling operations.get. The Operation will complete when entire workflow is finished.The running workflow can be aborted via operations.cancel. This will cause any inflight jobs to be cancelled and workflow-owned clusters to be deleted.The Operation.metadata will be WorkflowMetadata (https://cloud.google.com/dataproc/docs/reference/rpc/google.cloud.dataproc.v1#workflowmetadata). Also see Using WorkflowMetadata (https://cloud.google.com/dataproc/docs/concepts/workflows/debugging#using_workflowmetadata).On successful completion, Operation.response will be Empty.", "flatPath": "v1/projects/{projectsId}/locations/{locationsId}/workflowTemplates/{workflowTemplatesId}:instantiate", "httpMethod": "POST", "id": "dataproc.projects.locations.workflowTemplates.instantiate", "parameterOrder": ["name"], "parameters": {"name": {"description": "Required. The resource name of the workflow template, as described in https://cloud.google.com/apis/design/resource_names. For projects.regions.workflowTemplates.instantiate, the resource name of the template has the following format: projects/{project_id}/regions/{region}/workflowTemplates/{template_id} For projects.locations.workflowTemplates.instantiate, the resource name of the template has the following format: projects/{project_id}/locations/{location}/workflowTemplates/{template_id}", "location": "path", "pattern": "^projects/[^/]+/locations/[^/]+/workflowTemplates/[^/]+$", "required": true, "type": "string"}}, "path": "v1/{+name}:instantiate", "request": {"$ref": "InstantiateWorkflowTemplateRequest"}, "response": {"$ref": "Operation"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform"]}, "instantiateInline": {"description": "Instantiates a template and begins execution.This method is equivalent to executing the sequence CreateWorkflowTemplate, InstantiateWorkflowTemplate, DeleteWorkflowTemplate.The returned Operation can be used to track execution of workflow by polling operations.get. The Operation will complete when entire workflow is finished.The running workflow can be aborted via operations.cancel. This will cause any inflight jobs to be cancelled and workflow-owned clusters to be deleted.The Operation.metadata will be WorkflowMetadata (https://cloud.google.com/dataproc/docs/reference/rpc/google.cloud.dataproc.v1#workflowmetadata). Also see Using WorkflowMetadata (https://cloud.google.com/dataproc/docs/concepts/workflows/debugging#using_workflowmetadata).On successful completion, Operation.response will be Empty.", "flatPath": "v1/projects/{projectsId}/locations/{locationsId}/workflowTemplates:instantiateInline", "httpMethod": "POST", "id": "dataproc.projects.locations.workflowTemplates.instantiateInline", "parameterOrder": ["parent"], "parameters": {"parent": {"description": "Required. The resource name of the region or location, as described in https://cloud.google.com/apis/design/resource_names. For projects.regions.workflowTemplates,instantiateinline, the resource name of the region has the following format: projects/{project_id}/regions/{region} For projects.locations.workflowTemplates.instantiateinline, the resource name of the location has the following format: projects/{project_id}/locations/{location}", "location": "path", "pattern": "^projects/[^/]+/locations/[^/]+$", "required": true, "type": "string"}, "requestId": {"description": "Optional. A tag that prevents multiple concurrent workflow instances with the same tag from running. This mitigates risk of concurrent instances started due to retries.It is recommended to always set this value to a UUID (https://en.wikipedia.org/wiki/Universally_unique_identifier).The tag must contain only letters (a-z, A-Z), numbers (0-9), underscores (_), and hyphens (-). The maximum length is 40 characters.", "location": "query", "type": "string"}}, "path": "v1/{+parent}/workflowTemplates:instantiateInline", "request": {"$ref": "WorkflowTemplate"}, "response": {"$ref": "Operation"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform"]}, "list": {"description": "Lists workflows that match the specified filter in the request.", "flatPath": "v1/projects/{projectsId}/locations/{locationsId}/workflowTemplates", "httpMethod": "GET", "id": "dataproc.projects.locations.workflowTemplates.list", "parameterOrder": ["parent"], "parameters": {"pageSize": {"description": "Optional. The maximum number of results to return in each response.", "format": "int32", "location": "query", "type": "integer"}, "pageToken": {"description": "Optional. The page token, returned by a previous call, to request the next page of results.", "location": "query", "type": "string"}, "parent": {"description": "Required. The resource name of the region or location, as described in https://cloud.google.com/apis/design/resource_names. For projects.regions.workflowTemplates,list, the resource name of the region has the following format: projects/{project_id}/regions/{region} For projects.locations.workflowTemplates.list, the resource name of the location has the following format: projects/{project_id}/locations/{location}", "location": "path", "pattern": "^projects/[^/]+/locations/[^/]+$", "required": true, "type": "string"}}, "path": "v1/{+parent}/workflowTemplates", "response": {"$ref": "ListWorkflowTemplatesResponse"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform"]}, "setIamPolicy": {"description": "Sets the access control policy on the specified resource. Replaces any existing policy.Can return NOT_FOUND, INVALID_ARGUMENT, and PERMISSION_DENIED errors.", "flatPath": "v1/projects/{projectsId}/locations/{locationsId}/workflowTemplates/{workflowTemplatesId}:setIamPolicy", "httpMethod": "POST", "id": "dataproc.projects.locations.workflowTemplates.setIamPolicy", "parameterOrder": ["resource"], "parameters": {"resource": {"description": "REQUIRED: The resource for which the policy is being specified. See Resource names (https://cloud.google.com/apis/design/resource_names) for the appropriate value for this field.", "location": "path", "pattern": "^projects/[^/]+/locations/[^/]+/workflowTemplates/[^/]+$", "required": true, "type": "string"}}, "path": "v1/{+resource}:setIamPolicy", "request": {"$ref": "SetIamPolicyRequest"}, "response": {"$ref": "Policy"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform"]}, "testIamPermissions": {"description": "Returns permissions that a caller has on the specified resource. If the resource does not exist, this will return an empty set of permissions, not a NOT_FOUND error.Note: This operation is designed to be used for building permission-aware UIs and command-line tools, not for authorization checking. This operation may \"fail open\" without warning.", "flatPath": "v1/projects/{projectsId}/locations/{locationsId}/workflowTemplates/{workflowTemplatesId}:testIamPermissions", "httpMethod": "POST", "id": "dataproc.projects.locations.workflowTemplates.testIamPermissions", "parameterOrder": ["resource"], "parameters": {"resource": {"description": "REQUIRED: The resource for which the policy detail is being requested. See Resource names (https://cloud.google.com/apis/design/resource_names) for the appropriate value for this field.", "location": "path", "pattern": "^projects/[^/]+/locations/[^/]+/workflowTemplates/[^/]+$", "required": true, "type": "string"}}, "path": "v1/{+resource}:testIamPermissions", "request": {"$ref": "TestIamPermissionsRequest"}, "response": {"$ref": "TestIamPermissionsResponse"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform"]}, "update": {"description": "Updates (replaces) workflow template. The updated template must contain version that matches the current server version.", "flatPath": "v1/projects/{projectsId}/locations/{locationsId}/workflowTemplates/{workflowTemplatesId}", "httpMethod": "PUT", "id": "dataproc.projects.locations.workflowTemplates.update", "parameterOrder": ["name"], "parameters": {"name": {"description": "Output only. The resource name of the workflow template, as described in https://cloud.google.com/apis/design/resource_names. For projects.regions.workflowTemplates, the resource name of the template has the following format: projects/{project_id}/regions/{region}/workflowTemplates/{template_id} For projects.locations.workflowTemplates, the resource name of the template has the following format: projects/{project_id}/locations/{location}/workflowTemplates/{template_id}", "location": "path", "pattern": "^projects/[^/]+/locations/[^/]+/workflowTemplates/[^/]+$", "required": true, "type": "string"}}, "path": "v1/{+name}", "request": {"$ref": "WorkflowTemplate"}, "response": {"$ref": "WorkflowTemplate"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform"]}}}}}, "regions": {"resources": {"autoscalingPolicies": {"methods": {"create": {"description": "Creates new autoscaling policy.", "flatPath": "v1/projects/{projectsId}/regions/{regionsId}/autoscalingPolicies", "httpMethod": "POST", "id": "dataproc.projects.regions.autoscalingPolicies.create", "parameterOrder": ["parent"], "parameters": {"parent": {"description": "Required. The \"resource name\" of the region or location, as described in https://cloud.google.com/apis/design/resource_names. For projects.regions.autoscalingPolicies.create, the resource name of the region has the following format: projects/{project_id}/regions/{region} For projects.locations.autoscalingPolicies.create, the resource name of the location has the following format: projects/{project_id}/locations/{location}", "location": "path", "pattern": "^projects/[^/]+/regions/[^/]+$", "required": true, "type": "string"}}, "path": "v1/{+parent}/autoscalingPolicies", "request": {"$ref": "AutoscalingPolicy"}, "response": {"$ref": "AutoscalingPolicy"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform"]}, "delete": {"description": "Deletes an autoscaling policy. It is an error to delete an autoscaling policy that is in use by one or more clusters.", "flatPath": "v1/projects/{projectsId}/regions/{regionsId}/autoscalingPolicies/{autoscalingPoliciesId}", "httpMethod": "DELETE", "id": "dataproc.projects.regions.autoscalingPolicies.delete", "parameterOrder": ["name"], "parameters": {"name": {"description": "Required. The \"resource name\" of the autoscaling policy, as described in https://cloud.google.com/apis/design/resource_names. For projects.regions.autoscalingPolicies.delete, the resource name of the policy has the following format: projects/{project_id}/regions/{region}/autoscalingPolicies/{policy_id} For projects.locations.autoscalingPolicies.delete, the resource name of the policy has the following format: projects/{project_id}/locations/{location}/autoscalingPolicies/{policy_id}", "location": "path", "pattern": "^projects/[^/]+/regions/[^/]+/autoscalingPolicies/[^/]+$", "required": true, "type": "string"}}, "path": "v1/{+name}", "response": {"$ref": "Empty"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform"]}, "get": {"description": "Retrieves autoscaling policy.", "flatPath": "v1/projects/{projectsId}/regions/{regionsId}/autoscalingPolicies/{autoscalingPoliciesId}", "httpMethod": "GET", "id": "dataproc.projects.regions.autoscalingPolicies.get", "parameterOrder": ["name"], "parameters": {"name": {"description": "Required. The \"resource name\" of the autoscaling policy, as described in https://cloud.google.com/apis/design/resource_names. For projects.regions.autoscalingPolicies.get, the resource name of the policy has the following format: projects/{project_id}/regions/{region}/autoscalingPolicies/{policy_id} For projects.locations.autoscalingPolicies.get, the resource name of the policy has the following format: projects/{project_id}/locations/{location}/autoscalingPolicies/{policy_id}", "location": "path", "pattern": "^projects/[^/]+/regions/[^/]+/autoscalingPolicies/[^/]+$", "required": true, "type": "string"}}, "path": "v1/{+name}", "response": {"$ref": "AutoscalingPolicy"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform"]}, "getIamPolicy": {"description": "Gets the access control policy for a resource. Returns an empty policy if the resource exists and does not have a policy set.", "flatPath": "v1/projects/{projectsId}/regions/{regionsId}/autoscalingPolicies/{autoscalingPoliciesId}:getIamPolicy", "httpMethod": "POST", "id": "dataproc.projects.regions.autoscalingPolicies.getIamPolicy", "parameterOrder": ["resource"], "parameters": {"resource": {"description": "REQUIRED: The resource for which the policy is being requested. See Resource names (https://cloud.google.com/apis/design/resource_names) for the appropriate value for this field.", "location": "path", "pattern": "^projects/[^/]+/regions/[^/]+/autoscalingPolicies/[^/]+$", "required": true, "type": "string"}}, "path": "v1/{+resource}:getIamPolicy", "request": {"$ref": "GetIamPolicyRequest"}, "response": {"$ref": "Policy"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform"]}, "list": {"description": "Lists autoscaling policies in the project.", "flatPath": "v1/projects/{projectsId}/regions/{regionsId}/autoscalingPolicies", "httpMethod": "GET", "id": "dataproc.projects.regions.autoscalingPolicies.list", "parameterOrder": ["parent"], "parameters": {"pageSize": {"description": "Optional. The maximum number of results to return in each response. Must be less than or equal to 1000. Defaults to 100.", "format": "int32", "location": "query", "type": "integer"}, "pageToken": {"description": "Optional. The page token, returned by a previous call, to request the next page of results.", "location": "query", "type": "string"}, "parent": {"description": "Required. The \"resource name\" of the region or location, as described in https://cloud.google.com/apis/design/resource_names. For projects.regions.autoscalingPolicies.list, the resource name of the region has the following format: projects/{project_id}/regions/{region} For projects.locations.autoscalingPolicies.list, the resource name of the location has the following format: projects/{project_id}/locations/{location}", "location": "path", "pattern": "^projects/[^/]+/regions/[^/]+$", "required": true, "type": "string"}}, "path": "v1/{+parent}/autoscalingPolicies", "response": {"$ref": "ListAutoscalingPoliciesResponse"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform"]}, "setIamPolicy": {"description": "Sets the access control policy on the specified resource. Replaces any existing policy.Can return NOT_FOUND, INVALID_ARGUMENT, and PERMISSION_DENIED errors.", "flatPath": "v1/projects/{projectsId}/regions/{regionsId}/autoscalingPolicies/{autoscalingPoliciesId}:setIamPolicy", "httpMethod": "POST", "id": "dataproc.projects.regions.autoscalingPolicies.setIamPolicy", "parameterOrder": ["resource"], "parameters": {"resource": {"description": "REQUIRED: The resource for which the policy is being specified. See Resource names (https://cloud.google.com/apis/design/resource_names) for the appropriate value for this field.", "location": "path", "pattern": "^projects/[^/]+/regions/[^/]+/autoscalingPolicies/[^/]+$", "required": true, "type": "string"}}, "path": "v1/{+resource}:setIamPolicy", "request": {"$ref": "SetIamPolicyRequest"}, "response": {"$ref": "Policy"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform"]}, "testIamPermissions": {"description": "Returns permissions that a caller has on the specified resource. If the resource does not exist, this will return an empty set of permissions, not a NOT_FOUND error.Note: This operation is designed to be used for building permission-aware UIs and command-line tools, not for authorization checking. This operation may \"fail open\" without warning.", "flatPath": "v1/projects/{projectsId}/regions/{regionsId}/autoscalingPolicies/{autoscalingPoliciesId}:testIamPermissions", "httpMethod": "POST", "id": "dataproc.projects.regions.autoscalingPolicies.testIamPermissions", "parameterOrder": ["resource"], "parameters": {"resource": {"description": "REQUIRED: The resource for which the policy detail is being requested. See Resource names (https://cloud.google.com/apis/design/resource_names) for the appropriate value for this field.", "location": "path", "pattern": "^projects/[^/]+/regions/[^/]+/autoscalingPolicies/[^/]+$", "required": true, "type": "string"}}, "path": "v1/{+resource}:testIamPermissions", "request": {"$ref": "TestIamPermissionsRequest"}, "response": {"$ref": "TestIamPermissionsResponse"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform"]}, "update": {"description": "Updates (replaces) autoscaling policy.Disabled check for update_mask, because all updates will be full replacements.", "flatPath": "v1/projects/{projectsId}/regions/{regionsId}/autoscalingPolicies/{autoscalingPoliciesId}", "httpMethod": "PUT", "id": "dataproc.projects.regions.autoscalingPolicies.update", "parameterOrder": ["name"], "parameters": {"name": {"description": "Output only. The \"resource name\" of the autoscaling policy, as described in https://cloud.google.com/apis/design/resource_names. For projects.regions.autoscalingPolicies, the resource name of the policy has the following format: projects/{project_id}/regions/{region}/autoscalingPolicies/{policy_id} For projects.locations.autoscalingPolicies, the resource name of the policy has the following format: projects/{project_id}/locations/{location}/autoscalingPolicies/{policy_id}", "location": "path", "pattern": "^projects/[^/]+/regions/[^/]+/autoscalingPolicies/[^/]+$", "required": true, "type": "string"}}, "path": "v1/{+name}", "request": {"$ref": "AutoscalingPolicy"}, "response": {"$ref": "AutoscalingPolicy"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform"]}}}, "clusters": {"methods": {"create": {"description": "Creates a cluster in a project. The returned Operation.metadata will be ClusterOperationMetadata (https://cloud.google.com/dataproc/docs/reference/rpc/google.cloud.dataproc.v1#clusteroperationmetadata).", "flatPath": "v1/projects/{projectId}/regions/{region}/clusters", "httpMethod": "POST", "id": "dataproc.projects.regions.clusters.create", "parameterOrder": ["projectId", "region"], "parameters": {"actionOnFailedPrimaryWorkers": {"description": "Optional. Failure action when primary worker creation fails.", "enum": ["FAILURE_ACTION_UNSPECIFIED", "NO_ACTION", "DELETE"], "enumDescriptions": ["When FailureAction is unspecified, failure action defaults to NO_ACTION.", "Take no action on failure to create a cluster resource. NO_ACTION is the default.", "Delete the failed cluster resource."], "location": "query", "type": "string"}, "projectId": {"description": "Required. The ID of the Google Cloud Platform project that the cluster belongs to.", "location": "path", "required": true, "type": "string"}, "region": {"description": "Required. The Dataproc region in which to handle the request.", "location": "path", "required": true, "type": "string"}, "requestId": {"description": "Optional. A unique ID used to identify the request. If the server receives two CreateClusterRequest (https://cloud.google.com/dataproc/docs/reference/rpc/google.cloud.dataproc.v1#google.cloud.dataproc.v1.CreateClusterRequest)s with the same id, then the second request will be ignored and the first google.longrunning.Operation created and stored in the backend is returned.It is recommended to always set this value to a UUID (https://en.wikipedia.org/wiki/Universally_unique_identifier).The ID must contain only letters (a-z, A-Z), numbers (0-9), underscores (_), and hyphens (-). The maximum length is 40 characters.", "location": "query", "type": "string"}}, "path": "v1/projects/{projectId}/regions/{region}/clusters", "request": {"$ref": "Cluster"}, "response": {"$ref": "Operation"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform"]}, "delete": {"description": "Deletes a cluster in a project. The returned Operation.metadata will be ClusterOperationMetadata (https://cloud.google.com/dataproc/docs/reference/rpc/google.cloud.dataproc.v1#clusteroperationmetadata).", "flatPath": "v1/projects/{projectId}/regions/{region}/clusters/{clusterName}", "httpMethod": "DELETE", "id": "dataproc.projects.regions.clusters.delete", "parameterOrder": ["projectId", "region", "clusterName"], "parameters": {"clusterName": {"description": "Required. The cluster name.", "location": "path", "required": true, "type": "string"}, "clusterUuid": {"description": "Optional. Specifying the cluster_uuid means the RPC should fail (with error NOT_FOUND) if cluster with specified UUID does not exist.", "location": "query", "type": "string"}, "gracefulTerminationTimeout": {"description": "Optional. The graceful termination timeout for the deletion of the cluster. Indicate the time the request will wait to complete the running jobs on the cluster before its forceful deletion. Default value is 0 indicating that the user has not enabled the graceful termination. Value can be between 60 second and 6 Hours, in case the graceful termination is enabled. (There is no separate flag to check the enabling or disabling of graceful termination, it can be checked by the values in the field).", "format": "google-duration", "location": "query", "type": "string"}, "projectId": {"description": "Required. The ID of the Google Cloud Platform project that the cluster belongs to.", "location": "path", "required": true, "type": "string"}, "region": {"description": "Required. The Dataproc region in which to handle the request.", "location": "path", "required": true, "type": "string"}, "requestId": {"description": "Optional. A unique ID used to identify the request. If the server receives two DeleteClusterRequest (https://cloud.google.com/dataproc/docs/reference/rpc/google.cloud.dataproc.v1#google.cloud.dataproc.v1.DeleteClusterRequest)s with the same id, then the second request will be ignored and the first google.longrunning.Operation created and stored in the backend is returned.It is recommended to always set this value to a UUID (https://en.wikipedia.org/wiki/Universally_unique_identifier).The ID must contain only letters (a-z, A-Z), numbers (0-9), underscores (_), and hyphens (-). The maximum length is 40 characters.", "location": "query", "type": "string"}}, "path": "v1/projects/{projectId}/regions/{region}/clusters/{clusterName}", "response": {"$ref": "Operation"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform"]}, "diagnose": {"description": "Gets cluster diagnostic information. The returned Operation.metadata will be ClusterOperationMetadata (https://cloud.google.com/dataproc/docs/reference/rpc/google.cloud.dataproc.v1#clusteroperationmetadata). After the operation completes, Operation.response contains DiagnoseClusterResults (https://cloud.google.com/dataproc/docs/reference/rpc/google.cloud.dataproc.v1#diagnoseclusterresults).", "flatPath": "v1/projects/{projectId}/regions/{region}/clusters/{clusterName}:diagnose", "httpMethod": "POST", "id": "dataproc.projects.regions.clusters.diagnose", "parameterOrder": ["projectId", "region", "clusterName"], "parameters": {"clusterName": {"description": "Required. The cluster name.", "location": "path", "required": true, "type": "string"}, "projectId": {"description": "Required. The ID of the Google Cloud Platform project that the cluster belongs to.", "location": "path", "required": true, "type": "string"}, "region": {"description": "Required. The Dataproc region in which to handle the request.", "location": "path", "required": true, "type": "string"}}, "path": "v1/projects/{projectId}/regions/{region}/clusters/{clusterName}:diagnose", "request": {"$ref": "DiagnoseClusterRequest"}, "response": {"$ref": "Operation"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform"]}, "get": {"description": "Gets the resource representation for a cluster in a project.", "flatPath": "v1/projects/{projectId}/regions/{region}/clusters/{clusterName}", "httpMethod": "GET", "id": "dataproc.projects.regions.clusters.get", "parameterOrder": ["projectId", "region", "clusterName"], "parameters": {"clusterName": {"description": "Required. The cluster name.", "location": "path", "required": true, "type": "string"}, "projectId": {"description": "Required. The ID of the Google Cloud Platform project that the cluster belongs to.", "location": "path", "required": true, "type": "string"}, "region": {"description": "Required. The Dataproc region in which to handle the request.", "location": "path", "required": true, "type": "string"}}, "path": "v1/projects/{projectId}/regions/{region}/clusters/{clusterName}", "response": {"$ref": "Cluster"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform"]}, "getIamPolicy": {"description": "Gets the access control policy for a resource. Returns an empty policy if the resource exists and does not have a policy set.", "flatPath": "v1/projects/{projectsId}/regions/{regionsId}/clusters/{clustersId}:getIamPolicy", "httpMethod": "POST", "id": "dataproc.projects.regions.clusters.getIamPolicy", "parameterOrder": ["resource"], "parameters": {"resource": {"description": "REQUIRED: The resource for which the policy is being requested. See Resource names (https://cloud.google.com/apis/design/resource_names) for the appropriate value for this field.", "location": "path", "pattern": "^projects/[^/]+/regions/[^/]+/clusters/[^/]+$", "required": true, "type": "string"}}, "path": "v1/{+resource}:getIamPolicy", "request": {"$ref": "GetIamPolicyRequest"}, "response": {"$ref": "Policy"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform"]}, "injectCredentials": {"description": "Inject encrypted credentials into all of the VMs in a cluster.The target cluster must be a personal auth cluster assigned to the user who is issuing the RPC.", "flatPath": "v1/projects/{projectsId}/regions/{regionsId}/clusters/{clustersId}:injectCredentials", "httpMethod": "POST", "id": "dataproc.projects.regions.clusters.injectCredentials", "parameterOrder": ["project", "region", "cluster"], "parameters": {"cluster": {"description": "Required. The cluster, in the form clusters/.", "location": "path", "pattern": "^clusters/[^/]+$", "required": true, "type": "string"}, "project": {"description": "Required. The ID of the Google Cloud Platform project the cluster belongs to, of the form projects/.", "location": "path", "pattern": "^projects/[^/]+$", "required": true, "type": "string"}, "region": {"description": "Required. The region containing the cluster, of the form regions/.", "location": "path", "pattern": "^regions/[^/]+$", "required": true, "type": "string"}}, "path": "v1/{+project}/{+region}/{+cluster}:injectCredentials", "request": {"$ref": "InjectCredentialsRequest"}, "response": {"$ref": "Operation"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform"]}, "list": {"description": "Lists all regions/{region}/clusters in a project alphabetically.", "flatPath": "v1/projects/{projectId}/regions/{region}/clusters", "httpMethod": "GET", "id": "dataproc.projects.regions.clusters.list", "parameterOrder": ["projectId", "region"], "parameters": {"filter": {"description": "Optional. A filter constraining the clusters to list. Filters are case-sensitive and have the following syntax:field = value AND field = value ...where field is one of status.state, clusterName, or labels.[KEY], and [KEY] is a label key. value can be * to match all values. status.state can be one of the following: ACTIVE, INACTIVE, CREATING, RUNNING, ERROR, DELETING, UPDATING, STOPPING, or STOPPED. ACTIVE contains the CREATING, UPDATING, and RUNNING states. INACTIVE contains the DELETING, ERROR, STOPPING, and STOPPED states. clusterName is the name of the cluster provided at creation time. Only the logical AND operator is supported; space-separated items are treated as having an implicit AND operator.Example filter:status.state = ACTIVE AND clusterName = mycluster AND labels.env = staging AND labels.starred = *", "location": "query", "type": "string"}, "pageSize": {"description": "Optional. The standard List page size.", "format": "int32", "location": "query", "type": "integer"}, "pageToken": {"description": "Optional. The standard List page token.", "location": "query", "type": "string"}, "projectId": {"description": "Required. The ID of the Google Cloud Platform project that the cluster belongs to.", "location": "path", "required": true, "type": "string"}, "region": {"description": "Required. The Dataproc region in which to handle the request.", "location": "path", "required": true, "type": "string"}}, "path": "v1/projects/{projectId}/regions/{region}/clusters", "response": {"$ref": "ListClustersResponse"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform"]}, "patch": {"description": "Updates a cluster in a project. The returned Operation.metadata will be ClusterOperationMetadata (https://cloud.google.com/dataproc/docs/reference/rpc/google.cloud.dataproc.v1#clusteroperationmetadata). The cluster must be in a RUNNING state or an error is returned.", "flatPath": "v1/projects/{projectId}/regions/{region}/clusters/{clusterName}", "httpMethod": "PATCH", "id": "dataproc.projects.regions.clusters.patch", "parameterOrder": ["projectId", "region", "clusterName"], "parameters": {"clusterName": {"description": "Required. The cluster name.", "location": "path", "required": true, "type": "string"}, "gracefulDecommissionTimeout": {"description": "Optional. Timeout for graceful YARN decommissioning. Graceful decommissioning allows removing nodes from the cluster without interrupting jobs in progress. Timeout specifies how long to wait for jobs in progress to finish before forcefully removing nodes (and potentially interrupting jobs). Default timeout is 0 (for forceful decommission), and the maximum allowed timeout is 1 day. (see JSON representation of Duration (https://developers.google.com/protocol-buffers/docs/proto3#json)).Only supported on Dataproc image versions 1.2 and higher.", "format": "google-duration", "location": "query", "type": "string"}, "projectId": {"description": "Required. The ID of the Google Cloud Platform project the cluster belongs to.", "location": "path", "required": true, "type": "string"}, "region": {"description": "Required. The Dataproc region in which to handle the request.", "location": "path", "required": true, "type": "string"}, "requestId": {"description": "Optional. A unique ID used to identify the request. If the server receives two UpdateClusterRequest (https://cloud.google.com/dataproc/docs/reference/rpc/google.cloud.dataproc.v1#google.cloud.dataproc.v1.UpdateClusterRequest)s with the same id, then the second request will be ignored and the first google.longrunning.Operation created and stored in the backend is returned.It is recommended to always set this value to a UUID (https://en.wikipedia.org/wiki/Universally_unique_identifier).The ID must contain only letters (a-z, A-Z), numbers (0-9), underscores (_), and hyphens (-). The maximum length is 40 characters.", "location": "query", "type": "string"}, "updateMask": {"description": "Required. Specifies the path, relative to Cluster, of the field to update. For example, to change the number of workers in a cluster to 5, the update_mask parameter would be specified as config.worker_config.num_instances, and the PATCH request body would specify the new value, as follows: { \"config\":{ \"workerConfig\":{ \"numInstances\":\"5\" } } } Similarly, to change the number of preemptible workers in a cluster to 5, the update_mask parameter would be config.secondary_worker_config.num_instances, and the PATCH request body would be set as follows: { \"config\":{ \"secondaryWorkerConfig\":{ \"numInstances\":\"5\" } } } *Note:* Currently, only the following fields can be updated: *Mask* *Purpose* *labels* Update labels *config.worker_config.num_instances* Resize primary worker group *config.secondary_worker_config.num_instances* Resize secondary worker group config.autoscaling_config.policy_uri Use, stop using, or change autoscaling policies ", "format": "google-fieldmask", "location": "query", "type": "string"}}, "path": "v1/projects/{projectId}/regions/{region}/clusters/{clusterName}", "request": {"$ref": "Cluster"}, "response": {"$ref": "Operation"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform"]}, "repair": {"description": "Repairs a cluster.", "flatPath": "v1/projects/{projectId}/regions/{region}/clusters/{clusterName}:repair", "httpMethod": "POST", "id": "dataproc.projects.regions.clusters.repair", "parameterOrder": ["projectId", "region", "clusterName"], "parameters": {"clusterName": {"description": "Required. The cluster name.", "location": "path", "required": true, "type": "string"}, "projectId": {"description": "Required. The ID of the Google Cloud Platform project the cluster belongs to.", "location": "path", "required": true, "type": "string"}, "region": {"description": "Required. The Dataproc region in which to handle the request.", "location": "path", "required": true, "type": "string"}}, "path": "v1/projects/{projectId}/regions/{region}/clusters/{clusterName}:repair", "request": {"$ref": "RepairClusterRequest"}, "response": {"$ref": "Operation"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform"]}, "setIamPolicy": {"description": "Sets the access control policy on the specified resource. Replaces any existing policy.Can return NOT_FOUND, INVALID_ARGUMENT, and PERMISSION_DENIED errors.", "flatPath": "v1/projects/{projectsId}/regions/{regionsId}/clusters/{clustersId}:setIamPolicy", "httpMethod": "POST", "id": "dataproc.projects.regions.clusters.setIamPolicy", "parameterOrder": ["resource"], "parameters": {"resource": {"description": "REQUIRED: The resource for which the policy is being specified. See Resource names (https://cloud.google.com/apis/design/resource_names) for the appropriate value for this field.", "location": "path", "pattern": "^projects/[^/]+/regions/[^/]+/clusters/[^/]+$", "required": true, "type": "string"}}, "path": "v1/{+resource}:setIamPolicy", "request": {"$ref": "SetIamPolicyRequest"}, "response": {"$ref": "Policy"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform"]}, "start": {"description": "Starts a cluster in a project.", "flatPath": "v1/projects/{projectId}/regions/{region}/clusters/{clusterName}:start", "httpMethod": "POST", "id": "dataproc.projects.regions.clusters.start", "parameterOrder": ["projectId", "region", "clusterName"], "parameters": {"clusterName": {"description": "Required. The cluster name.", "location": "path", "required": true, "type": "string"}, "projectId": {"description": "Required. The ID of the Google Cloud Platform project the cluster belongs to.", "location": "path", "required": true, "type": "string"}, "region": {"description": "Required. The Dataproc region in which to handle the request.", "location": "path", "required": true, "type": "string"}}, "path": "v1/projects/{projectId}/regions/{region}/clusters/{clusterName}:start", "request": {"$ref": "StartClusterRequest"}, "response": {"$ref": "Operation"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform"]}, "stop": {"description": "Stops a cluster in a project.", "flatPath": "v1/projects/{projectId}/regions/{region}/clusters/{clusterName}:stop", "httpMethod": "POST", "id": "dataproc.projects.regions.clusters.stop", "parameterOrder": ["projectId", "region", "clusterName"], "parameters": {"clusterName": {"description": "Required. The cluster name.", "location": "path", "required": true, "type": "string"}, "projectId": {"description": "Required. The ID of the Google Cloud Platform project the cluster belongs to.", "location": "path", "required": true, "type": "string"}, "region": {"description": "Required. The Dataproc region in which to handle the request.", "location": "path", "required": true, "type": "string"}}, "path": "v1/projects/{projectId}/regions/{region}/clusters/{clusterName}:stop", "request": {"$ref": "StopClusterRequest"}, "response": {"$ref": "Operation"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform"]}, "testIamPermissions": {"description": "Returns permissions that a caller has on the specified resource. If the resource does not exist, this will return an empty set of permissions, not a NOT_FOUND error.Note: This operation is designed to be used for building permission-aware UIs and command-line tools, not for authorization checking. This operation may \"fail open\" without warning.", "flatPath": "v1/projects/{projectsId}/regions/{regionsId}/clusters/{clustersId}:testIamPermissions", "httpMethod": "POST", "id": "dataproc.projects.regions.clusters.testIamPermissions", "parameterOrder": ["resource"], "parameters": {"resource": {"description": "REQUIRED: The resource for which the policy detail is being requested. See Resource names (https://cloud.google.com/apis/design/resource_names) for the appropriate value for this field.", "location": "path", "pattern": "^projects/[^/]+/regions/[^/]+/clusters/[^/]+$", "required": true, "type": "string"}}, "path": "v1/{+resource}:testIamPermissions", "request": {"$ref": "TestIamPermissionsRequest"}, "response": {"$ref": "TestIamPermissionsResponse"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform"]}}, "resources": {"nodeGroups": {"methods": {"create": {"description": "Creates a node group in a cluster. The returned Operation.metadata is NodeGroupOperationMetadata (https://cloud.google.com/dataproc/docs/reference/rpc/google.cloud.dataproc.v1#nodegroupoperationmetadata).", "flatPath": "v1/projects/{projectsId}/regions/{regionsId}/clusters/{clustersId}/nodeGroups", "httpMethod": "POST", "id": "dataproc.projects.regions.clusters.nodeGroups.create", "parameterOrder": ["parent"], "parameters": {"nodeGroupId": {"description": "Optional. An optional node group ID. Generated if not specified.The ID must contain only letters (a-z, A-Z), numbers (0-9), underscores (_), and hyphens (-). Cannot begin or end with underscore or hyphen. Must consist of from 3 to 33 characters.", "location": "query", "type": "string"}, "parent": {"description": "Required. The parent resource where this node group will be created. Format: projects/{project}/regions/{region}/clusters/{cluster}", "location": "path", "pattern": "^projects/[^/]+/regions/[^/]+/clusters/[^/]+$", "required": true, "type": "string"}, "parentOperationId": {"description": "Optional. operation id of the parent operation sending the create request", "location": "query", "type": "string"}, "requestId": {"description": "Optional. A unique ID used to identify the request. If the server receives two CreateNodeGroupRequest (https://cloud.google.com/dataproc/docs/reference/rpc/google.cloud.dataproc.v1#google.cloud.dataproc.v1.CreateNodeGroupRequest) with the same ID, the second request is ignored and the first google.longrunning.Operation created and stored in the backend is returned.Recommendation: Set this value to a UUID (https://en.wikipedia.org/wiki/Universally_unique_identifier).The ID must contain only letters (a-z, A-Z), numbers (0-9), underscores (_), and hyphens (-). The maximum length is 40 characters.", "location": "query", "type": "string"}}, "path": "v1/{+parent}/nodeGroups", "request": {"$ref": "NodeGroup"}, "response": {"$ref": "Operation"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform"]}, "get": {"description": "Gets the resource representation for a node group in a cluster.", "flatPath": "v1/projects/{projectsId}/regions/{regionsId}/clusters/{clustersId}/nodeGroups/{nodeGroupsId}", "httpMethod": "GET", "id": "dataproc.projects.regions.clusters.nodeGroups.get", "parameterOrder": ["name"], "parameters": {"name": {"description": "Required. The name of the node group to retrieve. Format: projects/{project}/regions/{region}/clusters/{cluster}/nodeGroups/{nodeGroup}", "location": "path", "pattern": "^projects/[^/]+/regions/[^/]+/clusters/[^/]+/nodeGroups/[^/]+$", "required": true, "type": "string"}}, "path": "v1/{+name}", "response": {"$ref": "NodeGroup"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform"]}, "repair": {"description": "Repair nodes in a node group.", "flatPath": "v1/projects/{projectsId}/regions/{regionsId}/clusters/{clustersId}/nodeGroups/{nodeGroupsId}:repair", "httpMethod": "POST", "id": "dataproc.projects.regions.clusters.nodeGroups.repair", "parameterOrder": ["name"], "parameters": {"name": {"description": "Required. The name of the node group to resize. Format: projects/{project}/regions/{region}/clusters/{cluster}/nodeGroups/{nodeGroup}", "location": "path", "pattern": "^projects/[^/]+/regions/[^/]+/clusters/[^/]+/nodeGroups/[^/]+$", "required": true, "type": "string"}}, "path": "v1/{+name}:repair", "request": {"$ref": "RepairNodeGroupRequest"}, "response": {"$ref": "Operation"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform"]}, "resize": {"description": "Resizes a node group in a cluster. The returned Operation.metadata is NodeGroupOperationMetadata (https://cloud.google.com/dataproc/docs/reference/rpc/google.cloud.dataproc.v1#nodegroupoperationmetadata).", "flatPath": "v1/projects/{projectsId}/regions/{regionsId}/clusters/{clustersId}/nodeGroups/{nodeGroupsId}:resize", "httpMethod": "POST", "id": "dataproc.projects.regions.clusters.nodeGroups.resize", "parameterOrder": ["name"], "parameters": {"name": {"description": "Required. The name of the node group to resize. Format: projects/{project}/regions/{region}/clusters/{cluster}/nodeGroups/{nodeGroup}", "location": "path", "pattern": "^projects/[^/]+/regions/[^/]+/clusters/[^/]+/nodeGroups/[^/]+$", "required": true, "type": "string"}}, "path": "v1/{+name}:resize", "request": {"$ref": "ResizeNodeGroupRequest"}, "response": {"$ref": "Operation"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform"]}}}}}, "jobs": {"methods": {"cancel": {"description": "Starts a job cancellation request. To access the job resource after cancellation, call regions/{region}/jobs.list (https://cloud.google.com/dataproc/docs/reference/rest/v1/projects.regions.jobs/list) or regions/{region}/jobs.get (https://cloud.google.com/dataproc/docs/reference/rest/v1/projects.regions.jobs/get).", "flatPath": "v1/projects/{projectId}/regions/{region}/jobs/{jobId}:cancel", "httpMethod": "POST", "id": "dataproc.projects.regions.jobs.cancel", "parameterOrder": ["projectId", "region", "jobId"], "parameters": {"jobId": {"description": "Required. The job ID.", "location": "path", "required": true, "type": "string"}, "projectId": {"description": "Required. The ID of the Google Cloud Platform project that the job belongs to.", "location": "path", "required": true, "type": "string"}, "region": {"description": "Required. The Dataproc region in which to handle the request.", "location": "path", "required": true, "type": "string"}}, "path": "v1/projects/{projectId}/regions/{region}/jobs/{jobId}:cancel", "request": {"$ref": "CancelJobRequest"}, "response": {"$ref": "Job"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform"]}, "delete": {"description": "Deletes the job from the project. If the job is active, the delete fails, and the response returns FAILED_PRECONDITION.", "flatPath": "v1/projects/{projectId}/regions/{region}/jobs/{jobId}", "httpMethod": "DELETE", "id": "dataproc.projects.regions.jobs.delete", "parameterOrder": ["projectId", "region", "jobId"], "parameters": {"jobId": {"description": "Required. The job ID.", "location": "path", "required": true, "type": "string"}, "projectId": {"description": "Required. The ID of the Google Cloud Platform project that the job belongs to.", "location": "path", "required": true, "type": "string"}, "region": {"description": "Required. The Dataproc region in which to handle the request.", "location": "path", "required": true, "type": "string"}}, "path": "v1/projects/{projectId}/regions/{region}/jobs/{jobId}", "response": {"$ref": "Empty"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform"]}, "get": {"description": "Gets the resource representation for a job in a project.", "flatPath": "v1/projects/{projectId}/regions/{region}/jobs/{jobId}", "httpMethod": "GET", "id": "dataproc.projects.regions.jobs.get", "parameterOrder": ["projectId", "region", "jobId"], "parameters": {"jobId": {"description": "Required. The job ID.", "location": "path", "required": true, "type": "string"}, "projectId": {"description": "Required. The ID of the Google Cloud Platform project that the job belongs to.", "location": "path", "required": true, "type": "string"}, "region": {"description": "Required. The Dataproc region in which to handle the request.", "location": "path", "required": true, "type": "string"}}, "path": "v1/projects/{projectId}/regions/{region}/jobs/{jobId}", "response": {"$ref": "Job"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform"]}, "getIamPolicy": {"description": "Gets the access control policy for a resource. Returns an empty policy if the resource exists and does not have a policy set.", "flatPath": "v1/projects/{projectsId}/regions/{regionsId}/jobs/{jobsId}:getIamPolicy", "httpMethod": "POST", "id": "dataproc.projects.regions.jobs.getIamPolicy", "parameterOrder": ["resource"], "parameters": {"resource": {"description": "REQUIRED: The resource for which the policy is being requested. See Resource names (https://cloud.google.com/apis/design/resource_names) for the appropriate value for this field.", "location": "path", "pattern": "^projects/[^/]+/regions/[^/]+/jobs/[^/]+$", "required": true, "type": "string"}}, "path": "v1/{+resource}:getIamPolicy", "request": {"$ref": "GetIamPolicyRequest"}, "response": {"$ref": "Policy"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform"]}, "list": {"description": "Lists regions/{region}/jobs in a project.", "flatPath": "v1/projects/{projectId}/regions/{region}/jobs", "httpMethod": "GET", "id": "dataproc.projects.regions.jobs.list", "parameterOrder": ["projectId", "region"], "parameters": {"clusterName": {"description": "Optional. If set, the returned jobs list includes only jobs that were submitted to the named cluster.", "location": "query", "type": "string"}, "filter": {"description": "Optional. A filter constraining the jobs to list. Filters are case-sensitive and have the following syntax:field = value AND field = value ...where field is status.state or labels.[KEY], and [KEY] is a label key. value can be * to match all values. status.state can be either ACTIVE or NON_ACTIVE. Only the logical AND operator is supported; space-separated items are treated as having an implicit AND operator.Example filter:status.state = ACTIVE AND labels.env = staging AND labels.starred = *", "location": "query", "type": "string"}, "jobStateMatcher": {"description": "Optional. Specifies enumerated categories of jobs to list. (default = match ALL jobs).If filter is provided, jobStateMatcher will be ignored.", "enum": ["ALL", "ACTIVE", "NON_ACTIVE"], "enumDescriptions": ["Match all jobs, regardless of state.", "Only match jobs in non-terminal states: PENDING, RUNNING, or CANCEL_PENDING.", "Only match jobs in terminal states: CANCELLED, DONE, or ERROR."], "location": "query", "type": "string"}, "pageSize": {"description": "Optional. The number of results to return in each response.", "format": "int32", "location": "query", "type": "integer"}, "pageToken": {"description": "Optional. The page token, returned by a previous call, to request the next page of results.", "location": "query", "type": "string"}, "projectId": {"description": "Required. The ID of the Google Cloud Platform project that the job belongs to.", "location": "path", "required": true, "type": "string"}, "region": {"description": "Required. The Dataproc region in which to handle the request.", "location": "path", "required": true, "type": "string"}}, "path": "v1/projects/{projectId}/regions/{region}/jobs", "response": {"$ref": "ListJobsResponse"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform"]}, "patch": {"description": "Updates a job in a project.", "flatPath": "v1/projects/{projectId}/regions/{region}/jobs/{jobId}", "httpMethod": "PATCH", "id": "dataproc.projects.regions.jobs.patch", "parameterOrder": ["projectId", "region", "jobId"], "parameters": {"jobId": {"description": "Required. The job ID.", "location": "path", "required": true, "type": "string"}, "projectId": {"description": "Required. The ID of the Google Cloud Platform project that the job belongs to.", "location": "path", "required": true, "type": "string"}, "region": {"description": "Required. The Dataproc region in which to handle the request.", "location": "path", "required": true, "type": "string"}, "updateMask": {"description": "Required. Specifies the path, relative to <PERSON>, of the field to update. For example, to update the labels of a Job the update_mask parameter would be specified as labels, and the PATCH request body would specify the new value. *Note:* Currently, labels is the only field that can be updated.", "format": "google-fieldmask", "location": "query", "type": "string"}}, "path": "v1/projects/{projectId}/regions/{region}/jobs/{jobId}", "request": {"$ref": "Job"}, "response": {"$ref": "Job"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform"]}, "setIamPolicy": {"description": "Sets the access control policy on the specified resource. Replaces any existing policy.Can return NOT_FOUND, INVALID_ARGUMENT, and PERMISSION_DENIED errors.", "flatPath": "v1/projects/{projectsId}/regions/{regionsId}/jobs/{jobsId}:setIamPolicy", "httpMethod": "POST", "id": "dataproc.projects.regions.jobs.setIamPolicy", "parameterOrder": ["resource"], "parameters": {"resource": {"description": "REQUIRED: The resource for which the policy is being specified. See Resource names (https://cloud.google.com/apis/design/resource_names) for the appropriate value for this field.", "location": "path", "pattern": "^projects/[^/]+/regions/[^/]+/jobs/[^/]+$", "required": true, "type": "string"}}, "path": "v1/{+resource}:setIamPolicy", "request": {"$ref": "SetIamPolicyRequest"}, "response": {"$ref": "Policy"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform"]}, "submit": {"description": "Submits a job to a cluster.", "flatPath": "v1/projects/{projectId}/regions/{region}/jobs:submit", "httpMethod": "POST", "id": "dataproc.projects.regions.jobs.submit", "parameterOrder": ["projectId", "region"], "parameters": {"projectId": {"description": "Required. The ID of the Google Cloud Platform project that the job belongs to.", "location": "path", "required": true, "type": "string"}, "region": {"description": "Required. The Dataproc region in which to handle the request.", "location": "path", "required": true, "type": "string"}}, "path": "v1/projects/{projectId}/regions/{region}/jobs:submit", "request": {"$ref": "SubmitJobRequest"}, "response": {"$ref": "Job"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform"]}, "submitAsOperation": {"description": "Submits job to a cluster.", "flatPath": "v1/projects/{projectId}/regions/{region}/jobs:submitAsOperation", "httpMethod": "POST", "id": "dataproc.projects.regions.jobs.submitAsOperation", "parameterOrder": ["projectId", "region"], "parameters": {"projectId": {"description": "Required. The ID of the Google Cloud Platform project that the job belongs to.", "location": "path", "required": true, "type": "string"}, "region": {"description": "Required. The Dataproc region in which to handle the request.", "location": "path", "required": true, "type": "string"}}, "path": "v1/projects/{projectId}/regions/{region}/jobs:submitAsOperation", "request": {"$ref": "SubmitJobRequest"}, "response": {"$ref": "Operation"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform"]}, "testIamPermissions": {"description": "Returns permissions that a caller has on the specified resource. If the resource does not exist, this will return an empty set of permissions, not a NOT_FOUND error.Note: This operation is designed to be used for building permission-aware UIs and command-line tools, not for authorization checking. This operation may \"fail open\" without warning.", "flatPath": "v1/projects/{projectsId}/regions/{regionsId}/jobs/{jobsId}:testIamPermissions", "httpMethod": "POST", "id": "dataproc.projects.regions.jobs.testIamPermissions", "parameterOrder": ["resource"], "parameters": {"resource": {"description": "REQUIRED: The resource for which the policy detail is being requested. See Resource names (https://cloud.google.com/apis/design/resource_names) for the appropriate value for this field.", "location": "path", "pattern": "^projects/[^/]+/regions/[^/]+/jobs/[^/]+$", "required": true, "type": "string"}}, "path": "v1/{+resource}:testIamPermissions", "request": {"$ref": "TestIamPermissionsRequest"}, "response": {"$ref": "TestIamPermissionsResponse"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform"]}}}, "operations": {"methods": {"cancel": {"description": "Starts asynchronous cancellation on a long-running operation. The server makes a best effort to cancel the operation, but success is not guaranteed. If the server doesn't support this method, it returns google.rpc.Code.UNIMPLEMENTED. Clients can use Operations.GetOperation or other methods to check whether the cancellation succeeded or whether the operation completed despite cancellation. On successful cancellation, the operation is not deleted; instead, it becomes an operation with an Operation.error value with a google.rpc.Status.code of 1, corresponding to Code.CANCELLED.", "flatPath": "v1/projects/{projectsId}/regions/{regionsId}/operations/{operationsId}:cancel", "httpMethod": "POST", "id": "dataproc.projects.regions.operations.cancel", "parameterOrder": ["name"], "parameters": {"name": {"description": "The name of the operation resource to be cancelled.", "location": "path", "pattern": "^projects/[^/]+/regions/[^/]+/operations/[^/]+$", "required": true, "type": "string"}}, "path": "v1/{+name}:cancel", "response": {"$ref": "Empty"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform"]}, "delete": {"description": "Deletes a long-running operation. This method indicates that the client is no longer interested in the operation result. It does not cancel the operation. If the server doesn't support this method, it returns google.rpc.Code.UNIMPLEMENTED.", "flatPath": "v1/projects/{projectsId}/regions/{regionsId}/operations/{operationsId}", "httpMethod": "DELETE", "id": "dataproc.projects.regions.operations.delete", "parameterOrder": ["name"], "parameters": {"name": {"description": "The name of the operation resource to be deleted.", "location": "path", "pattern": "^projects/[^/]+/regions/[^/]+/operations/[^/]+$", "required": true, "type": "string"}}, "path": "v1/{+name}", "response": {"$ref": "Empty"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform"]}, "get": {"description": "Gets the latest state of a long-running operation. Clients can use this method to poll the operation result at intervals as recommended by the API service.", "flatPath": "v1/projects/{projectsId}/regions/{regionsId}/operations/{operationsId}", "httpMethod": "GET", "id": "dataproc.projects.regions.operations.get", "parameterOrder": ["name"], "parameters": {"name": {"description": "The name of the operation resource.", "location": "path", "pattern": "^projects/[^/]+/regions/[^/]+/operations/[^/]+$", "required": true, "type": "string"}}, "path": "v1/{+name}", "response": {"$ref": "Operation"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform"]}, "getIamPolicy": {"description": "Gets the access control policy for a resource. Returns an empty policy if the resource exists and does not have a policy set.", "flatPath": "v1/projects/{projectsId}/regions/{regionsId}/operations/{operationsId}:getIamPolicy", "httpMethod": "POST", "id": "dataproc.projects.regions.operations.getIamPolicy", "parameterOrder": ["resource"], "parameters": {"resource": {"description": "REQUIRED: The resource for which the policy is being requested. See Resource names (https://cloud.google.com/apis/design/resource_names) for the appropriate value for this field.", "location": "path", "pattern": "^projects/[^/]+/regions/[^/]+/operations/[^/]+$", "required": true, "type": "string"}}, "path": "v1/{+resource}:getIamPolicy", "request": {"$ref": "GetIamPolicyRequest"}, "response": {"$ref": "Policy"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform"]}, "list": {"description": "Lists operations that match the specified filter in the request. If the server doesn't support this method, it returns UNIMPLEMENTED.", "flatPath": "v1/projects/{projectsId}/regions/{regionsId}/operations", "httpMethod": "GET", "id": "dataproc.projects.regions.operations.list", "parameterOrder": ["name"], "parameters": {"filter": {"description": "The standard list filter.", "location": "query", "type": "string"}, "name": {"description": "The name of the operation's parent resource.", "location": "path", "pattern": "^projects/[^/]+/regions/[^/]+/operations$", "required": true, "type": "string"}, "pageSize": {"description": "The standard list page size.", "format": "int32", "location": "query", "type": "integer"}, "pageToken": {"description": "The standard list page token.", "location": "query", "type": "string"}}, "path": "v1/{+name}", "response": {"$ref": "ListOperationsResponse"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform"]}, "setIamPolicy": {"description": "Sets the access control policy on the specified resource. Replaces any existing policy.Can return NOT_FOUND, INVALID_ARGUMENT, and PERMISSION_DENIED errors.", "flatPath": "v1/projects/{projectsId}/regions/{regionsId}/operations/{operationsId}:setIamPolicy", "httpMethod": "POST", "id": "dataproc.projects.regions.operations.setIamPolicy", "parameterOrder": ["resource"], "parameters": {"resource": {"description": "REQUIRED: The resource for which the policy is being specified. See Resource names (https://cloud.google.com/apis/design/resource_names) for the appropriate value for this field.", "location": "path", "pattern": "^projects/[^/]+/regions/[^/]+/operations/[^/]+$", "required": true, "type": "string"}}, "path": "v1/{+resource}:setIamPolicy", "request": {"$ref": "SetIamPolicyRequest"}, "response": {"$ref": "Policy"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform"]}, "testIamPermissions": {"description": "Returns permissions that a caller has on the specified resource. If the resource does not exist, this will return an empty set of permissions, not a NOT_FOUND error.Note: This operation is designed to be used for building permission-aware UIs and command-line tools, not for authorization checking. This operation may \"fail open\" without warning.", "flatPath": "v1/projects/{projectsId}/regions/{regionsId}/operations/{operationsId}:testIamPermissions", "httpMethod": "POST", "id": "dataproc.projects.regions.operations.testIamPermissions", "parameterOrder": ["resource"], "parameters": {"resource": {"description": "REQUIRED: The resource for which the policy detail is being requested. See Resource names (https://cloud.google.com/apis/design/resource_names) for the appropriate value for this field.", "location": "path", "pattern": "^projects/[^/]+/regions/[^/]+/operations/[^/]+$", "required": true, "type": "string"}}, "path": "v1/{+resource}:testIamPermissions", "request": {"$ref": "TestIamPermissionsRequest"}, "response": {"$ref": "TestIamPermissionsResponse"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform"]}}}, "workflowTemplates": {"methods": {"create": {"description": "Creates new workflow template.", "flatPath": "v1/projects/{projectsId}/regions/{regionsId}/workflowTemplates", "httpMethod": "POST", "id": "dataproc.projects.regions.workflowTemplates.create", "parameterOrder": ["parent"], "parameters": {"parent": {"description": "Required. The resource name of the region or location, as described in https://cloud.google.com/apis/design/resource_names. For projects.regions.workflowTemplates.create, the resource name of the region has the following format: projects/{project_id}/regions/{region} For projects.locations.workflowTemplates.create, the resource name of the location has the following format: projects/{project_id}/locations/{location}", "location": "path", "pattern": "^projects/[^/]+/regions/[^/]+$", "required": true, "type": "string"}}, "path": "v1/{+parent}/workflowTemplates", "request": {"$ref": "WorkflowTemplate"}, "response": {"$ref": "WorkflowTemplate"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform"]}, "delete": {"description": "Deletes a workflow template. It does not cancel in-progress workflows.", "flatPath": "v1/projects/{projectsId}/regions/{regionsId}/workflowTemplates/{workflowTemplatesId}", "httpMethod": "DELETE", "id": "dataproc.projects.regions.workflowTemplates.delete", "parameterOrder": ["name"], "parameters": {"name": {"description": "Required. The resource name of the workflow template, as described in https://cloud.google.com/apis/design/resource_names. For projects.regions.workflowTemplates.delete, the resource name of the template has the following format: projects/{project_id}/regions/{region}/workflowTemplates/{template_id} For projects.locations.workflowTemplates.instantiate, the resource name of the template has the following format: projects/{project_id}/locations/{location}/workflowTemplates/{template_id}", "location": "path", "pattern": "^projects/[^/]+/regions/[^/]+/workflowTemplates/[^/]+$", "required": true, "type": "string"}, "version": {"description": "Optional. The version of workflow template to delete. If specified, will only delete the template if the current server version matches specified version.", "format": "int32", "location": "query", "type": "integer"}}, "path": "v1/{+name}", "response": {"$ref": "Empty"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform"]}, "get": {"description": "Retrieves the latest workflow template.Can retrieve previously instantiated template by specifying optional version parameter.", "flatPath": "v1/projects/{projectsId}/regions/{regionsId}/workflowTemplates/{workflowTemplatesId}", "httpMethod": "GET", "id": "dataproc.projects.regions.workflowTemplates.get", "parameterOrder": ["name"], "parameters": {"name": {"description": "Required. The resource name of the workflow template, as described in https://cloud.google.com/apis/design/resource_names. For projects.regions.workflowTemplates.get, the resource name of the template has the following format: projects/{project_id}/regions/{region}/workflowTemplates/{template_id} For projects.locations.workflowTemplates.get, the resource name of the template has the following format: projects/{project_id}/locations/{location}/workflowTemplates/{template_id}", "location": "path", "pattern": "^projects/[^/]+/regions/[^/]+/workflowTemplates/[^/]+$", "required": true, "type": "string"}, "version": {"description": "Optional. The version of workflow template to retrieve. Only previously instantiated versions can be retrieved.If unspecified, retrieves the current version.", "format": "int32", "location": "query", "type": "integer"}}, "path": "v1/{+name}", "response": {"$ref": "WorkflowTemplate"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform"]}, "getIamPolicy": {"description": "Gets the access control policy for a resource. Returns an empty policy if the resource exists and does not have a policy set.", "flatPath": "v1/projects/{projectsId}/regions/{regionsId}/workflowTemplates/{workflowTemplatesId}:getIamPolicy", "httpMethod": "POST", "id": "dataproc.projects.regions.workflowTemplates.getIamPolicy", "parameterOrder": ["resource"], "parameters": {"resource": {"description": "REQUIRED: The resource for which the policy is being requested. See Resource names (https://cloud.google.com/apis/design/resource_names) for the appropriate value for this field.", "location": "path", "pattern": "^projects/[^/]+/regions/[^/]+/workflowTemplates/[^/]+$", "required": true, "type": "string"}}, "path": "v1/{+resource}:getIamPolicy", "request": {"$ref": "GetIamPolicyRequest"}, "response": {"$ref": "Policy"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform"]}, "instantiate": {"description": "Instantiates a template and begins execution.The returned Operation can be used to track execution of workflow by polling operations.get. The Operation will complete when entire workflow is finished.The running workflow can be aborted via operations.cancel. This will cause any inflight jobs to be cancelled and workflow-owned clusters to be deleted.The Operation.metadata will be WorkflowMetadata (https://cloud.google.com/dataproc/docs/reference/rpc/google.cloud.dataproc.v1#workflowmetadata). Also see Using WorkflowMetadata (https://cloud.google.com/dataproc/docs/concepts/workflows/debugging#using_workflowmetadata).On successful completion, Operation.response will be Empty.", "flatPath": "v1/projects/{projectsId}/regions/{regionsId}/workflowTemplates/{workflowTemplatesId}:instantiate", "httpMethod": "POST", "id": "dataproc.projects.regions.workflowTemplates.instantiate", "parameterOrder": ["name"], "parameters": {"name": {"description": "Required. The resource name of the workflow template, as described in https://cloud.google.com/apis/design/resource_names. For projects.regions.workflowTemplates.instantiate, the resource name of the template has the following format: projects/{project_id}/regions/{region}/workflowTemplates/{template_id} For projects.locations.workflowTemplates.instantiate, the resource name of the template has the following format: projects/{project_id}/locations/{location}/workflowTemplates/{template_id}", "location": "path", "pattern": "^projects/[^/]+/regions/[^/]+/workflowTemplates/[^/]+$", "required": true, "type": "string"}}, "path": "v1/{+name}:instantiate", "request": {"$ref": "InstantiateWorkflowTemplateRequest"}, "response": {"$ref": "Operation"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform"]}, "instantiateInline": {"description": "Instantiates a template and begins execution.This method is equivalent to executing the sequence CreateWorkflowTemplate, InstantiateWorkflowTemplate, DeleteWorkflowTemplate.The returned Operation can be used to track execution of workflow by polling operations.get. The Operation will complete when entire workflow is finished.The running workflow can be aborted via operations.cancel. This will cause any inflight jobs to be cancelled and workflow-owned clusters to be deleted.The Operation.metadata will be WorkflowMetadata (https://cloud.google.com/dataproc/docs/reference/rpc/google.cloud.dataproc.v1#workflowmetadata). Also see Using WorkflowMetadata (https://cloud.google.com/dataproc/docs/concepts/workflows/debugging#using_workflowmetadata).On successful completion, Operation.response will be Empty.", "flatPath": "v1/projects/{projectsId}/regions/{regionsId}/workflowTemplates:instantiateInline", "httpMethod": "POST", "id": "dataproc.projects.regions.workflowTemplates.instantiateInline", "parameterOrder": ["parent"], "parameters": {"parent": {"description": "Required. The resource name of the region or location, as described in https://cloud.google.com/apis/design/resource_names. For projects.regions.workflowTemplates,instantiateinline, the resource name of the region has the following format: projects/{project_id}/regions/{region} For projects.locations.workflowTemplates.instantiateinline, the resource name of the location has the following format: projects/{project_id}/locations/{location}", "location": "path", "pattern": "^projects/[^/]+/regions/[^/]+$", "required": true, "type": "string"}, "requestId": {"description": "Optional. A tag that prevents multiple concurrent workflow instances with the same tag from running. This mitigates risk of concurrent instances started due to retries.It is recommended to always set this value to a UUID (https://en.wikipedia.org/wiki/Universally_unique_identifier).The tag must contain only letters (a-z, A-Z), numbers (0-9), underscores (_), and hyphens (-). The maximum length is 40 characters.", "location": "query", "type": "string"}}, "path": "v1/{+parent}/workflowTemplates:instantiateInline", "request": {"$ref": "WorkflowTemplate"}, "response": {"$ref": "Operation"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform"]}, "list": {"description": "Lists workflows that match the specified filter in the request.", "flatPath": "v1/projects/{projectsId}/regions/{regionsId}/workflowTemplates", "httpMethod": "GET", "id": "dataproc.projects.regions.workflowTemplates.list", "parameterOrder": ["parent"], "parameters": {"pageSize": {"description": "Optional. The maximum number of results to return in each response.", "format": "int32", "location": "query", "type": "integer"}, "pageToken": {"description": "Optional. The page token, returned by a previous call, to request the next page of results.", "location": "query", "type": "string"}, "parent": {"description": "Required. The resource name of the region or location, as described in https://cloud.google.com/apis/design/resource_names. For projects.regions.workflowTemplates,list, the resource name of the region has the following format: projects/{project_id}/regions/{region} For projects.locations.workflowTemplates.list, the resource name of the location has the following format: projects/{project_id}/locations/{location}", "location": "path", "pattern": "^projects/[^/]+/regions/[^/]+$", "required": true, "type": "string"}}, "path": "v1/{+parent}/workflowTemplates", "response": {"$ref": "ListWorkflowTemplatesResponse"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform"]}, "setIamPolicy": {"description": "Sets the access control policy on the specified resource. Replaces any existing policy.Can return NOT_FOUND, INVALID_ARGUMENT, and PERMISSION_DENIED errors.", "flatPath": "v1/projects/{projectsId}/regions/{regionsId}/workflowTemplates/{workflowTemplatesId}:setIamPolicy", "httpMethod": "POST", "id": "dataproc.projects.regions.workflowTemplates.setIamPolicy", "parameterOrder": ["resource"], "parameters": {"resource": {"description": "REQUIRED: The resource for which the policy is being specified. See Resource names (https://cloud.google.com/apis/design/resource_names) for the appropriate value for this field.", "location": "path", "pattern": "^projects/[^/]+/regions/[^/]+/workflowTemplates/[^/]+$", "required": true, "type": "string"}}, "path": "v1/{+resource}:setIamPolicy", "request": {"$ref": "SetIamPolicyRequest"}, "response": {"$ref": "Policy"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform"]}, "testIamPermissions": {"description": "Returns permissions that a caller has on the specified resource. If the resource does not exist, this will return an empty set of permissions, not a NOT_FOUND error.Note: This operation is designed to be used for building permission-aware UIs and command-line tools, not for authorization checking. This operation may \"fail open\" without warning.", "flatPath": "v1/projects/{projectsId}/regions/{regionsId}/workflowTemplates/{workflowTemplatesId}:testIamPermissions", "httpMethod": "POST", "id": "dataproc.projects.regions.workflowTemplates.testIamPermissions", "parameterOrder": ["resource"], "parameters": {"resource": {"description": "REQUIRED: The resource for which the policy detail is being requested. See Resource names (https://cloud.google.com/apis/design/resource_names) for the appropriate value for this field.", "location": "path", "pattern": "^projects/[^/]+/regions/[^/]+/workflowTemplates/[^/]+$", "required": true, "type": "string"}}, "path": "v1/{+resource}:testIamPermissions", "request": {"$ref": "TestIamPermissionsRequest"}, "response": {"$ref": "TestIamPermissionsResponse"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform"]}, "update": {"description": "Updates (replaces) workflow template. The updated template must contain version that matches the current server version.", "flatPath": "v1/projects/{projectsId}/regions/{regionsId}/workflowTemplates/{workflowTemplatesId}", "httpMethod": "PUT", "id": "dataproc.projects.regions.workflowTemplates.update", "parameterOrder": ["name"], "parameters": {"name": {"description": "Output only. The resource name of the workflow template, as described in https://cloud.google.com/apis/design/resource_names. For projects.regions.workflowTemplates, the resource name of the template has the following format: projects/{project_id}/regions/{region}/workflowTemplates/{template_id} For projects.locations.workflowTemplates, the resource name of the template has the following format: projects/{project_id}/locations/{location}/workflowTemplates/{template_id}", "location": "path", "pattern": "^projects/[^/]+/regions/[^/]+/workflowTemplates/[^/]+$", "required": true, "type": "string"}}, "path": "v1/{+name}", "request": {"$ref": "WorkflowTemplate"}, "response": {"$ref": "WorkflowTemplate"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform"]}}}}}}}}, "revision": "20250624", "rootUrl": "https://dataproc.googleapis.com/", "schemas": {"AcceleratorConfig": {"description": "Specifies the type and number of accelerator cards attached to the instances of an instance. See GPUs on Compute Engine (https://cloud.google.com/compute/docs/gpus/).", "id": "AcceleratorConfig", "properties": {"acceleratorCount": {"description": "The number of the accelerator cards of this type exposed to this instance.", "format": "int32", "type": "integer"}, "acceleratorTypeUri": {"description": "Full URL, partial URI, or short name of the accelerator type resource to expose to this instance. See Compute Engine AcceleratorTypes (https://cloud.google.com/compute/docs/reference/v1/acceleratorTypes).Examples: https://www.googleapis.com/compute/v1/projects/[project_id]/zones/[zone]/acceleratorTypes/nvidia-tesla-t4 projects/[project_id]/zones/[zone]/acceleratorTypes/nvidia-tesla-t4 nvidia-tesla-t4Auto Zone Exception: If you are using the Dataproc Auto Zone Placement (https://cloud.google.com/dataproc/docs/concepts/configuring-clusters/auto-zone#using_auto_zone_placement) feature, you must use the short name of the accelerator type resource, for example, nvidia-tesla-t4.", "type": "string"}}, "type": "object"}, "AccessSessionSparkApplicationEnvironmentInfoResponse": {"description": "Environment details of a Saprk Application.", "id": "AccessSessionSparkApplicationEnvironmentInfoResponse", "properties": {"applicationEnvironmentInfo": {"$ref": "ApplicationEnvironmentInfo", "description": "Details about the Environment that the application is running in."}}, "type": "object"}, "AccessSessionSparkApplicationJobResponse": {"description": "Details of a particular job associated with Spark Application", "id": "AccessSessionSparkApplicationJobResponse", "properties": {"jobData": {"$ref": "JobData", "description": "Output only. Data corresponding to a spark job.", "readOnly": true}}, "type": "object"}, "AccessSessionSparkApplicationResponse": {"description": "A summary of Spark Application", "id": "AccessSessionSparkApplicationResponse", "properties": {"application": {"$ref": "ApplicationInfo", "description": "Output only. High level information corresponding to an application.", "readOnly": true}}, "type": "object"}, "AccessSessionSparkApplicationSqlQueryResponse": {"description": "Details of a query for a Spark Application", "id": "AccessSessionSparkApplicationSqlQueryResponse", "properties": {"executionData": {"$ref": "SqlExecutionUiData", "description": "SQL Execution Data"}}, "type": "object"}, "AccessSessionSparkApplicationSqlSparkPlanGraphResponse": {"description": "SparkPlanGraph for a Spark Application execution limited to maximum 10000 clusters.", "id": "AccessSessionSparkApplicationSqlSparkPlanGraphResponse", "properties": {"sparkPlanGraph": {"$ref": "SparkPlanGraph", "description": "SparkPlanGraph for a Spark Application execution."}}, "type": "object"}, "AccessSessionSparkApplicationStageAttemptResponse": {"description": "Stage Attempt for a Stage of a Spark Application", "id": "AccessSessionSparkApplicationStageAttemptResponse", "properties": {"stageData": {"$ref": "StageData", "description": "Output only. Data corresponding to a stage.", "readOnly": true}}, "type": "object"}, "AccessSessionSparkApplicationStageRddOperationGraphResponse": {"description": "RDD operation graph for a Spark Application Stage limited to maximum 10000 clusters.", "id": "AccessSessionSparkApplicationStageRddOperationGraphResponse", "properties": {"rddOperationGraph": {"$ref": "RddOperationGraph", "description": "RDD operation graph for a Spark Application Stage."}}, "type": "object"}, "AccessSparkApplicationEnvironmentInfoResponse": {"description": "Environment details of a Saprk Application.", "id": "AccessSparkApplicationEnvironmentInfoResponse", "properties": {"applicationEnvironmentInfo": {"$ref": "ApplicationEnvironmentInfo", "description": "Details about the Environment that the application is running in."}}, "type": "object"}, "AccessSparkApplicationJobResponse": {"description": "Details of a particular job associated with Spark Application", "id": "AccessSparkApplicationJobResponse", "properties": {"jobData": {"$ref": "JobData", "description": "Output only. Data corresponding to a spark job.", "readOnly": true}}, "type": "object"}, "AccessSparkApplicationResponse": {"description": "A summary of Spark Application", "id": "AccessSparkApplicationResponse", "properties": {"application": {"$ref": "ApplicationInfo", "description": "Output only. High level information corresponding to an application.", "readOnly": true}}, "type": "object"}, "AccessSparkApplicationSqlQueryResponse": {"description": "Details of a query for a Spark Application", "id": "AccessSparkApplicationSqlQueryResponse", "properties": {"executionData": {"$ref": "SqlExecutionUiData", "description": "SQL Execution Data"}}, "type": "object"}, "AccessSparkApplicationSqlSparkPlanGraphResponse": {"description": "SparkPlanGraph for a Spark Application execution limited to maximum 10000 clusters.", "id": "AccessSparkApplicationSqlSparkPlanGraphResponse", "properties": {"sparkPlanGraph": {"$ref": "SparkPlanGraph", "description": "SparkPlanGraph for a Spark Application execution."}}, "type": "object"}, "AccessSparkApplicationStageAttemptResponse": {"description": "Stage Attempt for a Stage of a Spark Application", "id": "AccessSparkApplicationStageAttemptResponse", "properties": {"stageData": {"$ref": "StageData", "description": "Output only. Data corresponding to a stage.", "readOnly": true}}, "type": "object"}, "AccessSparkApplicationStageRddOperationGraphResponse": {"description": "RDD operation graph for a Spark Application Stage limited to maximum 10000 clusters.", "id": "AccessSparkApplicationStageRddOperationGraphResponse", "properties": {"rddOperationGraph": {"$ref": "RddOperationGraph", "description": "RDD operation graph for a Spark Application Stage."}}, "type": "object"}, "AccumulableInfo": {"id": "AccumulableInfo", "properties": {"accumullableInfoId": {"format": "int64", "type": "string"}, "name": {"type": "string"}, "update": {"type": "string"}, "value": {"type": "string"}}, "type": "object"}, "AnalyzeBatchRequest": {"description": "A request to analyze a batch workload.", "id": "AnalyzeBatchRequest", "properties": {"requestId": {"description": "Optional. A unique ID used to identify the request. If the service receives two AnalyzeBatchRequest (http://cloud/dataproc/docs/reference/rpc/google.cloud.dataproc.v1#google.cloud.dataproc.v1.AnalyzeBatchRequest)s with the same request_id, the second request is ignored and the Operation that corresponds to the first request created and stored in the backend is returned.Recommendation: Set this value to a UUID (https://en.wikipedia.org/wiki/Universally_unique_identifier).The value must contain only letters (a-z, A-Z), numbers (0-9), underscores (_), and hyphens (-). The maximum length is 40 characters.", "type": "string"}}, "type": "object"}, "AnalyzeOperationMetadata": {"description": "Metadata describing the Analyze operation.", "id": "AnalyzeOperationMetadata", "properties": {"analyzedWorkloadName": {"description": "Output only. name of the workload being analyzed.", "readOnly": true, "type": "string"}, "analyzedWorkloadType": {"description": "Output only. Type of the workload being analyzed.", "enum": ["WORKLOAD_TYPE_UNSPECIFIED", "BATCH"], "enumDescriptions": ["Undefined option", "Serverless batch job"], "readOnly": true, "type": "string"}, "analyzedWorkloadUuid": {"description": "Output only. unique identifier of the workload typically generated by control plane. E.g. batch uuid.", "readOnly": true, "type": "string"}, "createTime": {"description": "Output only. The time when the operation was created.", "format": "google-datetime", "readOnly": true, "type": "string"}, "description": {"description": "Output only. Short description of the operation.", "readOnly": true, "type": "string"}, "doneTime": {"description": "Output only. The time when the operation finished.", "format": "google-datetime", "readOnly": true, "type": "string"}, "labels": {"additionalProperties": {"type": "string"}, "description": "Output only. Labels associated with the operation.", "readOnly": true, "type": "object"}, "warnings": {"description": "Output only. Warnings encountered during operation execution.", "items": {"type": "string"}, "readOnly": true, "type": "array"}}, "type": "object"}, "AppSummary": {"id": "AppSummary", "properties": {"numCompletedJobs": {"format": "int32", "type": "integer"}, "numCompletedStages": {"format": "int32", "type": "integer"}}, "type": "object"}, "ApplicationAttemptInfo": {"description": "Specific attempt of an application.", "id": "ApplicationAttemptInfo", "properties": {"appSparkVersion": {"type": "string"}, "attemptId": {"type": "string"}, "completed": {"type": "boolean"}, "durationMillis": {"format": "int64", "type": "string"}, "endTime": {"format": "google-datetime", "type": "string"}, "lastUpdated": {"format": "google-datetime", "type": "string"}, "sparkUser": {"type": "string"}, "startTime": {"format": "google-datetime", "type": "string"}}, "type": "object"}, "ApplicationEnvironmentInfo": {"description": "Details about the Environment that the application is running in.", "id": "ApplicationEnvironmentInfo", "properties": {"classpathEntries": {"additionalProperties": {"type": "string"}, "type": "object"}, "hadoopProperties": {"additionalProperties": {"type": "string"}, "type": "object"}, "metricsProperties": {"additionalProperties": {"type": "string"}, "type": "object"}, "resourceProfiles": {"items": {"$ref": "ResourceProfileInfo"}, "type": "array"}, "runtime": {"$ref": "SparkRuntimeInfo"}, "sparkProperties": {"additionalProperties": {"type": "string"}, "type": "object"}, "systemProperties": {"additionalProperties": {"type": "string"}, "type": "object"}}, "type": "object"}, "ApplicationInfo": {"description": "High level information corresponding to an application.", "id": "ApplicationInfo", "properties": {"applicationContextIngestionStatus": {"enum": ["APPLICATION_CONTEXT_INGESTION_STATUS_UNSPECIFIED", "APPLICATION_CONTEXT_INGESTION_STATUS_COMPLETED"], "enumDescriptions": ["", ""], "type": "string"}, "applicationId": {"type": "string"}, "attempts": {"items": {"$ref": "ApplicationAttemptInfo"}, "type": "array"}, "coresGranted": {"format": "int32", "type": "integer"}, "coresPerExecutor": {"format": "int32", "type": "integer"}, "maxCores": {"format": "int32", "type": "integer"}, "memoryPerExecutorMb": {"format": "int32", "type": "integer"}, "name": {"type": "string"}, "quantileDataStatus": {"enum": ["QUANTILE_DATA_STATUS_UNSPECIFIED", "QUANTILE_DATA_STATUS_COMPLETED", "QUANTILE_DATA_STATUS_FAILED"], "enumDescriptions": ["", "", ""], "type": "string"}}, "type": "object"}, "AuthenticationConfig": {"description": "Authentication configuration for a workload is used to set the default identity for the workload execution. The config specifies the type of identity (service account or user) that will be used by workloads to access resources on the project(s).", "id": "AuthenticationConfig", "properties": {"userWorkloadAuthenticationType": {"description": "Optional. Authentication type for the user workload running in containers.", "enum": ["AUTHENTICATION_TYPE_UNSPECIFIED", "SERVICE_ACCOUNT", "END_USER_CREDENTIALS"], "enumDescriptions": ["If AuthenticationType is unspecified then END_USER_CREDENTIALS is used for 3.0 and newer runtimes, and SERVICE_ACCOUNT is used for older runtimes.", "Use service account credentials for authenticating to other services.", "Use OAuth credentials associated with the workload creator/user for authenticating to other services."], "type": "string"}}, "type": "object"}, "AutoscalingConfig": {"description": "Autoscaling Policy config associated with the cluster.", "id": "AutoscalingConfig", "properties": {"policyUri": {"description": "Optional. The autoscaling policy used by the cluster.Only resource names including projectid and location (region) are valid. Examples: https://www.googleapis.com/compute/v1/projects/[project_id]/locations/[dataproc_region]/autoscalingPolicies/[policy_id] projects/[project_id]/locations/[dataproc_region]/autoscalingPolicies/[policy_id]Note that the policy must be in the same project and Dataproc region.", "type": "string"}}, "type": "object"}, "AutoscalingPolicy": {"description": "Describes an autoscaling policy for Dataproc cluster autoscaler.", "id": "AutoscalingPolicy", "properties": {"basicAlgorithm": {"$ref": "BasicAutoscalingAlgorithm"}, "clusterType": {"description": "Optional. The type of the clusters for which this autoscaling policy is to be configured.", "enum": ["CLUSTER_TYPE_UNSPECIFIED", "STANDARD", "ZERO_SCALE"], "enumDescriptions": ["Not set.", "Standard dataproc cluster with a minimum of two primary workers.", "Clusters that can use only secondary workers and be scaled down to zero secondary worker nodes."], "type": "string"}, "id": {"description": "Required. The policy id.The id must contain only letters (a-z, A-Z), numbers (0-9), underscores (_), and hyphens (-). Cannot begin or end with underscore or hyphen. Must consist of between 3 and 50 characters.", "type": "string"}, "labels": {"additionalProperties": {"type": "string"}, "description": "Optional. The labels to associate with this autoscaling policy. Label keys must contain 1 to 63 characters, and must conform to RFC 1035 (https://www.ietf.org/rfc/rfc1035.txt). Label values may be empty, but, if present, must contain 1 to 63 characters, and must conform to RFC 1035 (https://www.ietf.org/rfc/rfc1035.txt). No more than 32 labels can be associated with an autoscaling policy.", "type": "object"}, "name": {"description": "Output only. The \"resource name\" of the autoscaling policy, as described in https://cloud.google.com/apis/design/resource_names. For projects.regions.autoscalingPolicies, the resource name of the policy has the following format: projects/{project_id}/regions/{region}/autoscalingPolicies/{policy_id} For projects.locations.autoscalingPolicies, the resource name of the policy has the following format: projects/{project_id}/locations/{location}/autoscalingPolicies/{policy_id}", "readOnly": true, "type": "string"}, "secondaryWorkerConfig": {"$ref": "InstanceGroupAutoscalingPolicyConfig", "description": "Optional. Describes how the autoscaler will operate for secondary workers."}, "workerConfig": {"$ref": "InstanceGroupAutoscalingPolicyConfig", "description": "Required. Describes how the autoscaler will operate for primary workers."}}, "type": "object"}, "AutotuningConfig": {"description": "Autotuning configuration of the workload.", "id": "AutotuningConfig", "properties": {"scenarios": {"description": "Optional. Scenarios for which tunings are applied.", "items": {"enum": ["SCENARIO_UNSPECIFIED", "SCALING", "BROADCAST_HASH_JOIN", "MEMORY", "NONE", "AUTO"], "enumDescriptions": ["Default value.", "Scaling recommendations such as initialExecutors.", "Adding hints for potential relation broadcasts.", "Memory management for workloads.", "No autotuning.", "Automatic selection of scenarios."], "type": "string"}, "type": "array"}}, "type": "object"}, "AuxiliaryNodeGroup": {"description": "Node group identification and configuration information.", "id": "AuxiliaryNodeGroup", "properties": {"nodeGroup": {"$ref": "NodeGroup", "description": "Required. Node group configuration."}, "nodeGroupId": {"description": "Optional. A node group ID. Generated if not specified.The ID must contain only letters (a-z, A-Z), numbers (0-9), underscores (_), and hyphens (-). Cannot begin or end with underscore or hyphen. Must consist of from 3 to 33 characters.", "type": "string"}}, "type": "object"}, "AuxiliaryServicesConfig": {"description": "Auxiliary services configuration for a Cluster.", "id": "AuxiliaryServicesConfig", "properties": {"metastoreConfig": {"$ref": "MetastoreConfig", "description": "Optional. The Hive Metastore configuration for this workload."}, "sparkHistoryServerConfig": {"$ref": "SparkHistoryServerConfig", "description": "Optional. The Spark History Server configuration for the workload."}}, "type": "object"}, "BasicAutoscalingAlgorithm": {"description": "Basic algorithm for autoscaling.", "id": "BasicAutoscalingAlgorithm", "properties": {"cooldownPeriod": {"description": "Optional. Duration between scaling events. A scaling period starts after the update operation from the previous event has completed.Bounds: 2m, 1d. Default: 2m.", "format": "google-duration", "type": "string"}, "sparkStandaloneConfig": {"$ref": "SparkStandaloneAutoscalingConfig", "description": "Optional. Spark Standalone autoscaling configuration"}, "yarnConfig": {"$ref": "BasicYarnAutoscalingConfig", "description": "Optional. YARN autoscaling configuration."}}, "type": "object"}, "BasicYarnAutoscalingConfig": {"description": "Basic autoscaling configurations for YARN.", "id": "BasicYarnAutoscalingConfig", "properties": {"gracefulDecommissionTimeout": {"description": "Required. Timeout for YARN graceful decommissioning of Node Managers. Specifies the duration to wait for jobs to complete before forcefully removing workers (and potentially interrupting jobs). Only applicable to downscaling operations.Bounds: 0s, 1d.", "format": "google-duration", "type": "string"}, "scaleDownFactor": {"description": "Required. Fraction of average YARN pending memory in the last cooldown period for which to remove workers. A scale-down factor of 1 will result in scaling down so that there is no available memory remaining after the update (more aggressive scaling). A scale-down factor of 0 disables removing workers, which can be beneficial for autoscaling a single job. See How autoscaling works (https://cloud.google.com/dataproc/docs/concepts/configuring-clusters/autoscaling#how_autoscaling_works) for more information.Bounds: 0.0, 1.0.", "format": "double", "type": "number"}, "scaleDownMinWorkerFraction": {"description": "Optional. Minimum scale-down threshold as a fraction of total cluster size before scaling occurs. For example, in a 20-worker cluster, a threshold of 0.1 means the autoscaler must recommend at least a 2 worker scale-down for the cluster to scale. A threshold of 0 means the autoscaler will scale down on any recommended change.Bounds: 0.0, 1.0. De<PERSON>ult: 0.0.", "format": "double", "type": "number"}, "scaleUpFactor": {"description": "Required. Fraction of average YARN pending memory in the last cooldown period for which to add workers. A scale-up factor of 1.0 will result in scaling up so that there is no pending memory remaining after the update (more aggressive scaling). A scale-up factor closer to 0 will result in a smaller magnitude of scaling up (less aggressive scaling). See How autoscaling works (https://cloud.google.com/dataproc/docs/concepts/configuring-clusters/autoscaling#how_autoscaling_works) for more information.Bounds: 0.0, 1.0.", "format": "double", "type": "number"}, "scaleUpMinWorkerFraction": {"description": "Optional. Minimum scale-up threshold as a fraction of total cluster size before scaling occurs. For example, in a 20-worker cluster, a threshold of 0.1 means the autoscaler must recommend at least a 2-worker scale-up for the cluster to scale. A threshold of 0 means the autoscaler will scale up on any recommended change.Bounds: 0.0, 1.0. De<PERSON>ult: 0.0.", "format": "double", "type": "number"}}, "type": "object"}, "Batch": {"description": "A representation of a batch workload in the service.", "id": "<PERSON><PERSON>", "properties": {"createTime": {"description": "Output only. The time when the batch was created.", "format": "google-datetime", "readOnly": true, "type": "string"}, "creator": {"description": "Output only. The email address of the user who created the batch.", "readOnly": true, "type": "string"}, "environmentConfig": {"$ref": "EnvironmentConfig", "description": "Optional. Environment configuration for the batch execution."}, "labels": {"additionalProperties": {"type": "string"}, "description": "Optional. The labels to associate with this batch. Label keys must contain 1 to 63 characters, and must conform to RFC 1035 (https://www.ietf.org/rfc/rfc1035.txt). Label values may be empty, but, if present, must contain 1 to 63 characters, and must conform to RFC 1035 (https://www.ietf.org/rfc/rfc1035.txt). No more than 32 labels can be associated with a batch.", "type": "object"}, "name": {"description": "Output only. The resource name of the batch.", "readOnly": true, "type": "string"}, "operation": {"description": "Output only. The resource name of the operation associated with this batch.", "readOnly": true, "type": "string"}, "pysparkBatch": {"$ref": "PySparkBatch", "description": "Optional. PySpark batch config."}, "runtimeConfig": {"$ref": "RuntimeConfig", "description": "Optional. Runtime configuration for the batch execution."}, "runtimeInfo": {"$ref": "RuntimeInfo", "description": "Output only. Runtime information about batch execution.", "readOnly": true}, "sparkBatch": {"$ref": "SparkBatch", "description": "Optional. Spark batch config."}, "sparkRBatch": {"$ref": "SparkRBatch", "description": "Optional. SparkR batch config."}, "sparkSqlBatch": {"$ref": "SparkSqlBatch", "description": "Optional. SparkSql batch config."}, "state": {"description": "Output only. The state of the batch.", "enum": ["STATE_UNSPECIFIED", "PENDING", "RUNNING", "CANCELLING", "CANCELLED", "SUCCEEDED", "FAILED"], "enumDescriptions": ["The batch state is unknown.", "The batch is created before running.", "The batch is running.", "The batch is cancelling.", "The batch cancellation was successful.", "The batch completed successfully.", "The batch is no longer running due to an error."], "readOnly": true, "type": "string"}, "stateHistory": {"description": "Output only. Historical state information for the batch.", "items": {"$ref": "StateHistory"}, "readOnly": true, "type": "array"}, "stateMessage": {"description": "Output only. Batch state details, such as a failure description if the state is FAILED.", "readOnly": true, "type": "string"}, "stateTime": {"description": "Output only. The time when the batch entered a current state.", "format": "google-datetime", "readOnly": true, "type": "string"}, "uuid": {"description": "Output only. A batch UUID (Unique Universal Identifier). The service generates this value when it creates the batch.", "readOnly": true, "type": "string"}}, "type": "object"}, "BatchOperationMetadata": {"description": "Metadata describing the Batch operation.", "id": "BatchOperationMetadata", "properties": {"batch": {"description": "Name of the batch for the operation.", "type": "string"}, "batchUuid": {"description": "Batch UUID for the operation.", "type": "string"}, "createTime": {"description": "The time when the operation was created.", "format": "google-datetime", "type": "string"}, "description": {"description": "Short description of the operation.", "type": "string"}, "doneTime": {"description": "The time when the operation finished.", "format": "google-datetime", "type": "string"}, "labels": {"additionalProperties": {"type": "string"}, "description": "Labels associated with the operation.", "type": "object"}, "operationType": {"description": "The operation type.", "enum": ["BATCH_OPERATION_TYPE_UNSPECIFIED", "BATCH"], "enumDescriptions": ["Batch operation type is unknown.", "Batch operation type."], "type": "string"}, "warnings": {"description": "Warnings encountered during operation execution.", "items": {"type": "string"}, "type": "array"}}, "type": "object"}, "Binding": {"description": "Associates members, or principals, with a role.", "id": "Binding", "properties": {"condition": {"$ref": "Expr", "description": "The condition that is associated with this binding.If the condition evaluates to true, then this binding applies to the current request.If the condition evaluates to false, then this binding does not apply to the current request. However, a different role binding might grant the same role to one or more of the principals in this binding.To learn which resources support conditions in their IAM policies, see the IAM documentation (https://cloud.google.com/iam/help/conditions/resource-policies)."}, "members": {"description": "Specifies the principals requesting access for a Google Cloud resource. members can have the following values: allUsers: A special identifier that represents anyone who is on the internet; with or without a Google account. allAuthenticatedUsers: A special identifier that represents anyone who is authenticated with a Google account or a service account. Does not include identities that come from external identity providers (IdPs) through identity federation. user:{emailid}: An email address that represents a specific Google account. For example, <EMAIL> . serviceAccount:{emailid}: An email address that represents a Google service account. For example, <EMAIL>. serviceAccount:{projectid}.svc.id.goog[{namespace}/{kubernetes-sa}]: An identifier for a Kubernetes service account (https://cloud.google.com/kubernetes-engine/docs/how-to/kubernetes-service-accounts). For example, my-project.svc.id.goog[my-namespace/my-kubernetes-sa]. group:{emailid}: An email address that represents a Google group. For example, <EMAIL>. domain:{domain}: The G Suite domain (primary) that represents all the users of that domain. For example, google.com or example.com. principal://iam.googleapis.com/locations/global/workforcePools/{pool_id}/subject/{subject_attribute_value}: A single identity in a workforce identity pool. principalSet://iam.googleapis.com/locations/global/workforcePools/{pool_id}/group/{group_id}: All workforce identities in a group. principalSet://iam.googleapis.com/locations/global/workforcePools/{pool_id}/attribute.{attribute_name}/{attribute_value}: All workforce identities with a specific attribute value. principalSet://iam.googleapis.com/locations/global/workforcePools/{pool_id}/*: All identities in a workforce identity pool. principal://iam.googleapis.com/projects/{project_number}/locations/global/workloadIdentityPools/{pool_id}/subject/{subject_attribute_value}: A single identity in a workload identity pool. principalSet://iam.googleapis.com/projects/{project_number}/locations/global/workloadIdentityPools/{pool_id}/group/{group_id}: A workload identity pool group. principalSet://iam.googleapis.com/projects/{project_number}/locations/global/workloadIdentityPools/{pool_id}/attribute.{attribute_name}/{attribute_value}: All identities in a workload identity pool with a certain attribute. principalSet://iam.googleapis.com/projects/{project_number}/locations/global/workloadIdentityPools/{pool_id}/*: All identities in a workload identity pool. deleted:user:{emailid}?uid={uniqueid}: An email address (plus unique identifier) representing a user that has been recently deleted. For example, <EMAIL>?uid=123456789012345678901. If the user is recovered, this value reverts to user:{emailid} and the recovered user retains the role in the binding. deleted:serviceAccount:{emailid}?uid={uniqueid}: An email address (plus unique identifier) representing a service account that has been recently deleted. For example, <EMAIL>?uid=123456789012345678901. If the service account is undeleted, this value reverts to serviceAccount:{emailid} and the undeleted service account retains the role in the binding. deleted:group:{emailid}?uid={uniqueid}: An email address (plus unique identifier) representing a Google group that has been recently deleted. For example, <EMAIL>?uid=123456789012345678901. If the group is recovered, this value reverts to group:{emailid} and the recovered group retains the role in the binding. deleted:principal://iam.googleapis.com/locations/global/workforcePools/{pool_id}/subject/{subject_attribute_value}: Deleted single identity in a workforce identity pool. For example, deleted:principal://iam.googleapis.com/locations/global/workforcePools/my-pool-id/subject/my-subject-attribute-value.", "items": {"type": "string"}, "type": "array"}, "role": {"description": "Role that is assigned to the list of members, or principals. For example, roles/viewer, roles/editor, or roles/owner.For an overview of the IAM roles and permissions, see the IAM documentation (https://cloud.google.com/iam/docs/roles-overview). For a list of the available pre-defined roles, see here (https://cloud.google.com/iam/docs/understanding-roles).", "type": "string"}}, "type": "object"}, "BuildInfo": {"description": "Native Build Info", "id": "BuildInfo", "properties": {"buildKey": {"description": "Optional. Build key.", "type": "string"}, "buildValue": {"description": "Optional. Build value.", "type": "string"}}, "type": "object"}, "CancelJobRequest": {"description": "A request to cancel a job.", "id": "CancelJobRequest", "properties": {}, "type": "object"}, "Cluster": {"description": "Describes the identifying information, config, and status of a Dataproc cluster", "id": "Cluster", "properties": {"clusterName": {"description": "Required. The cluster name, which must be unique within a project. The name must start with a lowercase letter, and can contain up to 51 lowercase letters, numbers, and hyphens. It cannot end with a hyphen. The name of a deleted cluster can be reused.", "type": "string"}, "clusterUuid": {"description": "Output only. A cluster UUID (Unique Universal Identifier). Dataproc generates this value when it creates the cluster.", "readOnly": true, "type": "string"}, "config": {"$ref": "ClusterConfig", "description": "Optional. The cluster config for a cluster of Compute Engine Instances. Note that Dataproc may set default values, and values may change when clusters are updated.Exactly one of ClusterConfig or VirtualClusterConfig must be specified."}, "labels": {"additionalProperties": {"type": "string"}, "description": "Optional. The labels to associate with this cluster. Label keys must contain 1 to 63 characters, and must conform to RFC 1035 (https://www.ietf.org/rfc/rfc1035.txt). Label values may be empty, but, if present, must contain 1 to 63 characters, and must conform to RFC 1035 (https://www.ietf.org/rfc/rfc1035.txt). No more than 32 labels can be associated with a cluster.", "type": "object"}, "metrics": {"$ref": "ClusterMetrics", "description": "Output only. Contains cluster daemon metrics such as HDFS and YARN stats.Beta Feature: This report is available for testing purposes only. It may be changed before final release.", "readOnly": true}, "projectId": {"description": "Required. The Google Cloud Platform project ID that the cluster belongs to.", "type": "string"}, "status": {"$ref": "ClusterStatus", "description": "Output only. Cluster status.", "readOnly": true}, "statusHistory": {"description": "Output only. The previous cluster status.", "items": {"$ref": "ClusterStatus"}, "readOnly": true, "type": "array"}, "virtualClusterConfig": {"$ref": "VirtualClusterConfig", "description": "Optional. The virtual cluster config is used when creating a Dataproc cluster that does not directly control the underlying compute resources, for example, when creating a Dataproc-on-GKE cluster (https://cloud.google.com/dataproc/docs/guides/dpgke/dataproc-gke-overview). Dataproc may set default values, and values may change when clusters are updated. Exactly one of config or virtual_cluster_config must be specified."}}, "type": "object"}, "ClusterConfig": {"description": "The cluster config.", "id": "ClusterConfig", "properties": {"autoscalingConfig": {"$ref": "AutoscalingConfig", "description": "Optional. Autoscaling config for the policy associated with the cluster. Cluster does not autoscale if this field is unset."}, "auxiliaryNodeGroups": {"description": "Optional. The node group settings.", "items": {"$ref": "AuxiliaryNodeGroup"}, "type": "array"}, "clusterTier": {"description": "Optional. The tier of the cluster.", "enum": ["CLUSTER_TIER_UNSPECIFIED", "CLUSTER_TIER_STANDARD", "CLUSTER_TIER_PREMIUM"], "enumDescriptions": ["Not set. Works the same as CLUSTER_TIER_STANDARD.", "Standard dataproc cluster.", "Premium dataproc cluster."], "type": "string"}, "clusterType": {"description": "Optional. The type of the cluster.", "enum": ["CLUSTER_TYPE_UNSPECIFIED", "STANDARD", "SINGLE_NODE", "ZERO_SCALE"], "enumDescriptions": ["Not set.", "Standard dataproc cluster with a minimum of two primary workers.", "https://cloud.google.com/dataproc/docs/concepts/configuring-clusters/single-node-clusters", "Clusters that can use only secondary workers and be scaled down to zero secondary worker nodes."], "type": "string"}, "configBucket": {"description": "Optional. A Cloud Storage bucket used to stage job dependencies, config files, and job driver console output. If you do not specify a staging bucket, Cloud Dataproc will determine a Cloud Storage location (US, ASIA, or EU) for your cluster's staging bucket according to the Compute Engine zone where your cluster is deployed, and then create and manage this project-level, per-location bucket (see Dataproc staging and temp buckets (https://cloud.google.com/dataproc/docs/concepts/configuring-clusters/staging-bucket)). This field requires a Cloud Storage bucket name, not a gs://... URI to a Cloud Storage bucket.", "type": "string"}, "dataprocMetricConfig": {"$ref": "DataprocMetricConfig", "description": "Optional. The config for Dataproc metrics."}, "encryptionConfig": {"$ref": "EncryptionConfig", "description": "Optional. Encryption settings for the cluster."}, "endpointConfig": {"$ref": "EndpointConfig", "description": "Optional. Port/endpoint configuration for this cluster"}, "gceClusterConfig": {"$ref": "GceClusterConfig", "description": "Optional. The shared Compute Engine config settings for all instances in a cluster."}, "gkeClusterConfig": {"$ref": "GkeClusterConfig", "deprecated": true, "description": "Optional. BETA. The Kubernetes Engine config for Dataproc clusters deployed to The Kubernetes Engine config for Dataproc clusters deployed to Kubernetes. These config settings are mutually exclusive with Compute Engine-based options, such as gce_cluster_config, master_config, worker_config, secondary_worker_config, and autoscaling_config."}, "initializationActions": {"description": "Optional. Commands to execute on each node after config is completed. By default, executables are run on master and all worker nodes. You can test a node's role metadata to run an executable on a master or worker node, as shown below using curl (you can also use wget): ROLE=$(curl -H Metadata-Flavor:Google http://metadata/computeMetadata/v1/instance/attributes/dataproc-role) if [[ \"${ROLE}\" == 'Master' ]]; then ... master specific actions ... else ... worker specific actions ... fi ", "items": {"$ref": "NodeInitializationAction"}, "type": "array"}, "lifecycleConfig": {"$ref": "LifecycleConfig", "description": "Optional. Lifecycle setting for the cluster."}, "masterConfig": {"$ref": "InstanceGroupConfig", "description": "Optional. The Compute Engine config settings for the cluster's master instance."}, "metastoreConfig": {"$ref": "MetastoreConfig", "description": "Optional. Metastore configuration."}, "secondaryWorkerConfig": {"$ref": "InstanceGroupConfig", "description": "Optional. The Compute Engine config settings for a cluster's secondary worker instances"}, "securityConfig": {"$ref": "SecurityConfig", "description": "Optional. Security settings for the cluster."}, "softwareConfig": {"$ref": "SoftwareConfig", "description": "Optional. The config settings for cluster software."}, "tempBucket": {"description": "Optional. A Cloud Storage bucket used to store ephemeral cluster and jobs data, such as Spark and MapReduce history files. If you do not specify a temp bucket, Dataproc will determine a Cloud Storage location (US, ASIA, or EU) for your cluster's temp bucket according to the Compute Engine zone where your cluster is deployed, and then create and manage this project-level, per-location bucket. The default bucket has a TTL of 90 days, but you can use any TTL (or none) if you specify a bucket (see Dataproc staging and temp buckets (https://cloud.google.com/dataproc/docs/concepts/configuring-clusters/staging-bucket)). This field requires a Cloud Storage bucket name, not a gs://... URI to a Cloud Storage bucket.", "type": "string"}, "workerConfig": {"$ref": "InstanceGroupConfig", "description": "Optional. The Compute Engine config settings for the cluster's worker instances."}}, "type": "object"}, "ClusterMetrics": {"description": "Contains cluster daemon metrics, such as HDFS and YARN stats.Beta Feature: This report is available for testing purposes only. It may be changed before final release.", "id": "ClusterMetrics", "properties": {"hdfsMetrics": {"additionalProperties": {"format": "int64", "type": "string"}, "description": "The HDFS metrics.", "type": "object"}, "yarnMetrics": {"additionalProperties": {"format": "int64", "type": "string"}, "description": "YARN metrics.", "type": "object"}}, "type": "object"}, "ClusterOperation": {"description": "The cluster operation triggered by a workflow.", "id": "ClusterOperation", "properties": {"done": {"description": "Output only. Indicates the operation is done.", "readOnly": true, "type": "boolean"}, "error": {"description": "Output only. Error, if operation failed.", "readOnly": true, "type": "string"}, "operationId": {"description": "Output only. The id of the cluster operation.", "readOnly": true, "type": "string"}}, "type": "object"}, "ClusterOperationMetadata": {"description": "Metadata describing the operation.", "id": "ClusterOperationMetadata", "properties": {"childOperationIds": {"description": "Output only. Child operation ids", "items": {"type": "string"}, "readOnly": true, "type": "array"}, "clusterName": {"description": "Output only. Name of the cluster for the operation.", "readOnly": true, "type": "string"}, "clusterUuid": {"description": "Output only. Cluster UUID for the operation.", "readOnly": true, "type": "string"}, "description": {"description": "Output only. Short description of operation.", "readOnly": true, "type": "string"}, "labels": {"additionalProperties": {"type": "string"}, "description": "Output only. Labels associated with the operation", "readOnly": true, "type": "object"}, "operationType": {"description": "Output only. The operation type.", "readOnly": true, "type": "string"}, "status": {"$ref": "ClusterOperationStatus", "description": "Output only. Current operation status.", "readOnly": true}, "statusHistory": {"description": "Output only. The previous operation status.", "items": {"$ref": "ClusterOperationStatus"}, "readOnly": true, "type": "array"}, "warnings": {"description": "Output only. Errors encountered during operation execution.", "items": {"type": "string"}, "readOnly": true, "type": "array"}}, "type": "object"}, "ClusterOperationStatus": {"description": "The status of the operation.", "id": "ClusterOperationStatus", "properties": {"details": {"description": "Output only. A message containing any operation metadata details.", "readOnly": true, "type": "string"}, "innerState": {"description": "Output only. A message containing the detailed operation state.", "readOnly": true, "type": "string"}, "state": {"description": "Output only. A message containing the operation state.", "enum": ["UNKNOWN", "PENDING", "RUNNING", "DONE"], "enumDescriptions": ["Unused.", "The operation has been created.", "The operation is running.", "The operation is done; either cancelled or completed."], "readOnly": true, "type": "string"}, "stateStartTime": {"description": "Output only. The time this state was entered.", "format": "google-datetime", "readOnly": true, "type": "string"}}, "type": "object"}, "ClusterSelector": {"description": "A selector that chooses target cluster for jobs based on metadata.", "id": "ClusterSelector", "properties": {"clusterLabels": {"additionalProperties": {"type": "string"}, "description": "Required. The cluster labels. Cluster must have all labels to match.", "type": "object"}, "zone": {"description": "Optional. The zone where workflow process executes. This parameter does not affect the selection of the cluster.If unspecified, the zone of the first cluster matching the selector is used.", "type": "string"}}, "type": "object"}, "ClusterStatus": {"description": "The status of a cluster and its instances.", "id": "ClusterStatus", "properties": {"detail": {"description": "Optional. Output only. Details of cluster's state.", "readOnly": true, "type": "string"}, "state": {"description": "Output only. The cluster's state.", "enum": ["UNKNOWN", "CREATING", "RUNNING", "ERROR", "ERROR_DUE_TO_UPDATE", "DELETING", "UPDATING", "STOPPING", "STOPPED", "STARTING", "REPAIRING", "SCHEDULED"], "enumDescriptions": ["The cluster state is unknown.", "The cluster is being created and set up. It is not ready for use.", "The cluster is currently running and healthy. It is ready for use.Note: The cluster state changes from \"creating\" to \"running\" status after the master node(s), first two primary worker nodes (and the last primary worker node if primary workers > 2) are running.", "The cluster encountered an error. It is not ready for use.", "The cluster has encountered an error while being updated. Jobs can be submitted to the cluster, but the cluster cannot be updated.", "The cluster is being deleted. It cannot be used.", "The cluster is being updated. It continues to accept and process jobs.", "The cluster is being stopped. It cannot be used.", "The cluster is currently stopped. It is not ready for use.", "The cluster is being started. It is not ready for use.", "The cluster is being repaired. It is not ready for use.", "Cluster creation is currently waiting for resources to be available. Once all resources are available, it will transition to CREATING and then RUNNING."], "readOnly": true, "type": "string"}, "stateStartTime": {"description": "Output only. Time when this state was entered (see JSON representation of Timestamp (https://developers.google.com/protocol-buffers/docs/proto3#json)).", "format": "google-datetime", "readOnly": true, "type": "string"}, "substate": {"description": "Output only. Additional state information that includes status reported by the agent.", "enum": ["UNSPECIFIED", "UNHEALTHY", "STALE_STATUS"], "enumDescriptions": ["The cluster substate is unknown.", "The cluster is known to be in an unhealthy state (for example, critical daemons are not running or HDFS capacity is exhausted).Applies to RUNNING state.", "The agent-reported status is out of date (may occur if Dataproc loses communication with Agent).Applies to RUNNING state."], "readOnly": true, "type": "string"}}, "type": "object"}, "ClusterToRepair": {"description": "Cluster to be repaired", "id": "ClusterToRepair", "properties": {"clusterRepairAction": {"description": "Required. Repair action to take on the cluster resource.", "enum": ["CLUSTER_REPAIR_ACTION_UNSPECIFIED", "REPAIR_ERROR_DUE_TO_UPDATE_CLUSTER"], "enumDescriptions": ["No action will be taken by default.", "Repair cluster in ERROR_DUE_TO_UPDATE states."], "type": "string"}}, "type": "object"}, "ConfidentialInstanceConfig": {"description": "Confidential Instance Config for clusters using Confidential VMs (https://cloud.google.com/compute/confidential-vm/docs)", "id": "ConfidentialInstanceConfig", "properties": {"enableConfidentialCompute": {"description": "Optional. Defines whether the instance should have confidential compute enabled.", "type": "boolean"}}, "type": "object"}, "ConsolidatedExecutorSummary": {"description": "Consolidated summary about executors used by the application.", "id": "ConsolidatedExecutorSummary", "properties": {"activeTasks": {"format": "int32", "type": "integer"}, "completedTasks": {"format": "int32", "type": "integer"}, "count": {"format": "int32", "type": "integer"}, "diskUsed": {"format": "int64", "type": "string"}, "failedTasks": {"format": "int32", "type": "integer"}, "isExcluded": {"format": "int32", "type": "integer"}, "maxMemory": {"format": "int64", "type": "string"}, "memoryMetrics": {"$ref": "MemoryMetrics"}, "memoryUsed": {"format": "int64", "type": "string"}, "rddBlocks": {"format": "int32", "type": "integer"}, "totalCores": {"format": "int32", "type": "integer"}, "totalDurationMillis": {"format": "int64", "type": "string"}, "totalGcTimeMillis": {"format": "int64", "type": "string"}, "totalInputBytes": {"format": "int64", "type": "string"}, "totalShuffleRead": {"format": "int64", "type": "string"}, "totalShuffleWrite": {"format": "int64", "type": "string"}, "totalTasks": {"format": "int32", "type": "integer"}}, "type": "object"}, "DataprocMetricConfig": {"description": "Dataproc metric config.", "id": "DataprocMetricConfig", "properties": {"metrics": {"description": "Required. Metrics sources to enable.", "items": {"$ref": "Metric"}, "type": "array"}}, "type": "object"}, "DiagnoseClusterRequest": {"description": "A request to collect cluster diagnostic information.", "id": "DiagnoseClusterRequest", "properties": {"diagnosisInterval": {"$ref": "Interval", "description": "Optional. Time interval in which diagnosis should be carried out on the cluster."}, "job": {"deprecated": true, "description": "Optional. DEPRECATED Specifies the job on which diagnosis is to be performed. Format: projects/{project}/regions/{region}/jobs/{job}", "type": "string"}, "jobs": {"description": "Optional. Specifies a list of jobs on which diagnosis is to be performed. Format: projects/{project}/regions/{region}/jobs/{job}", "items": {"type": "string"}, "type": "array"}, "tarballAccess": {"description": "Optional. (Optional) The access type to the diagnostic tarball. If not specified, falls back to default access of the bucket", "enum": ["TARBALL_ACCESS_UNSPECIFIED", "GOOGLE_CLOUD_SUPPORT", "GOOGLE_DATAPROC_DIAGNOSE"], "enumDescriptions": ["Tarball Access unspecified. Falls back to default access of the bucket", "Google Cloud Support group has read access to the diagnostic tarball", "Google Cloud Dataproc Diagnose service account has read access to the diagnostic tarball"], "type": "string"}, "tarballGcsDir": {"description": "Optional. (Optional) The output Cloud Storage directory for the diagnostic tarball. If not specified, a task-specific directory in the cluster's staging bucket will be used.", "type": "string"}, "yarnApplicationId": {"deprecated": true, "description": "Optional. DEPRECATED Specifies the yarn application on which diagnosis is to be performed.", "type": "string"}, "yarnApplicationIds": {"description": "Optional. Specifies a list of yarn applications on which diagnosis is to be performed.", "items": {"type": "string"}, "type": "array"}}, "type": "object"}, "DiagnoseClusterResults": {"description": "The location of diagnostic output.", "id": "DiagnoseClusterResults", "properties": {"outputUri": {"description": "Output only. The Cloud Storage URI of the diagnostic output. The output report is a plain text file with a summary of collected diagnostics.", "readOnly": true, "type": "string"}}, "type": "object"}, "DiskConfig": {"description": "Specifies the config of boot disk and attached disk options for a group of VM instances.", "id": "DiskConfig", "properties": {"bootDiskProvisionedIops": {"description": "Optional. Indicates how many IOPS to provision for the disk. This sets the number of I/O operations per second that the disk can handle. This field is supported only if boot_disk_type is hyperdisk-balanced.", "format": "int64", "type": "string"}, "bootDiskProvisionedThroughput": {"description": "Optional. Indicates how much throughput to provision for the disk. This sets the number of throughput mb per second that the disk can handle. Values must be greater than or equal to 1. This field is supported only if boot_disk_type is hyperdisk-balanced.", "format": "int64", "type": "string"}, "bootDiskSizeGb": {"description": "Optional. Size in GB of the boot disk (default is 500GB).", "format": "int32", "type": "integer"}, "bootDiskType": {"description": "Optional. Type of the boot disk (default is \"pd-standard\"). Valid values: \"pd-balanced\" (Persistent Disk Balanced Solid State Drive), \"pd-ssd\" (Persistent Disk Solid State Drive), or \"pd-standard\" (Persistent Disk Hard Disk Drive). See Disk types (https://cloud.google.com/compute/docs/disks#disk-types).", "type": "string"}, "localSsdInterface": {"description": "Optional. Interface type of local SSDs (default is \"scsi\"). Valid values: \"scsi\" (Small Computer System Interface), \"nvme\" (Non-Volatile Memory Express). See local SSD performance (https://cloud.google.com/compute/docs/disks/local-ssd#performance).", "type": "string"}, "numLocalSsds": {"description": "Optional. Number of attached SSDs, from 0 to 8 (default is 0). If SSDs are not attached, the boot disk is used to store runtime logs and HDFS (https://hadoop.apache.org/docs/r1.2.1/hdfs_user_guide.html) data. If one or more SSDs are attached, this runtime bulk data is spread across them, and the boot disk contains only basic config and installed binaries.Note: Local SSD options may vary by machine type and number of vCPUs selected.", "format": "int32", "type": "integer"}}, "type": "object"}, "DriverSchedulingConfig": {"description": "Driver scheduling configuration.", "id": "DriverSchedulingConfig", "properties": {"memoryMb": {"description": "Required. The amount of memory in MB the driver is requesting.", "format": "int32", "type": "integer"}, "vcores": {"description": "Required. The number of vCPUs the driver is requesting.", "format": "int32", "type": "integer"}}, "type": "object"}, "Empty": {"description": "A generic empty message that you can re-use to avoid defining duplicated empty messages in your APIs. A typical example is to use it as the request or the response type of an API method. For instance: service Foo { rpc Bar(google.protobuf.Empty) returns (google.protobuf.Empty); } ", "id": "Empty", "properties": {}, "type": "object"}, "EncryptionConfig": {"description": "Encryption settings for the cluster.", "id": "EncryptionConfig", "properties": {"gcePdKmsKeyName": {"description": "Optional. The Cloud KMS key resource name to use for persistent disk encryption for all instances in the cluster. See Use CMEK with cluster data (https://cloud.google.com//dataproc/docs/concepts/configuring-clusters/customer-managed-encryption#use_cmek_with_cluster_data) for more information.", "type": "string"}, "kmsKey": {"description": "Optional. The Cloud KMS key resource name to use for cluster persistent disk and job argument encryption. See Use CMEK with cluster data (https://cloud.google.com//dataproc/docs/concepts/configuring-clusters/customer-managed-encryption#use_cmek_with_cluster_data) for more information.When this key resource name is provided, the following job arguments of the following job types submitted to the cluster are encrypted using CMEK: FlinkJob args (https://cloud.google.com/dataproc/docs/reference/rest/v1/FlinkJob) HadoopJob args (https://cloud.google.com/dataproc/docs/reference/rest/v1/HadoopJob) SparkJob args (https://cloud.google.com/dataproc/docs/reference/rest/v1/SparkJob) SparkRJob args (https://cloud.google.com/dataproc/docs/reference/rest/v1/SparkRJob) PySparkJob args (https://cloud.google.com/dataproc/docs/reference/rest/v1/PySparkJob) SparkSqlJob (https://cloud.google.com/dataproc/docs/reference/rest/v1/SparkSqlJob) scriptVariables and queryList.queries HiveJob (https://cloud.google.com/dataproc/docs/reference/rest/v1/HiveJob) scriptVariables and queryList.queries PigJob (https://cloud.google.com/dataproc/docs/reference/rest/v1/PigJob) scriptVariables and queryList.queries PrestoJob (https://cloud.google.com/dataproc/docs/reference/rest/v1/PrestoJob) scriptVariables and queryList.queries", "type": "string"}}, "type": "object"}, "EndpointConfig": {"description": "Endpoint config for this cluster", "id": "EndpointConfig", "properties": {"enableHttpPortAccess": {"description": "Optional. If true, enable http access to specific ports on the cluster from external sources. Defaults to false.", "type": "boolean"}, "httpPorts": {"additionalProperties": {"type": "string"}, "description": "Output only. The map of port descriptions to URLs. Will only be populated if enable_http_port_access is true.", "readOnly": true, "type": "object"}}, "type": "object"}, "EnvironmentConfig": {"description": "Environment configuration for a workload.", "id": "EnvironmentConfig", "properties": {"executionConfig": {"$ref": "ExecutionConfig", "description": "Optional. Execution configuration for a workload."}, "peripheralsConfig": {"$ref": "PeripheralsConfig", "description": "Optional. Peripherals configuration that workload has access to."}}, "type": "object"}, "ExecutionConfig": {"description": "Execution configuration for a workload.", "id": "ExecutionConfig", "properties": {"authenticationConfig": {"$ref": "AuthenticationConfig", "description": "Optional. Authentication configuration used to set the default identity for the workload execution. The config specifies the type of identity (service account or user) that will be used by workloads to access resources on the project(s)."}, "idleTtl": {"description": "Optional. Applies to sessions only. The duration to keep the session alive while it's idling. Exceeding this threshold causes the session to terminate. This field cannot be set on a batch workload. Minimum value is 10 minutes; maximum value is 14 days (see JSON representation of Duration (https://developers.google.com/protocol-buffers/docs/proto3#json)). Defaults to 1 hour if not set. If both ttl and idle_ttl are specified for an interactive session, the conditions are treated as OR conditions: the workload will be terminated when it has been idle for idle_ttl or when ttl has been exceeded, whichever occurs first.", "format": "google-duration", "type": "string"}, "kmsKey": {"description": "Optional. The Cloud KMS key to use for encryption.", "type": "string"}, "networkTags": {"description": "Optional. Tags used for network traffic control.", "items": {"type": "string"}, "type": "array"}, "networkUri": {"description": "Optional. Network URI to connect workload to.", "type": "string"}, "serviceAccount": {"description": "Optional. Service account that used to execute workload.", "type": "string"}, "stagingBucket": {"description": "Optional. A Cloud Storage bucket used to stage workload dependencies, config files, and store workload output and other ephemeral data, such as Spark history files. If you do not specify a staging bucket, Cloud Dataproc will determine a Cloud Storage location according to the region where your workload is running, and then create and manage project-level, per-location staging and temporary buckets. This field requires a Cloud Storage bucket name, not a gs://... URI to a Cloud Storage bucket.", "type": "string"}, "subnetworkUri": {"description": "Optional. Subnetwork URI to connect workload to.", "type": "string"}, "ttl": {"description": "Optional. The duration after which the workload will be terminated, specified as the JSON representation for Duration (https://protobuf.dev/programming-guides/proto3/#json). When the workload exceeds this duration, it will be unconditionally terminated without waiting for ongoing work to finish. If ttl is not specified for a batch workload, the workload will be allowed to run until it exits naturally (or run forever without exiting). If ttl is not specified for an interactive session, it defaults to 24 hours. If ttl is not specified for a batch that uses 2.1+ runtime version, it defaults to 4 hours. Minimum value is 10 minutes; maximum value is 14 days. If both ttl and idle_ttl are specified (for an interactive session), the conditions are treated as OR conditions: the workload will be terminated when it has been idle for idle_ttl or when ttl has been exceeded, whichever occurs first.", "format": "google-duration", "type": "string"}}, "type": "object"}, "ExecutorMetrics": {"id": "ExecutorMetrics", "properties": {"metrics": {"additionalProperties": {"format": "int64", "type": "string"}, "type": "object"}}, "type": "object"}, "ExecutorMetricsDistributions": {"id": "ExecutorMetricsDistributions", "properties": {"diskBytesSpilled": {"items": {"format": "double", "type": "number"}, "type": "array"}, "failedTasks": {"items": {"format": "double", "type": "number"}, "type": "array"}, "inputBytes": {"items": {"format": "double", "type": "number"}, "type": "array"}, "inputRecords": {"items": {"format": "double", "type": "number"}, "type": "array"}, "killedTasks": {"items": {"format": "double", "type": "number"}, "type": "array"}, "memoryBytesSpilled": {"items": {"format": "double", "type": "number"}, "type": "array"}, "outputBytes": {"items": {"format": "double", "type": "number"}, "type": "array"}, "outputRecords": {"items": {"format": "double", "type": "number"}, "type": "array"}, "peakMemoryMetrics": {"$ref": "ExecutorPeakMetricsDistributions"}, "quantiles": {"items": {"format": "double", "type": "number"}, "type": "array"}, "shuffleRead": {"items": {"format": "double", "type": "number"}, "type": "array"}, "shuffleReadRecords": {"items": {"format": "double", "type": "number"}, "type": "array"}, "shuffleWrite": {"items": {"format": "double", "type": "number"}, "type": "array"}, "shuffleWriteRecords": {"items": {"format": "double", "type": "number"}, "type": "array"}, "succeededTasks": {"items": {"format": "double", "type": "number"}, "type": "array"}, "taskTimeMillis": {"items": {"format": "double", "type": "number"}, "type": "array"}}, "type": "object"}, "ExecutorPeakMetricsDistributions": {"id": "ExecutorPeakMetricsDistributions", "properties": {"executorMetrics": {"items": {"$ref": "ExecutorMetrics"}, "type": "array"}, "quantiles": {"items": {"format": "double", "type": "number"}, "type": "array"}}, "type": "object"}, "ExecutorResourceRequest": {"description": "Resources used per executor used by the application.", "id": "ExecutorResourceRequest", "properties": {"amount": {"format": "int64", "type": "string"}, "discoveryScript": {"type": "string"}, "resourceName": {"type": "string"}, "vendor": {"type": "string"}}, "type": "object"}, "ExecutorStageSummary": {"description": "Executor resources consumed by a stage.", "id": "ExecutorStageSummary", "properties": {"diskBytesSpilled": {"format": "int64", "type": "string"}, "executorId": {"type": "string"}, "failedTasks": {"format": "int32", "type": "integer"}, "inputBytes": {"format": "int64", "type": "string"}, "inputRecords": {"format": "int64", "type": "string"}, "isExcludedForStage": {"type": "boolean"}, "killedTasks": {"format": "int32", "type": "integer"}, "memoryBytesSpilled": {"format": "int64", "type": "string"}, "outputBytes": {"format": "int64", "type": "string"}, "outputRecords": {"format": "int64", "type": "string"}, "peakMemoryMetrics": {"$ref": "ExecutorMetrics"}, "shuffleRead": {"format": "int64", "type": "string"}, "shuffleReadRecords": {"format": "int64", "type": "string"}, "shuffleWrite": {"format": "int64", "type": "string"}, "shuffleWriteRecords": {"format": "int64", "type": "string"}, "stageAttemptId": {"format": "int32", "type": "integer"}, "stageId": {"format": "int64", "type": "string"}, "succeededTasks": {"format": "int32", "type": "integer"}, "taskTimeMillis": {"format": "int64", "type": "string"}}, "type": "object"}, "ExecutorSummary": {"description": "Details about executors used by the application.", "id": "Executor<PERSON>ummary", "properties": {"activeTasks": {"format": "int32", "type": "integer"}, "addTime": {"format": "google-datetime", "type": "string"}, "attributes": {"additionalProperties": {"type": "string"}, "type": "object"}, "completedTasks": {"format": "int32", "type": "integer"}, "diskUsed": {"format": "int64", "type": "string"}, "excludedInStages": {"items": {"format": "int64", "type": "string"}, "type": "array"}, "executorId": {"type": "string"}, "executorLogs": {"additionalProperties": {"type": "string"}, "type": "object"}, "failedTasks": {"format": "int32", "type": "integer"}, "hostPort": {"type": "string"}, "isActive": {"type": "boolean"}, "isExcluded": {"type": "boolean"}, "maxMemory": {"format": "int64", "type": "string"}, "maxTasks": {"format": "int32", "type": "integer"}, "memoryMetrics": {"$ref": "MemoryMetrics"}, "memoryUsed": {"format": "int64", "type": "string"}, "peakMemoryMetrics": {"$ref": "ExecutorMetrics"}, "rddBlocks": {"format": "int32", "type": "integer"}, "removeReason": {"type": "string"}, "removeTime": {"format": "google-datetime", "type": "string"}, "resourceProfileId": {"format": "int32", "type": "integer"}, "resources": {"additionalProperties": {"$ref": "ResourceInformation"}, "type": "object"}, "totalCores": {"format": "int32", "type": "integer"}, "totalDurationMillis": {"format": "int64", "type": "string"}, "totalGcTimeMillis": {"format": "int64", "type": "string"}, "totalInputBytes": {"format": "int64", "type": "string"}, "totalShuffleRead": {"format": "int64", "type": "string"}, "totalShuffleWrite": {"format": "int64", "type": "string"}, "totalTasks": {"format": "int32", "type": "integer"}}, "type": "object"}, "Expr": {"description": "Represents a textual expression in the Common Expression Language (CEL) syntax. CEL is a C-like expression language. The syntax and semantics of CEL are documented at https://github.com/google/cel-spec.Example (Comparison): title: \"Summary size limit\" description: \"Determines if a summary is less than 100 chars\" expression: \"document.summary.size() < 100\" Example (Equality): title: \"Requestor is owner\" description: \"Determines if requestor is the document owner\" expression: \"document.owner == request.auth.claims.email\" Example (Logic): title: \"Public documents\" description: \"Determine whether the document should be publicly visible\" expression: \"document.type != 'private' && document.type != 'internal'\" Example (Data Manipulation): title: \"Notification string\" description: \"Create a notification string with a timestamp.\" expression: \"'New message received at ' + string(document.create_time)\" The exact variables and functions that may be referenced within an expression are determined by the service that evaluates it. See the service documentation for additional information.", "id": "Expr", "properties": {"description": {"description": "Optional. Description of the expression. This is a longer text which describes the expression, e.g. when hovered over it in a UI.", "type": "string"}, "expression": {"description": "Textual representation of an expression in Common Expression Language syntax.", "type": "string"}, "location": {"description": "Optional. String indicating the location of the expression for error reporting, e.g. a file name and a position in the file.", "type": "string"}, "title": {"description": "Optional. Title for the expression, i.e. a short string describing its purpose. This can be used e.g. in UIs which allow to enter the expression.", "type": "string"}}, "type": "object"}, "FallbackReason": {"description": "Native SQL Execution Data", "id": "FallbackReason", "properties": {"fallbackNode": {"description": "Optional. Fallback node information.", "type": "string"}, "fallbackReason": {"description": "Optional. Fallback to Spark reason.", "type": "string"}}, "type": "object"}, "FlinkJob": {"description": "A Dataproc job for running Apache Flink applications on YARN.", "id": "FlinkJob", "properties": {"args": {"description": "Optional. The arguments to pass to the driver. Do not include arguments, such as --conf, that can be set as job properties, since a collision might occur that causes an incorrect job submission.", "items": {"type": "string"}, "type": "array"}, "jarFileUris": {"description": "Optional. HCFS URIs of jar files to add to the CLASSPATHs of the Flink driver and tasks.", "items": {"type": "string"}, "type": "array"}, "loggingConfig": {"$ref": "LoggingConfig", "description": "Optional. The runtime log config for job execution."}, "mainClass": {"description": "The name of the driver's main class. The jar file that contains the class must be in the default CLASSPATH or specified in jarFileUris.", "type": "string"}, "mainJarFileUri": {"description": "The HCFS URI of the jar file that contains the main class.", "type": "string"}, "properties": {"additionalProperties": {"type": "string"}, "description": "Optional. A mapping of property names to values, used to configure Flink. Properties that conflict with values set by the Dataproc API might be overwritten. Can include properties set in /etc/flink/conf/flink-defaults.conf and classes in user code.", "type": "object"}, "savepointUri": {"description": "Optional. HCFS URI of the savepoint, which contains the last saved progress for starting the current job.", "type": "string"}}, "type": "object"}, "GceClusterConfig": {"description": "Common config settings for resources of Compute Engine cluster instances, applicable to all instances in the cluster.", "id": "GceClusterConfig", "properties": {"confidentialInstanceConfig": {"$ref": "ConfidentialInstanceConfig", "description": "Optional. Confidential Instance Config for clusters using Confidential VMs (https://cloud.google.com/compute/confidential-vm/docs)."}, "internalIpOnly": {"description": "Optional. This setting applies to subnetwork-enabled networks. It is set to true by default in clusters created with image versions 2.2.x.When set to true: All cluster VMs have internal IP addresses. Google Private Access (https://cloud.google.com/vpc/docs/private-google-access) must be enabled to access Dataproc and other Google Cloud APIs. Off-cluster dependencies must be configured to be accessible without external IP addresses.When set to false: Cluster VMs are not restricted to internal IP addresses. Ephemeral external IP addresses are assigned to each cluster VM.", "type": "boolean"}, "metadata": {"additionalProperties": {"type": "string"}, "description": "Optional. The Compute Engine metadata entries to add to all instances (see Project and instance metadata (https://cloud.google.com/compute/docs/storing-retrieving-metadata#project_and_instance_metadata)).", "type": "object"}, "networkUri": {"description": "Optional. The Compute Engine network to be used for machine communications. Cannot be specified with subnetwork_uri. If neither network_uri nor subnetwork_uri is specified, the \"default\" network of the project is used, if it exists. Cannot be a \"Custom Subnet Network\" (see Using Subnetworks (https://cloud.google.com/compute/docs/subnetworks) for more information).A full URL, partial URI, or short name are valid. Examples: https://www.googleapis.com/compute/v1/projects/[project_id]/global/networks/default projects/[project_id]/global/networks/default default", "type": "string"}, "nodeGroupAffinity": {"$ref": "NodeGroupAffinity", "description": "Optional. Node Group Affinity for sole-tenant clusters."}, "privateIpv6GoogleAccess": {"description": "Optional. The type of IPv6 access for a cluster.", "enum": ["PRIVATE_IPV6_GOOGLE_ACCESS_UNSPECIFIED", "INHERIT_FROM_SUBNETWORK", "OUTBOUND", "BIDIRECTIONAL"], "enumDescriptions": ["If unspecified, Compute Engine default behavior will apply, which is the same as INHERIT_FROM_SUBNETWORK.", "Private access to and from Google Services configuration inherited from the subnetwork configuration. This is the default Compute Engine behavior.", "Enables outbound private IPv6 access to Google Services from the Dataproc cluster.", "Enables bidirectional private IPv6 access between Google Services and the Dataproc cluster."], "type": "string"}, "reservationAffinity": {"$ref": "ReservationAffinity", "description": "Optional. Reservation Affinity for consuming Zonal reservation."}, "resourceManagerTags": {"additionalProperties": {"type": "string"}, "description": "Optional. Resource manager tags (https://cloud.google.com/resource-manager/docs/tags/tags-creating-and-managing) to add to all instances (see Use secure tags in Dataproc (https://cloud.google.com/dataproc/docs/guides/attach-secure-tags)).", "type": "object"}, "serviceAccount": {"description": "Optional. The Dataproc service account (https://cloud.google.com/dataproc/docs/concepts/configuring-clusters/service-accounts#service_accounts_in_dataproc) (also see VM Data Plane identity (https://cloud.google.com/dataproc/docs/concepts/iam/dataproc-principals#vm_service_account_data_plane_identity)) used by Dataproc cluster VM instances to access Google Cloud Platform services.If not specified, the Compute Engine default service account (https://cloud.google.com/compute/docs/access/service-accounts#default_service_account) is used.", "type": "string"}, "serviceAccountScopes": {"description": "Optional. The URIs of service account scopes to be included in Compute Engine instances. The following base set of scopes is always included: https://www.googleapis.com/auth/cloud.useraccounts.readonly https://www.googleapis.com/auth/devstorage.read_write https://www.googleapis.com/auth/logging.writeIf no scopes are specified, the following defaults are also provided: https://www.googleapis.com/auth/bigquery https://www.googleapis.com/auth/bigtable.admin.table https://www.googleapis.com/auth/bigtable.data https://www.googleapis.com/auth/devstorage.full_control", "items": {"type": "string"}, "type": "array"}, "shieldedInstanceConfig": {"$ref": "ShieldedInstanceConfig", "description": "Optional. Shielded Instance Config for clusters using Compute Engine Shielded VMs (https://cloud.google.com/security/shielded-cloud/shielded-vm)."}, "subnetworkUri": {"description": "Optional. The Compute Engine subnetwork to be used for machine communications. Cannot be specified with network_uri.A full URL, partial URI, or short name are valid. Examples: https://www.googleapis.com/compute/v1/projects/[project_id]/regions/[region]/subnetworks/sub0 projects/[project_id]/regions/[region]/subnetworks/sub0 sub0", "type": "string"}, "tags": {"description": "The Compute Engine network tags to add to all instances (see Tagging instances (https://cloud.google.com/vpc/docs/add-remove-network-tags)).", "items": {"type": "string"}, "type": "array"}, "zoneUri": {"description": "Optional. The Compute Engine zone where the Dataproc cluster will be located. If omitted, the service will pick a zone in the cluster's Compute Engine region. On a get request, zone will always be present.A full URL, partial URI, or short name are valid. Examples: https://www.googleapis.com/compute/v1/projects/[project_id]/zones/[zone] projects/[project_id]/zones/[zone] [zone]", "type": "string"}}, "type": "object"}, "GetIamPolicyRequest": {"description": "Request message for GetIamPolicy method.", "id": "GetIamPolicyRequest", "properties": {"options": {"$ref": "GetPolicyOptions", "description": "OPTIONAL: A GetPolicyOptions object for specifying options to GetIamPolicy."}}, "type": "object"}, "GetPolicyOptions": {"description": "Encapsulates settings provided to GetIamPolicy.", "id": "GetPolicyOptions", "properties": {"requestedPolicyVersion": {"description": "Optional. The maximum policy version that will be used to format the policy.Valid values are 0, 1, and 3. Requests specifying an invalid value will be rejected.Requests for policies with any conditional role bindings must specify version 3. Policies with no conditional role bindings may specify any valid value or leave the field unset.The policy in the response might use the policy version that you specified, or it might use a lower policy version. For example, if you specify version 3, but the policy has no conditional role bindings, the response uses version 1.To learn which resources support conditions in their IAM policies, see the IAM documentation (https://cloud.google.com/iam/help/conditions/resource-policies).", "format": "int32", "type": "integer"}}, "type": "object"}, "GkeClusterConfig": {"description": "The cluster's GKE config.", "id": "GkeClusterConfig", "properties": {"gkeClusterTarget": {"description": "Optional. A target GKE cluster to deploy to. It must be in the same project and region as the Dataproc cluster (the GKE cluster can be zonal or regional). Format: 'projects/{project}/locations/{location}/clusters/{cluster_id}'", "type": "string"}, "namespacedGkeDeploymentTarget": {"$ref": "NamespacedGkeDeploymentTarget", "deprecated": true, "description": "Optional. Deprecated. Use gkeClusterTarget. Used only for the deprecated beta. A target for the deployment."}, "nodePoolTarget": {"description": "Optional. GKE node pools where workloads will be scheduled. At least one node pool must be assigned the DEFAULT GkeNodePoolTarget.Role. If a GkeNodePoolTarget is not specified, Dataproc constructs a DEFAULT GkeNodePoolTarget. Each role can be given to only one GkeNodePoolTarget. All node pools must have the same location settings.", "items": {"$ref": "GkeNodePoolTarget"}, "type": "array"}}, "type": "object"}, "GkeNodeConfig": {"description": "Parameters that describe cluster nodes.", "id": "GkeNodeConfig", "properties": {"accelerators": {"description": "Optional. A list of hardware accelerators (https://cloud.google.com/compute/docs/gpus) to attach to each node.", "items": {"$ref": "GkeNodePoolAcceleratorConfig"}, "type": "array"}, "bootDiskKmsKey": {"description": "Optional. The Customer Managed Encryption Key (CMEK) (https://cloud.google.com/kubernetes-engine/docs/how-to/using-cmek) used to encrypt the boot disk attached to each node in the node pool. Specify the key using the following format: projects/{project}/locations/{location}/keyRings/{key_ring}/cryptoKeys/{crypto_key}", "type": "string"}, "localSsdCount": {"description": "Optional. The number of local SSD disks to attach to the node, which is limited by the maximum number of disks allowable per zone (see Adding Local SSDs (https://cloud.google.com/compute/docs/disks/local-ssd)).", "format": "int32", "type": "integer"}, "machineType": {"description": "Optional. The name of a Compute Engine machine type (https://cloud.google.com/compute/docs/machine-types).", "type": "string"}, "minCpuPlatform": {"description": "Optional. Minimum CPU platform (https://cloud.google.com/compute/docs/instances/specify-min-cpu-platform) to be used by this instance. The instance may be scheduled on the specified or a newer CPU platform. Specify the friendly names of CPU platforms, such as \"Intel Haswell\"` or Intel Sandy Bridge\".", "type": "string"}, "preemptible": {"description": "Optional. Whether the nodes are created as legacy preemptible VM instances (https://cloud.google.com/compute/docs/instances/preemptible). Also see Spot VMs, preemptible VM instances without a maximum lifetime. Legacy and Spot preemptible nodes cannot be used in a node pool with the CONTROLLER role or in the DEFAULT node pool if the CONTROLLER role is not assigned (the DEFAULT node pool will assume the CONTROLLER role).", "type": "boolean"}, "spot": {"description": "Optional. Whether the nodes are created as Spot VM instances (https://cloud.google.com/compute/docs/instances/spot). Spot VMs are the latest update to legacy preemptible VMs. Spot VMs do not have a maximum lifetime. Legacy and Spot preemptible nodes cannot be used in a node pool with the CONTROLLER role or in the DEFAULT node pool if the CONTROLLER role is not assigned (the DEFAULT node pool will assume the CONTROLLER role).", "type": "boolean"}}, "type": "object"}, "GkeNodePoolAcceleratorConfig": {"description": "A GkeNodeConfigAcceleratorConfig represents a Hardware Accelerator request for a node pool.", "id": "GkeNodePoolAcceleratorConfig", "properties": {"acceleratorCount": {"description": "The number of accelerator cards exposed to an instance.", "format": "int64", "type": "string"}, "acceleratorType": {"description": "The accelerator type resource namename (see GPUs on Compute Engine).", "type": "string"}, "gpuPartitionSize": {"description": "Size of partitions to create on the GPU. Valid values are described in the NVIDIA mig user guide (https://docs.nvidia.com/datacenter/tesla/mig-user-guide/#partitioning).", "type": "string"}}, "type": "object"}, "GkeNodePoolAutoscalingConfig": {"description": "GkeNodePoolAutoscaling contains information the cluster autoscaler needs to adjust the size of the node pool to the current cluster usage.", "id": "GkeNodePoolAutoscalingConfig", "properties": {"maxNodeCount": {"description": "The maximum number of nodes in the node pool. Must be >= min_node_count, and must be > 0. Note: Quota must be sufficient to scale up the cluster.", "format": "int32", "type": "integer"}, "minNodeCount": {"description": "The minimum number of nodes in the node pool. Must be >= 0 and <= max_node_count.", "format": "int32", "type": "integer"}}, "type": "object"}, "GkeNodePoolConfig": {"description": "The configuration of a GKE node pool used by a Dataproc-on-GKE cluster (https://cloud.google.com/dataproc/docs/concepts/jobs/dataproc-gke#create-a-dataproc-on-gke-cluster).", "id": "GkeNodePoolConfig", "properties": {"autoscaling": {"$ref": "GkeNodePoolAutoscalingConfig", "description": "Optional. The autoscaler configuration for this node pool. The autoscaler is enabled only when a valid configuration is present."}, "config": {"$ref": "GkeNodeConfig", "description": "Optional. The node pool configuration."}, "locations": {"description": "Optional. The list of Compute Engine zones (https://cloud.google.com/compute/docs/zones#available) where node pool nodes associated with a Dataproc on GKE virtual cluster will be located.Note: All node pools associated with a virtual cluster must be located in the same region as the virtual cluster, and they must be located in the same zone within that region.If a location is not specified during node pool creation, Dataproc on GKE will choose the zone.", "items": {"type": "string"}, "type": "array"}}, "type": "object"}, "GkeNodePoolTarget": {"description": "GKE node pools that Dataproc workloads run on.", "id": "GkeNodePoolTarget", "properties": {"nodePool": {"description": "Required. The target GKE node pool. Format: 'projects/{project}/locations/{location}/clusters/{cluster}/nodePools/{node_pool}'", "type": "string"}, "nodePoolConfig": {"$ref": "GkeNodePoolConfig", "description": "Input only. The configuration for the GKE node pool.If specified, Dataproc attempts to create a node pool with the specified shape. If one with the same name already exists, it is verified against all specified fields. If a field differs, the virtual cluster creation will fail.If omitted, any node pool with the specified name is used. If a node pool with the specified name does not exist, Dataproc create a node pool with default values.This is an input only field. It will not be returned by the API."}, "roles": {"description": "Required. The roles associated with the GKE node pool.", "items": {"enum": ["ROLE_UNSPECIFIED", "DEFAULT", "CONTROLLER", "SPARK_DRIVER", "SPARK_EXECUTOR"], "enumDescriptions": ["Role is unspecified.", "At least one node pool must have the DEFAULT role. Work assigned to a role that is not associated with a node pool is assigned to the node pool with the DEFAULT role. For example, work assigned to the CONTROLLER role will be assigned to the node pool with the DEFAULT role if no node pool has the CONTROLLER role.", "Run work associated with the Dataproc control plane (for example, controllers and webhooks). Very low resource requirements.", "Run work associated with a Spark driver of a job.", "Run work associated with a Spark executor of a job."], "type": "string"}, "type": "array"}}, "type": "object"}, "GoogleCloudDataprocV1WorkflowTemplateEncryptionConfig": {"description": "Encryption settings for encrypting workflow template job arguments.", "id": "GoogleCloudDataprocV1WorkflowTemplateEncryptionConfig", "properties": {"kmsKey": {"description": "Optional. The Cloud KMS key name to use for encrypting workflow template job arguments.When this this key is provided, the following workflow template job arguments (https://cloud.google.com/dataproc/docs/concepts/workflows/use-workflows#adding_jobs_to_a_template), if present, are CMEK encrypted (https://cloud.google.com/dataproc/docs/concepts/configuring-clusters/customer-managed-encryption#use_cmek_with_workflow_template_data): FlinkJob args (https://cloud.google.com/dataproc/docs/reference/rest/v1/FlinkJob) HadoopJob args (https://cloud.google.com/dataproc/docs/reference/rest/v1/HadoopJob) SparkJob args (https://cloud.google.com/dataproc/docs/reference/rest/v1/SparkJob) SparkRJob args (https://cloud.google.com/dataproc/docs/reference/rest/v1/SparkRJob) PySparkJob args (https://cloud.google.com/dataproc/docs/reference/rest/v1/PySparkJob) SparkSqlJob (https://cloud.google.com/dataproc/docs/reference/rest/v1/SparkSqlJob) scriptVariables and queryList.queries HiveJob (https://cloud.google.com/dataproc/docs/reference/rest/v1/HiveJob) scriptVariables and queryList.queries PigJob (https://cloud.google.com/dataproc/docs/reference/rest/v1/PigJob) scriptVariables and queryList.queries PrestoJob (https://cloud.google.com/dataproc/docs/reference/rest/v1/PrestoJob) scriptVariables and queryList.queries", "type": "string"}}, "type": "object"}, "HadoopJob": {"description": "A Dataproc job for running Apache Hadoop MapReduce (https://hadoop.apache.org/docs/current/hadoop-mapreduce-client/hadoop-mapreduce-client-core/MapReduceTutorial.html) jobs on Apache Hadoop YARN (https://hadoop.apache.org/docs/r2.7.1/hadoop-yarn/hadoop-yarn-site/YARN.html).", "id": "<PERSON><PERSON><PERSON><PERSON>", "properties": {"archiveUris": {"description": "Optional. HCFS URIs of archives to be extracted in the working directory of Hadoop drivers and tasks. Supported file types: .jar, .tar, .tar.gz, .tgz, or .zip.", "items": {"type": "string"}, "type": "array"}, "args": {"description": "Optional. The arguments to pass to the driver. Do not include arguments, such as -libjars or -Dfoo=bar, that can be set as job properties, since a collision might occur that causes an incorrect job submission.", "items": {"type": "string"}, "type": "array"}, "fileUris": {"description": "Optional. HCFS (Hadoop Compatible Filesystem) URIs of files to be copied to the working directory of Hadoop drivers and distributed tasks. Useful for naively parallel tasks.", "items": {"type": "string"}, "type": "array"}, "jarFileUris": {"description": "Optional. Jar file URIs to add to the CLASSPATHs of the Hadoop driver and tasks.", "items": {"type": "string"}, "type": "array"}, "loggingConfig": {"$ref": "LoggingConfig", "description": "Optional. The runtime log config for job execution."}, "mainClass": {"description": "The name of the driver's main class. The jar file containing the class must be in the default CLASSPATH or specified in jar_file_uris.", "type": "string"}, "mainJarFileUri": {"description": "The HCFS URI of the jar file containing the main class. Examples: 'gs://foo-bucket/analytics-binaries/extract-useful-metrics-mr.jar' 'hdfs:/tmp/test-samples/custom-wordcount.jar' 'file:///home/<USER>/lib/hadoop-mapreduce/hadoop-mapreduce-examples.jar'", "type": "string"}, "properties": {"additionalProperties": {"type": "string"}, "description": "Optional. A mapping of property names to values, used to configure Hadoop. Properties that conflict with values set by the Dataproc API might be overwritten. Can include properties set in /etc/hadoop/conf/*-site and classes in user code.", "type": "object"}}, "type": "object"}, "HiveJob": {"description": "A Dataproc job for running Apache Hive (https://hive.apache.org/) queries on YARN.", "id": "HiveJob", "properties": {"continueOnFailure": {"description": "Optional. Whether to continue executing queries if a query fails. The default value is false. Setting to true can be useful when executing independent parallel queries.", "type": "boolean"}, "jarFileUris": {"description": "Optional. HCFS URIs of jar files to add to the CLASSPATH of the Hive server and Hadoop MapReduce (MR) tasks. Can contain Hive SerDes and UDFs.", "items": {"type": "string"}, "type": "array"}, "properties": {"additionalProperties": {"type": "string"}, "description": "Optional. A mapping of property names and values, used to configure Hive. Properties that conflict with values set by the Dataproc API might be overwritten. Can include properties set in /etc/hadoop/conf/*-site.xml, /etc/hive/conf/hive-site.xml, and classes in user code.", "type": "object"}, "queryFileUri": {"description": "The HCFS URI of the script that contains Hive queries.", "type": "string"}, "queryList": {"$ref": "QueryList", "description": "A list of queries."}, "scriptVariables": {"additionalProperties": {"type": "string"}, "description": "Optional. Mapping of query variable names to values (equivalent to the Hive command: SET name=\"value\";).", "type": "object"}}, "type": "object"}, "IdentityConfig": {"description": "Identity related configuration, including service account based secure multi-tenancy user mappings.", "id": "IdentityConfig", "properties": {"userServiceAccountMapping": {"additionalProperties": {"type": "string"}, "description": "Required. Map of user to service account.", "type": "object"}}, "type": "object"}, "InjectCredentialsRequest": {"description": "A request to inject credentials into a cluster.", "id": "InjectCredentialsRequest", "properties": {"clusterUuid": {"description": "Required. The cluster UUID.", "type": "string"}, "credentialsCiphertext": {"description": "Required. The encrypted credentials being injected in to the cluster.The client is responsible for encrypting the credentials in a way that is supported by the cluster.A wrapped value is used here so that the actual contents of the encrypted credentials are not written to audit logs.", "type": "string"}}, "type": "object"}, "InputMetrics": {"description": "Metrics about the input data read by the task.", "id": "InputMetrics", "properties": {"bytesRead": {"format": "int64", "type": "string"}, "recordsRead": {"format": "int64", "type": "string"}}, "type": "object"}, "InputQuantileMetrics": {"id": "InputQuantileMetrics", "properties": {"bytesRead": {"$ref": "Quantiles"}, "recordsRead": {"$ref": "Quantiles"}}, "type": "object"}, "InstanceFlexibilityPolicy": {"description": "Instance flexibility Policy allowing a mixture of VM shapes and provisioning models.", "id": "InstanceFlexibilityPolicy", "properties": {"instanceSelectionList": {"description": "Optional. List of instance selection options that the group will use when creating new VMs.", "items": {"$ref": "InstanceSelection"}, "type": "array"}, "instanceSelectionResults": {"description": "Output only. A list of instance selection results in the group.", "items": {"$ref": "InstanceSelectionResult"}, "readOnly": true, "type": "array"}, "provisioningModelMix": {"$ref": "ProvisioningModelMix", "description": "Optional. Defines how the Group selects the provisioning model to ensure required reliability."}}, "type": "object"}, "InstanceGroupAutoscalingPolicyConfig": {"description": "Configuration for the size bounds of an instance group, including its proportional size to other groups.", "id": "InstanceGroupAutoscalingPolicyConfig", "properties": {"maxInstances": {"description": "Required. Maximum number of instances for this group. Required for primary workers. Note that by default, clusters will not use secondary workers. Required for secondary workers if the minimum secondary instances is set.Primary workers - Bounds: [min_instances, ). Secondary workers - Bounds: [min_instances, ). Default: 0.", "format": "int32", "type": "integer"}, "minInstances": {"description": "Optional. Minimum number of instances for this group.Primary workers - Bounds: 2, max_instances. Default: 2. Secondary workers - Bounds: 0, max_instances. Default: 0.", "format": "int32", "type": "integer"}, "weight": {"description": "Optional. Weight for the instance group, which is used to determine the fraction of total workers in the cluster from this instance group. For example, if primary workers have weight 2, and secondary workers have weight 1, the cluster will have approximately 2 primary workers for each secondary worker.The cluster may not reach the specified balance if constrained by min/max bounds or other autoscaling settings. For example, if max_instances for secondary workers is 0, then only primary workers will be added. The cluster can also be out of balance when created.If weight is not set on any instance group, the cluster will default to equal weight for all groups: the cluster will attempt to maintain an equal number of workers in each group within the configured size bounds for each group. If weight is set for one group only, the cluster will default to zero weight on the unset group. For example if weight is set only on primary workers, the cluster will use primary workers only and no secondary workers.", "format": "int32", "type": "integer"}}, "type": "object"}, "InstanceGroupConfig": {"description": "The config settings for Compute Engine resources in an instance group, such as a master or worker group.", "id": "InstanceGroupConfig", "properties": {"accelerators": {"description": "Optional. The Compute Engine accelerator configuration for these instances.", "items": {"$ref": "AcceleratorConfig"}, "type": "array"}, "diskConfig": {"$ref": "DiskConfig", "description": "Optional. Disk option config settings."}, "imageUri": {"description": "Optional. The Compute Engine image resource used for cluster instances.The URI can represent an image or image family.Image examples: https://www.googleapis.com/compute/v1/projects/[project_id]/global/images/[image-id] projects/[project_id]/global/images/[image-id] image-idImage family examples. Dataproc will use the most recent image from the family: https://www.googleapis.com/compute/v1/projects/[project_id]/global/images/family/[custom-image-family-name] projects/[project_id]/global/images/family/[custom-image-family-name]If the URI is unspecified, it will be inferred from SoftwareConfig.image_version or the system default.", "type": "string"}, "instanceFlexibilityPolicy": {"$ref": "InstanceFlexibilityPolicy", "description": "Optional. Instance flexibility Policy allowing a mixture of VM shapes and provisioning models."}, "instanceNames": {"description": "Output only. The list of instance names. Dataproc derives the names from cluster_name, num_instances, and the instance group.", "items": {"type": "string"}, "readOnly": true, "type": "array"}, "instanceReferences": {"description": "Output only. List of references to Compute Engine instances.", "items": {"$ref": "InstanceReference"}, "readOnly": true, "type": "array"}, "isPreemptible": {"description": "Output only. Specifies that this instance group contains preemptible instances.", "readOnly": true, "type": "boolean"}, "machineTypeUri": {"description": "Optional. The Compute Engine machine type used for cluster instances.A full URL, partial URI, or short name are valid. Examples: https://www.googleapis.com/compute/v1/projects/[project_id]/zones/[zone]/machineTypes/n1-standard-2 projects/[project_id]/zones/[zone]/machineTypes/n1-standard-2 n1-standard-2Auto Zone Exception: If you are using the Dataproc Auto Zone Placement (https://cloud.google.com/dataproc/docs/concepts/configuring-clusters/auto-zone#using_auto_zone_placement) feature, you must use the short name of the machine type resource, for example, n1-standard-2.", "type": "string"}, "managedGroupConfig": {"$ref": "ManagedGroupConfig", "description": "Output only. The config for Compute Engine Instance Group Manager that manages this group. This is only used for preemptible instance groups.", "readOnly": true}, "minCpuPlatform": {"description": "Optional. Specifies the minimum cpu platform for the Instance Group. See Dataproc -> Minimum CPU Platform (https://cloud.google.com/dataproc/docs/concepts/compute/dataproc-min-cpu).", "type": "string"}, "minNumInstances": {"description": "Optional. The minimum number of primary worker instances to create. If min_num_instances is set, cluster creation will succeed if the number of primary workers created is at least equal to the min_num_instances number.Example: Cluster creation request with num_instances = 5 and min_num_instances = 3: If 4 VMs are created and 1 instance fails, the failed VM is deleted. The cluster is resized to 4 instances and placed in a RUNNING state. If 2 instances are created and 3 instances fail, the cluster in placed in an ERROR state. The failed VMs are not deleted.", "format": "int32", "type": "integer"}, "numInstances": {"description": "Optional. The number of VM instances in the instance group. For HA cluster master_config groups, must be set to 3. For standard cluster master_config groups, must be set to 1.", "format": "int32", "type": "integer"}, "preemptibility": {"description": "Optional. Specifies the preemptibility of the instance group.The default value for master and worker groups is NON_PREEMPTIBLE. This default cannot be changed.The default value for secondary instances is PREEMPTIBLE.", "enum": ["PREEMPTIBILITY_UNSPECIFIED", "NON_PREEMPTIBLE", "PREEMPTIBLE", "SPOT"], "enumDescriptions": ["Preemptibility is unspecified, the system will choose the appropriate setting for each instance group.", "Instances are non-preemptible.This option is allowed for all instance groups and is the only valid value for Master and Worker instance groups.", "Instances are preemptible (https://cloud.google.com/compute/docs/instances/preemptible).This option is allowed only for secondary worker (https://cloud.google.com/dataproc/docs/concepts/compute/secondary-vms) groups.", "Instances are Spot VMs (https://cloud.google.com/compute/docs/instances/spot).This option is allowed only for secondary worker (https://cloud.google.com/dataproc/docs/concepts/compute/secondary-vms) groups. Spot VMs are the latest version of preemptible VMs (https://cloud.google.com/compute/docs/instances/preemptible), and provide additional features."], "type": "string"}, "startupConfig": {"$ref": "StartupConfig", "description": "Optional. Configuration to handle the startup of instances during cluster create and update process."}}, "type": "object"}, "InstanceReference": {"description": "A reference to a Compute Engine instance.", "id": "InstanceReference", "properties": {"instanceId": {"description": "The unique identifier of the Compute Engine instance.", "type": "string"}, "instanceName": {"description": "The user-friendly name of the Compute Engine instance.", "type": "string"}, "publicEciesKey": {"description": "The public ECIES key used for sharing data with this instance.", "type": "string"}, "publicKey": {"description": "The public RSA key used for sharing data with this instance.", "type": "string"}}, "type": "object"}, "InstanceSelection": {"description": "Defines machines types and a rank to which the machines types belong.", "id": "InstanceSelection", "properties": {"machineTypes": {"description": "Optional. Full machine-type names, e.g. \"n1-standard-16\".", "items": {"type": "string"}, "type": "array"}, "rank": {"description": "Optional. Preference of this instance selection. Lower number means higher preference. Dataproc will first try to create a VM based on the machine-type with priority rank and fallback to next rank based on availability. Machine types and instance selections with the same priority have the same preference.", "format": "int32", "type": "integer"}}, "type": "object"}, "InstanceSelectionResult": {"description": "Defines a mapping from machine types to the number of VMs that are created with each machine type.", "id": "InstanceSelectionResult", "properties": {"machineType": {"description": "Output only. Full machine-type names, e.g. \"n1-standard-16\".", "readOnly": true, "type": "string"}, "vmCount": {"description": "Output only. Number of VM provisioned with the machine_type.", "format": "int32", "readOnly": true, "type": "integer"}}, "type": "object"}, "InstantiateWorkflowTemplateRequest": {"description": "A request to instantiate a workflow template.", "id": "InstantiateWorkflowTemplateRequest", "properties": {"parameters": {"additionalProperties": {"type": "string"}, "description": "Optional. Map from parameter names to values that should be used for those parameters. Values may not exceed 1000 characters.", "type": "object"}, "requestId": {"description": "Optional. A tag that prevents multiple concurrent workflow instances with the same tag from running. This mitigates risk of concurrent instances started due to retries.It is recommended to always set this value to a UUID (https://en.wikipedia.org/wiki/Universally_unique_identifier).The tag must contain only letters (a-z, A-Z), numbers (0-9), underscores (_), and hyphens (-). The maximum length is 40 characters.", "type": "string"}, "version": {"description": "Optional. The version of workflow template to instantiate. If specified, the workflow will be instantiated only if the current version of the workflow template has the supplied version.This option cannot be used to instantiate a previous version of workflow template.", "format": "int32", "type": "integer"}}, "type": "object"}, "Interval": {"description": "Represents a time interval, encoded as a Timestamp start (inclusive) and a Timestamp end (exclusive).The start must be less than or equal to the end. When the start equals the end, the interval is empty (matches no time). When both start and end are unspecified, the interval matches any time.", "id": "Interval", "properties": {"endTime": {"description": "Optional. Exclusive end of the interval.If specified, a Timestamp matching this interval will have to be before the end.", "format": "google-datetime", "type": "string"}, "startTime": {"description": "Optional. Inclusive start of the interval.If specified, a Timestamp matching this interval will have to be the same or after the start.", "format": "google-datetime", "type": "string"}}, "type": "object"}, "Job": {"description": "A Dataproc job resource.", "id": "Job", "properties": {"done": {"description": "Output only. Indicates whether the job is completed. If the value is false, the job is still in progress. If true, the job is completed, and status.state field will indicate if it was successful, failed, or cancelled.", "readOnly": true, "type": "boolean"}, "driverControlFilesUri": {"description": "Output only. If present, the location of miscellaneous control files which can be used as part of job setup and handling. If not present, control files might be placed in the same location as driver_output_uri.", "readOnly": true, "type": "string"}, "driverOutputResourceUri": {"description": "Output only. A URI pointing to the location of the stdout of the job's driver program.", "readOnly": true, "type": "string"}, "driverSchedulingConfig": {"$ref": "DriverSchedulingConfig", "description": "Optional. Driver scheduling configuration."}, "flinkJob": {"$ref": "FlinkJob", "description": "Optional. Job is a Flink job."}, "hadoopJob": {"$ref": "<PERSON><PERSON><PERSON><PERSON>", "description": "Optional. Job is a <PERSON><PERSON> job."}, "hiveJob": {"$ref": "HiveJob", "description": "Optional. Job is a Hive job."}, "jobUuid": {"description": "Output only. A UUID that uniquely identifies a job within the project over time. This is in contrast to a user-settable reference.job_id that might be reused over time.", "readOnly": true, "type": "string"}, "labels": {"additionalProperties": {"type": "string"}, "description": "Optional. The labels to associate with this job. Label keys must contain 1 to 63 characters, and must conform to RFC 1035 (https://www.ietf.org/rfc/rfc1035.txt). Label values can be empty, but, if present, must contain 1 to 63 characters, and must conform to RFC 1035 (https://www.ietf.org/rfc/rfc1035.txt). No more than 32 labels can be associated with a job.", "type": "object"}, "pigJob": {"$ref": "<PERSON><PERSON><PERSON>", "description": "Optional. Job is a Pig job."}, "placement": {"$ref": "JobPlacement", "description": "Required. Job information, including how, when, and where to run the job."}, "prestoJob": {"$ref": "PrestoJob", "description": "Optional. Job is a Presto job."}, "pysparkJob": {"$ref": "PySparkJob", "description": "Optional. Job is a PySpark job."}, "reference": {"$ref": "JobReference", "description": "Optional. The fully qualified reference to the job, which can be used to obtain the equivalent REST path of the job resource. If this property is not specified when a job is created, the server generates a job_id."}, "scheduling": {"$ref": "JobScheduling", "description": "Optional. Job scheduling configuration."}, "sparkJob": {"$ref": "SparkJob", "description": "Optional. Job is a Spark job."}, "sparkRJob": {"$ref": "SparkRJob", "description": "Optional. Job is a SparkR job."}, "sparkSqlJob": {"$ref": "SparkSqlJob", "description": "Optional. Job is a SparkSql job."}, "status": {"$ref": "JobStatus", "description": "Output only. The job status. Additional application-specific status information might be contained in the type_job and yarn_applications fields.", "readOnly": true}, "statusHistory": {"description": "Output only. The previous job status.", "items": {"$ref": "JobStatus"}, "readOnly": true, "type": "array"}, "trinoJob": {"$ref": "TrinoJob", "description": "Optional. Job is a Trino job."}, "yarnApplications": {"description": "Output only. The collection of YARN applications spun up by this job.Beta Feature: This report is available for testing purposes only. It might be changed before final release.", "items": {"$ref": "YarnApplication"}, "readOnly": true, "type": "array"}}, "type": "object"}, "JobData": {"description": "Data corresponding to a spark job.", "id": "JobData", "properties": {"completionTime": {"format": "google-datetime", "type": "string"}, "description": {"type": "string"}, "jobGroup": {"type": "string"}, "jobId": {"format": "int64", "type": "string"}, "killTasksSummary": {"additionalProperties": {"format": "int32", "type": "integer"}, "type": "object"}, "name": {"type": "string"}, "numActiveStages": {"format": "int32", "type": "integer"}, "numActiveTasks": {"format": "int32", "type": "integer"}, "numCompletedIndices": {"format": "int32", "type": "integer"}, "numCompletedStages": {"format": "int32", "type": "integer"}, "numCompletedTasks": {"format": "int32", "type": "integer"}, "numFailedStages": {"format": "int32", "type": "integer"}, "numFailedTasks": {"format": "int32", "type": "integer"}, "numKilledTasks": {"format": "int32", "type": "integer"}, "numSkippedStages": {"format": "int32", "type": "integer"}, "numSkippedTasks": {"format": "int32", "type": "integer"}, "numTasks": {"format": "int32", "type": "integer"}, "skippedStages": {"items": {"format": "int32", "type": "integer"}, "type": "array"}, "sqlExecutionId": {"format": "int64", "type": "string"}, "stageIds": {"items": {"format": "int64", "type": "string"}, "type": "array"}, "status": {"enum": ["JOB_EXECUTION_STATUS_UNSPECIFIED", "JOB_EXECUTION_STATUS_RUNNING", "JOB_EXECUTION_STATUS_SUCCEEDED", "JOB_EXECUTION_STATUS_FAILED", "JOB_EXECUTION_STATUS_UNKNOWN"], "enumDescriptions": ["", "", "", "", ""], "type": "string"}, "submissionTime": {"format": "google-datetime", "type": "string"}}, "type": "object"}, "JobMetadata": {"description": "Job Operation metadata.", "id": "JobMetadata", "properties": {"jobId": {"description": "Output only. The job id.", "readOnly": true, "type": "string"}, "operationType": {"description": "Output only. Operation type.", "readOnly": true, "type": "string"}, "startTime": {"description": "Output only. Job submission time.", "format": "google-datetime", "readOnly": true, "type": "string"}, "status": {"$ref": "JobStatus", "description": "Output only. Most recent job status.", "readOnly": true}}, "type": "object"}, "JobPlacement": {"description": "Dataproc job config.", "id": "JobPlacement", "properties": {"clusterLabels": {"additionalProperties": {"type": "string"}, "description": "Optional. Cluster labels to identify a cluster where the job will be submitted.", "type": "object"}, "clusterName": {"description": "Required. The name of the cluster where the job will be submitted.", "type": "string"}, "clusterUuid": {"description": "Output only. A cluster UUID generated by the Dataproc service when the job is submitted.", "readOnly": true, "type": "string"}}, "type": "object"}, "JobReference": {"description": "Encapsulates the full scoping used to reference a job.", "id": "JobReference", "properties": {"jobId": {"description": "Optional. The job ID, which must be unique within the project.The ID must contain only letters (a-z, A-Z), numbers (0-9), underscores (_), or hyphens (-). The maximum length is 100 characters.If not specified by the caller, the job ID will be provided by the server.", "type": "string"}, "projectId": {"description": "Optional. The ID of the Google Cloud Platform project that the job belongs to. If specified, must match the request project ID.", "type": "string"}}, "type": "object"}, "JobScheduling": {"description": "Job scheduling options.", "id": "JobScheduling", "properties": {"maxFailuresPerHour": {"description": "Optional. Maximum number of times per hour a driver can be restarted as a result of driver exiting with non-zero code before job is reported failed.A job might be reported as thrashing if the driver exits with a non-zero code four times within a 10-minute window.Maximum value is 10.Note: This restartable job option is not supported in Dataproc workflow templates (https://cloud.google.com/dataproc/docs/concepts/workflows/using-workflows#adding_jobs_to_a_template).", "format": "int32", "type": "integer"}, "maxFailuresTotal": {"description": "Optional. Maximum total number of times a driver can be restarted as a result of the driver exiting with a non-zero code. After the maximum number is reached, the job will be reported as failed.Maximum value is 240.Note: Currently, this restartable job option is not supported in Dataproc workflow templates (https://cloud.google.com/dataproc/docs/concepts/workflows/using-workflows#adding_jobs_to_a_template).", "format": "int32", "type": "integer"}}, "type": "object"}, "JobStatus": {"description": "Dataproc job status.", "id": "JobStatus", "properties": {"details": {"description": "Optional. Output only. Job state details, such as an error description if the state is ERROR.", "readOnly": true, "type": "string"}, "state": {"description": "Output only. A state message specifying the overall job state.", "enum": ["STATE_UNSPECIFIED", "PENDING", "SETUP_DONE", "RUNNING", "CANCEL_PENDING", "CANCEL_STARTED", "CANCELLED", "DONE", "ERROR", "ATTEMPT_FAILURE"], "enumDescriptions": ["The job state is unknown.", "The job is pending; it has been submitted, but is not yet running.", "Job has been received by the service and completed initial setup; it will soon be submitted to the cluster.", "The job is running on the cluster.", "A CancelJob request has been received, but is pending.", "Transient in-flight resources have been canceled, and the request to cancel the running job has been issued to the cluster.", "The job cancellation was successful.", "The job has completed successfully.", "The job has completed, but encountered an error.", "Job attempt has failed. The detail field contains failure details for this attempt.Applies to restartable jobs only."], "readOnly": true, "type": "string"}, "stateStartTime": {"description": "Output only. The time when this state was entered.", "format": "google-datetime", "readOnly": true, "type": "string"}, "substate": {"description": "Output only. Additional state information, which includes status reported by the agent.", "enum": ["UNSPECIFIED", "SUBMITTED", "QUEUED", "STALE_STATUS"], "enumDescriptions": ["The job substate is unknown.", "The Job is submitted to the agent.Applies to RUNNING state.", "The Job has been received and is awaiting execution (it might be waiting for a condition to be met). See the \"details\" field for the reason for the delay.Applies to RUNNING state.", "The agent-reported status is out of date, which can be caused by a loss of communication between the agent and Dataproc. If the agent does not send a timely update, the job will fail.Applies to RUNNING state."], "readOnly": true, "type": "string"}}, "type": "object"}, "JobsSummary": {"description": "Data related to Jobs page summary", "id": "JobsSummary", "properties": {"activeJobs": {"description": "Number of active jobs", "format": "int32", "type": "integer"}, "applicationId": {"description": "Spark Application Id", "type": "string"}, "attempts": {"description": "Attempts info", "items": {"$ref": "ApplicationAttemptInfo"}, "type": "array"}, "completedJobs": {"description": "Number of completed jobs", "format": "int32", "type": "integer"}, "failedJobs": {"description": "Number of failed jobs", "format": "int32", "type": "integer"}, "schedulingMode": {"description": "Spark Scheduling mode", "type": "string"}}, "type": "object"}, "JupyterConfig": {"description": "Jupy<PERSON> configuration for an interactive session.", "id": "JupyterConfig", "properties": {"displayName": {"description": "Optional. Display name, shown in the Jupyter kernelspec card.", "type": "string"}, "kernel": {"description": "Optional. Kernel", "enum": ["KERNEL_UNSPECIFIED", "PYTHON", "SCALA"], "enumDescriptions": ["The kernel is unknown.", "Python kernel.", "Scala kernel."], "type": "string"}}, "type": "object"}, "KerberosConfig": {"description": "Specifies Kerberos related configuration.", "id": "KerberosConfig", "properties": {"crossRealmTrustAdminServer": {"description": "Optional. The admin server (IP or hostname) for the remote trusted realm in a cross realm trust relationship.", "type": "string"}, "crossRealmTrustKdc": {"description": "Optional. The KDC (IP or hostname) for the remote trusted realm in a cross realm trust relationship.", "type": "string"}, "crossRealmTrustRealm": {"description": "Optional. The remote realm the Dataproc on-cluster KDC will trust, should the user enable cross realm trust.", "type": "string"}, "crossRealmTrustSharedPasswordUri": {"description": "Optional. The Cloud Storage URI of a KMS encrypted file containing the shared password between the on-cluster Kerberos realm and the remote trusted realm, in a cross realm trust relationship.", "type": "string"}, "enableKerberos": {"description": "Optional. Flag to indicate whether to Kerberize the cluster (default: false). Set this field to true to enable Kerberos on a cluster.", "type": "boolean"}, "kdcDbKeyUri": {"description": "Optional. The Cloud Storage URI of a KMS encrypted file containing the master key of the KDC database.", "type": "string"}, "keyPasswordUri": {"description": "Optional. The Cloud Storage URI of a KMS encrypted file containing the password to the user provided key. For the self-signed certificate, this password is generated by Dataproc.", "type": "string"}, "keystorePasswordUri": {"description": "Optional. The Cloud Storage URI of a KMS encrypted file containing the password to the user provided keystore. For the self-signed certificate, this password is generated by Dataproc.", "type": "string"}, "keystoreUri": {"description": "Optional. The Cloud Storage URI of the keystore file used for SSL encryption. If not provided, Dataproc will provide a self-signed certificate.", "type": "string"}, "kmsKeyUri": {"description": "Optional. The URI of the KMS key used to encrypt sensitive files.", "type": "string"}, "realm": {"description": "Optional. The name of the on-cluster Kerberos realm. If not specified, the uppercased domain of hostnames will be the realm.", "type": "string"}, "rootPrincipalPasswordUri": {"description": "Optional. The Cloud Storage URI of a KMS encrypted file containing the root principal password.", "type": "string"}, "tgtLifetimeHours": {"description": "Optional. The lifetime of the ticket granting ticket, in hours. If not specified, or user specifies 0, then default value 10 will be used.", "format": "int32", "type": "integer"}, "truststorePasswordUri": {"description": "Optional. The Cloud Storage URI of a KMS encrypted file containing the password to the user provided truststore. For the self-signed certificate, this password is generated by Dataproc.", "type": "string"}, "truststoreUri": {"description": "Optional. The Cloud Storage URI of the truststore file used for SSL encryption. If not provided, Dataproc will provide a self-signed certificate.", "type": "string"}}, "type": "object"}, "KubernetesClusterConfig": {"description": "The configuration for running the Dataproc cluster on Kubernetes.", "id": "KubernetesClusterConfig", "properties": {"gkeClusterConfig": {"$ref": "GkeClusterConfig", "description": "Required. The configuration for running the Dataproc cluster on GKE."}, "kubernetesNamespace": {"description": "Optional. A namespace within the Kubernetes cluster to deploy into. If this namespace does not exist, it is created. If it exists, Dataproc verifies that another Dataproc VirtualCluster is not installed into it. If not specified, the name of the Dataproc Cluster is used.", "type": "string"}, "kubernetesSoftwareConfig": {"$ref": "KubernetesSoftwareConfig", "description": "Optional. The software configuration for this Dataproc cluster running on Kubernetes."}}, "type": "object"}, "KubernetesSoftwareConfig": {"description": "The software configuration for this Dataproc cluster running on Kubernetes.", "id": "KubernetesSoftwareConfig", "properties": {"componentVersion": {"additionalProperties": {"type": "string"}, "description": "The components that should be installed in this Dataproc cluster. The key must be a string from the KubernetesComponent enumeration. The value is the version of the software to be installed. At least one entry must be specified.", "type": "object"}, "properties": {"additionalProperties": {"type": "string"}, "description": "The properties to set on daemon config files.Property keys are specified in prefix:property format, for example spark:spark.kubernetes.container.image. The following are supported prefixes and their mappings: spark: spark-defaults.confFor more information, see Cluster properties (https://cloud.google.com/dataproc/docs/concepts/cluster-properties).", "type": "object"}}, "type": "object"}, "LifecycleConfig": {"description": "Specifies the cluster auto-delete schedule configuration.", "id": "LifecycleConfig", "properties": {"autoDeleteTime": {"description": "Optional. The time when cluster will be auto-deleted (see JSON representation of Timestamp (https://developers.google.com/protocol-buffers/docs/proto3#json)).", "format": "google-datetime", "type": "string"}, "autoDeleteTtl": {"description": "Optional. The lifetime duration of cluster. The cluster will be auto-deleted at the end of this period. Minimum value is 10 minutes; maximum value is 14 days (see JSON representation of Duration (https://developers.google.com/protocol-buffers/docs/proto3#json)).", "format": "google-duration", "type": "string"}, "autoStopTime": {"description": "Optional. The time when cluster will be auto-stopped (see JSON representation of Timestamp (https://developers.google.com/protocol-buffers/docs/proto3#json)).", "format": "google-datetime", "type": "string"}, "autoStopTtl": {"description": "Optional. The lifetime duration of the cluster. The cluster will be auto-stopped at the end of this period, calculated from the time of submission of the create or update cluster request. Minimum value is 10 minutes; maximum value is 14 days (see JSON representation of Duration (https://developers.google.com/protocol-buffers/docs/proto3#json)).", "format": "google-duration", "type": "string"}, "idleDeleteTtl": {"description": "Optional. The duration to keep the cluster alive while idling (when no jobs are running). Passing this threshold will cause the cluster to be deleted. Minimum value is 5 minutes; maximum value is 14 days (see JSON representation of Duration (https://developers.google.com/protocol-buffers/docs/proto3#json)).", "format": "google-duration", "type": "string"}, "idleStartTime": {"description": "Output only. The time when cluster became idle (most recent job finished) and became eligible for deletion due to idleness (see JSON representation of Timestamp (https://developers.google.com/protocol-buffers/docs/proto3#json)).", "format": "google-datetime", "readOnly": true, "type": "string"}, "idleStopTtl": {"description": "Optional. The duration to keep the cluster started while idling (when no jobs are running). Passing this threshold will cause the cluster to be stopped. Minimum value is 5 minutes; maximum value is 14 days (see JSON representation of Duration (https://developers.google.com/protocol-buffers/docs/proto3#json)).", "format": "google-duration", "type": "string"}}, "type": "object"}, "ListAutoscalingPoliciesResponse": {"description": "A response to a request to list autoscaling policies in a project.", "id": "ListAutoscalingPoliciesResponse", "properties": {"nextPageToken": {"description": "Output only. This token is included in the response if there are more results to fetch.", "readOnly": true, "type": "string"}, "policies": {"description": "Output only. Autoscaling policies list.", "items": {"$ref": "AutoscalingPolicy"}, "readOnly": true, "type": "array"}}, "type": "object"}, "ListBatchesResponse": {"description": "A list of batch workloads.", "id": "ListBatchesResponse", "properties": {"batches": {"description": "Output only. The batches from the specified collection.", "items": {"$ref": "<PERSON><PERSON>"}, "readOnly": true, "type": "array"}, "nextPageToken": {"description": "A token, which can be sent as page_token to retrieve the next page. If this field is omitted, there are no subsequent pages.", "type": "string"}, "unreachable": {"description": "Output only. List of Batches that could not be included in the response. Attempting to get one of these resources may indicate why it was not included in the list response.", "items": {"type": "string"}, "readOnly": true, "type": "array"}}, "type": "object"}, "ListClustersResponse": {"description": "The list of all clusters in a project.", "id": "ListClustersResponse", "properties": {"clusters": {"description": "Output only. The clusters in the project.", "items": {"$ref": "Cluster"}, "readOnly": true, "type": "array"}, "nextPageToken": {"description": "Output only. This token is included in the response if there are more results to fetch. To fetch additional results, provide this value as the page_token in a subsequent ListClustersRequest.", "readOnly": true, "type": "string"}}, "type": "object"}, "ListJobsResponse": {"description": "A list of jobs in a project.", "id": "ListJobsResponse", "properties": {"jobs": {"description": "Output only. Jobs list.", "items": {"$ref": "Job"}, "readOnly": true, "type": "array"}, "nextPageToken": {"description": "Optional. This token is included in the response if there are more results to fetch. To fetch additional results, provide this value as the page_token in a subsequent ListJobsRequest.", "type": "string"}, "unreachable": {"description": "Output only. List of jobs with kms_key-encrypted parameters that could not be decrypted. A response to a jobs.get request may indicate the reason for the decryption failure for a specific job.", "items": {"type": "string"}, "readOnly": true, "type": "array"}}, "type": "object"}, "ListOperationsResponse": {"description": "The response message for Operations.ListOperations.", "id": "ListOperationsResponse", "properties": {"nextPageToken": {"description": "The standard List next-page token.", "type": "string"}, "operations": {"description": "A list of operations that matches the specified filter in the request.", "items": {"$ref": "Operation"}, "type": "array"}}, "type": "object"}, "ListSessionTemplatesResponse": {"description": "A list of session templates.", "id": "ListSessionTemplatesResponse", "properties": {"nextPageToken": {"description": "A token, which can be sent as page_token to retrieve the next page. If this field is omitted, there are no subsequent pages.", "type": "string"}, "sessionTemplates": {"description": "Output only. Session template list", "items": {"$ref": "SessionTemplate"}, "readOnly": true, "type": "array"}}, "type": "object"}, "ListSessionsResponse": {"description": "A list of interactive sessions.", "id": "ListSessionsResponse", "properties": {"nextPageToken": {"description": "A token, which can be sent as page_token, to retrieve the next page. If this field is omitted, there are no subsequent pages.", "type": "string"}, "sessions": {"description": "Output only. The sessions from the specified collection.", "items": {"$ref": "Session"}, "readOnly": true, "type": "array"}}, "type": "object"}, "ListWorkflowTemplatesResponse": {"description": "A response to a request to list workflow templates in a project.", "id": "ListWorkflowTemplatesResponse", "properties": {"nextPageToken": {"description": "Output only. This token is included in the response if there are more results to fetch. To fetch additional results, provide this value as the page_token in a subsequent ListWorkflowTemplatesRequest.", "readOnly": true, "type": "string"}, "templates": {"description": "Output only. WorkflowTemplates list.", "items": {"$ref": "WorkflowTemplate"}, "readOnly": true, "type": "array"}, "unreachable": {"description": "Output only. List of workflow templates that could not be included in the response. Attempting to get one of these resources may indicate why it was not included in the list response.", "items": {"type": "string"}, "readOnly": true, "type": "array"}}, "type": "object"}, "LoggingConfig": {"description": "The runtime logging config of the job.", "id": "LoggingConfig", "properties": {"driverLogLevels": {"additionalProperties": {"enum": ["LEVEL_UNSPECIFIED", "ALL", "TRACE", "DEBUG", "INFO", "WARN", "ERROR", "FATAL", "OFF"], "enumDescriptions": ["Level is unspecified. Use default level for log4j.", "Use ALL level for log4j.", "Use TRACE level for log4j.", "Use DEBUG level for log4j.", "Use INFO level for log4j.", "Use WARN level for log4j.", "Use ERROR level for log4j.", "Use FATAL level for log4j.", "Turn off log4j."], "type": "string"}, "description": "The per-package log levels for the driver. This can include \"root\" package name to configure rootLogger. Examples: - 'com.google = FATAL' - 'root = INFO' - 'org.apache = DEBUG'", "type": "object"}}, "type": "object"}, "ManagedCluster": {"description": "Cluster that is managed by the workflow.", "id": "ManagedCluster", "properties": {"clusterName": {"description": "Required. The cluster name prefix. A unique cluster name will be formed by appending a random suffix.The name must contain only lower-case letters (a-z), numbers (0-9), and hyphens (-). Must begin with a letter. Cannot begin or end with hyphen. Must consist of between 2 and 35 characters.", "type": "string"}, "config": {"$ref": "ClusterConfig", "description": "Required. The cluster configuration."}, "labels": {"additionalProperties": {"type": "string"}, "description": "Optional. The labels to associate with this cluster.Label keys must be between 1 and 63 characters long, and must conform to the following PCRE regular expression: \\p{Ll}\\p{Lo}{0,62}Label values must be between 1 and 63 characters long, and must conform to the following PCRE regular expression: \\p{Ll}\\p{Lo}\\p{N}_-{0,63}No more than 32 labels can be associated with a given cluster.", "type": "object"}}, "type": "object"}, "ManagedGroupConfig": {"description": "Specifies the resources used to actively manage an instance group.", "id": "ManagedGroupConfig", "properties": {"instanceGroupManagerName": {"description": "Output only. The name of the Instance Group Manager for this group.", "readOnly": true, "type": "string"}, "instanceGroupManagerUri": {"description": "Output only. The partial URI to the instance group manager for this group. E.g. projects/my-project/regions/us-central1/instanceGroupManagers/my-igm.", "readOnly": true, "type": "string"}, "instanceTemplateName": {"description": "Output only. The name of the Instance Template used for the Managed Instance Group.", "readOnly": true, "type": "string"}}, "type": "object"}, "MemoryMetrics": {"id": "MemoryMetrics", "properties": {"totalOffHeapStorageMemory": {"format": "int64", "type": "string"}, "totalOnHeapStorageMemory": {"format": "int64", "type": "string"}, "usedOffHeapStorageMemory": {"format": "int64", "type": "string"}, "usedOnHeapStorageMemory": {"format": "int64", "type": "string"}}, "type": "object"}, "MetastoreConfig": {"description": "Specifies a Metastore configuration.", "id": "MetastoreConfig", "properties": {"dataprocMetastoreService": {"description": "Required. Resource name of an existing Dataproc Metastore service.Example: projects/[project_id]/locations/[dataproc_region]/services/[service-name]", "type": "string"}}, "type": "object"}, "Metric": {"description": "A Dataproc custom metric.", "id": "Metric", "properties": {"metricOverrides": {"description": "Optional. Specify one or more Custom metrics (https://cloud.google.com/dataproc/docs/guides/dataproc-metrics#custom_metrics) to collect for the metric course (for the SPARK metric source (any Spark metric (https://spark.apache.org/docs/latest/monitoring.html#metrics) can be specified).Provide metrics in the following format: METRIC_SOURCE: INSTANCE:GROUP:METRIC Use camelcase as appropriate.Examples: yarn:ResourceManager:QueueMetrics:AppsCompleted spark:driver:DAGScheduler:job.allJobs sparkHistoryServer:JVM:Memory:NonHeapMemoryUsage.committed hiveserver2:JVM:Memory:NonHeapMemoryUsage.used Notes: Only the specified overridden metrics are collected for the metric source. For example, if one or more spark:executive metrics are listed as metric overrides, other SPARK metrics are not collected. The collection of the metrics for other enabled custom metric sources is unaffected. For example, if both SPARK and YARN metric sources are enabled, and overrides are provided for Spark metrics only, all YARN metrics are collected.", "items": {"type": "string"}, "type": "array"}, "metricSource": {"description": "Required. A standard set of metrics is collected unless metricOverrides are specified for the metric source (see Custom metrics (https://cloud.google.com/dataproc/docs/guides/dataproc-metrics#custom_metrics) for more information).", "enum": ["METRIC_SOURCE_UNSPECIFIED", "MONITORING_AGENT_DEFAULTS", "HDFS", "SPARK", "YARN", "SPARK_HISTORY_SERVER", "HIVESERVER2", "HIVEMETASTORE", "FLINK"], "enumDescriptions": ["Required unspecified metric source.", "Monitoring agent metrics. If this source is enabled, Dataproc enables the monitoring agent in Compute Engine, and collects monitoring agent metrics, which are published with an agent.googleapis.com prefix.", "HDFS metric source.", "Spark metric source.", "YARN metric source.", "Spark History Server metric source.", "Hiveserver2 metric source.", "hivemetastore metric source", "flink metric source"], "type": "string"}}, "type": "object"}, "NamespacedGkeDeploymentTarget": {"deprecated": true, "description": "Deprecated. Used only for the deprecated beta. A full, namespace-isolated deployment target for an existing GKE cluster.", "id": "NamespacedGkeDeploymentTarget", "properties": {"clusterNamespace": {"description": "Optional. A namespace within the GKE cluster to deploy into.", "type": "string"}, "targetGkeCluster": {"description": "Optional. The target GKE cluster to deploy to. Format: 'projects/{project}/locations/{location}/clusters/{cluster_id}'", "type": "string"}}, "type": "object"}, "NativeBuildInfoUiData": {"id": "NativeBuildInfoUiData", "properties": {"buildClass": {"description": "Optional. Build class of Native.", "type": "string"}, "buildInfo": {"description": "Optional. Build related details.", "items": {"$ref": "BuildInfo"}, "type": "array"}}, "type": "object"}, "NativeSqlExecutionUiData": {"description": "Native SQL Execution Data", "id": "NativeSqlExecutionUiData", "properties": {"description": {"description": "Optional. Description of the execution.", "type": "string"}, "executionId": {"description": "Required. Execution ID of the Native SQL Execution.", "format": "int64", "type": "string"}, "fallbackDescription": {"description": "Optional. Description of the fallback.", "type": "string"}, "fallbackNodeToReason": {"description": "Optional. Fallback node to reason.", "items": {"$ref": "FallbackReason"}, "type": "array"}, "numFallbackNodes": {"description": "Optional. Number of nodes fallen back to Spark.", "format": "int32", "type": "integer"}, "numNativeNodes": {"description": "Optional. Number of nodes in Native.", "format": "int32", "type": "integer"}}, "type": "object"}, "NodeGroup": {"description": "Dataproc Node Group. The Dataproc NodeGroup resource is not related to the Dataproc NodeGroupAffinity resource.", "id": "NodeGroup", "properties": {"labels": {"additionalProperties": {"type": "string"}, "description": "Optional. Node group labels. Label keys must consist of from 1 to 63 characters and conform to RFC 1035 (https://www.ietf.org/rfc/rfc1035.txt). Label values can be empty. If specified, they must consist of from 1 to 63 characters and conform to RFC 1035 (https://www.ietf.org/rfc/rfc1035.txt). The node group must have no more than 32 labels.", "type": "object"}, "name": {"description": "The Node group resource name (https://aip.dev/122).", "type": "string"}, "nodeGroupConfig": {"$ref": "InstanceGroupConfig", "description": "Optional. The node group instance group configuration."}, "roles": {"description": "Required. Node group roles.", "items": {"enum": ["ROLE_UNSPECIFIED", "DRIVER"], "enumDescriptions": ["Required unspecified role.", "Job drivers run on the node pool."], "type": "string"}, "type": "array"}}, "type": "object"}, "NodeGroupAffinity": {"description": "Node Group Affinity for clusters using sole-tenant node groups. The Dataproc NodeGroupAffinity resource is not related to the Dataproc NodeGroup resource.", "id": "NodeGroupAffinity", "properties": {"nodeGroupUri": {"description": "Required. The URI of a sole-tenant node group resource (https://cloud.google.com/compute/docs/reference/rest/v1/nodeGroups) that the cluster will be created on.A full URL, partial URI, or node group name are valid. Examples: https://www.googleapis.com/compute/v1/projects/[project_id]/zones/[zone]/nodeGroups/node-group-1 projects/[project_id]/zones/[zone]/nodeGroups/node-group-1 node-group-1", "type": "string"}}, "type": "object"}, "NodeGroupOperationMetadata": {"description": "Metadata describing the node group operation.", "id": "NodeGroupOperationMetadata", "properties": {"clusterUuid": {"description": "Output only. Cluster UUID associated with the node group operation.", "readOnly": true, "type": "string"}, "description": {"description": "Output only. Short description of operation.", "readOnly": true, "type": "string"}, "labels": {"additionalProperties": {"type": "string"}, "description": "Output only. Labels associated with the operation.", "readOnly": true, "type": "object"}, "nodeGroupId": {"description": "Output only. Node group ID for the operation.", "readOnly": true, "type": "string"}, "operationType": {"description": "The operation type.", "enum": ["NODE_GROUP_OPERATION_TYPE_UNSPECIFIED", "CREATE", "UPDATE", "DELETE", "RESIZE", "REPAIR", "UPDATE_LABELS", "START", "STOP", "UPDATE_METADATA_CONFIG"], "enumDescriptions": ["Node group operation type is unknown.", "Create node group operation type.", "Update node group operation type.", "Delete node group operation type.", "Resize node group operation type.", "Repair node group operation type.", "Update node group label operation type.", "Start node group operation type.", "Stop node group operation type.", "This operation type is used to update the metadata config of a node group. We update the metadata of the VMs in the node group and await for intended config change to be completed at the node group level. Currently, only the identity config update is supported."], "type": "string"}, "status": {"$ref": "ClusterOperationStatus", "description": "Output only. Current operation status.", "readOnly": true}, "statusHistory": {"description": "Output only. The previous operation status.", "items": {"$ref": "ClusterOperationStatus"}, "readOnly": true, "type": "array"}, "warnings": {"description": "Output only. Errors encountered during operation execution.", "items": {"type": "string"}, "readOnly": true, "type": "array"}}, "type": "object"}, "NodeInitializationAction": {"description": "Specifies an executable to run on a fully configured node and a timeout period for executable completion.", "id": "NodeInitializationAction", "properties": {"executableFile": {"description": "Required. Cloud Storage URI of executable file.", "type": "string"}, "executionTimeout": {"description": "Optional. Amount of time executable has to complete. Default is 10 minutes (see JSON representation of Duration (https://developers.google.com/protocol-buffers/docs/proto3#json)).Cluster creation fails with an explanatory error message (the name of the executable that caused the error and the exceeded timeout period) if the executable is not completed at end of the timeout period.", "format": "google-duration", "type": "string"}}, "type": "object"}, "NodePool": {"description": "indicating a list of workers of same type", "id": "NodePool", "properties": {"id": {"description": "Required. A unique id of the node pool. Primary and Secondary workers can be specified using special reserved ids PRIMARY_WORKER_POOL and SECONDARY_WORKER_POOL respectively. Aux node pools can be referenced using corresponding pool id.", "type": "string"}, "instanceNames": {"description": "Name of instances to be repaired. These instances must belong to specified node pool.", "items": {"type": "string"}, "type": "array"}, "repairAction": {"description": "Required. Repair action to take on specified resources of the node pool.", "enum": ["REPAIR_ACTION_UNSPECIFIED", "DELETE"], "enumDescriptions": ["No action will be taken by default.", "delete the specified list of nodes."], "type": "string"}}, "type": "object"}, "Operation": {"description": "This resource represents a long-running operation that is the result of a network API call.", "id": "Operation", "properties": {"done": {"description": "If the value is false, it means the operation is still in progress. If true, the operation is completed, and either error or response is available.", "type": "boolean"}, "error": {"$ref": "Status", "description": "The error result of the operation in case of failure or cancellation."}, "metadata": {"additionalProperties": {"description": "Properties of the object. Contains field @type with type URL.", "type": "any"}, "description": "Service-specific metadata associated with the operation. It typically contains progress information and common metadata such as create time. Some services might not provide such metadata. Any method that returns a long-running operation should document the metadata type, if any.", "type": "object"}, "name": {"description": "The server-assigned name, which is only unique within the same service that originally returns it. If you use the default HTTP mapping, the name should be a resource name ending with operations/{unique_id}.", "type": "string"}, "response": {"additionalProperties": {"description": "Properties of the object. Contains field @type with type URL.", "type": "any"}, "description": "The normal, successful response of the operation. If the original method returns no data on success, such as Delete, the response is google.protobuf.Empty. If the original method is standard Get/Create/Update, the response should be the resource. For other methods, the response should have the type XxxResponse, where Xxx is the original method name. For example, if the original method name is TakeSnapshot(), the inferred response type is TakeSnapshotResponse.", "type": "object"}}, "type": "object"}, "OrderedJob": {"description": "A job executed by the workflow.", "id": "Ordered<PERSON>ob", "properties": {"flinkJob": {"$ref": "FlinkJob", "description": "Optional. Job is a Flink job."}, "hadoopJob": {"$ref": "<PERSON><PERSON><PERSON><PERSON>", "description": "Optional. Job is a <PERSON><PERSON> job."}, "hiveJob": {"$ref": "HiveJob", "description": "Optional. Job is a Hive job."}, "labels": {"additionalProperties": {"type": "string"}, "description": "Optional. The labels to associate with this job.Label keys must be between 1 and 63 characters long, and must conform to the following regular expression: \\p{Ll}\\p{Lo}{0,62}Label values must be between 1 and 63 characters long, and must conform to the following regular expression: \\p{Ll}\\p{Lo}\\p{N}_-{0,63}No more than 32 labels can be associated with a given job.", "type": "object"}, "pigJob": {"$ref": "<PERSON><PERSON><PERSON>", "description": "Optional. Job is a Pig job."}, "prerequisiteStepIds": {"description": "Optional. The optional list of prerequisite job step_ids. If not specified, the job will start at the beginning of workflow.", "items": {"type": "string"}, "type": "array"}, "prestoJob": {"$ref": "PrestoJob", "description": "Optional. Job is a Presto job."}, "pysparkJob": {"$ref": "PySparkJob", "description": "Optional. Job is a PySpark job."}, "scheduling": {"$ref": "JobScheduling", "description": "Optional. Job scheduling configuration."}, "sparkJob": {"$ref": "SparkJob", "description": "Optional. Job is a Spark job."}, "sparkRJob": {"$ref": "SparkRJob", "description": "Optional. Job is a SparkR job."}, "sparkSqlJob": {"$ref": "SparkSqlJob", "description": "Optional. Job is a SparkSql job."}, "stepId": {"description": "Required. The step id. The id must be unique among all jobs within the template.The step id is used as prefix for job id, as job goog-dataproc-workflow-step-id label, and in prerequisiteStepIds field from other steps.The id must contain only letters (a-z, A-Z), numbers (0-9), underscores (_), and hyphens (-). Cannot begin or end with underscore or hyphen. Must consist of between 3 and 50 characters.", "type": "string"}, "trinoJob": {"$ref": "TrinoJob", "description": "Optional. Job is a Trino job."}}, "type": "object"}, "OutputMetrics": {"description": "Metrics about the data written by the task.", "id": "OutputMetrics", "properties": {"bytesWritten": {"format": "int64", "type": "string"}, "recordsWritten": {"format": "int64", "type": "string"}}, "type": "object"}, "OutputQuantileMetrics": {"id": "OutputQuantileMetrics", "properties": {"bytesWritten": {"$ref": "Quantiles"}, "recordsWritten": {"$ref": "Quantiles"}}, "type": "object"}, "ParameterValidation": {"description": "Configuration for parameter validation.", "id": "ParameterValidation", "properties": {"regex": {"$ref": "RegexValidation", "description": "Validation based on regular expressions."}, "values": {"$ref": "ValueValidation", "description": "Validation based on a list of allowed values."}}, "type": "object"}, "PeripheralsConfig": {"description": "Auxiliary services configuration for a workload.", "id": "PeripheralsConfig", "properties": {"metastoreService": {"description": "Optional. Resource name of an existing Dataproc Metastore service.Example: projects/[project_id]/locations/[region]/services/[service_id]", "type": "string"}, "sparkHistoryServerConfig": {"$ref": "SparkHistoryServerConfig", "description": "Optional. The Spark History Server configuration for the workload."}}, "type": "object"}, "PigJob": {"description": "A Dataproc job for running Apache Pig (https://pig.apache.org/) queries on YARN.", "id": "<PERSON><PERSON><PERSON>", "properties": {"continueOnFailure": {"description": "Optional. Whether to continue executing queries if a query fails. The default value is false. Setting to true can be useful when executing independent parallel queries.", "type": "boolean"}, "jarFileUris": {"description": "Optional. HCFS URIs of jar files to add to the CLASSPATH of the Pig Client and Hadoop MapReduce (MR) tasks. Can contain Pig UDFs.", "items": {"type": "string"}, "type": "array"}, "loggingConfig": {"$ref": "LoggingConfig", "description": "Optional. The runtime log config for job execution."}, "properties": {"additionalProperties": {"type": "string"}, "description": "Optional. A mapping of property names to values, used to configure Pig. Properties that conflict with values set by the Dataproc API might be overwritten. Can include properties set in /etc/hadoop/conf/*-site.xml, /etc/pig/conf/pig.properties, and classes in user code.", "type": "object"}, "queryFileUri": {"description": "The HCFS URI of the script that contains the Pig queries.", "type": "string"}, "queryList": {"$ref": "QueryList", "description": "A list of queries."}, "scriptVariables": {"additionalProperties": {"type": "string"}, "description": "Optional. Mapping of query variable names to values (equivalent to the Pig command: name=[value]).", "type": "object"}}, "type": "object"}, "Policy": {"description": "An Identity and Access Management (IAM) policy, which specifies access controls for Google Cloud resources.A Policy is a collection of bindings. A binding binds one or more members, or principals, to a single role. Principals can be user accounts, service accounts, Google groups, and domains (such as G Suite). A role is a named list of permissions; each role can be an IAM predefined role or a user-created custom role.For some types of Google Cloud resources, a binding can also specify a condition, which is a logical expression that allows access to a resource only if the expression evaluates to true. A condition can add constraints based on attributes of the request, the resource, or both. To learn which resources support conditions in their IAM policies, see the IAM documentation (https://cloud.google.com/iam/help/conditions/resource-policies).JSON example: { \"bindings\": [ { \"role\": \"roles/resourcemanager.organizationAdmin\", \"members\": [ \"user:<EMAIL>\", \"group:<EMAIL>\", \"domain:google.com\", \"serviceAccount:<EMAIL>\" ] }, { \"role\": \"roles/resourcemanager.organizationViewer\", \"members\": [ \"user:<EMAIL>\" ], \"condition\": { \"title\": \"expirable access\", \"description\": \"Does not grant access after Sep 2020\", \"expression\": \"request.time < timestamp('2020-10-01T00:00:00.000Z')\", } } ], \"etag\": \"BwWWja0YfJA=\", \"version\": 3 } YAML example: bindings: - members: - user:<EMAIL> - group:<EMAIL> - domain:google.com - serviceAccount:<EMAIL> role: roles/resourcemanager.organizationAdmin - members: - user:<EMAIL> role: roles/resourcemanager.organizationViewer condition: title: expirable access description: Does not grant access after Sep 2020 expression: request.time < timestamp('2020-10-01T00:00:00.000Z') etag: BwWWja0YfJA= version: 3 For a description of IAM and its features, see the IAM documentation (https://cloud.google.com/iam/docs/).", "id": "Policy", "properties": {"bindings": {"description": "Associates a list of members, or principals, with a role. Optionally, may specify a condition that determines how and when the bindings are applied. Each of the bindings must contain at least one principal.The bindings in a Policy can refer to up to 1,500 principals; up to 250 of these principals can be Google groups. Each occurrence of a principal counts towards these limits. For example, if the bindings grant 50 different roles to user:<EMAIL>, and not to any other principal, then you can add another 1,450 principals to the bindings in the Policy.", "items": {"$ref": "Binding"}, "type": "array"}, "etag": {"description": "etag is used for optimistic concurrency control as a way to help prevent simultaneous updates of a policy from overwriting each other. It is strongly suggested that systems make use of the etag in the read-modify-write cycle to perform policy updates in order to avoid race conditions: An etag is returned in the response to getIamPolicy, and systems are expected to put that etag in the request to setIamPolicy to ensure that their change will be applied to the same version of the policy.Important: If you use IAM Conditions, you must include the etag field whenever you call setIamPolicy. If you omit this field, then IAM allows you to overwrite a version 3 policy with a version 1 policy, and all of the conditions in the version 3 policy are lost.", "format": "byte", "type": "string"}, "version": {"description": "Specifies the format of the policy.Valid values are 0, 1, and 3. Requests that specify an invalid value are rejected.Any operation that affects conditional role bindings must specify version 3. This requirement applies to the following operations: Getting a policy that includes a conditional role binding Adding a conditional role binding to a policy Changing a conditional role binding in a policy Removing any role binding, with or without a condition, from a policy that includes conditionsImportant: If you use IAM Conditions, you must include the etag field whenever you call setIamPolicy. If you omit this field, then IAM allows you to overwrite a version 3 policy with a version 1 policy, and all of the conditions in the version 3 policy are lost.If a policy does not include any conditions, operations on that policy may specify any valid version or leave the field unset.To learn which resources support conditions in their IAM policies, see the IAM documentation (https://cloud.google.com/iam/help/conditions/resource-policies).", "format": "int32", "type": "integer"}}, "type": "object"}, "PoolData": {"description": "Pool Data", "id": "PoolData", "properties": {"name": {"type": "string"}, "stageIds": {"items": {"format": "int64", "type": "string"}, "type": "array"}}, "type": "object"}, "PrestoJob": {"description": "A Dataproc job for running Presto (https://prestosql.io/) queries. IMPORTANT: The Dataproc Presto Optional Component (https://cloud.google.com/dataproc/docs/concepts/components/presto) must be enabled when the cluster is created to submit a Presto job to the cluster.", "id": "PrestoJob", "properties": {"clientTags": {"description": "Optional. Presto client tags to attach to this query", "items": {"type": "string"}, "type": "array"}, "continueOnFailure": {"description": "Optional. Whether to continue executing queries if a query fails. The default value is false. Setting to true can be useful when executing independent parallel queries.", "type": "boolean"}, "loggingConfig": {"$ref": "LoggingConfig", "description": "Optional. The runtime log config for job execution."}, "outputFormat": {"description": "Optional. The format in which query output will be displayed. See the Presto documentation for supported output formats", "type": "string"}, "properties": {"additionalProperties": {"type": "string"}, "description": "Optional. A mapping of property names to values. Used to set Presto session properties (https://prestodb.io/docs/current/sql/set-session.html) Equivalent to using the --session flag in the Presto CLI", "type": "object"}, "queryFileUri": {"description": "The HCFS URI of the script that contains SQL queries.", "type": "string"}, "queryList": {"$ref": "QueryList", "description": "A list of queries."}}, "type": "object"}, "ProcessSummary": {"description": "Process Summary", "id": "ProcessSummary", "properties": {"addTime": {"format": "google-datetime", "type": "string"}, "hostPort": {"type": "string"}, "isActive": {"type": "boolean"}, "processId": {"type": "string"}, "processLogs": {"additionalProperties": {"type": "string"}, "type": "object"}, "removeTime": {"format": "google-datetime", "type": "string"}, "totalCores": {"format": "int32", "type": "integer"}}, "type": "object"}, "PropertiesInfo": {"description": "Properties of the workload organized by origin.", "id": "PropertiesInfo", "properties": {"autotuningProperties": {"additionalProperties": {"$ref": "ValueInfo"}, "description": "Output only. Properties set by autotuning engine.", "readOnly": true, "type": "object"}}, "type": "object"}, "ProvisioningModelMix": {"description": "Defines how Dataproc should create VMs with a mixture of provisioning models.", "id": "ProvisioningModelMix", "properties": {"standardCapacityBase": {"description": "Optional. The base capacity that will always use Standard VMs to avoid risk of more preemption than the minimum capacity you need. Dataproc will create only standard VMs until it reaches standard_capacity_base, then it will start using standard_capacity_percent_above_base to mix Spot with Standard VMs. eg. If 15 instances are requested and standard_capacity_base is 5, Dataproc will create 5 standard VMs and then start mixing spot and standard VMs for remaining 10 instances.", "format": "int32", "type": "integer"}, "standardCapacityPercentAboveBase": {"description": "Optional. The percentage of target capacity that should use Standard VM. The remaining percentage will use Spot VMs. The percentage applies only to the capacity above standard_capacity_base. eg. If 15 instances are requested and standard_capacity_base is 5 and standard_capacity_percent_above_base is 30, Dataproc will create 5 standard VMs and then start mixing spot and standard VMs for remaining 10 instances. The mix will be 30% standard and 70% spot.", "format": "int32", "type": "integer"}}, "type": "object"}, "PyPiRepositoryConfig": {"description": "Configuration for PyPi repository", "id": "PyPiRepositoryConfig", "properties": {"pypiRepository": {"description": "Optional. PyPi repository address", "type": "string"}}, "type": "object"}, "PySparkBatch": {"description": "A configuration for running an Apache PySpark (https://spark.apache.org/docs/latest/api/python/getting_started/quickstart.html) batch workload.", "id": "PySparkBatch", "properties": {"archiveUris": {"description": "Optional. HCFS URIs of archives to be extracted into the working directory of each executor. Supported file types: .jar, .tar, .tar.gz, .tgz, and .zip.", "items": {"type": "string"}, "type": "array"}, "args": {"description": "Optional. The arguments to pass to the driver. Do not include arguments that can be set as batch properties, such as --conf, since a collision can occur that causes an incorrect batch submission.", "items": {"type": "string"}, "type": "array"}, "fileUris": {"description": "Optional. HCFS URIs of files to be placed in the working directory of each executor.", "items": {"type": "string"}, "type": "array"}, "jarFileUris": {"description": "Optional. HCFS URIs of jar files to add to the classpath of the Spark driver and tasks.", "items": {"type": "string"}, "type": "array"}, "mainPythonFileUri": {"description": "Required. The HCFS URI of the main Python file to use as the Spark driver. Must be a .py file.", "type": "string"}, "pythonFileUris": {"description": "Optional. HCFS file URIs of Python files to pass to the PySpark framework. Supported file types: .py, .egg, and .zip.", "items": {"type": "string"}, "type": "array"}}, "type": "object"}, "PySparkJob": {"description": "A Dataproc job for running Apache PySpark (https://spark.apache.org/docs/latest/api/python/index.html#pyspark-overview) applications on YARN.", "id": "PySparkJob", "properties": {"archiveUris": {"description": "Optional. HCFS URIs of archives to be extracted into the working directory of each executor. Supported file types: .jar, .tar, .tar.gz, .tgz, and .zip.", "items": {"type": "string"}, "type": "array"}, "args": {"description": "Optional. The arguments to pass to the driver. Do not include arguments, such as --conf, that can be set as job properties, since a collision may occur that causes an incorrect job submission.", "items": {"type": "string"}, "type": "array"}, "fileUris": {"description": "Optional. HCFS URIs of files to be placed in the working directory of each executor. Useful for naively parallel tasks.", "items": {"type": "string"}, "type": "array"}, "jarFileUris": {"description": "Optional. HCFS URIs of jar files to add to the CLASSPATHs of the Python driver and tasks.", "items": {"type": "string"}, "type": "array"}, "loggingConfig": {"$ref": "LoggingConfig", "description": "Optional. The runtime log config for job execution."}, "mainPythonFileUri": {"description": "Required. The HCFS URI of the main Python file to use as the driver. Must be a .py file.", "type": "string"}, "properties": {"additionalProperties": {"type": "string"}, "description": "Optional. A mapping of property names to values, used to configure PySpark. Properties that conflict with values set by the Dataproc API might be overwritten. Can include properties set in /etc/spark/conf/spark-defaults.conf and classes in user code.", "type": "object"}, "pythonFileUris": {"description": "Optional. HCFS file URIs of Python files to pass to the PySpark framework. Supported file types: .py, .egg, and .zip.", "items": {"type": "string"}, "type": "array"}}, "type": "object"}, "Quantiles": {"description": "Quantile metrics data related to Tasks. Units can be seconds, bytes, milliseconds, etc depending on the message type.", "id": "Quantiles", "properties": {"count": {"format": "int64", "type": "string"}, "maximum": {"format": "int64", "type": "string"}, "minimum": {"format": "int64", "type": "string"}, "percentile25": {"format": "int64", "type": "string"}, "percentile50": {"format": "int64", "type": "string"}, "percentile75": {"format": "int64", "type": "string"}, "sum": {"format": "int64", "type": "string"}}, "type": "object"}, "QueryList": {"description": "A list of queries to run on a cluster.", "id": "QueryList", "properties": {"queries": {"description": "Required. The queries to execute. You do not need to end a query expression with a semicolon. Multiple queries can be specified in one string by separating each with a semicolon. Here is an example of a Dataproc API snippet that uses a QueryList to specify a HiveJob: \"hiveJob\": { \"queryList\": { \"queries\": [ \"query1\", \"query2\", \"query3;query4\", ] } } ", "items": {"type": "string"}, "type": "array"}}, "type": "object"}, "RddDataDistribution": {"description": "Details about RDD usage.", "id": "RddDataDistribution", "properties": {"address": {"type": "string"}, "diskUsed": {"format": "int64", "type": "string"}, "memoryRemaining": {"format": "int64", "type": "string"}, "memoryUsed": {"format": "int64", "type": "string"}, "offHeapMemoryRemaining": {"format": "int64", "type": "string"}, "offHeapMemoryUsed": {"format": "int64", "type": "string"}, "onHeapMemoryRemaining": {"format": "int64", "type": "string"}, "onHeapMemoryUsed": {"format": "int64", "type": "string"}}, "type": "object"}, "RddOperationCluster": {"description": "A grouping of nodes representing higher level constructs (stage, job etc.).", "id": "RddOperationCluster", "properties": {"childClusters": {"items": {"$ref": "RddOperationCluster"}, "type": "array"}, "childNodes": {"items": {"$ref": "RddOperationNode"}, "type": "array"}, "name": {"type": "string"}, "rddClusterId": {"type": "string"}}, "type": "object"}, "RddOperationEdge": {"description": "A directed edge representing dependency between two RDDs.", "id": "RddOperationEdge", "properties": {"fromId": {"format": "int32", "type": "integer"}, "toId": {"format": "int32", "type": "integer"}}, "type": "object"}, "RddOperationGraph": {"description": "Graph representing RDD dependencies. Consists of edges and a root cluster.", "id": "RddOperationGraph", "properties": {"edges": {"items": {"$ref": "RddOperationEdge"}, "type": "array"}, "incomingEdges": {"items": {"$ref": "RddOperationEdge"}, "type": "array"}, "outgoingEdges": {"items": {"$ref": "RddOperationEdge"}, "type": "array"}, "rootCluster": {"$ref": "RddOperationCluster"}, "stageId": {"format": "int64", "type": "string"}}, "type": "object"}, "RddOperationNode": {"description": "A node in the RDD operation graph. Corresponds to a single RDD.", "id": "RddOperationNode", "properties": {"barrier": {"type": "boolean"}, "cached": {"type": "boolean"}, "callsite": {"type": "string"}, "name": {"type": "string"}, "nodeId": {"format": "int32", "type": "integer"}, "outputDeterministicLevel": {"enum": ["DETERMINISTIC_LEVEL_UNSPECIFIED", "DETERMINISTIC_LEVEL_DETERMINATE", "DETERMINISTIC_LEVEL_UNORDERED", "DETERMINISTIC_LEVEL_INDETERMINATE"], "enumDescriptions": ["", "", "", ""], "type": "string"}}, "type": "object"}, "RddPartitionInfo": {"description": "Information about RDD partitions.", "id": "RddPartitionInfo", "properties": {"blockName": {"type": "string"}, "diskUsed": {"format": "int64", "type": "string"}, "executors": {"items": {"type": "string"}, "type": "array"}, "memoryUsed": {"format": "int64", "type": "string"}, "storageLevel": {"type": "string"}}, "type": "object"}, "RddStorageInfo": {"description": "Overall data about RDD storage.", "id": "RddStorageInfo", "properties": {"dataDistribution": {"items": {"$ref": "RddDataDistribution"}, "type": "array"}, "diskUsed": {"format": "int64", "type": "string"}, "memoryUsed": {"format": "int64", "type": "string"}, "name": {"type": "string"}, "numCachedPartitions": {"format": "int32", "type": "integer"}, "numPartitions": {"format": "int32", "type": "integer"}, "partitions": {"items": {"$ref": "RddPartitionInfo"}, "type": "array"}, "rddStorageId": {"format": "int32", "type": "integer"}, "storageLevel": {"type": "string"}}, "type": "object"}, "RegexValidation": {"description": "Validation based on regular expressions.", "id": "RegexValidation", "properties": {"regexes": {"description": "Required. RE2 regular expressions used to validate the parameter's value. The value must match the regex in its entirety (substring matches are not sufficient).", "items": {"type": "string"}, "type": "array"}}, "type": "object"}, "RepairClusterRequest": {"description": "A request to repair a cluster.", "id": "RepairClusterRequest", "properties": {"cluster": {"$ref": "ClusterToRepair", "description": "Optional. Cluster to be repaired"}, "clusterUuid": {"description": "Optional. Specifying the cluster_uuid means the RPC will fail (with error NOT_FOUND) if a cluster with the specified UUID does not exist.", "type": "string"}, "gracefulDecommissionTimeout": {"description": "Optional. Timeout for graceful YARN decommissioning. Graceful decommissioning facilitates the removal of cluster nodes without interrupting jobs in progress. The timeout specifies the amount of time to wait for jobs finish before forcefully removing nodes. The default timeout is 0 for forceful decommissioning, and the maximum timeout period is 1 day. (see JSON Mapping—Duration (https://developers.google.com/protocol-buffers/docs/proto3#json)).graceful_decommission_timeout is supported in Dataproc image versions 1.2+.", "format": "google-duration", "type": "string"}, "nodePools": {"description": "Optional. Node pools and corresponding repair action to be taken. All node pools should be unique in this request. i.e. Multiple entries for the same node pool id are not allowed.", "items": {"$ref": "NodePool"}, "type": "array"}, "parentOperationId": {"description": "Optional. operation id of the parent operation sending the repair request", "type": "string"}, "requestId": {"description": "Optional. A unique ID used to identify the request. If the server receives two RepairClusterRequests with the same ID, the second request is ignored, and the first google.longrunning.Operation created and stored in the backend is returned.Recommendation: Set this value to a UUID (https://en.wikipedia.org/wiki/Universally_unique_identifier).The ID must contain only letters (a-z, A-Z), numbers (0-9), underscores (_), and hyphens (-). The maximum length is 40 characters.", "type": "string"}}, "type": "object"}, "RepairNodeGroupRequest": {"id": "RepairNodeGroupRequest", "properties": {"instanceNames": {"description": "Required. Name of instances to be repaired. These instances must belong to specified node pool.", "items": {"type": "string"}, "type": "array"}, "repairAction": {"description": "Required. Repair action to take on specified resources of the node pool.", "enum": ["REPAIR_ACTION_UNSPECIFIED", "REPLACE"], "enumDescriptions": ["No action will be taken by default.", "replace the specified list of nodes."], "type": "string"}, "requestId": {"description": "Optional. A unique ID used to identify the request. If the server receives two RepairNodeGroupRequest with the same ID, the second request is ignored and the first google.longrunning.Operation created and stored in the backend is returned.Recommendation: Set this value to a UUID (https://en.wikipedia.org/wiki/Universally_unique_identifier).The ID must contain only letters (a-z, A-Z), numbers (0-9), underscores (_), and hyphens (-). The maximum length is 40 characters.", "type": "string"}}, "type": "object"}, "RepositoryConfig": {"description": "Configuration for dependency repositories", "id": "RepositoryConfig", "properties": {"pypiRepositoryConfig": {"$ref": "PyPiRepositoryConfig", "description": "Optional. Configuration for PyPi repository."}}, "type": "object"}, "ReservationAffinity": {"description": "Reservation Affinity for consuming Zonal reservation.", "id": "ReservationAffinity", "properties": {"consumeReservationType": {"description": "Optional. Type of reservation to consume", "enum": ["TYPE_UNSPECIFIED", "NO_RESERVATION", "ANY_RESERVATION", "SPECIFIC_RESERVATION"], "enumDescriptions": ["", "Do not consume from any allocated capacity.", "Consume any reservation available.", "Must consume from a specific reservation. Must specify key value fields for specifying the reservations."], "type": "string"}, "key": {"description": "Optional. Corresponds to the label key of reservation resource.", "type": "string"}, "values": {"description": "Optional. Corresponds to the label values of reservation resource.", "items": {"type": "string"}, "type": "array"}}, "type": "object"}, "ResizeNodeGroupRequest": {"description": "A request to resize a node group.", "id": "ResizeNodeGroupRequest", "properties": {"gracefulDecommissionTimeout": {"description": "Optional. Timeout for graceful YARN decommissioning. Graceful decommissioning (https://cloud.google.com/dataproc/docs/concepts/configuring-clusters/scaling-clusters#graceful_decommissioning) allows the removal of nodes from the Compute Engine node group without interrupting jobs in progress. This timeout specifies how long to wait for jobs in progress to finish before forcefully removing nodes (and potentially interrupting jobs). Default timeout is 0 (for forceful decommission), and the maximum allowed timeout is 1 day. (see JSON representation of Duration (https://developers.google.com/protocol-buffers/docs/proto3#json)).Only supported on Dataproc image versions 1.2 and higher.", "format": "google-duration", "type": "string"}, "parentOperationId": {"description": "Optional. operation id of the parent operation sending the resize request", "type": "string"}, "requestId": {"description": "Optional. A unique ID used to identify the request. If the server receives two ResizeNodeGroupRequest (https://cloud.google.com/dataproc/docs/reference/rpc/google.cloud.dataproc.v1#google.cloud.dataproc.v1.ResizeNodeGroupRequests) with the same ID, the second request is ignored and the first google.longrunning.Operation created and stored in the backend is returned.Recommendation: Set this value to a UUID (https://en.wikipedia.org/wiki/Universally_unique_identifier).The ID must contain only letters (a-z, A-Z), numbers (0-9), underscores (_), and hyphens (-). The maximum length is 40 characters.", "type": "string"}, "size": {"description": "Required. The number of running instances for the node group to maintain. The group adds or removes instances to maintain the number of instances specified by this parameter.", "format": "int32", "type": "integer"}}, "type": "object"}, "ResourceInformation": {"id": "ResourceInformation", "properties": {"addresses": {"items": {"type": "string"}, "type": "array"}, "name": {"type": "string"}}, "type": "object"}, "ResourceProfileInfo": {"description": "Resource profile that contains information about all the resources required by executors and tasks.", "id": "ResourceProfileInfo", "properties": {"executorResources": {"additionalProperties": {"$ref": "ExecutorResourceRequest"}, "type": "object"}, "resourceProfileId": {"format": "int32", "type": "integer"}, "taskResources": {"additionalProperties": {"$ref": "TaskResourceRequest"}, "type": "object"}}, "type": "object"}, "RuntimeConfig": {"description": "Runtime configuration for a workload.", "id": "RuntimeConfig", "properties": {"autotuningConfig": {"$ref": "AutotuningConfig", "description": "Optional. Autotuning configuration of the workload."}, "cohort": {"description": "Optional. Cohort identifier. Identifies families of the workloads having the same shape, e.g. daily ETL jobs.", "type": "string"}, "containerImage": {"description": "Optional. Optional custom container image for the job runtime environment. If not specified, a default container image will be used.", "type": "string"}, "properties": {"additionalProperties": {"type": "string"}, "description": "Optional. A mapping of property names to values, which are used to configure workload execution.", "type": "object"}, "repositoryConfig": {"$ref": "RepositoryConfig", "description": "Optional. Dependency repository configuration."}, "version": {"description": "Optional. Version of the batch runtime.", "type": "string"}}, "type": "object"}, "RuntimeInfo": {"description": "Runtime information about workload execution.", "id": "RuntimeInfo", "properties": {"approximateUsage": {"$ref": "UsageMetrics", "description": "Output only. Approximate workload resource usage, calculated when the workload completes (see Dataproc Serverless pricing (https://cloud.google.com/dataproc-serverless/pricing)).Note: This metric calculation may change in the future, for example, to capture cumulative workload resource consumption during workload execution (see the Dataproc Serverless release notes (https://cloud.google.com/dataproc-serverless/docs/release-notes) for announcements, changes, fixes and other Dataproc developments).", "readOnly": true}, "currentUsage": {"$ref": "UsageSnapshot", "description": "Output only. Snapshot of current workload resource usage.", "readOnly": true}, "diagnosticOutputUri": {"description": "Output only. A URI pointing to the location of the diagnostics tarball.", "readOnly": true, "type": "string"}, "endpoints": {"additionalProperties": {"type": "string"}, "description": "Output only. Map of remote access endpoints (such as web interfaces and APIs) to their URIs.", "readOnly": true, "type": "object"}, "outputUri": {"description": "Output only. A URI pointing to the location of the stdout and stderr of the workload.", "readOnly": true, "type": "string"}, "propertiesInfo": {"$ref": "PropertiesInfo", "description": "Optional. Properties of the workload organized by origin."}}, "type": "object"}, "SearchSessionSparkApplicationExecutorStageSummaryResponse": {"description": "List of Executors associated with a Spark Application Stage.", "id": "SearchSessionSparkApplicationExecutorStageSummaryResponse", "properties": {"nextPageToken": {"description": "This token is included in the response if there are more results to fetch. To fetch additional results, provide this value as the page_token in a subsequent SearchSessionSparkApplicationExecutorStageSummaryRequest.", "type": "string"}, "sparkApplicationStageExecutors": {"description": "Details about executors used by the application stage.", "items": {"$ref": "ExecutorStageSummary"}, "type": "array"}}, "type": "object"}, "SearchSessionSparkApplicationExecutorsResponse": {"description": "List of Executors associated with a Spark Application.", "id": "SearchSessionSparkApplicationExecutorsResponse", "properties": {"nextPageToken": {"description": "This token is included in the response if there are more results to fetch. To fetch additional results, provide this value as the page_token in a subsequent SearchSessionSparkApplicationExecutorsRequest.", "type": "string"}, "sparkApplicationExecutors": {"description": "Details about executors used by the application.", "items": {"$ref": "Executor<PERSON>ummary"}, "type": "array"}}, "type": "object"}, "SearchSessionSparkApplicationJobsResponse": {"description": "A list of Jobs associated with a Spark Application.", "id": "SearchSessionSparkApplicationJobsResponse", "properties": {"nextPageToken": {"description": "This token is included in the response if there are more results to fetch. To fetch additional results, provide this value as the page_token in a subsequent SearchSessionSparkApplicationJobsRequest.", "type": "string"}, "sparkApplicationJobs": {"description": "Output only. Data corresponding to a spark job.", "items": {"$ref": "JobData"}, "readOnly": true, "type": "array"}}, "type": "object"}, "SearchSessionSparkApplicationSqlQueriesResponse": {"description": "List of all queries for a Spark Application.", "id": "SearchSessionSparkApplicationSqlQueriesResponse", "properties": {"nextPageToken": {"description": "This token is included in the response if there are more results to fetch. To fetch additional results, provide this value as the page_token in a subsequent SearchSessionSparkApplicationSqlQueriesRequest.", "type": "string"}, "sparkApplicationSqlQueries": {"description": "Output only. SQL Execution Data", "items": {"$ref": "SqlExecutionUiData"}, "readOnly": true, "type": "array"}}, "type": "object"}, "SearchSessionSparkApplicationStageAttemptTasksResponse": {"description": "List of tasks for a stage of a Spark Application", "id": "SearchSessionSparkApplicationStageAttemptTasksResponse", "properties": {"nextPageToken": {"description": "This token is included in the response if there are more results to fetch. To fetch additional results, provide this value as the page_token in a subsequent SearchSessionSparkApplicationStageAttemptTasksRequest.", "type": "string"}, "sparkApplicationStageAttemptTasks": {"description": "Output only. Data corresponding to tasks created by spark.", "items": {"$ref": "TaskData"}, "readOnly": true, "type": "array"}}, "type": "object"}, "SearchSessionSparkApplicationStageAttemptsResponse": {"description": "A list of Stage Attempts for a Stage of a Spark Application.", "id": "SearchSessionSparkApplicationStageAttemptsResponse", "properties": {"nextPageToken": {"description": "This token is included in the response if there are more results to fetch. To fetch additional results, provide this value as the page_token in a subsequent SearchSessionSparkApplicationStageAttemptsRequest.", "type": "string"}, "sparkApplicationStageAttempts": {"description": "Output only. Data corresponding to a stage attempts", "items": {"$ref": "StageData"}, "readOnly": true, "type": "array"}}, "type": "object"}, "SearchSessionSparkApplicationStagesResponse": {"description": "A list of stages associated with a Spark Application.", "id": "SearchSessionSparkApplicationStagesResponse", "properties": {"nextPageToken": {"description": "This token is included in the response if there are more results to fetch. To fetch additional results, provide this value as the page_token in a subsequent SearchSessionSparkApplicationStages.", "type": "string"}, "sparkApplicationStages": {"description": "Output only. Data corresponding to a stage.", "items": {"$ref": "StageData"}, "readOnly": true, "type": "array"}}, "type": "object"}, "SearchSessionSparkApplicationsResponse": {"description": "A list of summary of Spark Applications", "id": "SearchSessionSparkApplicationsResponse", "properties": {"nextPageToken": {"description": "This token is included in the response if there are more results to fetch. To fetch additional results, provide this value as the page_token in a subsequent SearchSessionSparkApplicationsRequest.", "type": "string"}, "sparkApplications": {"description": "Output only. High level information corresponding to an application.", "items": {"$ref": "SparkApplication"}, "readOnly": true, "type": "array"}}, "type": "object"}, "SearchSparkApplicationExecutorStageSummaryResponse": {"description": "List of Executors associated with a Spark Application Stage.", "id": "SearchSparkApplicationExecutorStageSummaryResponse", "properties": {"nextPageToken": {"description": "This token is included in the response if there are more results to fetch. To fetch additional results, provide this value as the page_token in a subsequent SearchSparkApplicationExecutorsListRequest.", "type": "string"}, "sparkApplicationStageExecutors": {"description": "Details about executors used by the application stage.", "items": {"$ref": "ExecutorStageSummary"}, "type": "array"}}, "type": "object"}, "SearchSparkApplicationExecutorsResponse": {"description": "List of Executors associated with a Spark Application.", "id": "SearchSparkApplicationExecutorsResponse", "properties": {"nextPageToken": {"description": "This token is included in the response if there are more results to fetch. To fetch additional results, provide this value as the page_token in a subsequent SearchSparkApplicationExecutorsListRequest.", "type": "string"}, "sparkApplicationExecutors": {"description": "Details about executors used by the application.", "items": {"$ref": "Executor<PERSON>ummary"}, "type": "array"}}, "type": "object"}, "SearchSparkApplicationJobsResponse": {"description": "A list of Jobs associated with a Spark Application.", "id": "SearchSparkApplicationJobsResponse", "properties": {"nextPageToken": {"description": "This token is included in the response if there are more results to fetch. To fetch additional results, provide this value as the page_token in a subsequent SearchSparkApplicationJobsRequest.", "type": "string"}, "sparkApplicationJobs": {"description": "Output only. Data corresponding to a spark job.", "items": {"$ref": "JobData"}, "readOnly": true, "type": "array"}}, "type": "object"}, "SearchSparkApplicationSqlQueriesResponse": {"description": "List of all queries for a Spark Application.", "id": "SearchSparkApplicationSqlQueriesResponse", "properties": {"nextPageToken": {"description": "This token is included in the response if there are more results to fetch. To fetch additional results, provide this value as the page_token in a subsequent SearchSparkApplicationSqlQueriesRequest.", "type": "string"}, "sparkApplicationSqlQueries": {"description": "Output only. SQL Execution Data", "items": {"$ref": "SqlExecutionUiData"}, "readOnly": true, "type": "array"}}, "type": "object"}, "SearchSparkApplicationStageAttemptTasksResponse": {"description": "List of tasks for a stage of a Spark Application", "id": "SearchSparkApplicationStageAttemptTasksResponse", "properties": {"nextPageToken": {"description": "This token is included in the response if there are more results to fetch. To fetch additional results, provide this value as the page_token in a subsequent ListSparkApplicationStageAttemptTasksRequest.", "type": "string"}, "sparkApplicationStageAttemptTasks": {"description": "Output only. Data corresponding to tasks created by spark.", "items": {"$ref": "TaskData"}, "readOnly": true, "type": "array"}}, "type": "object"}, "SearchSparkApplicationStageAttemptsResponse": {"description": "A list of Stage Attempts for a Stage of a Spark Application.", "id": "SearchSparkApplicationStageAttemptsResponse", "properties": {"nextPageToken": {"description": "This token is included in the response if there are more results to fetch. To fetch additional results, provide this value as the page_token in a subsequent ListSparkApplicationStageAttemptsRequest.", "type": "string"}, "sparkApplicationStageAttempts": {"description": "Output only. Data corresponding to a stage attempts", "items": {"$ref": "StageData"}, "readOnly": true, "type": "array"}}, "type": "object"}, "SearchSparkApplicationStagesResponse": {"description": "A list of stages associated with a Spark Application.", "id": "SearchSparkApplicationStagesResponse", "properties": {"nextPageToken": {"description": "This token is included in the response if there are more results to fetch. To fetch additional results, provide this value as the page_token in a subsequent SearchSparkApplicationStages.", "type": "string"}, "sparkApplicationStages": {"description": "Output only. Data corresponding to a stage.", "items": {"$ref": "StageData"}, "readOnly": true, "type": "array"}}, "type": "object"}, "SearchSparkApplicationsResponse": {"description": "A list of summary of Spark Applications", "id": "SearchSparkApplicationsResponse", "properties": {"nextPageToken": {"description": "This token is included in the response if there are more results to fetch. To fetch additional results, provide this value as the page_token in a subsequent SearchSparkApplicationsRequest.", "type": "string"}, "sparkApplications": {"description": "Output only. High level information corresponding to an application.", "items": {"$ref": "SparkApplication"}, "readOnly": true, "type": "array"}}, "type": "object"}, "SecurityConfig": {"description": "Security related configuration, including encryption, Kerberos, etc.", "id": "SecurityConfig", "properties": {"identityConfig": {"$ref": "IdentityConfig", "description": "Optional. Identity related configuration, including service account based secure multi-tenancy user mappings."}, "kerberosConfig": {"$ref": "KerberosConfig", "description": "Optional. Kerberos related configuration."}}, "type": "object"}, "Session": {"description": "A representation of a session.", "id": "Session", "properties": {"createTime": {"description": "Output only. The time when the session was created.", "format": "google-datetime", "readOnly": true, "type": "string"}, "creator": {"description": "Output only. The email address of the user who created the session.", "readOnly": true, "type": "string"}, "environmentConfig": {"$ref": "EnvironmentConfig", "description": "Optional. Environment configuration for the session execution."}, "jupyterSession": {"$ref": "JupyterConfig", "description": "Optional. Jupyter session config."}, "labels": {"additionalProperties": {"type": "string"}, "description": "Optional. The labels to associate with the session. Label keys must contain 1 to 63 characters, and must conform to RFC 1035 (https://www.ietf.org/rfc/rfc1035.txt). Label values may be empty, but, if present, must contain 1 to 63 characters, and must conform to RFC 1035 (https://www.ietf.org/rfc/rfc1035.txt). No more than 32 labels can be associated with a session.", "type": "object"}, "name": {"description": "Identifier. The resource name of the session.", "type": "string"}, "runtimeConfig": {"$ref": "RuntimeConfig", "description": "Optional. Runtime configuration for the session execution."}, "runtimeInfo": {"$ref": "RuntimeInfo", "description": "Output only. Runtime information about session execution.", "readOnly": true}, "sessionTemplate": {"description": "Optional. The session template used by the session.Only resource names, including project ID and location, are valid.Example: * https://www.googleapis.com/compute/v1/projects/[project_id]/locations/[dataproc_region]/sessionTemplates/[template_id] * projects/[project_id]/locations/[dataproc_region]/sessionTemplates/[template_id]The template must be in the same project and Dataproc region as the session.", "type": "string"}, "sparkConnectSession": {"$ref": "SparkConnectConfig", "description": "Optional. Spark connect session config."}, "state": {"description": "Output only. A state of the session.", "enum": ["STATE_UNSPECIFIED", "CREATING", "ACTIVE", "TERMINATING", "TERMINATED", "FAILED"], "enumDescriptions": ["The session state is unknown.", "The session is created prior to running.", "The session is running.", "The session is terminating.", "The session is terminated successfully.", "The session is no longer running due to an error."], "readOnly": true, "type": "string"}, "stateHistory": {"description": "Output only. Historical state information for the session.", "items": {"$ref": "SessionStateHistory"}, "readOnly": true, "type": "array"}, "stateMessage": {"description": "Output only. Session state details, such as the failure description if the state is FAILED.", "readOnly": true, "type": "string"}, "stateTime": {"description": "Output only. The time when the session entered the current state.", "format": "google-datetime", "readOnly": true, "type": "string"}, "user": {"description": "Optional. The email address of the user who owns the session.", "type": "string"}, "uuid": {"description": "Output only. A session UUID (Unique Universal Identifier). The service generates this value when it creates the session.", "readOnly": true, "type": "string"}}, "type": "object"}, "SessionOperationMetadata": {"description": "Metadata describing the Session operation.", "id": "SessionOperationMetadata", "properties": {"createTime": {"description": "The time when the operation was created.", "format": "google-datetime", "type": "string"}, "description": {"description": "Short description of the operation.", "type": "string"}, "doneTime": {"description": "The time when the operation was finished.", "format": "google-datetime", "type": "string"}, "labels": {"additionalProperties": {"type": "string"}, "description": "Labels associated with the operation.", "type": "object"}, "operationType": {"description": "The operation type.", "enum": ["SESSION_OPERATION_TYPE_UNSPECIFIED", "CREATE", "TERMINATE", "DELETE"], "enumDescriptions": ["Session operation type is unknown.", "Create Session operation type.", "Terminate Session operation type.", "Delete Session operation type."], "type": "string"}, "session": {"description": "Name of the session for the operation.", "type": "string"}, "sessionUuid": {"description": "Session UUID for the operation.", "type": "string"}, "warnings": {"description": "Warnings encountered during operation execution.", "items": {"type": "string"}, "type": "array"}}, "type": "object"}, "SessionStateHistory": {"description": "Historical state information.", "id": "SessionStateHistory", "properties": {"state": {"description": "Output only. The state of the session at this point in the session history.", "enum": ["STATE_UNSPECIFIED", "CREATING", "ACTIVE", "TERMINATING", "TERMINATED", "FAILED"], "enumDescriptions": ["The session state is unknown.", "The session is created prior to running.", "The session is running.", "The session is terminating.", "The session is terminated successfully.", "The session is no longer running due to an error."], "readOnly": true, "type": "string"}, "stateMessage": {"description": "Output only. Details about the state at this point in the session history.", "readOnly": true, "type": "string"}, "stateStartTime": {"description": "Output only. The time when the session entered the historical state.", "format": "google-datetime", "readOnly": true, "type": "string"}}, "type": "object"}, "SessionTemplate": {"description": "A representation of a session template.", "id": "SessionTemplate", "properties": {"createTime": {"description": "Output only. The time when the template was created.", "format": "google-datetime", "readOnly": true, "type": "string"}, "creator": {"description": "Output only. The email address of the user who created the template.", "readOnly": true, "type": "string"}, "description": {"description": "Optional. Brief description of the template.", "type": "string"}, "environmentConfig": {"$ref": "EnvironmentConfig", "description": "Optional. Environment configuration for session execution."}, "jupyterSession": {"$ref": "JupyterConfig", "description": "Optional. Jupyter session config."}, "labels": {"additionalProperties": {"type": "string"}, "description": "Optional. Labels to associate with sessions created using this template. Label keys must contain 1 to 63 characters, and must conform to RFC 1035 (https://www.ietf.org/rfc/rfc1035.txt). Label values can be empty, but, if present, must contain 1 to 63 characters and conform to RFC 1035 (https://www.ietf.org/rfc/rfc1035.txt). No more than 32 labels can be associated with a session.", "type": "object"}, "name": {"description": "Required. The resource name of the session template.", "type": "string"}, "runtimeConfig": {"$ref": "RuntimeConfig", "description": "Optional. Runtime configuration for session execution."}, "sparkConnectSession": {"$ref": "SparkConnectConfig", "description": "Optional. Spark connect session config."}, "updateTime": {"description": "Output only. The time the template was last updated.", "format": "google-datetime", "readOnly": true, "type": "string"}, "uuid": {"description": "Output only. A session template UUID (Unique Universal Identifier). The service generates this value when it creates the session template.", "readOnly": true, "type": "string"}}, "type": "object"}, "SetIamPolicyRequest": {"description": "Request message for SetIamPolicy method.", "id": "SetIamPolicyRequest", "properties": {"policy": {"$ref": "Policy", "description": "REQUIRED: The complete policy to be applied to the resource. The size of the policy is limited to a few 10s of KB. An empty policy is a valid policy but certain Google Cloud services (such as Projects) might reject them."}}, "type": "object"}, "ShieldedInstanceConfig": {"description": "Shielded Instance Config for clusters using Compute Engine Shielded VMs (https://cloud.google.com/security/shielded-cloud/shielded-vm).", "id": "ShieldedInstanceConfig", "properties": {"enableIntegrityMonitoring": {"description": "Optional. Defines whether instances have integrity monitoring enabled.", "type": "boolean"}, "enableSecureBoot": {"description": "Optional. Defines whether instances have Secure Boot enabled.", "type": "boolean"}, "enableVtpm": {"description": "Optional. Defines whether instances have the vTPM enabled.", "type": "boolean"}}, "type": "object"}, "ShufflePushReadMetrics": {"id": "ShufflePushReadMetrics", "properties": {"corruptMergedBlockChunks": {"format": "int64", "type": "string"}, "localMergedBlocksFetched": {"format": "int64", "type": "string"}, "localMergedBytesRead": {"format": "int64", "type": "string"}, "localMergedChunksFetched": {"format": "int64", "type": "string"}, "mergedFetchFallbackCount": {"format": "int64", "type": "string"}, "remoteMergedBlocksFetched": {"format": "int64", "type": "string"}, "remoteMergedBytesRead": {"format": "int64", "type": "string"}, "remoteMergedChunksFetched": {"format": "int64", "type": "string"}, "remoteMergedReqsDuration": {"format": "int64", "type": "string"}}, "type": "object"}, "ShufflePushReadQuantileMetrics": {"id": "ShufflePushReadQuantileMetrics", "properties": {"corruptMergedBlockChunks": {"$ref": "Quantiles"}, "localMergedBlocksFetched": {"$ref": "Quantiles"}, "localMergedBytesRead": {"$ref": "Quantiles"}, "localMergedChunksFetched": {"$ref": "Quantiles"}, "mergedFetchFallbackCount": {"$ref": "Quantiles"}, "remoteMergedBlocksFetched": {"$ref": "Quantiles"}, "remoteMergedBytesRead": {"$ref": "Quantiles"}, "remoteMergedChunksFetched": {"$ref": "Quantiles"}, "remoteMergedReqsDuration": {"$ref": "Quantiles"}}, "type": "object"}, "ShuffleReadMetrics": {"description": "Shuffle data read by the task.", "id": "ShuffleReadMetrics", "properties": {"fetchWaitTimeMillis": {"format": "int64", "type": "string"}, "localBlocksFetched": {"format": "int64", "type": "string"}, "localBytesRead": {"format": "int64", "type": "string"}, "recordsRead": {"format": "int64", "type": "string"}, "remoteBlocksFetched": {"format": "int64", "type": "string"}, "remoteBytesRead": {"format": "int64", "type": "string"}, "remoteBytesReadToDisk": {"format": "int64", "type": "string"}, "remoteReqsDuration": {"format": "int64", "type": "string"}, "shufflePushReadMetrics": {"$ref": "ShufflePushReadMetrics"}}, "type": "object"}, "ShuffleReadQuantileMetrics": {"id": "ShuffleReadQuantileMetrics", "properties": {"fetchWaitTimeMillis": {"$ref": "Quantiles"}, "localBlocksFetched": {"$ref": "Quantiles"}, "readBytes": {"$ref": "Quantiles"}, "readRecords": {"$ref": "Quantiles"}, "remoteBlocksFetched": {"$ref": "Quantiles"}, "remoteBytesRead": {"$ref": "Quantiles"}, "remoteBytesReadToDisk": {"$ref": "Quantiles"}, "remoteReqsDuration": {"$ref": "Quantiles"}, "shufflePushReadMetrics": {"$ref": "ShufflePushReadQuantileMetrics"}, "totalBlocksFetched": {"$ref": "Quantiles"}}, "type": "object"}, "ShuffleWriteMetrics": {"description": "Shuffle data written by task.", "id": "ShuffleWriteMetrics", "properties": {"bytesWritten": {"format": "int64", "type": "string"}, "recordsWritten": {"format": "int64", "type": "string"}, "writeTimeNanos": {"format": "int64", "type": "string"}}, "type": "object"}, "ShuffleWriteQuantileMetrics": {"id": "ShuffleWriteQuantileMetrics", "properties": {"writeBytes": {"$ref": "Quantiles"}, "writeRecords": {"$ref": "Quantiles"}, "writeTimeNanos": {"$ref": "Quantiles"}}, "type": "object"}, "SinkProgress": {"id": "SinkProgress", "properties": {"description": {"type": "string"}, "metrics": {"additionalProperties": {"type": "string"}, "type": "object"}, "numOutputRows": {"format": "int64", "type": "string"}}, "type": "object"}, "SoftwareConfig": {"description": "Specifies the selection and config of software inside the cluster.", "id": "SoftwareConfig", "properties": {"imageVersion": {"description": "Optional. The version of software inside the cluster. It must be one of the supported Dataproc Versions (https://cloud.google.com/dataproc/docs/concepts/versioning/dataproc-versions#supported-dataproc-image-versions), such as \"1.2\" (including a subminor version, such as \"1.2.29\"), or the \"preview\" version (https://cloud.google.com/dataproc/docs/concepts/versioning/dataproc-versions#other_versions). If unspecified, it defaults to the latest Debian version.", "type": "string"}, "optionalComponents": {"description": "Optional. The set of components to activate on the cluster.", "items": {"enum": ["COMPONENT_UNSPECIFIED", "ANACONDA", "DELTA", "DOCKER", "DRUID", "FLINK", "HBASE", "HIVE_WEBHCAT", "HUDI", "ICEBERG", "JUPYTER", "PIG", "PRESTO", "TRINO", "RANGER", "SOLR", "ZEPPELIN", "ZOOKEEPER"], "enumDescriptions": ["Unspecified component. Specifying this will cause Cluster creation to fail.", "The Anaconda component is no longer supported or applicable to supported Dataproc on Compute Engine image versions (https://cloud.google.com/dataproc/docs/concepts/versioning/dataproc-version-clusters#supported-dataproc-image-versions). It cannot be activated on clusters created with supported Dataproc on Compute Engine image versions.", "Delta Lake.", "<PERSON>er", "The Druid query engine. (alpha)", "Flink", "HBase. (beta)", "The Hive Web HCatalog (the REST service for accessing HCatalog).", "<PERSON><PERSON>.", "Iceberg.", "The Jupyter Notebook.", "The Pig component.", "The Presto query engine.", "The Trino query engine.", "The Ranger service.", "The Solr service.", "The Zeppelin notebook.", "The Zookeeper service."], "type": "string"}, "type": "array"}, "properties": {"additionalProperties": {"type": "string"}, "description": "Optional. The properties to set on daemon config files.Property keys are specified in prefix:property format, for example core:hadoop.tmp.dir. The following are supported prefixes and their mappings: capacity-scheduler: capacity-scheduler.xml core: core-site.xml distcp: distcp-default.xml hdfs: hdfs-site.xml hive: hive-site.xml mapred: mapred-site.xml pig: pig.properties spark: spark-defaults.conf yarn: yarn-site.xmlFor more information, see Cluster properties (https://cloud.google.com/dataproc/docs/concepts/cluster-properties).", "type": "object"}}, "type": "object"}, "SourceProgress": {"id": "SourceProgress", "properties": {"description": {"type": "string"}, "endOffset": {"type": "string"}, "inputRowsPerSecond": {"format": "double", "type": "number"}, "latestOffset": {"type": "string"}, "metrics": {"additionalProperties": {"type": "string"}, "type": "object"}, "numInputRows": {"format": "int64", "type": "string"}, "processedRowsPerSecond": {"format": "double", "type": "number"}, "startOffset": {"type": "string"}}, "type": "object"}, "SparkApplication": {"description": "A summary of Spark Application", "id": "SparkApplication", "properties": {"application": {"$ref": "ApplicationInfo", "description": "Output only. High level information corresponding to an application.", "readOnly": true}, "name": {"description": "Identifier. Name of the spark application", "type": "string"}}, "type": "object"}, "SparkBatch": {"description": "A configuration for running an Apache Spark (https://spark.apache.org/) batch workload.", "id": "SparkBatch", "properties": {"archiveUris": {"description": "Optional. HCFS URIs of archives to be extracted into the working directory of each executor. Supported file types: .jar, .tar, .tar.gz, .tgz, and .zip.", "items": {"type": "string"}, "type": "array"}, "args": {"description": "Optional. The arguments to pass to the driver. Do not include arguments that can be set as batch properties, such as --conf, since a collision can occur that causes an incorrect batch submission.", "items": {"type": "string"}, "type": "array"}, "fileUris": {"description": "Optional. HCFS URIs of files to be placed in the working directory of each executor.", "items": {"type": "string"}, "type": "array"}, "jarFileUris": {"description": "Optional. HCFS URIs of jar files to add to the classpath of the Spark driver and tasks.", "items": {"type": "string"}, "type": "array"}, "mainClass": {"description": "Optional. The name of the driver main class. The jar file that contains the class must be in the classpath or specified in jar_file_uris.", "type": "string"}, "mainJarFileUri": {"description": "Optional. The HCFS URI of the jar file that contains the main class.", "type": "string"}}, "type": "object"}, "SparkConnectConfig": {"description": "Spark connect configuration for an interactive session.", "id": "SparkConnectConfig", "properties": {}, "type": "object"}, "SparkHistoryServerConfig": {"description": "Spark History Server configuration for the workload.", "id": "SparkHistoryServerConfig", "properties": {"dataprocCluster": {"description": "Optional. Resource name of an existing Dataproc Cluster to act as a Spark History Server for the workload.Example: projects/[project_id]/regions/[region]/clusters/[cluster_name]", "type": "string"}}, "type": "object"}, "SparkJob": {"description": "A Dataproc job for running Apache Spark (https://spark.apache.org/) applications on YARN.", "id": "SparkJob", "properties": {"archiveUris": {"description": "Optional. HCFS URIs of archives to be extracted into the working directory of each executor. Supported file types: .jar, .tar, .tar.gz, .tgz, and .zip.", "items": {"type": "string"}, "type": "array"}, "args": {"description": "Optional. The arguments to pass to the driver. Do not include arguments, such as --conf, that can be set as job properties, since a collision may occur that causes an incorrect job submission.", "items": {"type": "string"}, "type": "array"}, "fileUris": {"description": "Optional. HCFS URIs of files to be placed in the working directory of each executor. Useful for naively parallel tasks.", "items": {"type": "string"}, "type": "array"}, "jarFileUris": {"description": "Optional. HCFS URIs of jar files to add to the CLASSPATHs of the Spark driver and tasks.", "items": {"type": "string"}, "type": "array"}, "loggingConfig": {"$ref": "LoggingConfig", "description": "Optional. The runtime log config for job execution."}, "mainClass": {"description": "The name of the driver's main class. The jar file that contains the class must be in the default CLASSPATH or specified in SparkJob.jar_file_uris.", "type": "string"}, "mainJarFileUri": {"description": "The HCFS URI of the jar file that contains the main class.", "type": "string"}, "properties": {"additionalProperties": {"type": "string"}, "description": "Optional. A mapping of property names to values, used to configure Spark. Properties that conflict with values set by the Dataproc API might be overwritten. Can include properties set in /etc/spark/conf/spark-defaults.conf and classes in user code.", "type": "object"}}, "type": "object"}, "SparkPlanGraph": {"description": "A graph used for storing information of an executionPlan of DataFrame.", "id": "SparkPlanGraph", "properties": {"edges": {"items": {"$ref": "SparkPlanGraphEdge"}, "type": "array"}, "executionId": {"format": "int64", "type": "string"}, "nodes": {"items": {"$ref": "SparkPlanGraphNodeWrapper"}, "type": "array"}}, "type": "object"}, "SparkPlanGraphCluster": {"description": "Represents a tree of spark plan.", "id": "SparkPlanGraphCluster", "properties": {"desc": {"type": "string"}, "metrics": {"items": {"$ref": "SqlPlanMetric"}, "type": "array"}, "name": {"type": "string"}, "nodes": {"items": {"$ref": "SparkPlanGraphNodeWrapper"}, "type": "array"}, "sparkPlanGraphClusterId": {"format": "int64", "type": "string"}}, "type": "object"}, "SparkPlanGraphEdge": {"description": "Represents a directed edge in the spark plan tree from child to parent.", "id": "SparkPlanGraphEdge", "properties": {"fromId": {"format": "int64", "type": "string"}, "toId": {"format": "int64", "type": "string"}}, "type": "object"}, "SparkPlanGraphNode": {"description": "Represents a node in the spark plan tree.", "id": "SparkPlanGraphNode", "properties": {"desc": {"type": "string"}, "metrics": {"items": {"$ref": "SqlPlanMetric"}, "type": "array"}, "name": {"type": "string"}, "sparkPlanGraphNodeId": {"format": "int64", "type": "string"}}, "type": "object"}, "SparkPlanGraphNodeWrapper": {"description": "Wrapper user to represent either a node or a cluster.", "id": "SparkPlanGraphNodeWrapper", "properties": {"cluster": {"$ref": "SparkPlanGraphCluster"}, "node": {"$ref": "SparkPlanGraphNode"}}, "type": "object"}, "SparkRBatch": {"description": "A configuration for running an Apache SparkR (https://spark.apache.org/docs/latest/sparkr.html) batch workload.", "id": "SparkRBatch", "properties": {"archiveUris": {"description": "Optional. HCFS URIs of archives to be extracted into the working directory of each executor. Supported file types: .jar, .tar, .tar.gz, .tgz, and .zip.", "items": {"type": "string"}, "type": "array"}, "args": {"description": "Optional. The arguments to pass to the Spark driver. Do not include arguments that can be set as batch properties, such as --conf, since a collision can occur that causes an incorrect batch submission.", "items": {"type": "string"}, "type": "array"}, "fileUris": {"description": "Optional. HCFS URIs of files to be placed in the working directory of each executor.", "items": {"type": "string"}, "type": "array"}, "mainRFileUri": {"description": "Required. The HCFS URI of the main R file to use as the driver. Must be a .R or .r file.", "type": "string"}}, "type": "object"}, "SparkRJob": {"description": "A Dataproc job for running Apache SparkR (https://spark.apache.org/docs/latest/sparkr.html) applications on YARN.", "id": "SparkRJob", "properties": {"archiveUris": {"description": "Optional. HCFS URIs of archives to be extracted into the working directory of each executor. Supported file types: .jar, .tar, .tar.gz, .tgz, and .zip.", "items": {"type": "string"}, "type": "array"}, "args": {"description": "Optional. The arguments to pass to the driver. Do not include arguments, such as --conf, that can be set as job properties, since a collision may occur that causes an incorrect job submission.", "items": {"type": "string"}, "type": "array"}, "fileUris": {"description": "Optional. HCFS URIs of files to be placed in the working directory of each executor. Useful for naively parallel tasks.", "items": {"type": "string"}, "type": "array"}, "loggingConfig": {"$ref": "LoggingConfig", "description": "Optional. The runtime log config for job execution."}, "mainRFileUri": {"description": "Required. The HCFS URI of the main R file to use as the driver. Must be a .R file.", "type": "string"}, "properties": {"additionalProperties": {"type": "string"}, "description": "Optional. A mapping of property names to values, used to configure SparkR. Properties that conflict with values set by the Dataproc API might be overwritten. Can include properties set in /etc/spark/conf/spark-defaults.conf and classes in user code.", "type": "object"}}, "type": "object"}, "SparkRuntimeInfo": {"id": "SparkRuntimeInfo", "properties": {"javaHome": {"type": "string"}, "javaVersion": {"type": "string"}, "scalaVersion": {"type": "string"}}, "type": "object"}, "SparkSqlBatch": {"description": "A configuration for running Apache Spark SQL (https://spark.apache.org/sql/) queries as a batch workload.", "id": "SparkSqlBatch", "properties": {"jarFileUris": {"description": "Optional. HCFS URIs of jar files to be added to the Spark CLASSPATH.", "items": {"type": "string"}, "type": "array"}, "queryFileUri": {"description": "Required. The HCFS URI of the script that contains Spark SQL queries to execute.", "type": "string"}, "queryVariables": {"additionalProperties": {"type": "string"}, "description": "Optional. Mapping of query variable names to values (equivalent to the Spark SQL command: SET name=\"value\";).", "type": "object"}}, "type": "object"}, "SparkSqlJob": {"description": "A Dataproc job for running Apache Spark SQL (https://spark.apache.org/sql/) queries.", "id": "SparkSqlJob", "properties": {"jarFileUris": {"description": "Optional. HCFS URIs of jar files to be added to the Spark CLASSPATH.", "items": {"type": "string"}, "type": "array"}, "loggingConfig": {"$ref": "LoggingConfig", "description": "Optional. The runtime log config for job execution."}, "properties": {"additionalProperties": {"type": "string"}, "description": "Optional. A mapping of property names to values, used to configure Spark SQL's SparkConf. Properties that conflict with values set by the Dataproc API might be overwritten.", "type": "object"}, "queryFileUri": {"description": "The HCFS URI of the script that contains SQL queries.", "type": "string"}, "queryList": {"$ref": "QueryList", "description": "A list of queries."}, "scriptVariables": {"additionalProperties": {"type": "string"}, "description": "Optional. Mapping of query variable names to values (equivalent to the Spark SQL command: SET name=\"value\";).", "type": "object"}}, "type": "object"}, "SparkStandaloneAutoscalingConfig": {"description": "Basic autoscaling configurations for Spark Standalone.", "id": "SparkStandaloneAutoscalingConfig", "properties": {"gracefulDecommissionTimeout": {"description": "Required. Timeout for Spark graceful decommissioning of spark workers. Specifies the duration to wait for spark worker to complete spark decommissioning tasks before forcefully removing workers. Only applicable to downscaling operations.Bounds: 0s, 1d.", "format": "google-duration", "type": "string"}, "removeOnlyIdleWorkers": {"description": "Optional. Remove only idle workers when scaling down cluster", "type": "boolean"}, "scaleDownFactor": {"description": "Required. Fraction of required executors to remove from Spark Serverless clusters. A scale-down factor of 1.0 will result in scaling down so that there are no more executors for the Spark Job.(more aggressive scaling). A scale-down factor closer to 0 will result in a smaller magnitude of scaling donw (less aggressive scaling).Bounds: 0.0, 1.0.", "format": "double", "type": "number"}, "scaleDownMinWorkerFraction": {"description": "Optional. Minimum scale-down threshold as a fraction of total cluster size before scaling occurs. For example, in a 20-worker cluster, a threshold of 0.1 means the autoscaler must recommend at least a 2 worker scale-down for the cluster to scale. A threshold of 0 means the autoscaler will scale down on any recommended change.Bounds: 0.0, 1.0. De<PERSON>ult: 0.0.", "format": "double", "type": "number"}, "scaleUpFactor": {"description": "Required. Fraction of required workers to add to Spark Standalone clusters. A scale-up factor of 1.0 will result in scaling up so that there are no more required workers for the Spark Job (more aggressive scaling). A scale-up factor closer to 0 will result in a smaller magnitude of scaling up (less aggressive scaling).Bounds: 0.0, 1.0.", "format": "double", "type": "number"}, "scaleUpMinWorkerFraction": {"description": "Optional. Minimum scale-up threshold as a fraction of total cluster size before scaling occurs. For example, in a 20-worker cluster, a threshold of 0.1 means the autoscaler must recommend at least a 2-worker scale-up for the cluster to scale. A threshold of 0 means the autoscaler will scale up on any recommended change.Bounds: 0.0, 1.0. De<PERSON>ult: 0.0.", "format": "double", "type": "number"}}, "type": "object"}, "SparkWrapperObject": {"description": "Outer message that contains the data obtained from spark listener, packaged with information that is required to process it.", "id": "SparkWrapperObject", "properties": {"appSummary": {"$ref": "AppSummary"}, "applicationEnvironmentInfo": {"$ref": "ApplicationEnvironmentInfo"}, "applicationId": {"description": "Application Id created by Spark.", "type": "string"}, "applicationInfo": {"$ref": "ApplicationInfo"}, "eventTimestamp": {"description": "VM Timestamp associated with the data object.", "format": "google-datetime", "type": "string"}, "executorStageSummary": {"$ref": "ExecutorStageSummary"}, "executorSummary": {"$ref": "Executor<PERSON>ummary"}, "jobData": {"$ref": "JobData"}, "nativeBuildInfoUiData": {"$ref": "NativeBuildInfoUiData", "description": "Native Build Info"}, "nativeSqlExecutionUiData": {"$ref": "NativeSqlExecutionUiData", "description": "Native SQL Execution Info"}, "poolData": {"$ref": "PoolData"}, "processSummary": {"$ref": "ProcessSummary"}, "rddOperationGraph": {"$ref": "RddOperationGraph"}, "rddStorageInfo": {"$ref": "RddStorageInfo"}, "resourceProfileInfo": {"$ref": "ResourceProfileInfo"}, "sparkPlanGraph": {"$ref": "SparkPlanGraph"}, "speculationStageSummary": {"$ref": "SpeculationStageSummary"}, "sqlExecutionUiData": {"$ref": "SqlExecutionUiData"}, "stageData": {"$ref": "StageData"}, "streamBlockData": {"$ref": "StreamBlockData"}, "streamingQueryData": {"$ref": "StreamingQueryData"}, "streamingQueryProgress": {"$ref": "StreamingQueryProgress"}, "taskData": {"$ref": "TaskData"}}, "type": "object"}, "SpeculationStageSummary": {"description": "Details of the speculation task when speculative execution is enabled.", "id": "SpeculationStageSummary", "properties": {"numActiveTasks": {"format": "int32", "type": "integer"}, "numCompletedTasks": {"format": "int32", "type": "integer"}, "numFailedTasks": {"format": "int32", "type": "integer"}, "numKilledTasks": {"format": "int32", "type": "integer"}, "numTasks": {"format": "int32", "type": "integer"}, "stageAttemptId": {"format": "int32", "type": "integer"}, "stageId": {"format": "int64", "type": "string"}}, "type": "object"}, "SqlExecutionUiData": {"description": "SQL Execution Data", "id": "SqlExecutionUiData", "properties": {"completionTime": {"format": "google-datetime", "type": "string"}, "description": {"type": "string"}, "details": {"type": "string"}, "errorMessage": {"type": "string"}, "executionId": {"format": "int64", "type": "string"}, "jobs": {"additionalProperties": {"enum": ["JOB_EXECUTION_STATUS_UNSPECIFIED", "JOB_EXECUTION_STATUS_RUNNING", "JOB_EXECUTION_STATUS_SUCCEEDED", "JOB_EXECUTION_STATUS_FAILED", "JOB_EXECUTION_STATUS_UNKNOWN"], "enumDescriptions": ["", "", "", "", ""], "type": "string"}, "type": "object"}, "metricValues": {"additionalProperties": {"type": "string"}, "type": "object"}, "metricValuesIsNull": {"type": "boolean"}, "metrics": {"items": {"$ref": "SqlPlanMetric"}, "type": "array"}, "modifiedConfigs": {"additionalProperties": {"type": "string"}, "type": "object"}, "physicalPlanDescription": {"type": "string"}, "rootExecutionId": {"format": "int64", "type": "string"}, "stages": {"items": {"format": "int64", "type": "string"}, "type": "array"}, "submissionTime": {"format": "google-datetime", "type": "string"}}, "type": "object"}, "SqlPlanMetric": {"description": "Metrics related to SQL execution.", "id": "SqlPlanMetric", "properties": {"accumulatorId": {"format": "int64", "type": "string"}, "metricType": {"type": "string"}, "name": {"type": "string"}}, "type": "object"}, "StageAttemptTasksSummary": {"description": "Data related to tasks summary for a Spark Stage Attempt", "id": "StageAttemptTasksSummary", "properties": {"applicationId": {"type": "string"}, "numFailedTasks": {"format": "int32", "type": "integer"}, "numKilledTasks": {"format": "int32", "type": "integer"}, "numPendingTasks": {"format": "int32", "type": "integer"}, "numRunningTasks": {"format": "int32", "type": "integer"}, "numSuccessTasks": {"format": "int32", "type": "integer"}, "numTasks": {"format": "int32", "type": "integer"}, "stageAttemptId": {"format": "int32", "type": "integer"}, "stageId": {"format": "int64", "type": "string"}}, "type": "object"}, "StageData": {"description": "Data corresponding to a stage.", "id": "StageData", "properties": {"accumulatorUpdates": {"items": {"$ref": "AccumulableInfo"}, "type": "array"}, "completionTime": {"format": "google-datetime", "type": "string"}, "description": {"type": "string"}, "details": {"type": "string"}, "executorMetricsDistributions": {"$ref": "ExecutorMetricsDistributions"}, "executorSummary": {"additionalProperties": {"$ref": "ExecutorStageSummary"}, "type": "object"}, "failureReason": {"type": "string"}, "firstTaskLaunchedTime": {"format": "google-datetime", "type": "string"}, "isShufflePushEnabled": {"type": "boolean"}, "jobIds": {"items": {"format": "int64", "type": "string"}, "type": "array"}, "killedTasksSummary": {"additionalProperties": {"format": "int32", "type": "integer"}, "type": "object"}, "locality": {"additionalProperties": {"format": "int64", "type": "string"}, "type": "object"}, "name": {"type": "string"}, "numActiveTasks": {"format": "int32", "type": "integer"}, "numCompleteTasks": {"format": "int32", "type": "integer"}, "numCompletedIndices": {"format": "int32", "type": "integer"}, "numFailedTasks": {"format": "int32", "type": "integer"}, "numKilledTasks": {"format": "int32", "type": "integer"}, "numTasks": {"format": "int32", "type": "integer"}, "parentStageIds": {"items": {"format": "int64", "type": "string"}, "type": "array"}, "peakExecutorMetrics": {"$ref": "ExecutorMetrics"}, "rddIds": {"items": {"format": "int64", "type": "string"}, "type": "array"}, "resourceProfileId": {"format": "int32", "type": "integer"}, "schedulingPool": {"type": "string"}, "shuffleMergersCount": {"format": "int32", "type": "integer"}, "speculationSummary": {"$ref": "SpeculationStageSummary"}, "stageAttemptId": {"format": "int32", "type": "integer"}, "stageId": {"format": "int64", "type": "string"}, "stageMetrics": {"$ref": "StageMetrics"}, "status": {"enum": ["STAGE_STATUS_UNSPECIFIED", "STAGE_STATUS_ACTIVE", "STAGE_STATUS_COMPLETE", "STAGE_STATUS_FAILED", "STAGE_STATUS_PENDING", "STAGE_STATUS_SKIPPED"], "enumDescriptions": ["", "", "", "", "", ""], "type": "string"}, "submissionTime": {"format": "google-datetime", "type": "string"}, "taskQuantileMetrics": {"$ref": "TaskQuantileMetrics", "description": "Summary metrics fields. These are included in response only if present in summary_metrics_mask field in request"}, "tasks": {"additionalProperties": {"$ref": "TaskData"}, "type": "object"}}, "type": "object"}, "StageInputMetrics": {"description": "Metrics about the input read by the stage.", "id": "StageInputMetrics", "properties": {"bytesRead": {"format": "int64", "type": "string"}, "recordsRead": {"format": "int64", "type": "string"}}, "type": "object"}, "StageMetrics": {"description": "Stage Level Aggregated Metrics", "id": "StageMetrics", "properties": {"diskBytesSpilled": {"format": "int64", "type": "string"}, "executorCpuTimeNanos": {"format": "int64", "type": "string"}, "executorDeserializeCpuTimeNanos": {"format": "int64", "type": "string"}, "executorDeserializeTimeMillis": {"format": "int64", "type": "string"}, "executorRunTimeMillis": {"format": "int64", "type": "string"}, "jvmGcTimeMillis": {"format": "int64", "type": "string"}, "memoryBytesSpilled": {"format": "int64", "type": "string"}, "peakExecutionMemoryBytes": {"format": "int64", "type": "string"}, "resultSerializationTimeMillis": {"format": "int64", "type": "string"}, "resultSize": {"format": "int64", "type": "string"}, "stageInputMetrics": {"$ref": "StageInputMetrics"}, "stageOutputMetrics": {"$ref": "StageOutputMetrics"}, "stageShuffleReadMetrics": {"$ref": "StageShuffleReadMetrics"}, "stageShuffleWriteMetrics": {"$ref": "StageShuffleWriteMetrics"}}, "type": "object"}, "StageOutputMetrics": {"description": "Metrics about the output written by the stage.", "id": "StageOutputMetrics", "properties": {"bytesWritten": {"format": "int64", "type": "string"}, "recordsWritten": {"format": "int64", "type": "string"}}, "type": "object"}, "StageShufflePushReadMetrics": {"id": "StageShufflePushReadMetrics", "properties": {"corruptMergedBlockChunks": {"format": "int64", "type": "string"}, "localMergedBlocksFetched": {"format": "int64", "type": "string"}, "localMergedBytesRead": {"format": "int64", "type": "string"}, "localMergedChunksFetched": {"format": "int64", "type": "string"}, "mergedFetchFallbackCount": {"format": "int64", "type": "string"}, "remoteMergedBlocksFetched": {"format": "int64", "type": "string"}, "remoteMergedBytesRead": {"format": "int64", "type": "string"}, "remoteMergedChunksFetched": {"format": "int64", "type": "string"}, "remoteMergedReqsDuration": {"format": "int64", "type": "string"}}, "type": "object"}, "StageShuffleReadMetrics": {"description": "Shuffle data read for the stage.", "id": "StageShuffleReadMetrics", "properties": {"bytesRead": {"format": "int64", "type": "string"}, "fetchWaitTimeMillis": {"format": "int64", "type": "string"}, "localBlocksFetched": {"format": "int64", "type": "string"}, "localBytesRead": {"format": "int64", "type": "string"}, "recordsRead": {"format": "int64", "type": "string"}, "remoteBlocksFetched": {"format": "int64", "type": "string"}, "remoteBytesRead": {"format": "int64", "type": "string"}, "remoteBytesReadToDisk": {"format": "int64", "type": "string"}, "remoteReqsDuration": {"format": "int64", "type": "string"}, "stageShufflePushReadMetrics": {"$ref": "StageShufflePushReadMetrics"}}, "type": "object"}, "StageShuffleWriteMetrics": {"description": "Shuffle data written for the stage.", "id": "StageShuffleWriteMetrics", "properties": {"bytesWritten": {"format": "int64", "type": "string"}, "recordsWritten": {"format": "int64", "type": "string"}, "writeTimeNanos": {"format": "int64", "type": "string"}}, "type": "object"}, "StagesSummary": {"description": "Data related to Stages page summary", "id": "StagesSummary", "properties": {"applicationId": {"type": "string"}, "numActiveStages": {"format": "int32", "type": "integer"}, "numCompletedStages": {"format": "int32", "type": "integer"}, "numFailedStages": {"format": "int32", "type": "integer"}, "numPendingStages": {"format": "int32", "type": "integer"}, "numSkippedStages": {"format": "int32", "type": "integer"}}, "type": "object"}, "StartClusterRequest": {"description": "A request to start a cluster.", "id": "StartClusterRequest", "properties": {"clusterUuid": {"description": "Optional. Specifying the cluster_uuid means the RPC will fail (with error NOT_FOUND) if a cluster with the specified UUID does not exist.", "type": "string"}, "requestId": {"description": "Optional. A unique ID used to identify the request. If the server receives two StartClusterRequest (https://cloud.google.com/dataproc/docs/reference/rpc/google.cloud.dataproc.v1#google.cloud.dataproc.v1.StartClusterRequest)s with the same id, then the second request will be ignored and the first google.longrunning.Operation created and stored in the backend is returned.Recommendation: Set this value to a UUID (https://en.wikipedia.org/wiki/Universally_unique_identifier).The ID must contain only letters (a-z, A-Z), numbers (0-9), underscores (_), and hyphens (-). The maximum length is 40 characters.", "type": "string"}}, "type": "object"}, "StartupConfig": {"description": "Configuration to handle the startup of instances during cluster create and update process.", "id": "StartupConfig", "properties": {"requiredRegistrationFraction": {"description": "Optional. The config setting to enable cluster creation/ updation to be successful only after required_registration_fraction of instances are up and running. This configuration is applicable to only secondary workers for now. The cluster will fail if required_registration_fraction of instances are not available. This will include instance creation, agent registration, and service registration (if enabled).", "format": "double", "type": "number"}}, "type": "object"}, "StateHistory": {"description": "Historical state information.", "id": "StateHistory", "properties": {"state": {"description": "Output only. The state of the batch at this point in history.", "enum": ["STATE_UNSPECIFIED", "PENDING", "RUNNING", "CANCELLING", "CANCELLED", "SUCCEEDED", "FAILED"], "enumDescriptions": ["The batch state is unknown.", "The batch is created before running.", "The batch is running.", "The batch is cancelling.", "The batch cancellation was successful.", "The batch completed successfully.", "The batch is no longer running due to an error."], "readOnly": true, "type": "string"}, "stateMessage": {"description": "Output only. Details about the state at this point in history.", "readOnly": true, "type": "string"}, "stateStartTime": {"description": "Output only. The time when the batch entered the historical state.", "format": "google-datetime", "readOnly": true, "type": "string"}}, "type": "object"}, "StateOperatorProgress": {"id": "StateOperatorProgress", "properties": {"allRemovalsTimeMs": {"format": "int64", "type": "string"}, "allUpdatesTimeMs": {"format": "int64", "type": "string"}, "commitTimeMs": {"format": "int64", "type": "string"}, "customMetrics": {"additionalProperties": {"format": "int64", "type": "string"}, "type": "object"}, "memoryUsedBytes": {"format": "int64", "type": "string"}, "numRowsDroppedByWatermark": {"format": "int64", "type": "string"}, "numRowsRemoved": {"format": "int64", "type": "string"}, "numRowsTotal": {"format": "int64", "type": "string"}, "numRowsUpdated": {"format": "int64", "type": "string"}, "numShufflePartitions": {"format": "int64", "type": "string"}, "numStateStoreInstances": {"format": "int64", "type": "string"}, "operatorName": {"type": "string"}}, "type": "object"}, "Status": {"description": "The Status type defines a logical error model that is suitable for different programming environments, including REST APIs and RPC APIs. It is used by gRPC (https://github.com/grpc). Each Status message contains three pieces of data: error code, error message, and error details.You can find out more about this error model and how to work with it in the API Design Guide (https://cloud.google.com/apis/design/errors).", "id": "Status", "properties": {"code": {"description": "The status code, which should be an enum value of google.rpc.Code.", "format": "int32", "type": "integer"}, "details": {"description": "A list of messages that carry the error details. There is a common set of message types for APIs to use.", "items": {"additionalProperties": {"description": "Properties of the object. Contains field @type with type URL.", "type": "any"}, "type": "object"}, "type": "array"}, "message": {"description": "A developer-facing error message, which should be in English. Any user-facing error message should be localized and sent in the google.rpc.Status.details field, or localized by the client.", "type": "string"}}, "type": "object"}, "StopClusterRequest": {"description": "A request to stop a cluster.", "id": "StopClusterRequest", "properties": {"clusterUuid": {"description": "Optional. Specifying the cluster_uuid means the RPC will fail (with error NOT_FOUND) if a cluster with the specified UUID does not exist.", "type": "string"}, "requestId": {"description": "Optional. A unique ID used to identify the request. If the server receives two StopClusterRequest (https://cloud.google.com/dataproc/docs/reference/rpc/google.cloud.dataproc.v1#google.cloud.dataproc.v1.StopClusterRequest)s with the same id, then the second request will be ignored and the first google.longrunning.Operation created and stored in the backend is returned.Recommendation: Set this value to a UUID (https://en.wikipedia.org/wiki/Universally_unique_identifier).The ID must contain only letters (a-z, A-Z), numbers (0-9), underscores (_), and hyphens (-). The maximum length is 40 characters.", "type": "string"}}, "type": "object"}, "StreamBlockData": {"description": "Stream Block Data.", "id": "StreamBlockData", "properties": {"deserialized": {"type": "boolean"}, "diskSize": {"format": "int64", "type": "string"}, "executorId": {"type": "string"}, "hostPort": {"type": "string"}, "memSize": {"format": "int64", "type": "string"}, "name": {"type": "string"}, "storageLevel": {"type": "string"}, "useDisk": {"type": "boolean"}, "useMemory": {"type": "boolean"}}, "type": "object"}, "StreamingQueryData": {"description": "Streaming", "id": "StreamingQueryData", "properties": {"endTimestamp": {"format": "int64", "type": "string"}, "exception": {"type": "string"}, "isActive": {"type": "boolean"}, "name": {"type": "string"}, "runId": {"type": "string"}, "startTimestamp": {"format": "int64", "type": "string"}, "streamingQueryId": {"type": "string"}}, "type": "object"}, "StreamingQueryProgress": {"id": "StreamingQueryProgress", "properties": {"batchDuration": {"format": "int64", "type": "string"}, "batchId": {"format": "int64", "type": "string"}, "durationMillis": {"additionalProperties": {"format": "int64", "type": "string"}, "type": "object"}, "eventTime": {"additionalProperties": {"type": "string"}, "type": "object"}, "name": {"type": "string"}, "observedMetrics": {"additionalProperties": {"type": "string"}, "type": "object"}, "runId": {"type": "string"}, "sink": {"$ref": "SinkProgress"}, "sources": {"items": {"$ref": "SourceProgress"}, "type": "array"}, "stateOperators": {"items": {"$ref": "StateOperatorProgress"}, "type": "array"}, "streamingQueryProgressId": {"type": "string"}, "timestamp": {"type": "string"}}, "type": "object"}, "SubmitJobRequest": {"description": "A request to submit a job.", "id": "SubmitJobRequest", "properties": {"job": {"$ref": "Job", "description": "Required. The job resource."}, "requestId": {"description": "Optional. A unique id used to identify the request. If the server receives two SubmitJobRequest (https://cloud.google.com/dataproc/docs/reference/rpc/google.cloud.dataproc.v1#google.cloud.dataproc.v1.SubmitJobRequest)s with the same id, then the second request will be ignored and the first Job created and stored in the backend is returned.It is recommended to always set this value to a UUID (https://en.wikipedia.org/wiki/Universally_unique_identifier).The id must contain only letters (a-z, A-Z), numbers (0-9), underscores (_), and hyphens (-). The maximum length is 40 characters.", "type": "string"}}, "type": "object"}, "SummarizeSessionSparkApplicationExecutorsResponse": {"description": "Consolidated summary of executors for a Spark Application.", "id": "SummarizeSessionSparkApplicationExecutorsResponse", "properties": {"activeExecutorSummary": {"$ref": "ConsolidatedExecutorSummary", "description": "Consolidated summary for active executors."}, "applicationId": {"description": "Spark Application Id", "type": "string"}, "deadExecutorSummary": {"$ref": "ConsolidatedExecutorSummary", "description": "Consolidated summary for dead executors."}, "totalExecutorSummary": {"$ref": "ConsolidatedExecutorSummary", "description": "Overall consolidated summary for all executors."}}, "type": "object"}, "SummarizeSessionSparkApplicationJobsResponse": {"description": "Summary of a Spark Application jobs.", "id": "SummarizeSessionSparkApplicationJobsResponse", "properties": {"jobsSummary": {"$ref": "JobsSummary", "description": "Summary of a Spark Application Jobs"}}, "type": "object"}, "SummarizeSessionSparkApplicationStageAttemptTasksResponse": {"description": "Summary of tasks for a Spark Application stage attempt.", "id": "SummarizeSessionSparkApplicationStageAttemptTasksResponse", "properties": {"stageAttemptTasksSummary": {"$ref": "StageAttemptTasksSummary", "description": "Summary of tasks for a Spark Application Stage Attempt"}}, "type": "object"}, "SummarizeSessionSparkApplicationStagesResponse": {"description": "Summary of a Spark Application stages.", "id": "SummarizeSessionSparkApplicationStagesResponse", "properties": {"stagesSummary": {"$ref": "StagesSummary", "description": "Summary of a Spark Application Stages"}}, "type": "object"}, "SummarizeSparkApplicationExecutorsResponse": {"description": "Consolidated summary of executors for a Spark Application.", "id": "SummarizeSparkApplicationExecutorsResponse", "properties": {"activeExecutorSummary": {"$ref": "ConsolidatedExecutorSummary", "description": "Consolidated summary for active executors."}, "applicationId": {"description": "Spark Application Id", "type": "string"}, "deadExecutorSummary": {"$ref": "ConsolidatedExecutorSummary", "description": "Consolidated summary for dead executors."}, "totalExecutorSummary": {"$ref": "ConsolidatedExecutorSummary", "description": "Overall consolidated summary for all executors."}}, "type": "object"}, "SummarizeSparkApplicationJobsResponse": {"description": "Summary of a Spark Application jobs.", "id": "SummarizeSparkApplicationJobsResponse", "properties": {"jobsSummary": {"$ref": "JobsSummary", "description": "Summary of a Spark Application Jobs"}}, "type": "object"}, "SummarizeSparkApplicationStageAttemptTasksResponse": {"description": "Summary of tasks for a Spark Application stage attempt.", "id": "SummarizeSparkApplicationStageAttemptTasksResponse", "properties": {"stageAttemptTasksSummary": {"$ref": "StageAttemptTasksSummary", "description": "Summary of tasks for a Spark Application Stage Attempt"}}, "type": "object"}, "SummarizeSparkApplicationStagesResponse": {"description": "Summary of a Spark Application stages.", "id": "SummarizeSparkApplicationStagesResponse", "properties": {"stagesSummary": {"$ref": "StagesSummary", "description": "Summary of a Spark Application Stages"}}, "type": "object"}, "TaskData": {"description": "Data corresponding to tasks created by spark.", "id": "TaskData", "properties": {"accumulatorUpdates": {"items": {"$ref": "AccumulableInfo"}, "type": "array"}, "attempt": {"format": "int32", "type": "integer"}, "durationMillis": {"format": "int64", "type": "string"}, "errorMessage": {"type": "string"}, "executorId": {"type": "string"}, "executorLogs": {"additionalProperties": {"type": "string"}, "type": "object"}, "gettingResultTimeMillis": {"format": "int64", "type": "string"}, "hasMetrics": {"type": "boolean"}, "host": {"type": "string"}, "index": {"format": "int32", "type": "integer"}, "launchTime": {"format": "google-datetime", "type": "string"}, "partitionId": {"format": "int32", "type": "integer"}, "resultFetchStart": {"format": "google-datetime", "type": "string"}, "schedulerDelayMillis": {"format": "int64", "type": "string"}, "speculative": {"type": "boolean"}, "stageAttemptId": {"format": "int32", "type": "integer"}, "stageId": {"format": "int64", "type": "string"}, "status": {"type": "string"}, "taskId": {"format": "int64", "type": "string"}, "taskLocality": {"type": "string"}, "taskMetrics": {"$ref": "TaskMetrics"}}, "type": "object"}, "TaskMetrics": {"description": "Executor Task Metrics", "id": "TaskMetrics", "properties": {"diskBytesSpilled": {"format": "int64", "type": "string"}, "executorCpuTimeNanos": {"format": "int64", "type": "string"}, "executorDeserializeCpuTimeNanos": {"format": "int64", "type": "string"}, "executorDeserializeTimeMillis": {"format": "int64", "type": "string"}, "executorRunTimeMillis": {"format": "int64", "type": "string"}, "inputMetrics": {"$ref": "InputMetrics"}, "jvmGcTimeMillis": {"format": "int64", "type": "string"}, "memoryBytesSpilled": {"format": "int64", "type": "string"}, "outputMetrics": {"$ref": "OutputMetrics"}, "peakExecutionMemoryBytes": {"format": "int64", "type": "string"}, "resultSerializationTimeMillis": {"format": "int64", "type": "string"}, "resultSize": {"format": "int64", "type": "string"}, "shuffleReadMetrics": {"$ref": "ShuffleReadMetrics"}, "shuffleWriteMetrics": {"$ref": "ShuffleWriteMetrics"}}, "type": "object"}, "TaskQuantileMetrics": {"id": "TaskQuantileMetrics", "properties": {"diskBytesSpilled": {"$ref": "Quantiles"}, "durationMillis": {"$ref": "Quantiles"}, "executorCpuTimeNanos": {"$ref": "Quantiles"}, "executorDeserializeCpuTimeNanos": {"$ref": "Quantiles"}, "executorDeserializeTimeMillis": {"$ref": "Quantiles"}, "executorRunTimeMillis": {"$ref": "Quantiles"}, "gettingResultTimeMillis": {"$ref": "Quantiles"}, "inputMetrics": {"$ref": "InputQuantileMetrics"}, "jvmGcTimeMillis": {"$ref": "Quantiles"}, "memoryBytesSpilled": {"$ref": "Quantiles"}, "outputMetrics": {"$ref": "OutputQuantileMetrics"}, "peakExecutionMemoryBytes": {"$ref": "Quantiles"}, "resultSerializationTimeMillis": {"$ref": "Quantiles"}, "resultSize": {"$ref": "Quantiles"}, "schedulerDelayMillis": {"$ref": "Quantiles"}, "shuffleReadMetrics": {"$ref": "ShuffleReadQuantileMetrics"}, "shuffleWriteMetrics": {"$ref": "ShuffleWriteQuantileMetrics"}}, "type": "object"}, "TaskResourceRequest": {"description": "Resources used per task created by the application.", "id": "TaskResourceRequest", "properties": {"amount": {"format": "double", "type": "number"}, "resourceName": {"type": "string"}}, "type": "object"}, "TemplateParameter": {"description": "A configurable parameter that replaces one or more fields in the template. Parameterizable fields: - Labels - File uris - Job properties - Job arguments - Script variables - Main class (in HadoopJob and SparkJob) - Zone (in ClusterSelector)", "id": "TemplateParameter", "properties": {"description": {"description": "Optional. Brief description of the parameter. Must not exceed 1024 characters.", "type": "string"}, "fields": {"description": "Required. Paths to all fields that the parameter replaces. A field is allowed to appear in at most one parameter's list of field paths.A field path is similar in syntax to a google.protobuf.FieldMask. For example, a field path that references the zone field of a workflow template's cluster selector would be specified as placement.clusterSelector.zone.Also, field paths can reference fields using the following syntax: Values in maps can be referenced by key: labels'key' placement.clusterSelector.clusterLabels'key' placement.managedCluster.labels'key' placement.clusterSelector.clusterLabels'key' jobs'step-id'.labels'key' Jobs in the jobs list can be referenced by step-id: jobs'step-id'.hadoopJob.mainJarFileUri jobs'step-id'.hiveJob.queryFileUri jobs'step-id'.pySparkJob.mainPythonFileUri jobs'step-id'.hadoopJob.jarFileUris0 jobs'step-id'.hadoopJob.archiveUris0 jobs'step-id'.hadoopJob.fileUris0 jobs'step-id'.pySparkJob.pythonFileUris0 Items in repeated fields can be referenced by a zero-based index: jobs'step-id'.sparkJob.args0 Other examples: jobs'step-id'.hadoopJob.properties'key' jobs'step-id'.hadoopJob.args0 jobs'step-id'.hiveJob.scriptVariables'key' jobs'step-id'.hadoopJob.mainJarFileUri placement.clusterSelector.zoneIt may not be possible to parameterize maps and repeated fields in their entirety since only individual map values and individual items in repeated fields can be referenced. For example, the following field paths are invalid: placement.clusterSelector.clusterLabels jobs'step-id'.sparkJob.args", "items": {"type": "string"}, "type": "array"}, "name": {"description": "Required. Parameter name. The parameter name is used as the key, and paired with the parameter value, which are passed to the template when the template is instantiated. The name must contain only capital letters (A-Z), numbers (0-9), and underscores (_), and must not start with a number. The maximum length is 40 characters.", "type": "string"}, "validation": {"$ref": "ParameterValidation", "description": "Optional. Validation rules to be applied to this parameter's value."}}, "type": "object"}, "TerminateSessionRequest": {"description": "A request to terminate an interactive session.", "id": "TerminateSessionRequest", "properties": {"requestId": {"description": "Optional. A unique ID used to identify the request. If the service receives two TerminateSessionRequest (https://cloud.google.com/dataproc/docs/reference/rpc/google.cloud.dataproc.v1#google.cloud.dataproc.v1.TerminateSessionRequest)s with the same ID, the second request is ignored.Recommendation: Set this value to a UUID (https://en.wikipedia.org/wiki/Universally_unique_identifier).The value must contain only letters (a-z, A-Z), numbers (0-9), underscores (_), and hyphens (-). The maximum length is 40 characters.", "type": "string"}}, "type": "object"}, "TestIamPermissionsRequest": {"description": "Request message for TestIamPermissions method.", "id": "TestIamPermissionsRequest", "properties": {"permissions": {"description": "The set of permissions to check for the resource. Permissions with wildcards (such as * or storage.*) are not allowed. For more information see IAM Overview (https://cloud.google.com/iam/docs/overview#permissions).", "items": {"type": "string"}, "type": "array"}}, "type": "object"}, "TestIamPermissionsResponse": {"description": "Response message for TestIamPermissions method.", "id": "TestIamPermissionsResponse", "properties": {"permissions": {"description": "A subset of TestPermissionsRequest.permissions that the caller is allowed.", "items": {"type": "string"}, "type": "array"}}, "type": "object"}, "TrinoJob": {"description": "A Dataproc job for running Trino (https://trino.io/) queries. IMPORTANT: The Dataproc Trino Optional Component (https://cloud.google.com/dataproc/docs/concepts/components/trino) must be enabled when the cluster is created to submit a Trino job to the cluster.", "id": "TrinoJob", "properties": {"clientTags": {"description": "Optional. Trino client tags to attach to this query", "items": {"type": "string"}, "type": "array"}, "continueOnFailure": {"description": "Optional. Whether to continue executing queries if a query fails. The default value is false. Setting to true can be useful when executing independent parallel queries.", "type": "boolean"}, "loggingConfig": {"$ref": "LoggingConfig", "description": "Optional. The runtime log config for job execution."}, "outputFormat": {"description": "Optional. The format in which query output will be displayed. See the Trino documentation for supported output formats", "type": "string"}, "properties": {"additionalProperties": {"type": "string"}, "description": "Optional. A mapping of property names to values. Used to set Trino session properties (https://trino.io/docs/current/sql/set-session.html) Equivalent to using the --session flag in the Trino CLI", "type": "object"}, "queryFileUri": {"description": "The HCFS URI of the script that contains SQL queries.", "type": "string"}, "queryList": {"$ref": "QueryList", "description": "A list of queries."}}, "type": "object"}, "UsageMetrics": {"description": "Usage metrics represent approximate total resources consumed by a workload.", "id": "UsageMetrics", "properties": {"acceleratorType": {"description": "Optional. Accelerator type being used, if any", "type": "string"}, "milliAcceleratorSeconds": {"description": "Optional. Accelerator usage in (milliAccelerator x seconds) (see Dataproc Serverless pricing (https://cloud.google.com/dataproc-serverless/pricing)).", "format": "int64", "type": "string"}, "milliDcuSeconds": {"description": "Optional. DCU (Dataproc Compute Units) usage in (milliDCU x seconds) (see Dataproc Serverless pricing (https://cloud.google.com/dataproc-serverless/pricing)).", "format": "int64", "type": "string"}, "milliSlotSeconds": {"description": "Optional. Slot usage in (milliSlot x seconds).", "format": "int64", "type": "string"}, "shuffleStorageGbSeconds": {"description": "Optional. Shuffle storage usage in (GB x seconds) (see Dataproc Serverless pricing (https://cloud.google.com/dataproc-serverless/pricing)).", "format": "int64", "type": "string"}, "updateTime": {"description": "Optional. The timestamp of the usage metrics.", "format": "google-datetime", "type": "string"}}, "type": "object"}, "UsageSnapshot": {"description": "The usage snapshot represents the resources consumed by a workload at a specified time.", "id": "UsageSnapshot", "properties": {"acceleratorType": {"description": "Optional. Accelerator type being used, if any", "type": "string"}, "milliAccelerator": {"description": "Optional. Milli (one-thousandth) accelerator. (see Dataproc Serverless pricing (https://cloud.google.com/dataproc-serverless/pricing))", "format": "int64", "type": "string"}, "milliDcu": {"description": "Optional. Milli (one-thousandth) Dataproc Compute Units (DCUs) (see Dataproc Serverless pricing (https://cloud.google.com/dataproc-serverless/pricing)).", "format": "int64", "type": "string"}, "milliDcuPremium": {"description": "Optional. Milli (one-thousandth) Dataproc Compute Units (DCUs) charged at premium tier (see Dataproc Serverless pricing (https://cloud.google.com/dataproc-serverless/pricing)).", "format": "int64", "type": "string"}, "milliSlot": {"description": "Optional. Milli (one-thousandth) Slot usage of the workload.", "format": "int64", "type": "string"}, "shuffleStorageGb": {"description": "Optional. Shuffle Storage in gigabytes (GB). (see Dataproc Serverless pricing (https://cloud.google.com/dataproc-serverless/pricing))", "format": "int64", "type": "string"}, "shuffleStorageGbPremium": {"description": "Optional. Shuffle Storage in gigabytes (GB) charged at premium tier. (see Dataproc Serverless pricing (https://cloud.google.com/dataproc-serverless/pricing))", "format": "int64", "type": "string"}, "snapshotTime": {"description": "Optional. The timestamp of the usage snapshot.", "format": "google-datetime", "type": "string"}}, "type": "object"}, "ValueInfo": {"description": "Annotatated property value.", "id": "ValueInfo", "properties": {"annotation": {"description": "Annotation, comment or explanation why the property was set.", "type": "string"}, "overriddenValue": {"description": "Optional. Value which was replaced by the corresponding component.", "type": "string"}, "value": {"description": "Property value.", "type": "string"}}, "type": "object"}, "ValueValidation": {"description": "Validation based on a list of allowed values.", "id": "ValueValidation", "properties": {"values": {"description": "Required. List of allowed values for the parameter.", "items": {"type": "string"}, "type": "array"}}, "type": "object"}, "VirtualClusterConfig": {"description": "The Dataproc cluster config for a cluster that does not directly control the underlying compute resources, such as a Dataproc-on-GKE cluster (https://cloud.google.com/dataproc/docs/guides/dpgke/dataproc-gke-overview).", "id": "VirtualClusterConfig", "properties": {"auxiliaryServicesConfig": {"$ref": "AuxiliaryServicesConfig", "description": "Optional. Configuration of auxiliary services used by this cluster."}, "kubernetesClusterConfig": {"$ref": "KubernetesClusterConfig", "description": "Required. The configuration for running the Dataproc cluster on Kubernetes."}, "stagingBucket": {"description": "Optional. A Cloud Storage bucket used to stage job dependencies, config files, and job driver console output. If you do not specify a staging bucket, Cloud Dataproc will determine a Cloud Storage location (US, ASIA, or EU) for your cluster's staging bucket according to the Compute Engine zone where your cluster is deployed, and then create and manage this project-level, per-location bucket (see Dataproc staging and temp buckets (https://cloud.google.com/dataproc/docs/concepts/configuring-clusters/staging-bucket)). This field requires a Cloud Storage bucket name, not a gs://... URI to a Cloud Storage bucket.", "type": "string"}}, "type": "object"}, "WorkflowGraph": {"description": "The workflow graph.", "id": "WorkflowGraph", "properties": {"nodes": {"description": "Output only. The workflow nodes.", "items": {"$ref": "WorkflowNode"}, "readOnly": true, "type": "array"}}, "type": "object"}, "WorkflowMetadata": {"description": "A Dataproc workflow template resource.", "id": "WorkflowMetadata", "properties": {"clusterName": {"description": "Output only. The name of the target cluster.", "readOnly": true, "type": "string"}, "clusterUuid": {"description": "Output only. The UUID of target cluster.", "readOnly": true, "type": "string"}, "createCluster": {"$ref": "ClusterOperation", "description": "Output only. The create cluster operation metadata.", "readOnly": true}, "dagEndTime": {"description": "Output only. DAG end time, only set for workflows with dag_timeout when DAG ends.", "format": "google-datetime", "readOnly": true, "type": "string"}, "dagStartTime": {"description": "Output only. DAG start time, only set for workflows with dag_timeout when DAG begins.", "format": "google-datetime", "readOnly": true, "type": "string"}, "dagTimeout": {"description": "Output only. The timeout duration for the DAG of jobs, expressed in seconds (see JSON representation of duration (https://developers.google.com/protocol-buffers/docs/proto3#json)).", "format": "google-duration", "readOnly": true, "type": "string"}, "deleteCluster": {"$ref": "ClusterOperation", "description": "Output only. The delete cluster operation metadata.", "readOnly": true}, "endTime": {"description": "Output only. Workflow end time.", "format": "google-datetime", "readOnly": true, "type": "string"}, "graph": {"$ref": "WorkflowGraph", "description": "Output only. The workflow graph.", "readOnly": true}, "parameters": {"additionalProperties": {"type": "string"}, "description": "Map from parameter names to values that were used for those parameters.", "type": "object"}, "startTime": {"description": "Output only. Workflow start time.", "format": "google-datetime", "readOnly": true, "type": "string"}, "state": {"description": "Output only. The workflow state.", "enum": ["UNKNOWN", "PENDING", "RUNNING", "DONE"], "enumDescriptions": ["Unused.", "The operation has been created.", "The operation is running.", "The operation is done; either cancelled or completed."], "readOnly": true, "type": "string"}, "template": {"description": "Output only. The resource name of the workflow template as described in https://cloud.google.com/apis/design/resource_names. For projects.regions.workflowTemplates, the resource name of the template has the following format: projects/{project_id}/regions/{region}/workflowTemplates/{template_id} For projects.locations.workflowTemplates, the resource name of the template has the following format: projects/{project_id}/locations/{location}/workflowTemplates/{template_id}", "readOnly": true, "type": "string"}, "version": {"description": "Output only. The version of template at the time of workflow instantiation.", "format": "int32", "readOnly": true, "type": "integer"}}, "type": "object"}, "WorkflowNode": {"description": "The workflow node.", "id": "WorkflowNode", "properties": {"error": {"description": "Output only. The error detail.", "readOnly": true, "type": "string"}, "jobId": {"description": "Output only. The job id; populated after the node enters RUNNING state.", "readOnly": true, "type": "string"}, "prerequisiteStepIds": {"description": "Output only. Node's prerequisite nodes.", "items": {"type": "string"}, "readOnly": true, "type": "array"}, "state": {"description": "Output only. The node state.", "enum": ["NODE_STATE_UNSPECIFIED", "BLOCKED", "RUNNABLE", "RUNNING", "COMPLETED", "FAILED"], "enumDescriptions": ["State is unspecified.", "The node is awaiting prerequisite node to finish.", "The node is runnable but not running.", "The node is running.", "The node completed successfully.", "The node failed. A node can be marked FAILED because its ancestor or peer failed."], "readOnly": true, "type": "string"}, "stepId": {"description": "Output only. The name of the node.", "readOnly": true, "type": "string"}}, "type": "object"}, "WorkflowTemplate": {"description": "A Dataproc workflow template resource.", "id": "WorkflowTemplate", "properties": {"createTime": {"description": "Output only. The time template was created.", "format": "google-datetime", "readOnly": true, "type": "string"}, "dagTimeout": {"description": "Optional. Timeout duration for the DAG of jobs, expressed in seconds (see JSON representation of duration (https://developers.google.com/protocol-buffers/docs/proto3#json)). The timeout duration must be from 10 minutes (\"600s\") to 24 hours (\"86400s\"). The timer begins when the first job is submitted. If the workflow is running at the end of the timeout period, any remaining jobs are cancelled, the workflow is ended, and if the workflow was running on a managed cluster, the cluster is deleted.", "format": "google-duration", "type": "string"}, "encryptionConfig": {"$ref": "GoogleCloudDataprocV1WorkflowTemplateEncryptionConfig", "description": "Optional. Encryption settings for encrypting workflow template job arguments."}, "id": {"type": "string"}, "jobs": {"description": "Required. The Directed Acyclic Graph of Jobs to submit.", "items": {"$ref": "Ordered<PERSON>ob"}, "type": "array"}, "labels": {"additionalProperties": {"type": "string"}, "description": "Optional. The labels to associate with this template. These labels will be propagated to all jobs and clusters created by the workflow instance.Label keys must contain 1 to 63 characters, and must conform to RFC 1035 (https://www.ietf.org/rfc/rfc1035.txt).Label values may be empty, but, if present, must contain 1 to 63 characters, and must conform to RFC 1035 (https://www.ietf.org/rfc/rfc1035.txt).No more than 32 labels can be associated with a template.", "type": "object"}, "name": {"description": "Output only. The resource name of the workflow template, as described in https://cloud.google.com/apis/design/resource_names. For projects.regions.workflowTemplates, the resource name of the template has the following format: projects/{project_id}/regions/{region}/workflowTemplates/{template_id} For projects.locations.workflowTemplates, the resource name of the template has the following format: projects/{project_id}/locations/{location}/workflowTemplates/{template_id}", "readOnly": true, "type": "string"}, "parameters": {"description": "Optional. Template parameters whose values are substituted into the template. Values for parameters must be provided when the template is instantiated.", "items": {"$ref": "TemplateParameter"}, "type": "array"}, "placement": {"$ref": "WorkflowTemplatePlacement", "description": "Required. WorkflowTemplate scheduling information."}, "updateTime": {"description": "Output only. The time template was last updated.", "format": "google-datetime", "readOnly": true, "type": "string"}, "version": {"description": "Optional. Used to perform a consistent read-modify-write.This field should be left blank for a CreateWorkflowTemplate request. It is required for an UpdateWorkflowTemplate request, and must match the current server version. A typical update template flow would fetch the current template with a GetWorkflowTemplate request, which will return the current template with the version field filled in with the current server version. The user updates other fields in the template, then returns it as part of the UpdateWorkflowTemplate request.", "format": "int32", "type": "integer"}}, "type": "object"}, "WorkflowTemplatePlacement": {"description": "Specifies workflow execution target.Either managed_cluster or cluster_selector is required.", "id": "WorkflowTemplatePlacement", "properties": {"clusterSelector": {"$ref": "ClusterSelector", "description": "Optional. A selector that chooses target cluster for jobs based on metadata.The selector is evaluated at the time each job is submitted."}, "managedCluster": {"$ref": "ManagedCluster", "description": "A cluster that is managed by the workflow."}}, "type": "object"}, "WriteSessionSparkApplicationContextRequest": {"description": "Write Spark Application data to internal storage systems", "id": "WriteSessionSparkApplicationContextRequest", "properties": {"parent": {"description": "Required. Parent (Batch) resource reference.", "type": "string"}, "sparkWrapperObjects": {"description": "Required. The batch of spark application context objects sent for ingestion.", "items": {"$ref": "SparkWrapperObject"}, "type": "array"}}, "type": "object"}, "WriteSessionSparkApplicationContextResponse": {"description": "Response returned as an acknowledgement of receipt of data.", "id": "WriteSessionSparkApplicationContextResponse", "properties": {}, "type": "object"}, "WriteSparkApplicationContextRequest": {"description": "Write Spark Application data to internal storage systems", "id": "WriteSparkApplicationContextRequest", "properties": {"parent": {"description": "Required. Parent (Batch) resource reference.", "type": "string"}, "sparkWrapperObjects": {"items": {"$ref": "SparkWrapperObject"}, "type": "array"}}, "type": "object"}, "WriteSparkApplicationContextResponse": {"description": "Response returned as an acknowledgement of receipt of data.", "id": "WriteSparkApplicationContextResponse", "properties": {}, "type": "object"}, "YarnApplication": {"description": "A YARN application created by a job. Application information is a subset of org.apache.hadoop.yarn.proto.YarnProtos.ApplicationReportProto.Beta Feature: This report is available for testing purposes only. It may be changed before final release.", "id": "YarnApplication", "properties": {"name": {"description": "Required. The application name.", "type": "string"}, "progress": {"description": "Required. The numerical progress of the application, from 1 to 100.", "format": "float", "type": "number"}, "state": {"description": "Required. The application state.", "enum": ["STATE_UNSPECIFIED", "NEW", "NEW_SAVING", "SUBMITTED", "ACCEPTED", "RUNNING", "FINISHED", "FAILED", "KILLED"], "enumDescriptions": ["Status is unspecified.", "Status is NEW.", "Status is NEW_SAVING.", "Status is SUBMITTED.", "Status is ACCEPTED.", "Status is RUNNING.", "Status is FINISHED.", "Status is FAILED.", "Status is KILLED."], "type": "string"}, "trackingUrl": {"description": "Optional. The HTTP URL of the ApplicationMaster, HistoryServer, or TimelineServer that provides application-specific information. The URL uses the internal hostname, and requires a proxy server for resolution and, possibly, access.", "type": "string"}}, "type": "object"}}, "servicePath": "", "title": "Cloud Dataproc API", "version": "v1"}