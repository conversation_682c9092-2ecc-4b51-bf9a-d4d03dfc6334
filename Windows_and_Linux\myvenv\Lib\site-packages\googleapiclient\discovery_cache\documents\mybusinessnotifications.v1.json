{"basePath": "", "baseUrl": "https://mybusinessnotifications.googleapis.com/", "batchPath": "batch", "canonicalName": "My Business Notification Settings", "description": "The My Business Notification Settings API enables managing notification settings for business accounts. Note - If you have a quota of 0 after enabling the API, please request for GBP API access.", "discoveryVersion": "v1", "documentationLink": "https://developers.google.com/my-business/", "fullyEncodeReservedExpansion": true, "icons": {"x16": "http://www.google.com/images/icons/product/search-16.gif", "x32": "http://www.google.com/images/icons/product/search-32.gif"}, "id": "mybusinessnotifications:v1", "kind": "discovery#restDescription", "mtlsRootUrl": "https://mybusinessnotifications.mtls.googleapis.com/", "name": "mybusinessnotifications", "ownerDomain": "google.com", "ownerName": "Google", "parameters": {"$.xgafv": {"description": "V1 error format.", "enum": ["1", "2"], "enumDescriptions": ["v1 error format", "v2 error format"], "location": "query", "type": "string"}, "access_token": {"description": "OAuth access token.", "location": "query", "type": "string"}, "alt": {"default": "json", "description": "Data format for response.", "enum": ["json", "media", "proto"], "enumDescriptions": ["Responses with Content-Type of application/json", "Media download with context-dependent Content-Type", "Responses with Content-Type of application/x-protobuf"], "location": "query", "type": "string"}, "callback": {"description": "JSONP", "location": "query", "type": "string"}, "fields": {"description": "Selector specifying which fields to include in a partial response.", "location": "query", "type": "string"}, "key": {"description": "API key. Your API key identifies your project and provides you with API access, quota, and reports. Required unless you provide an OAuth 2.0 token.", "location": "query", "type": "string"}, "oauth_token": {"description": "OAuth 2.0 token for the current user.", "location": "query", "type": "string"}, "prettyPrint": {"default": "true", "description": "Returns response with indentations and line breaks.", "location": "query", "type": "boolean"}, "quotaUser": {"description": "Available to use for quota purposes for server-side applications. Can be any arbitrary string assigned to a user, but should not exceed 40 characters.", "location": "query", "type": "string"}, "uploadType": {"description": "Legacy upload protocol for media (e.g. \"media\", \"multipart\").", "location": "query", "type": "string"}, "upload_protocol": {"description": "Upload protocol for media (e.g. \"raw\", \"multipart\").", "location": "query", "type": "string"}}, "protocol": "rest", "resources": {"accounts": {"methods": {"getNotificationSetting": {"description": "Returns the pubsub notification settings for the account.", "flatPath": "v1/accounts/{accountsId}/notificationSetting", "httpMethod": "GET", "id": "mybusinessnotifications.accounts.getNotificationSetting", "parameterOrder": ["name"], "parameters": {"name": {"description": "Required. The resource name of the notification setting we are trying to fetch.", "location": "path", "pattern": "^accounts/[^/]+/notificationSetting$", "required": true, "type": "string"}}, "path": "v1/{+name}", "response": {"$ref": "NotificationSetting"}}, "updateNotificationSetting": {"description": "Sets the pubsub notification setting for the account informing Google which topic to send pubsub notifications for. Use the notification_types field within notification_setting to manipulate the events an account wants to subscribe to. An account will only have one notification setting resource, and only one pubsub topic can be set. To delete the setting, update with an empty notification_types", "flatPath": "v1/accounts/{accountsId}/notificationSetting", "httpMethod": "PATCH", "id": "mybusinessnotifications.accounts.updateNotificationSetting", "parameterOrder": ["name"], "parameters": {"name": {"description": "Required. The resource name this setting is for. This is of the form `accounts/{account_id}/notificationSetting`.", "location": "path", "pattern": "^accounts/[^/]+/notificationSetting$", "required": true, "type": "string"}, "updateMask": {"description": "Required. The specific fields that should be updated. The only editable field is notification_setting.", "format": "google-fieldmask", "location": "query", "type": "string"}}, "path": "v1/{+name}", "request": {"$ref": "NotificationSetting"}, "response": {"$ref": "NotificationSetting"}}}}}, "revision": "********", "rootUrl": "https://mybusinessnotifications.googleapis.com/", "schemas": {"NotificationSetting": {"description": "A Google Pub/Sub topic where notifications can be published when a location is updated or has a new review. There will be only one notification setting resource per-account.", "id": "NotificationSetting", "properties": {"name": {"description": "Required. The resource name this setting is for. This is of the form `accounts/{account_id}/notificationSetting`.", "type": "string"}, "notificationTypes": {"description": "The types of notifications that will be sent to the Pub/Sub topic. To stop receiving notifications entirely, use NotificationSettings.UpdateNotificationSetting with an empty notification_types or set the pubsub_topic to an empty string.", "items": {"enum": ["NOTIFICATION_TYPE_UNSPECIFIED", "GOOGLE_UPDATE", "NEW_REVIEW", "UPDATED_REVIEW", "NEW_CUSTOMER_MEDIA", "NEW_QUESTION", "UPDATED_QUESTION", "NEW_ANSWER", "UPDATED_ANSWER", "DUPLICATE_LOCATION", "LOSS_OF_VOICE_OF_MERCHANT", "VOICE_OF_MERCHANT_UPDATED"], "enumDeprecated": [false, false, false, false, false, false, false, false, false, false, true, false], "enumDescriptions": ["No notification type. Will not match any notifications.", "The location has Google updates for review. The location_name field on the notification will provide the resource name of the location with Google updates.", "A new review has been added to the location. The review_name field on the notification will provide the resource name of the review that was added, and location_name will have the location's resource name.", "A review on the location has been updated. The review_name field on the notification will provide the resource name of the review that was added, and location_name will have the location's resource name.", "A new media item has been added to the location by a Google Maps user. The notification will provide the resource name of the new media item.", "A new question is added to the location. The notification will provide the resource name of question.", "A question of the location is updated. The notification will provide the resource name of question.", "A new answer is added to the location. The notification will provide the resource name of question and answer.", "An answer of the location is updated. The notification will provide the resource name of question and answer.", "Indicates whether there is a change in location metadata's duplicate location field.", "Deprecated: Migrate the existing usages of this value to the more expanded \"VOICE_OF_MERCHANT_UPDATED\".", "Indicates whether the location has an update in Voice of Merchant (VOM) status. VOM dictates whether the location is in good standing and the merchant has control over the business on Google. Any edits made to the location will propagate to Maps after passing the review phase. Call GetVoiceOfMerchantState rpc for more details."], "type": "string"}, "type": "array"}, "pubsubTopic": {"description": "Optional. The Google Pub/Sub topic that will receive notifications when locations managed by this account are updated. If unset, no notifications will be posted. <NAME_EMAIL> must have at least Publish permissions on the Pub/Sub topic.", "type": "string"}}, "type": "object"}}, "servicePath": "", "title": "My Business Notifications API", "version": "v1", "version_module": true}