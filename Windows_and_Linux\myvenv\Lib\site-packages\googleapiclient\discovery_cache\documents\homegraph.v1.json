{"auth": {"oauth2": {"scopes": {"https://www.googleapis.com/auth/homegraph": {"description": "Private Service: https://www.googleapis.com/auth/homegraph"}}}}, "basePath": "", "baseUrl": "https://homegraph.googleapis.com/", "batchPath": "batch", "canonicalName": "Home Graph Service", "description": "", "discoveryVersion": "v1", "documentationLink": "https://developers.home.google.com/cloud-to-cloud/get-started", "fullyEncodeReservedExpansion": true, "icons": {"x16": "http://www.google.com/images/icons/product/search-16.gif", "x32": "http://www.google.com/images/icons/product/search-32.gif"}, "id": "homegraph:v1", "kind": "discovery#restDescription", "mtlsRootUrl": "https://homegraph.mtls.googleapis.com/", "name": "homegraph", "ownerDomain": "google.com", "ownerName": "Google", "parameters": {"$.xgafv": {"description": "V1 error format.", "enum": ["1", "2"], "enumDescriptions": ["v1 error format", "v2 error format"], "location": "query", "type": "string"}, "access_token": {"description": "OAuth access token.", "location": "query", "type": "string"}, "alt": {"default": "json", "description": "Data format for response.", "enum": ["json", "media", "proto"], "enumDescriptions": ["Responses with Content-Type of application/json", "Media download with context-dependent Content-Type", "Responses with Content-Type of application/x-protobuf"], "location": "query", "type": "string"}, "callback": {"description": "JSONP", "location": "query", "type": "string"}, "fields": {"description": "Selector specifying which fields to include in a partial response.", "location": "query", "type": "string"}, "key": {"description": "API key. Your API key identifies your project and provides you with API access, quota, and reports. Required unless you provide an OAuth 2.0 token.", "location": "query", "type": "string"}, "oauth_token": {"description": "OAuth 2.0 token for the current user.", "location": "query", "type": "string"}, "prettyPrint": {"default": "true", "description": "Returns response with indentations and line breaks.", "location": "query", "type": "boolean"}, "quotaUser": {"description": "Available to use for quota purposes for server-side applications. Can be any arbitrary string assigned to a user, but should not exceed 40 characters.", "location": "query", "type": "string"}, "uploadType": {"description": "Legacy upload protocol for media (e.g. \"media\", \"multipart\").", "location": "query", "type": "string"}, "upload_protocol": {"description": "Upload protocol for media (e.g. \"raw\", \"multipart\").", "location": "query", "type": "string"}}, "protocol": "rest", "resources": {"agentUsers": {"methods": {"delete": {"description": "Unlinks the given third-party user from your smart home Action. All data related to this user will be deleted. For more details on how users link their accounts, see [fulfillment and authentication](https://developers.home.google.com/cloud-to-cloud/primer/fulfillment). The third-party user's identity is passed in via the `agent_user_id` (see DeleteAgentUserRequest). This request must be authorized using service account credentials from your Actions console project.", "flatPath": "v1/agentUsers/{agentUsersId}", "httpMethod": "DELETE", "id": "homegraph.agentUsers.delete", "parameterOrder": ["agentUserId"], "parameters": {"agentUserId": {"description": "Required. Third-party user ID.", "location": "path", "pattern": "^agentUsers/.*$", "required": true, "type": "string"}, "requestId": {"description": "Request ID used for debugging.", "location": "query", "type": "string"}}, "path": "v1/{+agentUserId}", "response": {"$ref": "Empty"}, "scopes": ["https://www.googleapis.com/auth/homegraph"]}}}, "devices": {"methods": {"query": {"description": "Gets the current states in Home Graph for the given set of the third-party user's devices. The third-party user's identity is passed in via the `agent_user_id` (see QueryRequest). This request must be authorized using service account credentials from your Actions console project.", "flatPath": "v1/devices:query", "httpMethod": "POST", "id": "homegraph.devices.query", "parameterOrder": [], "parameters": {}, "path": "v1/devices:query", "request": {"$ref": "QueryRequest"}, "response": {"$ref": "QueryResponse"}, "scopes": ["https://www.googleapis.com/auth/homegraph"]}, "reportStateAndNotification": {"description": "Reports device state and optionally sends device notifications. Called by your smart home Action when the state of a third-party device changes or you need to send a notification about the device. See [Implement Report State](https://developers.home.google.com/cloud-to-cloud/integration/report-state) for more information. This method updates the device state according to its declared [traits](https://developers.home.google.com/cloud-to-cloud/primer/device-types-and-traits). Publishing a new state value outside of these traits will result in an `INVALID_ARGUMENT` error response. The third-party user's identity is passed in via the `agent_user_id` (see ReportStateAndNotificationRequest). This request must be authorized using service account credentials from your Actions console project.", "flatPath": "v1/devices:reportStateAndNotification", "httpMethod": "POST", "id": "homegraph.devices.reportStateAndNotification", "parameterOrder": [], "parameters": {}, "path": "v1/devices:reportStateAndNotification", "request": {"$ref": "ReportStateAndNotificationRequest"}, "response": {"$ref": "ReportStateAndNotificationResponse"}, "scopes": ["https://www.googleapis.com/auth/homegraph"]}, "requestSync": {"description": "Requests Google to send an `action.devices.SYNC` [intent](https://developers.home.google.com/cloud-to-cloud/intents/sync) to your smart home Action to update device metadata for the given user. The third-party user's identity is passed via the `agent_user_id` (see RequestSyncDevicesRequest). This request must be authorized using service account credentials from your Actions console project.", "flatPath": "v1/devices:requestSync", "httpMethod": "POST", "id": "homegraph.devices.requestSync", "parameterOrder": [], "parameters": {}, "path": "v1/devices:requestSync", "request": {"$ref": "RequestSyncDevicesRequest"}, "response": {"$ref": "RequestSyncDevicesResponse"}, "scopes": ["https://www.googleapis.com/auth/homegraph"]}, "sync": {"description": "Gets all the devices associated with the given third-party user. The third-party user's identity is passed in via the `agent_user_id` (see SyncRequest). This request must be authorized using service account credentials from your Actions console project.", "flatPath": "v1/devices:sync", "httpMethod": "POST", "id": "homegraph.devices.sync", "parameterOrder": [], "parameters": {}, "path": "v1/devices:sync", "request": {"$ref": "SyncRequest"}, "response": {"$ref": "SyncResponse"}, "scopes": ["https://www.googleapis.com/auth/homegraph"]}}}}, "revision": "********", "rootUrl": "https://homegraph.googleapis.com/", "schemas": {"AgentDeviceId": {"description": "Third-party device ID for one device.", "id": "AgentDeviceId", "properties": {"id": {"description": "Third-party device ID.", "type": "string"}}, "type": "object"}, "AgentOtherDeviceId": {"description": "Alternate third-party device ID.", "id": "AgentOtherDeviceId", "properties": {"agentId": {"description": "Project ID for your smart home Action.", "type": "string"}, "deviceId": {"description": "Unique third-party device ID.", "type": "string"}}, "type": "object"}, "Device": {"description": "Third-party device definition.", "id": "<PERSON><PERSON>", "properties": {"attributes": {"additionalProperties": {"description": "Properties of the object.", "type": "any"}, "description": "Attributes for the traits supported by the device.", "type": "object"}, "customData": {"additionalProperties": {"description": "Properties of the object.", "type": "any"}, "description": "Custom device attributes stored in Home Graph and provided to your smart home Action in each [QUERY](https://developers.home.google.com/cloud-to-cloud/intents/query) and [EXECUTE](https://developers.home.google.com/cloud-to-cloud/intents/execute) intent. Data in this object has a few constraints: No sensitive information, including but not limited to Personally Identifiable Information.", "type": "object"}, "deviceInfo": {"$ref": "DeviceInfo", "description": "Device manufacturer, model, hardware version, and software version."}, "id": {"description": "Third-party device ID.", "type": "string"}, "name": {"$ref": "DeviceNames", "description": "Names given to this device by your smart home Action."}, "notificationSupportedByAgent": {"description": "Indicates whether your smart home Action will report notifications to Google for this device via ReportStateAndNotification. If your smart home Action enables users to control device notifications, you should update this field and call RequestSyncDevices.", "type": "boolean"}, "otherDeviceIds": {"description": "Alternate IDs associated with this device. This is used to identify cloud synced devices enabled for [local fulfillment](https://developers.home.google.com/local-home/overview).", "items": {"$ref": "AgentOtherDeviceId"}, "type": "array"}, "roomHint": {"description": "Suggested name for the room where this device is installed. Google attempts to use this value during user setup.", "type": "string"}, "structureHint": {"description": "Suggested name for the structure where this device is installed. Google attempts to use this value during user setup.", "type": "string"}, "traits": {"description": "Traits supported by the device. See [device traits](https://developers.home.google.com/cloud-to-cloud/traits).", "items": {"type": "string"}, "type": "array"}, "type": {"description": "Hardware type of the device. See [device types](https://developers.home.google.com/cloud-to-cloud/guides).", "type": "string"}, "willReportState": {"description": "Indicates whether your smart home Action will report state of this device to Google via ReportStateAndNotification.", "type": "boolean"}}, "type": "object"}, "DeviceInfo": {"description": "Device information.", "id": "DeviceInfo", "properties": {"hwVersion": {"description": "Device hardware version.", "type": "string"}, "manufacturer": {"description": "Device manufacturer.", "type": "string"}, "model": {"description": "Device model.", "type": "string"}, "swVersion": {"description": "Device software version.", "type": "string"}}, "type": "object"}, "DeviceNames": {"description": "Identifiers used to describe the device.", "id": "DeviceNames", "properties": {"defaultNames": {"description": "List of names provided by the manufacturer rather than the user, such as serial numbers, SKUs, etc.", "items": {"type": "string"}, "type": "array"}, "name": {"description": "Primary name of the device, generally provided by the user.", "type": "string"}, "nicknames": {"description": "Additional names provided by the user for the device.", "items": {"type": "string"}, "type": "array"}}, "type": "object"}, "Empty": {"description": "A generic empty message that you can re-use to avoid defining duplicated empty messages in your APIs. A typical example is to use it as the request or the response type of an API method. For instance: service Foo { rpc Bar(google.protobuf.Empty) returns (google.protobuf.Empty); }", "id": "Empty", "properties": {}, "type": "object"}, "QueryRequest": {"description": "Request type for the [`Query`](#google.home.graph.v1.HomeGraphApiService.Query) call.", "id": "QueryRequest", "properties": {"agentUserId": {"description": "Required. Third-party user ID.", "type": "string"}, "inputs": {"description": "Required. Inputs containing third-party device IDs for which to get the device states.", "items": {"$ref": "QueryRequestInput"}, "type": "array"}, "requestId": {"description": "Request ID used for debugging.", "type": "string"}}, "type": "object"}, "QueryRequestInput": {"description": "Device ID inputs to QueryRequest.", "id": "QueryRequestInput", "properties": {"payload": {"$ref": "QueryRequestPayload", "description": "Payload containing third-party device IDs."}}, "type": "object"}, "QueryRequestPayload": {"description": "Payload containing device IDs.", "id": "QueryRequestPayload", "properties": {"devices": {"description": "Third-party device IDs for which to get the device states.", "items": {"$ref": "AgentDeviceId"}, "type": "array"}}, "type": "object"}, "QueryResponse": {"description": "Response type for the [`Query`](#google.home.graph.v1.HomeGraphApiService.Query) call. This should follow the same format as the Google smart home `action.devices.QUERY` [response](https://developers.home.google.com/cloud-to-cloud/intents/query). Example: ```json { \"requestId\": \"ff36a3cc-ec34-11e6-b1a0-64510650abcf\", \"payload\": { \"devices\": { \"123\": { \"on\": true, \"online\": true }, \"456\": { \"on\": true, \"online\": true, \"brightness\": 80, \"color\": { \"name\": \"cerulean\", \"spectrumRGB\": 31655 } } } } } ```", "id": "QueryResponse", "properties": {"payload": {"$ref": "QueryResponsePayload", "description": "Device states for the devices given in the request."}, "requestId": {"description": "Request ID used for debugging. Copied from the request.", "type": "string"}}, "type": "object"}, "QueryResponsePayload": {"description": "Payload containing device states information.", "id": "QueryResponsePayload", "properties": {"devices": {"additionalProperties": {"additionalProperties": {"description": "Properties of the object.", "type": "any"}, "type": "object"}, "description": "States of the devices. Map of third-party device ID to struct of device states.", "type": "object"}}, "type": "object"}, "ReportStateAndNotificationDevice": {"description": "The states and notifications specific to a device.", "id": "ReportStateAndNotificationDevice", "properties": {"notifications": {"additionalProperties": {"description": "Properties of the object.", "type": "any"}, "description": "Notifications metadata for devices. See the **Device NOTIFICATIONS** section of the individual trait [reference guides](https://developers.home.google.com/cloud-to-cloud/traits).", "type": "object"}, "states": {"additionalProperties": {"description": "Properties of the object.", "type": "any"}, "description": "States of devices to update. See the **Device STATES** section of the individual trait [reference guides](https://developers.home.google.com/cloud-to-cloud/traits).", "type": "object"}}, "type": "object"}, "ReportStateAndNotificationRequest": {"description": "Request type for the [`ReportStateAndNotification`](#google.home.graph.v1.HomeGraphApiService.ReportStateAndNotification) call. It may include states, notifications, or both. States and notifications are defined per `device_id` (for example, \"123\" and \"456\" in the following example). Example: ```json { \"requestId\": \"ff36a3cc-ec34-11e6-b1a0-64510650abcf\", \"agentUserId\": \"1234\", \"payload\": { \"devices\": { \"states\": { \"123\": { \"on\": true }, \"456\": { \"on\": true, \"brightness\": 10 }, }, } } } ```", "id": "ReportStateAndNotificationRequest", "properties": {"agentUserId": {"description": "Required. Third-party user ID.", "type": "string"}, "eventId": {"description": "Unique identifier per event (for example, a doorbell press).", "type": "string"}, "followUpToken": {"deprecated": true, "description": "Deprecated.", "type": "string"}, "payload": {"$ref": "StateAndNotificationPayload", "description": "Required. State of devices to update and notification metadata for devices."}, "requestId": {"description": "Request ID used for debugging.", "type": "string"}}, "type": "object"}, "ReportStateAndNotificationResponse": {"description": "Response type for the [`ReportStateAndNotification`](#google.home.graph.v1.HomeGraphApiService.ReportStateAndNotification) call.", "id": "ReportStateAndNotificationResponse", "properties": {"requestId": {"description": "Request ID copied from ReportStateAndNotificationRequest.", "type": "string"}}, "type": "object"}, "RequestSyncDevicesRequest": {"description": "Request type for the [`RequestSyncDevices`](#google.home.graph.v1.HomeGraphApiService.RequestSyncDevices) call.", "id": "RequestSyncDevicesRequest", "properties": {"agentUserId": {"description": "Required. Third-party user ID.", "type": "string"}, "async": {"description": "Optional. If set, the request will be added to a queue and a response will be returned immediately. This enables concurrent requests for the given `agent_user_id`, but the caller will not receive any error responses.", "type": "boolean"}}, "type": "object"}, "RequestSyncDevicesResponse": {"description": "Response type for the [`RequestSyncDevices`](#google.home.graph.v1.HomeGraphApiService.RequestSyncDevices) call. Intentionally empty upon success. An HTTP response code is returned with more details upon failure.", "id": "RequestSyncDevicesResponse", "properties": {}, "type": "object"}, "StateAndNotificationPayload": {"description": "Payload containing the state and notification information for devices.", "id": "StateAndNotificationPayload", "properties": {"devices": {"$ref": "ReportStateAndNotificationDevice", "description": "The devices for updating state and sending notifications."}}, "type": "object"}, "SyncRequest": {"description": "Request type for the [`Sync`](#google.home.graph.v1.HomeGraphApiService.Sync) call.", "id": "SyncRequest", "properties": {"agentUserId": {"description": "Required. Third-party user ID.", "type": "string"}, "requestId": {"description": "Request ID used for debugging.", "type": "string"}}, "type": "object"}, "SyncResponse": {"description": "Response type for the [`Sync`](#google.home.graph.v1.HomeGraphApiService.Sync) call. This should follow the same format as the Google smart home `action.devices.SYNC` [response](https://developers.home.google.com/cloud-to-cloud/intents/sync). Example: ```json { \"requestId\": \"ff36a3cc-ec34-11e6-b1a0-64510650abcf\", \"payload\": { \"agentUserId\": \"1836.15267389\", \"devices\": [{ \"id\": \"123\", \"type\": \"action.devices.types.OUTLET\", \"traits\": [ \"action.devices.traits.OnOff\" ], \"name\": { \"defaultNames\": [\"My Outlet 1234\"], \"name\": \"Night light\", \"nicknames\": [\"wall plug\"] }, \"willReportState\": false, \"deviceInfo\": { \"manufacturer\": \"lights-out-inc\", \"model\": \"hs1234\", \"hwVersion\": \"3.2\", \"swVersion\": \"11.4\" }, \"customData\": { \"fooValue\": 74, \"barValue\": true, \"bazValue\": \"foo\" } }] } } ```", "id": "SyncResponse", "properties": {"payload": {"$ref": "SyncResponsePayload", "description": "Devices associated with the third-party user."}, "requestId": {"description": "Request ID used for debugging. Copied from the request.", "type": "string"}}, "type": "object"}, "SyncResponsePayload": {"description": "Payload containing device information.", "id": "SyncResponsePayload", "properties": {"agentUserId": {"description": "Third-party user ID", "type": "string"}, "devices": {"description": "Devices associated with the third-party user.", "items": {"$ref": "<PERSON><PERSON>"}, "type": "array"}}, "type": "object"}}, "servicePath": "", "title": "HomeGraph API", "version": "v1", "version_module": true}