
This file lists modules PyInstaller was not able to find. This does not
necessarily mean this module is required for running your program. Python and
Python 3rd-party packages include a lot of conditional or optional modules. For
example the module 'ntpath' only exists on Windows, whereas the module
'posixpath' only exists on Posix systems.

Types if import:
* top-level: imported at the top-level - look at these first
* conditional: imported within an if-statement
* delayed: imported within a function
* optional: imported within a try-except-statement

IMPORTANT: Do NOT post this list to the issue-tracker. Use it as a basis for
            tracking down the missing module yourself. Thanks!

missing module named pwd - imported by posixpath (delayed, conditional, optional), shutil (delayed, optional), tarfile (optional), pathlib (delayed, optional), subprocess (delayed, conditional, optional), http.server (delayed, optional), netrc (delayed, conditional), getpass (delayed), setuptools._distutils.util (delayed, conditional, optional), setuptools._vendor.backports.tarfile (optional), setuptools._distutils.archive_util (optional)
missing module named grp - imported by shutil (delayed, optional), tarfile (optional), pathlib (delayed, optional), subprocess (delayed, conditional, optional), setuptools._vendor.backports.tarfile (optional), setuptools._distutils.archive_util (optional)
missing module named posix - imported by os (conditional, optional), posixpath (optional), shutil (conditional), importlib._bootstrap_external (conditional)
missing module named resource - imported by posix (top-level)
missing module named _frozen_importlib_external - imported by importlib._bootstrap (delayed), importlib (optional), importlib.abc (optional), zipimport (top-level)
excluded module named _frozen_importlib - imported by importlib (optional), importlib.abc (optional), zipimport (top-level)
missing module named _posixsubprocess - imported by subprocess (conditional), multiprocessing.util (delayed)
missing module named fcntl - imported by subprocess (optional), tqdm.utils (delayed, optional)
missing module named _posixshmem - imported by multiprocessing.resource_tracker (conditional), multiprocessing.shared_memory (conditional)
missing module named _scproxy - imported by urllib.request (conditional)
missing module named termios - imported by tty (top-level), getpass (optional), tqdm.utils (delayed, optional)
missing module named multiprocessing.BufferTooShort - imported by multiprocessing (top-level), multiprocessing.connection (top-level)
missing module named multiprocessing.AuthenticationError - imported by multiprocessing (top-level), multiprocessing.connection (top-level)
missing module named multiprocessing.get_context - imported by multiprocessing (top-level), multiprocessing.pool (top-level), multiprocessing.managers (top-level), multiprocessing.sharedctypes (top-level)
missing module named multiprocessing.TimeoutError - imported by multiprocessing (top-level), multiprocessing.pool (top-level)
missing module named multiprocessing.set_start_method - imported by multiprocessing (top-level), multiprocessing.spawn (top-level)
missing module named multiprocessing.get_start_method - imported by multiprocessing (top-level), multiprocessing.spawn (top-level)
missing module named multiprocessing.RLock - imported by multiprocessing (delayed, conditional, optional), tqdm.std (delayed, conditional, optional)
missing module named pyimod02_importers - imported by C:\Users\<USER>\Desktop\Mes_projets\WritingToolsClean\Windows_and_Linux\myvenv\Lib\site-packages\PyInstaller\hooks\rthooks\pyi_rth_pkgutil.py (delayed)
missing module named asyncio.DefaultEventLoopPolicy - imported by asyncio (delayed, conditional), asyncio.events (delayed, conditional)
missing module named annotationlib - imported by typing_extensions (conditional)
missing module named 'unittest.mock' - imported by setuptools._distutils.compilers.C.msvc (top-level)
missing module named vms_lib - imported by platform (delayed, optional)
missing module named 'java.lang' - imported by platform (delayed, optional)
missing module named java - imported by platform (delayed)
missing module named _winreg - imported by platform (delayed, optional)
missing module named usercustomize - imported by site (delayed, optional)
missing module named sitecustomize - imported by site (delayed, optional)
missing module named readline - imported by cmd (delayed, conditional, optional), code (delayed, conditional, optional), pdb (delayed, optional), site (delayed, optional), rlcompleter (optional)
missing module named _typeshed - imported by pydantic_core._pydantic_core (top-level), pydantic._internal._dataclasses (conditional), anyio.abc._eventloop (conditional), anyio._core._sockets (conditional), anyio._core._fileio (conditional), anyio._core._tempfile (conditional), httpx._transports.wsgi (conditional), anyio._backends._asyncio (conditional), anyio._core._asyncio_selector_thread (conditional), anyio._backends._trio (conditional), setuptools._distutils.dist (conditional), setuptools.glob (conditional), setuptools.compat.py311 (conditional)
missing module named _manylinux - imported by packaging._manylinux (delayed, optional), setuptools._vendor.packaging._manylinux (delayed, optional), setuptools._vendor.wheel.vendored.packaging._manylinux (delayed, optional)
missing module named importlib_resources - imported by tqdm.cli (delayed, conditional, optional), setuptools._vendor.jaraco.text (optional)
missing module named trove_classifiers - imported by setuptools.config._validate_pyproject.formats (optional)
missing module named collections.Mapping - imported by collections (optional), google.auth.jwt (optional), google.auth.pluggable (optional), google.auth.identity_pool (optional)
missing module named cython - imported by pydantic.v1.version (optional)
excluded module named email_validator - imported by pydantic.networks (delayed, conditional, optional), pydantic.v1.networks (delayed, conditional, optional), pydantic.v1._hypothesis_plugin (optional)
missing module named toml - imported by pydantic.v1.mypy (delayed, conditional, optional)
missing module named 'mypy.version' - imported by pydantic.mypy (top-level), pydantic.v1.mypy (top-level)
missing module named 'mypy.util' - imported by pydantic.mypy (top-level), pydantic.v1.mypy (top-level)
missing module named 'mypy.typevars' - imported by pydantic.mypy (top-level), pydantic.v1.mypy (top-level)
missing module named 'mypy.types' - imported by pydantic.mypy (top-level), pydantic.v1.mypy (top-level)
missing module named 'mypy.server' - imported by pydantic.mypy (top-level), pydantic.v1.mypy (top-level)
missing module named 'mypy.semanal' - imported by pydantic.mypy (top-level), pydantic.v1.mypy (top-level)
missing module named 'mypy.plugins' - imported by pydantic.mypy (top-level), pydantic.v1.mypy (top-level)
missing module named 'mypy.plugin' - imported by pydantic.mypy (top-level), pydantic.v1.mypy (top-level)
missing module named 'mypy.options' - imported by pydantic.mypy (top-level), pydantic.v1.mypy (top-level)
missing module named 'mypy.nodes' - imported by pydantic.mypy (top-level), pydantic.v1.mypy (top-level)
missing module named 'mypy.errorcodes' - imported by pydantic.v1.mypy (top-level)
missing module named dotenv - imported by pydantic.v1.env_settings (delayed, optional)
missing module named hypothesis - imported by pydantic.v1._hypothesis_plugin (top-level)
missing module named 'mypy.typeops' - imported by pydantic.mypy (top-level)
missing module named 'mypy.type_visitor' - imported by pydantic.mypy (top-level)
missing module named 'mypy.state' - imported by pydantic.mypy (top-level)
missing module named 'mypy.expandtype' - imported by pydantic.mypy (top-level)
missing module named mypy - imported by pydantic.mypy (top-level)
missing module named eval_type_backport - imported by pydantic._internal._typing_extra (delayed, optional)
missing module named 'rich.pretty' - imported by pydantic._internal._core_utils (delayed)
missing module named rich - imported by pydantic._internal._core_utils (conditional)
missing module named pydantic.PydanticUserError - imported by pydantic (top-level), pydantic.root_model (top-level)
missing module named pydantic.PydanticSchemaGenerationError - imported by pydantic (delayed), pydantic.functional_validators (delayed, conditional)
missing module named pydantic.BaseModel - imported by pydantic (conditional), pydantic._internal._typing_extra (conditional), pydantic._internal._import_utils (delayed, conditional), pydantic._internal._core_utils (delayed), pydantic.deprecated.copy_internals (delayed, conditional), ollama._types (top-level), openai.resources.beta.realtime.realtime (top-level)
missing module named curio - imported by sniffio._impl (delayed, conditional)
missing module named 'trio.testing' - imported by anyio._backends._trio (delayed)
missing module named exceptiongroup - imported by anyio._core._exceptions (conditional), anyio._core._sockets (conditional), anyio._backends._asyncio (conditional), anyio._backends._trio (conditional)
missing module named 'trio.to_thread' - imported by anyio._backends._trio (top-level)
missing module named 'trio.socket' - imported by anyio._backends._trio (top-level)
missing module named outcome - imported by anyio._backends._trio (top-level)
missing module named 'trio.lowlevel' - imported by anyio._backends._trio (top-level)
missing module named 'trio.from_thread' - imported by anyio._backends._trio (top-level)
missing module named _pytest - imported by anyio._backends._asyncio (delayed)
missing module named uvloop - imported by anyio._backends._asyncio (delayed, conditional)
missing module named jiter.from_json - imported by jiter (top-level), openai.lib.streaming.chat._completions (top-level)
missing module named trio - imported by httpx._transports.asgi (delayed, conditional), httpcore._synchronization (optional), httpcore._backends.trio (top-level), openai.resources.vector_stores.file_batches (delayed, conditional)
missing module named sounddevice - imported by openai._extras.sounddevice_proxy (delayed, conditional, optional)
missing module named pandas - imported by tqdm.std (delayed, optional), openai._extras.pandas_proxy (delayed, conditional, optional)
missing module named numpy - imported by openai._extras.numpy_proxy (delayed, conditional, optional)
missing module named 'websockets.exceptions' - imported by openai.resources.beta.realtime.realtime (delayed)
missing module named 'websockets.asyncio' - imported by openai.resources.beta.realtime.realtime (delayed, conditional, optional)
missing module named 'websockets.sync' - imported by openai.resources.beta.realtime.realtime (delayed, conditional, optional)
missing module named 'websockets.extensions' - imported by openai.types.websocket_connection_options (conditional)
missing module named websockets - imported by openai.types.websocket_connection_options (conditional)
missing module named socksio - imported by httpcore._sync.socks_proxy (top-level), httpcore._async.socks_proxy (top-level), httpx._transports.default (delayed, conditional, optional)
missing module named 'h2.settings' - imported by httpcore._sync.http2 (top-level), httpcore._async.http2 (top-level)
missing module named 'h2.exceptions' - imported by httpcore._sync.http2 (top-level), httpcore._async.http2 (top-level)
missing module named 'h2.events' - imported by urllib3.http2.connection (top-level), httpcore._sync.http2 (top-level), httpcore._async.http2 (top-level)
missing module named 'h2.connection' - imported by urllib3.http2.connection (top-level), httpcore._sync.http2 (top-level), httpcore._async.http2 (top-level)
missing module named 'h2.config' - imported by httpcore._sync.http2 (top-level), httpcore._async.http2 (top-level)
missing module named 'rich.table' - imported by httpx._main (top-level)
missing module named 'rich.syntax' - imported by httpx._main (top-level)
missing module named 'rich.progress' - imported by httpx._main (top-level)
missing module named 'rich.markup' - imported by httpx._main (top-level)
missing module named 'rich.console' - imported by httpx._main (top-level)
missing module named 'pygments.util' - imported by httpx._main (top-level)
missing module named 'pygments.lexers' - imported by httpx._main (top-level)
missing module named click - imported by httpx._main (top-level)
missing module named '_typeshed.wsgi' - imported by httpx._transports.wsgi (conditional)
missing module named zstandard - imported by urllib3.util.request (optional), urllib3.response (optional), httpx._decoders (optional)
missing module named brotlicffi - imported by urllib3.util.request (optional), urllib3.response (optional), httpx._decoders (optional)
missing module named brotli - imported by urllib3.util.request (optional), urllib3.response (optional), httpx._decoders (optional)
missing module named h2 - imported by urllib3.http2.connection (top-level), httpx._client (delayed, conditional, optional)
missing module named httpx_aiohttp - imported by openai._base_client (optional)
missing module named google.protobuf.enable_deterministic_proto_serialization - imported by google.protobuf (optional), google.protobuf.internal.api_implementation (optional)
missing module named google.protobuf.pyext._message - imported by google.protobuf.pyext (conditional, optional), google.protobuf.internal.api_implementation (conditional, optional), google.protobuf.descriptor (conditional), google.protobuf.pyext.cpp_message (conditional), proto.marshal.compat (optional)
missing module named google.protobuf.internal._api_implementation - imported by google.protobuf.internal (optional), google.protobuf.internal.api_implementation (optional)
missing module named 'cryptography.x509' - imported by urllib3.contrib.pyopenssl (delayed, optional), google.auth.crypt._cryptography_rsa (top-level), google.auth.crypt.es256 (top-level)
missing module named 'cryptography.hazmat' - imported by google.auth.crypt._cryptography_rsa (top-level), google.auth.crypt.es256 (top-level), google.auth.transport._custom_tls_signer (delayed)
missing module named 'cryptography.exceptions' - imported by google.auth.crypt._cryptography_rsa (top-level), google.auth.crypt.es256 (top-level)
excluded module named cryptography - imported by urllib3.contrib.pyopenssl (top-level), requests (conditional, optional), google.auth.crypt.es256 (top-level)
excluded module named unittest - imported by doctest (top-level), httplib2.iri2uri (conditional)
missing module named 'pyu2f.model' - imported by google.oauth2.challenges (delayed, optional)
missing module named 'pyu2f.errors' - imported by google.oauth2.challenges (delayed, optional)
missing module named pyu2f - imported by google.oauth2.challenges (delayed, optional)
missing module named cffi - imported by google.auth.transport._custom_tls_signer (top-level)
missing module named 'OpenSSL.crypto' - imported by urllib3.contrib.pyopenssl (delayed, conditional)
missing module named compression - imported by urllib3.util.request (optional), urllib3.response (optional)
missing module named 'pyodide.ffi' - imported by urllib3.contrib.emscripten.fetch (delayed, optional)
missing module named pyodide - imported by urllib3.contrib.emscripten.fetch (top-level)
missing module named js - imported by urllib3.contrib.emscripten.fetch (top-level)
missing module named OpenSSL - imported by urllib3.contrib.pyopenssl (top-level), google.auth.transport._mtls_helper (delayed), google.auth.transport.requests (delayed, optional), google.auth.identity_pool (delayed)
missing module named 'requests.packages.urllib3' - imported by google.auth.transport.requests (top-level)
missing module named simplejson - imported by requests.compat (conditional, optional)
missing module named socks - imported by urllib3.contrib.socks (optional), httplib2 (optional)
missing module named dummy_threading - imported by requests.cookies (optional)
missing module named chardet - imported by requests (optional)
missing module named 'google.appengine' - imported by google.auth.app_engine (optional), googleapiclient.discovery_cache.appengine_memcache (top-level)
missing module named grpc_reflection - imported by grpc (optional)
missing module named grpc_health - imported by grpc (optional)
missing module named grpc_tools - imported by grpc._runtime_protos (delayed, optional), grpc (optional)
missing module named 'grpc_tools.protoc' - imported by grpc._runtime_protos (delayed, conditional)
missing module named grpc_gcp - imported by google.api_core.grpc_helpers (conditional, optional)
missing module named aiohttp - imported by google.auth.aio.transport.aiohttp (optional)
missing module named 'oauth2client.locked_file' - imported by googleapiclient.discovery_cache.file_cache (optional)
missing module named 'oauth2client.contrib' - imported by googleapiclient.discovery_cache.file_cache (optional)
missing module named 'oauth2client.client' - imported by googleapiclient._auth (optional)
missing module named oauth2client - imported by googleapiclient._auth (optional)
missing module named ca_certs_locater - imported by httplib2.certs (optional)
missing module named jinja2 - imported by pyparsing.diagram (top-level)
missing module named railroad - imported by pyparsing.diagram (top-level)
missing module named pyparsing.Word - imported by pyparsing (delayed), pyparsing.unicode (delayed)
missing module named 'PIL.Image' - imported by google.generativeai.types.content_types (conditional, optional)
missing module named 'IPython.display' - imported by google.generativeai.types.content_types (conditional, optional), tqdm.notebook (conditional, optional)
missing module named 'PIL.ImageFile' - imported by google.generativeai.types.content_types (conditional, optional)
missing module named PIL - imported by google.generativeai.types.content_types (conditional)
missing module named 'IPython.html' - imported by tqdm.notebook (conditional, optional)
missing module named ipywidgets - imported by tqdm.notebook (conditional, optional)
missing module named setuptools_scm - imported by tqdm.version (optional)
missing module named 'matplotlib.pyplot' - imported by tqdm.gui (delayed)
missing module named matplotlib - imported by tqdm.gui (delayed)
missing module named 'pandas.core' - imported by tqdm.std (delayed, optional)
missing module named test_markdown2 - imported by markdown2 (delayed, conditional)
missing module named wavedrom - imported by markdown2 (delayed, conditional, optional)
missing module named latex2mathml - imported by markdown2 (delayed, optional)
missing module named 'pygments.formatters' - imported by markdown2 (delayed)
missing module named pygments - imported by markdown2 (delayed, optional)
excluded module named PySide6.QtNetwork - imported by PySide6 (delayed, conditional, optional)
runtime module named six.moves - imported by pynput._util (top-level)
missing module named StringIO - imported by six (conditional)
missing module named 'Xlib.protocol' - imported by pynput.keyboard._xorg (top-level), pynput.mouse._xorg (top-level)
missing module named 'Xlib.X' - imported by pynput.keyboard._xorg (top-level), pynput.mouse._xorg (top-level)
missing module named 'Xlib.ext' - imported by pynput.keyboard._xorg (top-level), pynput.mouse._xorg (top-level)
missing module named 'Xlib.display' - imported by pynput.keyboard._xorg (top-level), pynput.mouse._xorg (top-level)
missing module named AppKit - imported by pyperclip (delayed, conditional, optional), pynput.mouse._darwin (top-level)
missing module named Quartz - imported by pynput._util.darwin (top-level), pynput.keyboard._darwin (top-level), pynput.mouse._darwin (top-level)
missing module named 'Xlib.keysymdef' - imported by pynput._util.xorg (top-level), pynput.keyboard._xorg (top-level)
missing module named 'Xlib.XK' - imported by pynput._util.xorg (top-level), pynput.keyboard._xorg (top-level)
missing module named 'evdev.events' - imported by pynput.keyboard._uinput (top-level)
missing module named evdev - imported by pynput._util.uinput (top-level), pynput.keyboard._uinput (top-level)
missing module named 'Xlib.threaded' - imported by pynput._util.xorg (top-level)
missing module named Xlib - imported by pynput._util.xorg (top-level)
missing module named CoreFoundation - imported by pynput._util.darwin (top-level)
missing module named HIServices - imported by pynput._util.darwin (top-level)
missing module named objc - imported by pynput._util.darwin (top-level)
missing module named Foundation - imported by darkdetect._mac_detect (optional), pyperclip (delayed, conditional, optional)
missing module named PyQt5 - imported by pyperclip (delayed, conditional, optional)
missing module named qtpy - imported by pyperclip (delayed, conditional, optional)
missing module named PyObjCTools - imported by darkdetect._mac_detect (optional)
