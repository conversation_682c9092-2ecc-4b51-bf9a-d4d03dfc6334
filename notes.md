run dev 

ferme bien une instance en cours si ouverte et s'effectue. Trouve les settings existants.


run build dev 

trouve les settings existants. Si une instance est déjà ouverte, le build fails. Il Faut donc fermer Les instances avant Que ce soit un script ou un exe. Avec la fonction qui existe appropriée Qu'on trouve bien sûr utilisé dans rundev.

idem pour build final

Par rapport à l'original Branche main. Le start on boot avait été fixé dans WritingToolsImprove. Dans le cas de Linux et Windows. Il n'est plus nécessaire pour les scripts Pour le mode dev On ne va plus l'utiliser.

Mettre à jour l'aide dans README's Linked Content

Faire un rapport Pour le pull request Pour expliquer tous les changements. (voir aussi WritingToolsImprove)