{"auth": {"oauth2": {"scopes": {"https://www.googleapis.com/auth/cloud-platform": {"description": "See, edit, configure, and delete your Google Cloud data and see the email address for your Google Account."}}}}, "basePath": "", "baseUrl": "https://datacatalog.googleapis.com/", "batchPath": "batch", "canonicalName": "Data Catalog", "description": "A fully managed and highly scalable data discovery and metadata management service. ", "discoveryVersion": "v1", "documentationLink": "https://cloud.google.com/data-catalog/docs/", "fullyEncodeReservedExpansion": true, "icons": {"x16": "http://www.google.com/images/icons/product/search-16.gif", "x32": "http://www.google.com/images/icons/product/search-32.gif"}, "id": "datacatalog:v1beta1", "kind": "discovery#restDescription", "mtlsRootUrl": "https://datacatalog.mtls.googleapis.com/", "name": "datacatalog", "ownerDomain": "google.com", "ownerName": "Google", "parameters": {"$.xgafv": {"description": "V1 error format.", "enum": ["1", "2"], "enumDescriptions": ["v1 error format", "v2 error format"], "location": "query", "type": "string"}, "access_token": {"description": "OAuth access token.", "location": "query", "type": "string"}, "alt": {"default": "json", "description": "Data format for response.", "enum": ["json", "media", "proto"], "enumDescriptions": ["Responses with Content-Type of application/json", "Media download with context-dependent Content-Type", "Responses with Content-Type of application/x-protobuf"], "location": "query", "type": "string"}, "callback": {"description": "JSONP", "location": "query", "type": "string"}, "fields": {"description": "Selector specifying which fields to include in a partial response.", "location": "query", "type": "string"}, "key": {"description": "API key. Your API key identifies your project and provides you with API access, quota, and reports. Required unless you provide an OAuth 2.0 token.", "location": "query", "type": "string"}, "oauth_token": {"description": "OAuth 2.0 token for the current user.", "location": "query", "type": "string"}, "prettyPrint": {"default": "true", "description": "Returns response with indentations and line breaks.", "location": "query", "type": "boolean"}, "quotaUser": {"description": "Available to use for quota purposes for server-side applications. Can be any arbitrary string assigned to a user, but should not exceed 40 characters.", "location": "query", "type": "string"}, "uploadType": {"description": "Legacy upload protocol for media (e.g. \"media\", \"multipart\").", "location": "query", "type": "string"}, "upload_protocol": {"description": "Upload protocol for media (e.g. \"raw\", \"multipart\").", "location": "query", "type": "string"}}, "protocol": "rest", "resources": {"catalog": {"deprecated": true, "methods": {"search": {"deprecated": true, "description": "Searches Data Catalog for multiple resources like entries, tags that match a query. This is a custom method (https://cloud.google.com/apis/design/custom_methods) and does not return the complete resource, only the resource identifier and high level fields. Clients can subsequently call `Get` methods. Note that Data Catalog search queries do not guarantee full recall. Query results that match your query may not be returned, even in subsequent result pages. Also note that results returned (and not returned) can vary across repeated search queries. See [Data Catalog Search Syntax](https://cloud.google.com/data-catalog/docs/how-to/search-reference) for more information.", "flatPath": "v1beta1/catalog:search", "httpMethod": "POST", "id": "datacatalog.catalog.search", "parameterOrder": [], "parameters": {}, "path": "v1beta1/catalog:search", "request": {"$ref": "GoogleCloudDatacatalogV1beta1SearchCatalogRequest"}, "response": {"$ref": "GoogleCloudDatacatalogV1beta1SearchCatalogResponse"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform"]}}}, "entries": {"deprecated": true, "methods": {"lookup": {"deprecated": true, "description": "Get an entry by target resource name. This method allows clients to use the resource name from the source Google Cloud Platform service to get the Data Catalog Entry.", "flatPath": "v1beta1/entries:lookup", "httpMethod": "GET", "id": "datacatalog.entries.lookup", "parameterOrder": [], "parameters": {"linkedResource": {"description": "The full name of the Google Cloud Platform resource the Data Catalog entry represents. See: https://cloud.google.com/apis/design/resource_names#full_resource_name. Full names are case-sensitive. Examples: * //bigquery.googleapis.com/projects/projectId/datasets/datasetId/tables/tableId * //pubsub.googleapis.com/projects/projectId/topics/topicId", "location": "query", "type": "string"}, "sqlResource": {"description": "The SQL name of the entry. SQL names are case-sensitive. Examples: * `pubsub.project_id.topic_id` * ``pubsub.project_id.`topic.id.with.dots` `` * `bigquery.table.project_id.dataset_id.table_id` * `bigquery.dataset.project_id.dataset_id` * `datacatalog.entry.project_id.location_id.entry_group_id.entry_id` `*_id`s should satisfy the GoogleSQL rules for identifiers. https://cloud.google.com/bigquery/docs/reference/standard-sql/lexical.", "location": "query", "type": "string"}}, "path": "v1beta1/entries:lookup", "response": {"$ref": "GoogleCloudDatacatalogV1beta1Entry"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform"]}}}, "projects": {"resources": {"locations": {"resources": {"entryGroups": {"methods": {"create": {"deprecated": true, "description": "A maximum of 10,000 entry groups may be created per organization across all locations. Users should enable the Data Catalog API in the project identified by the `parent` parameter (see [Data Catalog Resource Project] (https://cloud.google.com/data-catalog/docs/concepts/resource-project) for more information).", "flatPath": "v1beta1/projects/{projectsId}/locations/{locationsId}/entryGroups", "httpMethod": "POST", "id": "datacatalog.projects.locations.entryGroups.create", "parameterOrder": ["parent"], "parameters": {"entryGroupId": {"description": "Required. The id of the entry group to create. The id must begin with a letter or underscore, contain only English letters, numbers and underscores, and be at most 64 characters.", "location": "query", "type": "string"}, "parent": {"description": "Required. The name of the project this entry group is in. Example: * projects/{project_id}/locations/{location} Note that this EntryGroup and its child resources may not actually be stored in the location in this name.", "location": "path", "pattern": "^projects/[^/]+/locations/[^/]+$", "required": true, "type": "string"}}, "path": "v1beta1/{+parent}/entryGroups", "request": {"$ref": "GoogleCloudDatacatalogV1beta1EntryGroup"}, "response": {"$ref": "GoogleCloudDatacatalogV1beta1EntryGroup"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform"]}, "delete": {"deprecated": true, "description": "Deletes an EntryGroup. Only entry groups that do not contain entries can be deleted. Users should enable the Data Catalog API in the project identified by the `name` parameter (see [Data Catalog Resource Project] (https://cloud.google.com/data-catalog/docs/concepts/resource-project) for more information).", "flatPath": "v1beta1/projects/{projectsId}/locations/{locationsId}/entryGroups/{entryGroupsId}", "httpMethod": "DELETE", "id": "datacatalog.projects.locations.entryGroups.delete", "parameterOrder": ["name"], "parameters": {"force": {"description": "Optional. If true, deletes all entries in the entry group.", "location": "query", "type": "boolean"}, "name": {"description": "Required. The name of the entry group. For example, `projects/{project_id}/locations/{location}/entryGroups/{entry_group_id}`.", "location": "path", "pattern": "^projects/[^/]+/locations/[^/]+/entryGroups/[^/]+$", "required": true, "type": "string"}}, "path": "v1beta1/{+name}", "response": {"$ref": "Empty"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform"]}, "get": {"deprecated": true, "description": "Gets an EntryGroup.", "flatPath": "v1beta1/projects/{projectsId}/locations/{locationsId}/entryGroups/{entryGroupsId}", "httpMethod": "GET", "id": "datacatalog.projects.locations.entryGroups.get", "parameterOrder": ["name"], "parameters": {"name": {"description": "Required. The name of the entry group. For example, `projects/{project_id}/locations/{location}/entryGroups/{entry_group_id}`.", "location": "path", "pattern": "^projects/[^/]+/locations/[^/]+/entryGroups/[^/]+$", "required": true, "type": "string"}, "readMask": {"description": "The fields to return. If not set or empty, all fields are returned.", "format": "google-fieldmask", "location": "query", "type": "string"}}, "path": "v1beta1/{+name}", "response": {"$ref": "GoogleCloudDatacatalogV1beta1EntryGroup"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform"]}, "getIamPolicy": {"deprecated": true, "description": "Gets the access control policy for a resource. A `NOT_FOUND` error is returned if the resource does not exist. An empty policy is returned if the resource exists but does not have a policy set on it. Supported resources are: - Tag templates. - Entries. - Entry groups. Note, this method cannot be used to manage policies for BigQuery, Pub/Sub and any external Google Cloud Platform resources synced to Data Catalog. Callers must have following Google IAM permission - `datacatalog.tagTemplates.getIamPolicy` to get policies on tag templates. - `datacatalog.entries.getIamPolicy` to get policies on entries. - `datacatalog.entryGroups.getIamPolicy` to get policies on entry groups.", "flatPath": "v1beta1/projects/{projectsId}/locations/{locationsId}/entryGroups/{entryGroupsId}:getIamPolicy", "httpMethod": "POST", "id": "datacatalog.projects.locations.entryGroups.getIamPolicy", "parameterOrder": ["resource"], "parameters": {"resource": {"description": "REQUIRED: The resource for which the policy is being requested. See [Resource names](https://cloud.google.com/apis/design/resource_names) for the appropriate value for this field.", "location": "path", "pattern": "^projects/[^/]+/locations/[^/]+/entryGroups/[^/]+$", "required": true, "type": "string"}}, "path": "v1beta1/{+resource}:getIamPolicy", "request": {"$ref": "GetIamPolicyRequest"}, "response": {"$ref": "Policy"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform"]}, "list": {"deprecated": true, "description": "Lists entry groups.", "flatPath": "v1beta1/projects/{projectsId}/locations/{locationsId}/entryGroups", "httpMethod": "GET", "id": "datacatalog.projects.locations.entryGroups.list", "parameterOrder": ["parent"], "parameters": {"pageSize": {"description": "Optional. The maximum number of items to return. Default is 10. Max limit is 1000. Throws an invalid argument for `page_size > 1000`.", "format": "int32", "location": "query", "type": "integer"}, "pageToken": {"description": "Optional. Token that specifies which page is requested. If empty, the first page is returned.", "location": "query", "type": "string"}, "parent": {"description": "Required. The name of the location that contains the entry groups, which can be provided in URL format. Example: * projects/{project_id}/locations/{location}", "location": "path", "pattern": "^projects/[^/]+/locations/[^/]+$", "required": true, "type": "string"}}, "path": "v1beta1/{+parent}/entryGroups", "response": {"$ref": "GoogleCloudDatacatalogV1beta1ListEntryGroupsResponse"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform"]}, "patch": {"deprecated": true, "description": "Updates an EntryGroup. The user should enable the Data Catalog API in the project identified by the `entry_group.name` parameter (see [Data Catalog Resource Project] (https://cloud.google.com/data-catalog/docs/concepts/resource-project) for more information).", "flatPath": "v1beta1/projects/{projectsId}/locations/{locationsId}/entryGroups/{entryGroupsId}", "httpMethod": "PATCH", "id": "datacatalog.projects.locations.entryGroups.patch", "parameterOrder": ["name"], "parameters": {"name": {"description": "Identifier. The resource name of the entry group in URL format. Example: * projects/{project_id}/locations/{location}/entryGroups/{entry_group_id} Note that this EntryGroup and its child resources may not actually be stored in the location in this name.", "location": "path", "pattern": "^projects/[^/]+/locations/[^/]+/entryGroups/[^/]+$", "required": true, "type": "string"}, "updateMask": {"description": "Names of fields whose values to overwrite on an entry group. If this parameter is absent or empty, all modifiable fields are overwritten. If such fields are non-required and omitted in the request body, their values are emptied.", "format": "google-fieldmask", "location": "query", "type": "string"}}, "path": "v1beta1/{+name}", "request": {"$ref": "GoogleCloudDatacatalogV1beta1EntryGroup"}, "response": {"$ref": "GoogleCloudDatacatalogV1beta1EntryGroup"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform"]}, "setIamPolicy": {"deprecated": true, "description": "Sets the access control policy for a resource. Replaces any existing policy. Supported resources are: - Tag templates. - Entries. - Entry groups. Note, this method cannot be used to manage policies for BigQuery, Pub/Sub and any external Google Cloud Platform resources synced to Data Catalog. Callers must have following Google IAM permission - `datacatalog.tagTemplates.setIamPolicy` to set policies on tag templates. - `datacatalog.entries.setIamPolicy` to set policies on entries. - `datacatalog.entryGroups.setIamPolicy` to set policies on entry groups.", "flatPath": "v1beta1/projects/{projectsId}/locations/{locationsId}/entryGroups/{entryGroupsId}:setIamPolicy", "httpMethod": "POST", "id": "datacatalog.projects.locations.entryGroups.setIamPolicy", "parameterOrder": ["resource"], "parameters": {"resource": {"description": "REQUIRED: The resource for which the policy is being specified. See [Resource names](https://cloud.google.com/apis/design/resource_names) for the appropriate value for this field.", "location": "path", "pattern": "^projects/[^/]+/locations/[^/]+/entryGroups/[^/]+$", "required": true, "type": "string"}}, "path": "v1beta1/{+resource}:setIamPolicy", "request": {"$ref": "SetIamPolicyRequest"}, "response": {"$ref": "Policy"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform"]}, "testIamPermissions": {"deprecated": true, "description": "Returns the caller's permissions on a resource. If the resource does not exist, an empty set of permissions is returned (We don't return a `NOT_FOUND` error). Supported resources are: - Tag templates. - Entries. - Entry groups. Note, this method cannot be used to manage policies for BigQuery, Pub/Sub and any external Google Cloud Platform resources synced to Data Catalog. A caller is not required to have Google IAM permission to make this request.", "flatPath": "v1beta1/projects/{projectsId}/locations/{locationsId}/entryGroups/{entryGroupsId}:testIamPermissions", "httpMethod": "POST", "id": "datacatalog.projects.locations.entryGroups.testIamPermissions", "parameterOrder": ["resource"], "parameters": {"resource": {"description": "REQUIRED: The resource for which the policy detail is being requested. See [Resource names](https://cloud.google.com/apis/design/resource_names) for the appropriate value for this field.", "location": "path", "pattern": "^projects/[^/]+/locations/[^/]+/entryGroups/[^/]+$", "required": true, "type": "string"}}, "path": "v1beta1/{+resource}:testIamPermissions", "request": {"$ref": "TestIamPermissionsRequest"}, "response": {"$ref": "TestIamPermissionsResponse"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform"]}}, "resources": {"entries": {"methods": {"create": {"deprecated": true, "description": "Creates an entry. Only entries of 'FILESET' type or user-specified type can be created. Users should enable the Data Catalog API in the project identified by the `parent` parameter (see [Data Catalog Resource Project] (https://cloud.google.com/data-catalog/docs/concepts/resource-project) for more information). A maximum of 100,000 entries may be created per entry group.", "flatPath": "v1beta1/projects/{projectsId}/locations/{locationsId}/entryGroups/{entryGroupsId}/entries", "httpMethod": "POST", "id": "datacatalog.projects.locations.entryGroups.entries.create", "parameterOrder": ["parent"], "parameters": {"entryId": {"description": "Required. The id of the entry to create.", "location": "query", "type": "string"}, "parent": {"description": "Required. The name of the entry group this entry is in. Example: * projects/{project_id}/locations/{location}/entryGroups/{entry_group_id} Note that this Entry and its child resources may not actually be stored in the location in this name.", "location": "path", "pattern": "^projects/[^/]+/locations/[^/]+/entryGroups/[^/]+$", "required": true, "type": "string"}}, "path": "v1beta1/{+parent}/entries", "request": {"$ref": "GoogleCloudDatacatalogV1beta1Entry"}, "response": {"$ref": "GoogleCloudDatacatalogV1beta1Entry"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform"]}, "delete": {"deprecated": true, "description": "Deletes an existing entry. Only entries created through CreateEntry method can be deleted. Users should enable the Data Catalog API in the project identified by the `name` parameter (see [Data Catalog Resource Project] (https://cloud.google.com/data-catalog/docs/concepts/resource-project) for more information).", "flatPath": "v1beta1/projects/{projectsId}/locations/{locationsId}/entryGroups/{entryGroupsId}/entries/{entriesId}", "httpMethod": "DELETE", "id": "datacatalog.projects.locations.entryGroups.entries.delete", "parameterOrder": ["name"], "parameters": {"name": {"description": "Required. The name of the entry. Example: * projects/{project_id}/locations/{location}/entryGroups/{entry_group_id}/entries/{entry_id}", "location": "path", "pattern": "^projects/[^/]+/locations/[^/]+/entryGroups/[^/]+/entries/[^/]+$", "required": true, "type": "string"}}, "path": "v1beta1/{+name}", "response": {"$ref": "Empty"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform"]}, "get": {"deprecated": true, "description": "Gets an entry.", "flatPath": "v1beta1/projects/{projectsId}/locations/{locationsId}/entryGroups/{entryGroupsId}/entries/{entriesId}", "httpMethod": "GET", "id": "datacatalog.projects.locations.entryGroups.entries.get", "parameterOrder": ["name"], "parameters": {"name": {"description": "Required. The name of the entry. Example: * projects/{project_id}/locations/{location}/entryGroups/{entry_group_id}/entries/{entry_id}", "location": "path", "pattern": "^projects/[^/]+/locations/[^/]+/entryGroups/[^/]+/entries/[^/]+$", "required": true, "type": "string"}}, "path": "v1beta1/{+name}", "response": {"$ref": "GoogleCloudDatacatalogV1beta1Entry"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform"]}, "getIamPolicy": {"deprecated": true, "description": "Gets the access control policy for a resource. A `NOT_FOUND` error is returned if the resource does not exist. An empty policy is returned if the resource exists but does not have a policy set on it. Supported resources are: - Tag templates. - Entries. - Entry groups. Note, this method cannot be used to manage policies for BigQuery, Pub/Sub and any external Google Cloud Platform resources synced to Data Catalog. Callers must have following Google IAM permission - `datacatalog.tagTemplates.getIamPolicy` to get policies on tag templates. - `datacatalog.entries.getIamPolicy` to get policies on entries. - `datacatalog.entryGroups.getIamPolicy` to get policies on entry groups.", "flatPath": "v1beta1/projects/{projectsId}/locations/{locationsId}/entryGroups/{entryGroupsId}/entries/{entriesId}:getIamPolicy", "httpMethod": "POST", "id": "datacatalog.projects.locations.entryGroups.entries.getIamPolicy", "parameterOrder": ["resource"], "parameters": {"resource": {"description": "REQUIRED: The resource for which the policy is being requested. See [Resource names](https://cloud.google.com/apis/design/resource_names) for the appropriate value for this field.", "location": "path", "pattern": "^projects/[^/]+/locations/[^/]+/entryGroups/[^/]+/entries/[^/]+$", "required": true, "type": "string"}}, "path": "v1beta1/{+resource}:getIamPolicy", "request": {"$ref": "GetIamPolicyRequest"}, "response": {"$ref": "Policy"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform"]}, "list": {"deprecated": true, "description": "Lists entries.", "flatPath": "v1beta1/projects/{projectsId}/locations/{locationsId}/entryGroups/{entryGroupsId}/entries", "httpMethod": "GET", "id": "datacatalog.projects.locations.entryGroups.entries.list", "parameterOrder": ["parent"], "parameters": {"pageSize": {"description": "The maximum number of items to return. Default is 10. Max limit is 1000. Throws an invalid argument for `page_size > 1000`.", "format": "int32", "location": "query", "type": "integer"}, "pageToken": {"description": "Token that specifies which page is requested. If empty, the first page is returned.", "location": "query", "type": "string"}, "parent": {"description": "Required. The name of the entry group that contains the entries, which can be provided in URL format. Example: * projects/{project_id}/locations/{location}/entryGroups/{entry_group_id}", "location": "path", "pattern": "^projects/[^/]+/locations/[^/]+/entryGroups/[^/]+$", "required": true, "type": "string"}, "readMask": {"description": "The fields to return for each Entry. If not set or empty, all fields are returned. For example, setting read_mask to contain only one path \"name\" will cause ListEntries to return a list of Entries with only \"name\" field.", "format": "google-fieldmask", "location": "query", "type": "string"}}, "path": "v1beta1/{+parent}/entries", "response": {"$ref": "GoogleCloudDatacatalogV1beta1ListEntriesResponse"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform"]}, "patch": {"deprecated": true, "description": "Updates an existing entry. Users should enable the Data Catalog API in the project identified by the `entry.name` parameter (see [Data Catalog Resource Project] (https://cloud.google.com/data-catalog/docs/concepts/resource-project) for more information).", "flatPath": "v1beta1/projects/{projectsId}/locations/{locationsId}/entryGroups/{entryGroupsId}/entries/{entriesId}", "httpMethod": "PATCH", "id": "datacatalog.projects.locations.entryGroups.entries.patch", "parameterOrder": ["name"], "parameters": {"name": {"description": "Output only. Identifier. The Data Catalog resource name of the entry in URL format. Example: * projects/{project_id}/locations/{location}/entryGroups/{entry_group_id}/entries/{entry_id} Note that this Entry and its child resources may not actually be stored in the location in this name.", "location": "path", "pattern": "^projects/[^/]+/locations/[^/]+/entryGroups/[^/]+/entries/[^/]+$", "required": true, "type": "string"}, "updateMask": {"description": "Names of fields whose values to overwrite on an entry. If this parameter is absent or empty, all modifiable fields are overwritten. If such fields are non-required and omitted in the request body, their values are emptied. The following fields are modifiable: * For entries with type `DATA_STREAM`: * `schema` * For entries with type `FILESET`: * `schema` * `display_name` * `description` * `gcs_fileset_spec` * `gcs_fileset_spec.file_patterns` * For entries with `user_specified_type`: * `schema` * `display_name` * `description` * `user_specified_type` * `user_specified_system` * `linked_resource` * `source_system_timestamps`", "format": "google-fieldmask", "location": "query", "type": "string"}}, "path": "v1beta1/{+name}", "request": {"$ref": "GoogleCloudDatacatalogV1beta1Entry"}, "response": {"$ref": "GoogleCloudDatacatalogV1beta1Entry"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform"]}, "testIamPermissions": {"deprecated": true, "description": "Returns the caller's permissions on a resource. If the resource does not exist, an empty set of permissions is returned (We don't return a `NOT_FOUND` error). Supported resources are: - Tag templates. - Entries. - Entry groups. Note, this method cannot be used to manage policies for BigQuery, Pub/Sub and any external Google Cloud Platform resources synced to Data Catalog. A caller is not required to have Google IAM permission to make this request.", "flatPath": "v1beta1/projects/{projectsId}/locations/{locationsId}/entryGroups/{entryGroupsId}/entries/{entriesId}:testIamPermissions", "httpMethod": "POST", "id": "datacatalog.projects.locations.entryGroups.entries.testIamPermissions", "parameterOrder": ["resource"], "parameters": {"resource": {"description": "REQUIRED: The resource for which the policy detail is being requested. See [Resource names](https://cloud.google.com/apis/design/resource_names) for the appropriate value for this field.", "location": "path", "pattern": "^projects/[^/]+/locations/[^/]+/entryGroups/[^/]+/entries/[^/]+$", "required": true, "type": "string"}}, "path": "v1beta1/{+resource}:testIamPermissions", "request": {"$ref": "TestIamPermissionsRequest"}, "response": {"$ref": "TestIamPermissionsResponse"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform"]}}, "resources": {"tags": {"deprecated": true, "methods": {"create": {"deprecated": true, "description": "Creates a tag on an Entry. Note: The project identified by the `parent` parameter for the [tag](https://cloud.google.com/data-catalog/docs/reference/rest/v1beta1/projects.locations.entryGroups.entries.tags/create#path-parameters) and the [tag template](https://cloud.google.com/data-catalog/docs/reference/rest/v1beta1/projects.locations.tagTemplates/create#path-parameters) used to create the tag must be from the same organization.", "flatPath": "v1beta1/projects/{projectsId}/locations/{locationsId}/entryGroups/{entryGroupsId}/entries/{entriesId}/tags", "httpMethod": "POST", "id": "datacatalog.projects.locations.entryGroups.entries.tags.create", "parameterOrder": ["parent"], "parameters": {"parent": {"description": "Required. The name of the resource to attach this tag to. Tags can be attached to Entries. Example: * projects/{project_id}/locations/{location}/entryGroups/{entry_group_id}/entries/{entry_id} Note that this Tag and its child resources may not actually be stored in the location in this name.", "location": "path", "pattern": "^projects/[^/]+/locations/[^/]+/entryGroups/[^/]+/entries/[^/]+$", "required": true, "type": "string"}}, "path": "v1beta1/{+parent}/tags", "request": {"$ref": "GoogleCloudDatacatalogV1beta1Tag"}, "response": {"$ref": "GoogleCloudDatacatalogV1beta1Tag"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform"]}, "delete": {"deprecated": true, "description": "Deletes a tag.", "flatPath": "v1beta1/projects/{projectsId}/locations/{locationsId}/entryGroups/{entryGroupsId}/entries/{entriesId}/tags/{tagsId}", "httpMethod": "DELETE", "id": "datacatalog.projects.locations.entryGroups.entries.tags.delete", "parameterOrder": ["name"], "parameters": {"name": {"description": "Required. The name of the tag to delete. Example: * projects/{project_id}/locations/{location}/entryGroups/{entry_group_id}/entries/{entry_id}/tags/{tag_id}", "location": "path", "pattern": "^projects/[^/]+/locations/[^/]+/entryGroups/[^/]+/entries/[^/]+/tags/[^/]+$", "required": true, "type": "string"}}, "path": "v1beta1/{+name}", "response": {"$ref": "Empty"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform"]}, "list": {"deprecated": true, "description": "Lists tags assigned to an Entry. The columns in the response are lowercased.", "flatPath": "v1beta1/projects/{projectsId}/locations/{locationsId}/entryGroups/{entryGroupsId}/entries/{entriesId}/tags", "httpMethod": "GET", "id": "datacatalog.projects.locations.entryGroups.entries.tags.list", "parameterOrder": ["parent"], "parameters": {"pageSize": {"description": "The maximum number of tags to return. Default is 10. Max limit is 1000.", "format": "int32", "location": "query", "type": "integer"}, "pageToken": {"description": "Token that specifies which page is requested. If empty, the first page is returned.", "location": "query", "type": "string"}, "parent": {"description": "Required. The name of the Data Catalog resource to list the tags of. The resource could be an Entry or an EntryGroup. Examples: * projects/{project_id}/locations/{location}/entryGroups/{entry_group_id} * projects/{project_id}/locations/{location}/entryGroups/{entry_group_id}/entries/{entry_id}", "location": "path", "pattern": "^projects/[^/]+/locations/[^/]+/entryGroups/[^/]+/entries/[^/]+$", "required": true, "type": "string"}}, "path": "v1beta1/{+parent}/tags", "response": {"$ref": "GoogleCloudDatacatalogV1beta1ListTagsResponse"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform"]}, "patch": {"deprecated": true, "description": "Updates an existing tag.", "flatPath": "v1beta1/projects/{projectsId}/locations/{locationsId}/entryGroups/{entryGroupsId}/entries/{entriesId}/tags/{tagsId}", "httpMethod": "PATCH", "id": "datacatalog.projects.locations.entryGroups.entries.tags.patch", "parameterOrder": ["name"], "parameters": {"name": {"description": "Identifier. The resource name of the tag in URL format. Example: * projects/{project_id}/locations/{location}/entrygroups/{entry_group_id}/entries/{entry_id}/tags/{tag_id} where `tag_id` is a system-generated identifier. Note that this Tag may not actually be stored in the location in this name.", "location": "path", "pattern": "^projects/[^/]+/locations/[^/]+/entryGroups/[^/]+/entries/[^/]+/tags/[^/]+$", "required": true, "type": "string"}, "updateMask": {"description": "Note: Currently, this parameter can only take `\"fields\"` as value. Names of fields whose values to overwrite on a tag. Currently, a tag has the only modifiable field with the name `fields`. In general, if this parameter is absent or empty, all modifiable fields are overwritten. If such fields are non-required and omitted in the request body, their values are emptied.", "format": "google-fieldmask", "location": "query", "type": "string"}}, "path": "v1beta1/{+name}", "request": {"$ref": "GoogleCloudDatacatalogV1beta1Tag"}, "response": {"$ref": "GoogleCloudDatacatalogV1beta1Tag"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform"]}}}}}, "tags": {"deprecated": true, "methods": {"create": {"deprecated": true, "description": "Creates a tag on an Entry. Note: The project identified by the `parent` parameter for the [tag](https://cloud.google.com/data-catalog/docs/reference/rest/v1beta1/projects.locations.entryGroups.entries.tags/create#path-parameters) and the [tag template](https://cloud.google.com/data-catalog/docs/reference/rest/v1beta1/projects.locations.tagTemplates/create#path-parameters) used to create the tag must be from the same organization.", "flatPath": "v1beta1/projects/{projectsId}/locations/{locationsId}/entryGroups/{entryGroupsId}/tags", "httpMethod": "POST", "id": "datacatalog.projects.locations.entryGroups.tags.create", "parameterOrder": ["parent"], "parameters": {"parent": {"description": "Required. The name of the resource to attach this tag to. Tags can be attached to Entries. Example: * projects/{project_id}/locations/{location}/entryGroups/{entry_group_id}/entries/{entry_id} Note that this Tag and its child resources may not actually be stored in the location in this name.", "location": "path", "pattern": "^projects/[^/]+/locations/[^/]+/entryGroups/[^/]+$", "required": true, "type": "string"}}, "path": "v1beta1/{+parent}/tags", "request": {"$ref": "GoogleCloudDatacatalogV1beta1Tag"}, "response": {"$ref": "GoogleCloudDatacatalogV1beta1Tag"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform"]}, "delete": {"deprecated": true, "description": "Deletes a tag.", "flatPath": "v1beta1/projects/{projectsId}/locations/{locationsId}/entryGroups/{entryGroupsId}/tags/{tagsId}", "httpMethod": "DELETE", "id": "datacatalog.projects.locations.entryGroups.tags.delete", "parameterOrder": ["name"], "parameters": {"name": {"description": "Required. The name of the tag to delete. Example: * projects/{project_id}/locations/{location}/entryGroups/{entry_group_id}/entries/{entry_id}/tags/{tag_id}", "location": "path", "pattern": "^projects/[^/]+/locations/[^/]+/entryGroups/[^/]+/tags/[^/]+$", "required": true, "type": "string"}}, "path": "v1beta1/{+name}", "response": {"$ref": "Empty"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform"]}, "list": {"deprecated": true, "description": "Lists tags assigned to an Entry. The columns in the response are lowercased.", "flatPath": "v1beta1/projects/{projectsId}/locations/{locationsId}/entryGroups/{entryGroupsId}/tags", "httpMethod": "GET", "id": "datacatalog.projects.locations.entryGroups.tags.list", "parameterOrder": ["parent"], "parameters": {"pageSize": {"description": "The maximum number of tags to return. Default is 10. Max limit is 1000.", "format": "int32", "location": "query", "type": "integer"}, "pageToken": {"description": "Token that specifies which page is requested. If empty, the first page is returned.", "location": "query", "type": "string"}, "parent": {"description": "Required. The name of the Data Catalog resource to list the tags of. The resource could be an Entry or an EntryGroup. Examples: * projects/{project_id}/locations/{location}/entryGroups/{entry_group_id} * projects/{project_id}/locations/{location}/entryGroups/{entry_group_id}/entries/{entry_id}", "location": "path", "pattern": "^projects/[^/]+/locations/[^/]+/entryGroups/[^/]+$", "required": true, "type": "string"}}, "path": "v1beta1/{+parent}/tags", "response": {"$ref": "GoogleCloudDatacatalogV1beta1ListTagsResponse"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform"]}, "patch": {"deprecated": true, "description": "Updates an existing tag.", "flatPath": "v1beta1/projects/{projectsId}/locations/{locationsId}/entryGroups/{entryGroupsId}/tags/{tagsId}", "httpMethod": "PATCH", "id": "datacatalog.projects.locations.entryGroups.tags.patch", "parameterOrder": ["name"], "parameters": {"name": {"description": "Identifier. The resource name of the tag in URL format. Example: * projects/{project_id}/locations/{location}/entrygroups/{entry_group_id}/entries/{entry_id}/tags/{tag_id} where `tag_id` is a system-generated identifier. Note that this Tag may not actually be stored in the location in this name.", "location": "path", "pattern": "^projects/[^/]+/locations/[^/]+/entryGroups/[^/]+/tags/[^/]+$", "required": true, "type": "string"}, "updateMask": {"description": "Note: Currently, this parameter can only take `\"fields\"` as value. Names of fields whose values to overwrite on a tag. Currently, a tag has the only modifiable field with the name `fields`. In general, if this parameter is absent or empty, all modifiable fields are overwritten. If such fields are non-required and omitted in the request body, their values are emptied.", "format": "google-fieldmask", "location": "query", "type": "string"}}, "path": "v1beta1/{+name}", "request": {"$ref": "GoogleCloudDatacatalogV1beta1Tag"}, "response": {"$ref": "GoogleCloudDatacatalogV1beta1Tag"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform"]}}}}}, "tagTemplates": {"methods": {"create": {"deprecated": true, "description": "Creates a tag template. The user should enable the Data Catalog API in the project identified by the `parent` parameter (see [Data Catalog Resource Project](https://cloud.google.com/data-catalog/docs/concepts/resource-project) for more information).", "flatPath": "v1beta1/projects/{projectsId}/locations/{locationsId}/tagTemplates", "httpMethod": "POST", "id": "datacatalog.projects.locations.tagTemplates.create", "parameterOrder": ["parent"], "parameters": {"parent": {"description": "Required. The name of the project and the template location [region](https://cloud.google.com/data-catalog/docs/concepts/regions. Example: * projects/{project_id}/locations/us-central1", "location": "path", "pattern": "^projects/[^/]+/locations/[^/]+$", "required": true, "type": "string"}, "tagTemplateId": {"description": "Required. The id of the tag template to create.", "location": "query", "type": "string"}}, "path": "v1beta1/{+parent}/tagTemplates", "request": {"$ref": "GoogleCloudDatacatalogV1beta1TagTemplate"}, "response": {"$ref": "GoogleCloudDatacatalogV1beta1TagTemplate"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform"]}, "delete": {"deprecated": true, "description": "Deletes a tag template and all tags using the template. Users should enable the Data Catalog API in the project identified by the `name` parameter (see [Data Catalog Resource Project] (https://cloud.google.com/data-catalog/docs/concepts/resource-project) for more information).", "flatPath": "v1beta1/projects/{projectsId}/locations/{locationsId}/tagTemplates/{tagTemplatesId}", "httpMethod": "DELETE", "id": "datacatalog.projects.locations.tagTemplates.delete", "parameterOrder": ["name"], "parameters": {"force": {"description": "Required. Currently, this field must always be set to `true`. This confirms the deletion of any possible tags using this template. `force = false` will be supported in the future.", "location": "query", "type": "boolean"}, "name": {"description": "Required. The name of the tag template to delete. Example: * projects/{project_id}/locations/{location}/tagTemplates/{tag_template_id}", "location": "path", "pattern": "^projects/[^/]+/locations/[^/]+/tagTemplates/[^/]+$", "required": true, "type": "string"}}, "path": "v1beta1/{+name}", "response": {"$ref": "Empty"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform"]}, "get": {"deprecated": true, "description": "Gets a tag template.", "flatPath": "v1beta1/projects/{projectsId}/locations/{locationsId}/tagTemplates/{tagTemplatesId}", "httpMethod": "GET", "id": "datacatalog.projects.locations.tagTemplates.get", "parameterOrder": ["name"], "parameters": {"name": {"description": "Required. The name of the tag template. Example: * projects/{project_id}/locations/{location}/tagTemplates/{tag_template_id}", "location": "path", "pattern": "^projects/[^/]+/locations/[^/]+/tagTemplates/[^/]+$", "required": true, "type": "string"}}, "path": "v1beta1/{+name}", "response": {"$ref": "GoogleCloudDatacatalogV1beta1TagTemplate"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform"]}, "getIamPolicy": {"deprecated": true, "description": "Gets the access control policy for a resource. A `NOT_FOUND` error is returned if the resource does not exist. An empty policy is returned if the resource exists but does not have a policy set on it. Supported resources are: - Tag templates. - Entries. - Entry groups. Note, this method cannot be used to manage policies for BigQuery, Pub/Sub and any external Google Cloud Platform resources synced to Data Catalog. Callers must have following Google IAM permission - `datacatalog.tagTemplates.getIamPolicy` to get policies on tag templates. - `datacatalog.entries.getIamPolicy` to get policies on entries. - `datacatalog.entryGroups.getIamPolicy` to get policies on entry groups.", "flatPath": "v1beta1/projects/{projectsId}/locations/{locationsId}/tagTemplates/{tagTemplatesId}:getIamPolicy", "httpMethod": "POST", "id": "datacatalog.projects.locations.tagTemplates.getIamPolicy", "parameterOrder": ["resource"], "parameters": {"resource": {"description": "REQUIRED: The resource for which the policy is being requested. See [Resource names](https://cloud.google.com/apis/design/resource_names) for the appropriate value for this field.", "location": "path", "pattern": "^projects/[^/]+/locations/[^/]+/tagTemplates/[^/]+$", "required": true, "type": "string"}}, "path": "v1beta1/{+resource}:getIamPolicy", "request": {"$ref": "GetIamPolicyRequest"}, "response": {"$ref": "Policy"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform"]}, "patch": {"deprecated": true, "description": "Updates a tag template. This method cannot be used to update the fields of a template. The tag template fields are represented as separate resources and should be updated using their own create/update/delete methods. Users should enable the Data Catalog API in the project identified by the `tag_template.name` parameter (see [Data Catalog Resource Project] (https://cloud.google.com/data-catalog/docs/concepts/resource-project) for more information).", "flatPath": "v1beta1/projects/{projectsId}/locations/{locationsId}/tagTemplates/{tagTemplatesId}", "httpMethod": "PATCH", "id": "datacatalog.projects.locations.tagTemplates.patch", "parameterOrder": ["name"], "parameters": {"name": {"description": "Identifier. The resource name of the tag template in URL format. Example: * projects/{project_id}/locations/{location}/tagTemplates/{tag_template_id} Note that this TagTemplate and its child resources may not actually be stored in the location in this name.", "location": "path", "pattern": "^projects/[^/]+/locations/[^/]+/tagTemplates/[^/]+$", "required": true, "type": "string"}, "updateMask": {"description": "Names of fields whose values to overwrite on a tag template. Currently, only `display_name` can be overwritten. In general, if this parameter is absent or empty, all modifiable fields are overwritten. If such fields are non-required and omitted in the request body, their values are emptied.", "format": "google-fieldmask", "location": "query", "type": "string"}}, "path": "v1beta1/{+name}", "request": {"$ref": "GoogleCloudDatacatalogV1beta1TagTemplate"}, "response": {"$ref": "GoogleCloudDatacatalogV1beta1TagTemplate"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform"]}, "setIamPolicy": {"deprecated": true, "description": "Sets the access control policy for a resource. Replaces any existing policy. Supported resources are: - Tag templates. - Entries. - Entry groups. Note, this method cannot be used to manage policies for BigQuery, Pub/Sub and any external Google Cloud Platform resources synced to Data Catalog. Callers must have following Google IAM permission - `datacatalog.tagTemplates.setIamPolicy` to set policies on tag templates. - `datacatalog.entries.setIamPolicy` to set policies on entries. - `datacatalog.entryGroups.setIamPolicy` to set policies on entry groups.", "flatPath": "v1beta1/projects/{projectsId}/locations/{locationsId}/tagTemplates/{tagTemplatesId}:setIamPolicy", "httpMethod": "POST", "id": "datacatalog.projects.locations.tagTemplates.setIamPolicy", "parameterOrder": ["resource"], "parameters": {"resource": {"description": "REQUIRED: The resource for which the policy is being specified. See [Resource names](https://cloud.google.com/apis/design/resource_names) for the appropriate value for this field.", "location": "path", "pattern": "^projects/[^/]+/locations/[^/]+/tagTemplates/[^/]+$", "required": true, "type": "string"}}, "path": "v1beta1/{+resource}:setIamPolicy", "request": {"$ref": "SetIamPolicyRequest"}, "response": {"$ref": "Policy"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform"]}, "testIamPermissions": {"deprecated": true, "description": "Returns the caller's permissions on a resource. If the resource does not exist, an empty set of permissions is returned (We don't return a `NOT_FOUND` error). Supported resources are: - Tag templates. - Entries. - Entry groups. Note, this method cannot be used to manage policies for BigQuery, Pub/Sub and any external Google Cloud Platform resources synced to Data Catalog. A caller is not required to have Google IAM permission to make this request.", "flatPath": "v1beta1/projects/{projectsId}/locations/{locationsId}/tagTemplates/{tagTemplatesId}:testIamPermissions", "httpMethod": "POST", "id": "datacatalog.projects.locations.tagTemplates.testIamPermissions", "parameterOrder": ["resource"], "parameters": {"resource": {"description": "REQUIRED: The resource for which the policy detail is being requested. See [Resource names](https://cloud.google.com/apis/design/resource_names) for the appropriate value for this field.", "location": "path", "pattern": "^projects/[^/]+/locations/[^/]+/tagTemplates/[^/]+$", "required": true, "type": "string"}}, "path": "v1beta1/{+resource}:testIamPermissions", "request": {"$ref": "TestIamPermissionsRequest"}, "response": {"$ref": "TestIamPermissionsResponse"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform"]}}, "resources": {"fields": {"methods": {"create": {"deprecated": true, "description": "Creates a field in a tag template. The user should enable the Data Catalog API in the project identified by the `parent` parameter (see [Data Catalog Resource Project](https://cloud.google.com/data-catalog/docs/concepts/resource-project) for more information).", "flatPath": "v1beta1/projects/{projectsId}/locations/{locationsId}/tagTemplates/{tagTemplatesId}/fields", "httpMethod": "POST", "id": "datacatalog.projects.locations.tagTemplates.fields.create", "parameterOrder": ["parent"], "parameters": {"parent": {"description": "Required. The name of the project and the template location [region](https://cloud.google.com/data-catalog/docs/concepts/regions). Example: * projects/{project_id}/locations/us-central1/tagTemplates/{tag_template_id}", "location": "path", "pattern": "^projects/[^/]+/locations/[^/]+/tagTemplates/[^/]+$", "required": true, "type": "string"}, "tagTemplateFieldId": {"description": "Required. The ID of the tag template field to create. Field ids can contain letters (both uppercase and lowercase), numbers (0-9), underscores (_) and dashes (-). Field IDs must be at least 1 character long and at most 128 characters long. Field IDs must also be unique within their template.", "location": "query", "type": "string"}}, "path": "v1beta1/{+parent}/fields", "request": {"$ref": "GoogleCloudDatacatalogV1beta1TagTemplateField"}, "response": {"$ref": "GoogleCloudDatacatalogV1beta1TagTemplateField"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform"]}, "delete": {"deprecated": true, "description": "Deletes a field in a tag template and all uses of that field. Users should enable the Data Catalog API in the project identified by the `name` parameter (see [Data Catalog Resource Project] (https://cloud.google.com/data-catalog/docs/concepts/resource-project) for more information).", "flatPath": "v1beta1/projects/{projectsId}/locations/{locationsId}/tagTemplates/{tagTemplatesId}/fields/{fieldsId}", "httpMethod": "DELETE", "id": "datacatalog.projects.locations.tagTemplates.fields.delete", "parameterOrder": ["name"], "parameters": {"force": {"description": "Required. Currently, this field must always be set to `true`. This confirms the deletion of this field from any tags using this field. `force = false` will be supported in the future.", "location": "query", "type": "boolean"}, "name": {"description": "Required. The name of the tag template field to delete. Example: * projects/{project_id}/locations/{location}/tagTemplates/{tag_template_id}/fields/{tag_template_field_id}", "location": "path", "pattern": "^projects/[^/]+/locations/[^/]+/tagTemplates/[^/]+/fields/[^/]+$", "required": true, "type": "string"}}, "path": "v1beta1/{+name}", "response": {"$ref": "Empty"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform"]}, "patch": {"deprecated": true, "description": "Updates a field in a tag template. This method cannot be used to update the field type. Users should enable the Data Catalog API in the project identified by the `name` parameter (see [Data Catalog Resource Project] (https://cloud.google.com/data-catalog/docs/concepts/resource-project) for more information).", "flatPath": "v1beta1/projects/{projectsId}/locations/{locationsId}/tagTemplates/{tagTemplatesId}/fields/{fieldsId}", "httpMethod": "PATCH", "id": "datacatalog.projects.locations.tagTemplates.fields.patch", "parameterOrder": ["name"], "parameters": {"name": {"description": "Required. The name of the tag template field. Example: * projects/{project_id}/locations/{location}/tagTemplates/{tag_template_id}/fields/{tag_template_field_id}", "location": "path", "pattern": "^projects/[^/]+/locations/[^/]+/tagTemplates/[^/]+/fields/[^/]+$", "required": true, "type": "string"}, "updateMask": {"description": "Optional. Names of fields whose values to overwrite on an individual field of a tag template. The following fields are modifiable: * `display_name` * `type.enum_type` * `is_required` If this parameter is absent or empty, all modifiable fields are overwritten. If such fields are non-required and omitted in the request body, their values are emptied with one exception: when updating an enum type, the provided values are merged with the existing values. Therefore, enum values can only be added, existing enum values cannot be deleted or renamed. Additionally, updating a template field from optional to required is *not* allowed.", "format": "google-fieldmask", "location": "query", "type": "string"}}, "path": "v1beta1/{+name}", "request": {"$ref": "GoogleCloudDatacatalogV1beta1TagTemplateField"}, "response": {"$ref": "GoogleCloudDatacatalogV1beta1TagTemplateField"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform"]}, "rename": {"deprecated": true, "description": "Renames a field in a tag template. The user should enable the Data Catalog API in the project identified by the `name` parameter (see [Data Catalog Resource Project](https://cloud.google.com/data-catalog/docs/concepts/resource-project) for more information).", "flatPath": "v1beta1/projects/{projectsId}/locations/{locationsId}/tagTemplates/{tagTemplatesId}/fields/{fieldsId}:rename", "httpMethod": "POST", "id": "datacatalog.projects.locations.tagTemplates.fields.rename", "parameterOrder": ["name"], "parameters": {"name": {"description": "Required. The name of the tag template. Example: * projects/{project_id}/locations/{location}/tagTemplates/{tag_template_id}/fields/{tag_template_field_id}", "location": "path", "pattern": "^projects/[^/]+/locations/[^/]+/tagTemplates/[^/]+/fields/[^/]+$", "required": true, "type": "string"}}, "path": "v1beta1/{+name}:rename", "request": {"$ref": "GoogleCloudDatacatalogV1beta1RenameTagTemplateFieldRequest"}, "response": {"$ref": "GoogleCloudDatacatalogV1beta1TagTemplateField"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform"]}}, "resources": {"enumValues": {"deprecated": true, "methods": {"rename": {"deprecated": true, "description": "Renames an enum value in a tag template. The enum values have to be unique within one enum field. Thus, an enum value cannot be renamed with a name used in any other enum value within the same enum field.", "flatPath": "v1beta1/projects/{projectsId}/locations/{locationsId}/tagTemplates/{tagTemplatesId}/fields/{fieldsId}/enumValues/{enumValuesId}:rename", "httpMethod": "POST", "id": "datacatalog.projects.locations.tagTemplates.fields.enumValues.rename", "parameterOrder": ["name"], "parameters": {"name": {"description": "Required. The name of the enum field value. Example: * projects/{project_id}/locations/{location}/tagTemplates/{tag_template_id}/fields/{tag_template_field_id}/enumValues/{enum_value_display_name}", "location": "path", "pattern": "^projects/[^/]+/locations/[^/]+/tagTemplates/[^/]+/fields/[^/]+/enumValues/[^/]+$", "required": true, "type": "string"}}, "path": "v1beta1/{+name}:rename", "request": {"$ref": "GoogleCloudDatacatalogV1beta1RenameTagTemplateFieldEnumValueRequest"}, "response": {"$ref": "GoogleCloudDatacatalogV1beta1TagTemplateField"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform"]}}}}}}}, "taxonomies": {"methods": {"create": {"description": "Creates a taxonomy in the specified project.", "flatPath": "v1beta1/projects/{projectsId}/locations/{locationsId}/taxonomies", "httpMethod": "POST", "id": "datacatalog.projects.locations.taxonomies.create", "parameterOrder": ["parent"], "parameters": {"parent": {"description": "Required. Resource name of the project that the taxonomy will belong to.", "location": "path", "pattern": "^projects/[^/]+/locations/[^/]+$", "required": true, "type": "string"}}, "path": "v1beta1/{+parent}/taxonomies", "request": {"$ref": "GoogleCloudDatacatalogV1beta1Taxonomy"}, "response": {"$ref": "GoogleCloudDatacatalogV1beta1Taxonomy"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform"]}, "delete": {"description": "Deletes a taxonomy. This operation will also delete all policy tags in this taxonomy along with their associated policies.", "flatPath": "v1beta1/projects/{projectsId}/locations/{locationsId}/taxonomies/{taxonomiesId}", "httpMethod": "DELETE", "id": "datacatalog.projects.locations.taxonomies.delete", "parameterOrder": ["name"], "parameters": {"name": {"description": "Required. Resource name of the taxonomy to be deleted. All policy tags in this taxonomy will also be deleted.", "location": "path", "pattern": "^projects/[^/]+/locations/[^/]+/taxonomies/[^/]+$", "required": true, "type": "string"}}, "path": "v1beta1/{+name}", "response": {"$ref": "Empty"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform"]}, "export": {"description": "Exports all taxonomies and their policy tags in a project. This method generates SerializedTaxonomy protos with nested policy tags that can be used as an input for future ImportTaxonomies calls.", "flatPath": "v1beta1/projects/{projectsId}/locations/{locationsId}/taxonomies:export", "httpMethod": "GET", "id": "datacatalog.projects.locations.taxonomies.export", "parameterOrder": ["parent"], "parameters": {"parent": {"description": "Required. Resource name of the project that taxonomies to be exported will share.", "location": "path", "pattern": "^projects/[^/]+/locations/[^/]+$", "required": true, "type": "string"}, "serializedTaxonomies": {"description": "Export taxonomies as serialized taxonomies.", "location": "query", "type": "boolean"}, "taxonomies": {"description": "Required. Resource names of the taxonomies to be exported.", "location": "query", "repeated": true, "type": "string"}}, "path": "v1beta1/{+parent}/taxonomies:export", "response": {"$ref": "GoogleCloudDatacatalogV1beta1ExportTaxonomiesResponse"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform"]}, "get": {"description": "Gets a taxonomy.", "flatPath": "v1beta1/projects/{projectsId}/locations/{locationsId}/taxonomies/{taxonomiesId}", "httpMethod": "GET", "id": "datacatalog.projects.locations.taxonomies.get", "parameterOrder": ["name"], "parameters": {"name": {"description": "Required. Resource name of the requested taxonomy.", "location": "path", "pattern": "^projects/[^/]+/locations/[^/]+/taxonomies/[^/]+$", "required": true, "type": "string"}}, "path": "v1beta1/{+name}", "response": {"$ref": "GoogleCloudDatacatalogV1beta1Taxonomy"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform"]}, "getIamPolicy": {"description": "Gets the IAM policy for a taxonomy or a policy tag.", "flatPath": "v1beta1/projects/{projectsId}/locations/{locationsId}/taxonomies/{taxonomiesId}:getIamPolicy", "httpMethod": "POST", "id": "datacatalog.projects.locations.taxonomies.getIamPolicy", "parameterOrder": ["resource"], "parameters": {"resource": {"description": "REQUIRED: The resource for which the policy is being requested. See [Resource names](https://cloud.google.com/apis/design/resource_names) for the appropriate value for this field.", "location": "path", "pattern": "^projects/[^/]+/locations/[^/]+/taxonomies/[^/]+$", "required": true, "type": "string"}}, "path": "v1beta1/{+resource}:getIamPolicy", "request": {"$ref": "GetIamPolicyRequest"}, "response": {"$ref": "Policy"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform"]}, "import": {"description": "Imports all taxonomies and their policy tags to a project as new taxonomies. This method provides a bulk taxonomy / policy tag creation using nested proto structure.", "flatPath": "v1beta1/projects/{projectsId}/locations/{locationsId}/taxonomies:import", "httpMethod": "POST", "id": "datacatalog.projects.locations.taxonomies.import", "parameterOrder": ["parent"], "parameters": {"parent": {"description": "Required. Resource name of project that the imported taxonomies will belong to.", "location": "path", "pattern": "^projects/[^/]+/locations/[^/]+$", "required": true, "type": "string"}}, "path": "v1beta1/{+parent}/taxonomies:import", "request": {"$ref": "GoogleCloudDatacatalogV1beta1ImportTaxonomiesRequest"}, "response": {"$ref": "GoogleCloudDatacatalogV1beta1ImportTaxonomiesResponse"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform"]}, "list": {"description": "Lists all taxonomies in a project in a particular location that the caller has permission to view.", "flatPath": "v1beta1/projects/{projectsId}/locations/{locationsId}/taxonomies", "httpMethod": "GET", "id": "datacatalog.projects.locations.taxonomies.list", "parameterOrder": ["parent"], "parameters": {"filter": {"description": "Supported field for filter is 'service' and value is 'dataplex'. Eg: service=dataplex.", "location": "query", "type": "string"}, "pageSize": {"description": "The maximum number of items to return. Must be a value between 1 and 1000. If not set, defaults to 50.", "format": "int32", "location": "query", "type": "integer"}, "pageToken": {"description": "The next_page_token value returned from a previous list request, if any. If not set, defaults to an empty string.", "location": "query", "type": "string"}, "parent": {"description": "Required. Resource name of the project to list the taxonomies of.", "location": "path", "pattern": "^projects/[^/]+/locations/[^/]+$", "required": true, "type": "string"}}, "path": "v1beta1/{+parent}/taxonomies", "response": {"$ref": "GoogleCloudDatacatalogV1beta1ListTaxonomiesResponse"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform"]}, "patch": {"description": "Updates a taxonomy.", "flatPath": "v1beta1/projects/{projectsId}/locations/{locationsId}/taxonomies/{taxonomiesId}", "httpMethod": "PATCH", "id": "datacatalog.projects.locations.taxonomies.patch", "parameterOrder": ["name"], "parameters": {"name": {"description": "Identifier. Resource name of this taxonomy, whose format is: \"projects/{project_number}/locations/{location_id}/taxonomies/{id}\".", "location": "path", "pattern": "^projects/[^/]+/locations/[^/]+/taxonomies/[^/]+$", "required": true, "type": "string"}, "updateMask": {"description": "The update mask applies to the resource. For the `FieldMask` definition, see https://developers.google.com/protocol-buffers/docs/reference/google.protobuf#fieldmask If not set, defaults to all of the fields that are allowed to update.", "format": "google-fieldmask", "location": "query", "type": "string"}}, "path": "v1beta1/{+name}", "request": {"$ref": "GoogleCloudDatacatalogV1beta1Taxonomy"}, "response": {"$ref": "GoogleCloudDatacatalogV1beta1Taxonomy"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform"]}, "setIamPolicy": {"description": "Sets the IAM policy for a taxonomy or a policy tag.", "flatPath": "v1beta1/projects/{projectsId}/locations/{locationsId}/taxonomies/{taxonomiesId}:setIamPolicy", "httpMethod": "POST", "id": "datacatalog.projects.locations.taxonomies.setIamPolicy", "parameterOrder": ["resource"], "parameters": {"resource": {"description": "REQUIRED: The resource for which the policy is being specified. See [Resource names](https://cloud.google.com/apis/design/resource_names) for the appropriate value for this field.", "location": "path", "pattern": "^projects/[^/]+/locations/[^/]+/taxonomies/[^/]+$", "required": true, "type": "string"}}, "path": "v1beta1/{+resource}:setIamPolicy", "request": {"$ref": "SetIamPolicyRequest"}, "response": {"$ref": "Policy"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform"]}, "testIamPermissions": {"description": "Returns the permissions that a caller has on the specified taxonomy or policy tag.", "flatPath": "v1beta1/projects/{projectsId}/locations/{locationsId}/taxonomies/{taxonomiesId}:testIamPermissions", "httpMethod": "POST", "id": "datacatalog.projects.locations.taxonomies.testIamPermissions", "parameterOrder": ["resource"], "parameters": {"resource": {"description": "REQUIRED: The resource for which the policy detail is being requested. See [Resource names](https://cloud.google.com/apis/design/resource_names) for the appropriate value for this field.", "location": "path", "pattern": "^projects/[^/]+/locations/[^/]+/taxonomies/[^/]+$", "required": true, "type": "string"}}, "path": "v1beta1/{+resource}:testIamPermissions", "request": {"$ref": "TestIamPermissionsRequest"}, "response": {"$ref": "TestIamPermissionsResponse"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform"]}}, "resources": {"policyTags": {"methods": {"create": {"description": "Creates a policy tag in the specified taxonomy.", "flatPath": "v1beta1/projects/{projectsId}/locations/{locationsId}/taxonomies/{taxonomiesId}/policyTags", "httpMethod": "POST", "id": "datacatalog.projects.locations.taxonomies.policyTags.create", "parameterOrder": ["parent"], "parameters": {"parent": {"description": "Required. Resource name of the taxonomy that the policy tag will belong to.", "location": "path", "pattern": "^projects/[^/]+/locations/[^/]+/taxonomies/[^/]+$", "required": true, "type": "string"}}, "path": "v1beta1/{+parent}/policyTags", "request": {"$ref": "GoogleCloudDatacatalogV1beta1PolicyTag"}, "response": {"$ref": "GoogleCloudDatacatalogV1beta1PolicyTag"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform"]}, "delete": {"description": "Deletes a policy tag. Also deletes all of its descendant policy tags.", "flatPath": "v1beta1/projects/{projectsId}/locations/{locationsId}/taxonomies/{taxonomiesId}/policyTags/{policyTagsId}", "httpMethod": "DELETE", "id": "datacatalog.projects.locations.taxonomies.policyTags.delete", "parameterOrder": ["name"], "parameters": {"name": {"description": "Required. Resource name of the policy tag to be deleted. All of its descendant policy tags will also be deleted.", "location": "path", "pattern": "^projects/[^/]+/locations/[^/]+/taxonomies/[^/]+/policyTags/[^/]+$", "required": true, "type": "string"}}, "path": "v1beta1/{+name}", "response": {"$ref": "Empty"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform"]}, "get": {"description": "Gets a policy tag.", "flatPath": "v1beta1/projects/{projectsId}/locations/{locationsId}/taxonomies/{taxonomiesId}/policyTags/{policyTagsId}", "httpMethod": "GET", "id": "datacatalog.projects.locations.taxonomies.policyTags.get", "parameterOrder": ["name"], "parameters": {"name": {"description": "Required. Resource name of the requested policy tag.", "location": "path", "pattern": "^projects/[^/]+/locations/[^/]+/taxonomies/[^/]+/policyTags/[^/]+$", "required": true, "type": "string"}}, "path": "v1beta1/{+name}", "response": {"$ref": "GoogleCloudDatacatalogV1beta1PolicyTag"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform"]}, "getIamPolicy": {"description": "Gets the IAM policy for a taxonomy or a policy tag.", "flatPath": "v1beta1/projects/{projectsId}/locations/{locationsId}/taxonomies/{taxonomiesId}/policyTags/{policyTagsId}:getIamPolicy", "httpMethod": "POST", "id": "datacatalog.projects.locations.taxonomies.policyTags.getIamPolicy", "parameterOrder": ["resource"], "parameters": {"resource": {"description": "REQUIRED: The resource for which the policy is being requested. See [Resource names](https://cloud.google.com/apis/design/resource_names) for the appropriate value for this field.", "location": "path", "pattern": "^projects/[^/]+/locations/[^/]+/taxonomies/[^/]+/policyTags/[^/]+$", "required": true, "type": "string"}}, "path": "v1beta1/{+resource}:getIamPolicy", "request": {"$ref": "GetIamPolicyRequest"}, "response": {"$ref": "Policy"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform"]}, "list": {"description": "Lists all policy tags in a taxonomy.", "flatPath": "v1beta1/projects/{projectsId}/locations/{locationsId}/taxonomies/{taxonomiesId}/policyTags", "httpMethod": "GET", "id": "datacatalog.projects.locations.taxonomies.policyTags.list", "parameterOrder": ["parent"], "parameters": {"pageSize": {"description": "The maximum number of items to return. Must be a value between 1 and 1000. If not set, defaults to 50.", "format": "int32", "location": "query", "type": "integer"}, "pageToken": {"description": "The next_page_token value returned from a previous List request, if any. If not set, defaults to an empty string.", "location": "query", "type": "string"}, "parent": {"description": "Required. Resource name of the taxonomy to list the policy tags of.", "location": "path", "pattern": "^projects/[^/]+/locations/[^/]+/taxonomies/[^/]+$", "required": true, "type": "string"}}, "path": "v1beta1/{+parent}/policyTags", "response": {"$ref": "GoogleCloudDatacatalogV1beta1ListPolicyTagsResponse"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform"]}, "patch": {"description": "Updates a policy tag.", "flatPath": "v1beta1/projects/{projectsId}/locations/{locationsId}/taxonomies/{taxonomiesId}/policyTags/{policyTagsId}", "httpMethod": "PATCH", "id": "datacatalog.projects.locations.taxonomies.policyTags.patch", "parameterOrder": ["name"], "parameters": {"name": {"description": "Identifier. Resource name of this policy tag, whose format is: \"projects/{project_number}/locations/{location_id}/taxonomies/{taxonomy_id}/policyTags/{id}\".", "location": "path", "pattern": "^projects/[^/]+/locations/[^/]+/taxonomies/[^/]+/policyTags/[^/]+$", "required": true, "type": "string"}, "updateMask": {"description": "The update mask applies to the resource. Only display_name, description and parent_policy_tag can be updated and thus can be listed in the mask. If update_mask is not provided, all allowed fields (i.e. display_name, description and parent) will be updated. For more information including the `FieldMask` definition, see https://developers.google.com/protocol-buffers/docs/reference/google.protobuf#fieldmask If not set, defaults to all of the fields that are allowed to update.", "format": "google-fieldmask", "location": "query", "type": "string"}}, "path": "v1beta1/{+name}", "request": {"$ref": "GoogleCloudDatacatalogV1beta1PolicyTag"}, "response": {"$ref": "GoogleCloudDatacatalogV1beta1PolicyTag"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform"]}, "setIamPolicy": {"description": "Sets the IAM policy for a taxonomy or a policy tag.", "flatPath": "v1beta1/projects/{projectsId}/locations/{locationsId}/taxonomies/{taxonomiesId}/policyTags/{policyTagsId}:setIamPolicy", "httpMethod": "POST", "id": "datacatalog.projects.locations.taxonomies.policyTags.setIamPolicy", "parameterOrder": ["resource"], "parameters": {"resource": {"description": "REQUIRED: The resource for which the policy is being specified. See [Resource names](https://cloud.google.com/apis/design/resource_names) for the appropriate value for this field.", "location": "path", "pattern": "^projects/[^/]+/locations/[^/]+/taxonomies/[^/]+/policyTags/[^/]+$", "required": true, "type": "string"}}, "path": "v1beta1/{+resource}:setIamPolicy", "request": {"$ref": "SetIamPolicyRequest"}, "response": {"$ref": "Policy"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform"]}, "testIamPermissions": {"description": "Returns the permissions that a caller has on the specified taxonomy or policy tag.", "flatPath": "v1beta1/projects/{projectsId}/locations/{locationsId}/taxonomies/{taxonomiesId}/policyTags/{policyTagsId}:testIamPermissions", "httpMethod": "POST", "id": "datacatalog.projects.locations.taxonomies.policyTags.testIamPermissions", "parameterOrder": ["resource"], "parameters": {"resource": {"description": "REQUIRED: The resource for which the policy detail is being requested. See [Resource names](https://cloud.google.com/apis/design/resource_names) for the appropriate value for this field.", "location": "path", "pattern": "^projects/[^/]+/locations/[^/]+/taxonomies/[^/]+/policyTags/[^/]+$", "required": true, "type": "string"}}, "path": "v1beta1/{+resource}:testIamPermissions", "request": {"$ref": "TestIamPermissionsRequest"}, "response": {"$ref": "TestIamPermissionsResponse"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform"]}}}}}}}}}}, "revision": "20250609", "rootUrl": "https://datacatalog.googleapis.com/", "schemas": {"Binding": {"description": "Associates `members`, or principals, with a `role`.", "id": "Binding", "properties": {"condition": {"$ref": "Expr", "description": "The condition that is associated with this binding. If the condition evaluates to `true`, then this binding applies to the current request. If the condition evaluates to `false`, then this binding does not apply to the current request. However, a different role binding might grant the same role to one or more of the principals in this binding. To learn which resources support conditions in their IAM policies, see the [IAM documentation](https://cloud.google.com/iam/help/conditions/resource-policies)."}, "members": {"description": "Specifies the principals requesting access for a Google Cloud resource. `members` can have the following values: * `allUsers`: A special identifier that represents anyone who is on the internet; with or without a Google account. * `allAuthenticatedUsers`: A special identifier that represents anyone who is authenticated with a Google account or a service account. Does not include identities that come from external identity providers (IdPs) through identity federation. * `user:{emailid}`: An email address that represents a specific Google account. For example, `<EMAIL>` . * `serviceAccount:{emailid}`: An email address that represents a Google service account. For example, `<EMAIL>`. * `serviceAccount:{projectid}.svc.id.goog[{namespace}/{kubernetes-sa}]`: An identifier for a [Kubernetes service account](https://cloud.google.com/kubernetes-engine/docs/how-to/kubernetes-service-accounts). For example, `my-project.svc.id.goog[my-namespace/my-kubernetes-sa]`. * `group:{emailid}`: An email address that represents a Google group. For example, `<EMAIL>`. * `domain:{domain}`: The G Suite domain (primary) that represents all the users of that domain. For example, `google.com` or `example.com`. * `principal://iam.googleapis.com/locations/global/workforcePools/{pool_id}/subject/{subject_attribute_value}`: A single identity in a workforce identity pool. * `principalSet://iam.googleapis.com/locations/global/workforcePools/{pool_id}/group/{group_id}`: All workforce identities in a group. * `principalSet://iam.googleapis.com/locations/global/workforcePools/{pool_id}/attribute.{attribute_name}/{attribute_value}`: All workforce identities with a specific attribute value. * `principalSet://iam.googleapis.com/locations/global/workforcePools/{pool_id}/*`: All identities in a workforce identity pool. * `principal://iam.googleapis.com/projects/{project_number}/locations/global/workloadIdentityPools/{pool_id}/subject/{subject_attribute_value}`: A single identity in a workload identity pool. * `principalSet://iam.googleapis.com/projects/{project_number}/locations/global/workloadIdentityPools/{pool_id}/group/{group_id}`: A workload identity pool group. * `principalSet://iam.googleapis.com/projects/{project_number}/locations/global/workloadIdentityPools/{pool_id}/attribute.{attribute_name}/{attribute_value}`: All identities in a workload identity pool with a certain attribute. * `principalSet://iam.googleapis.com/projects/{project_number}/locations/global/workloadIdentityPools/{pool_id}/*`: All identities in a workload identity pool. * `deleted:user:{emailid}?uid={uniqueid}`: An email address (plus unique identifier) representing a user that has been recently deleted. For example, `<EMAIL>?uid=123456789012345678901`. If the user is recovered, this value reverts to `user:{emailid}` and the recovered user retains the role in the binding. * `deleted:serviceAccount:{emailid}?uid={uniqueid}`: An email address (plus unique identifier) representing a service account that has been recently deleted. For example, `<EMAIL>?uid=123456789012345678901`. If the service account is undeleted, this value reverts to `serviceAccount:{emailid}` and the undeleted service account retains the role in the binding. * `deleted:group:{emailid}?uid={uniqueid}`: An email address (plus unique identifier) representing a Google group that has been recently deleted. For example, `<EMAIL>?uid=123456789012345678901`. If the group is recovered, this value reverts to `group:{emailid}` and the recovered group retains the role in the binding. * `deleted:principal://iam.googleapis.com/locations/global/workforcePools/{pool_id}/subject/{subject_attribute_value}`: Deleted single identity in a workforce identity pool. For example, `deleted:principal://iam.googleapis.com/locations/global/workforcePools/my-pool-id/subject/my-subject-attribute-value`.", "items": {"type": "string"}, "type": "array"}, "role": {"description": "Role that is assigned to the list of `members`, or principals. For example, `roles/viewer`, `roles/editor`, or `roles/owner`. For an overview of the IAM roles and permissions, see the [IAM documentation](https://cloud.google.com/iam/docs/roles-overview). For a list of the available pre-defined roles, see [here](https://cloud.google.com/iam/docs/understanding-roles).", "type": "string"}}, "type": "object"}, "Empty": {"description": "A generic empty message that you can re-use to avoid defining duplicated empty messages in your APIs. A typical example is to use it as the request or the response type of an API method. For instance: service Foo { rpc Bar(google.protobuf.Empty) returns (google.protobuf.Empty); }", "id": "Empty", "properties": {}, "type": "object"}, "Expr": {"description": "Represents a textual expression in the Common Expression Language (CEL) syntax. CEL is a C-like expression language. The syntax and semantics of CEL are documented at https://github.com/google/cel-spec. Example (Comparison): title: \"Summary size limit\" description: \"Determines if a summary is less than 100 chars\" expression: \"document.summary.size() < 100\" Example (Equality): title: \"Requestor is owner\" description: \"Determines if requestor is the document owner\" expression: \"document.owner == request.auth.claims.email\" Example (Logic): title: \"Public documents\" description: \"Determine whether the document should be publicly visible\" expression: \"document.type != 'private' && document.type != 'internal'\" Example (Data Manipulation): title: \"Notification string\" description: \"Create a notification string with a timestamp.\" expression: \"'New message received at ' + string(document.create_time)\" The exact variables and functions that may be referenced within an expression are determined by the service that evaluates it. See the service documentation for additional information.", "id": "Expr", "properties": {"description": {"description": "Optional. Description of the expression. This is a longer text which describes the expression, e.g. when hovered over it in a UI.", "type": "string"}, "expression": {"description": "Textual representation of an expression in Common Expression Language syntax.", "type": "string"}, "location": {"description": "Optional. String indicating the location of the expression for error reporting, e.g. a file name and a position in the file.", "type": "string"}, "title": {"description": "Optional. Title for the expression, i.e. a short string describing its purpose. This can be used e.g. in UIs which allow to enter the expression.", "type": "string"}}, "type": "object"}, "GetIamPolicyRequest": {"description": "Request message for `GetIamPolicy` method.", "id": "GetIamPolicyRequest", "properties": {"options": {"$ref": "GetPolicyOptions", "description": "OPTIONAL: A `GetPolicyOptions` object for specifying options to `GetIamPolicy`."}}, "type": "object"}, "GetPolicyOptions": {"description": "Encapsulates settings provided to GetIamPolicy.", "id": "GetPolicyOptions", "properties": {"requestedPolicyVersion": {"description": "Optional. The maximum policy version that will be used to format the policy. Valid values are 0, 1, and 3. Requests specifying an invalid value will be rejected. Requests for policies with any conditional role bindings must specify version 3. Policies with no conditional role bindings may specify any valid value or leave the field unset. The policy in the response might use the policy version that you specified, or it might use a lower policy version. For example, if you specify version 3, but the policy has no conditional role bindings, the response uses version 1. To learn which resources support conditions in their IAM policies, see the [IAM documentation](https://cloud.google.com/iam/help/conditions/resource-policies).", "format": "int32", "type": "integer"}}, "type": "object"}, "GoogleCloudDatacatalogV1BigQueryConnectionSpec": {"description": "Specification for the BigQuery connection.", "id": "GoogleCloudDatacatalogV1BigQueryConnectionSpec", "properties": {"cloudSql": {"$ref": "GoogleCloudDatacatalogV1CloudSqlBigQueryConnectionSpec", "description": "Specification for the BigQuery connection to a Cloud SQL instance."}, "connectionType": {"description": "The type of the BigQuery connection.", "enum": ["CONNECTION_TYPE_UNSPECIFIED", "CLOUD_SQL"], "enumDescriptions": ["Unspecified type.", "Cloud SQL connection."], "type": "string"}, "hasCredential": {"description": "True if there are credentials attached to the BigQuery connection; false otherwise.", "type": "boolean"}}, "type": "object"}, "GoogleCloudDatacatalogV1BigQueryDateShardedSpec": {"description": "Specification for a group of BigQuery tables with the `[prefix]YYYYMMDD` name pattern. For more information, see [Introduction to partitioned tables] (https://cloud.google.com/bigquery/docs/partitioned-tables#partitioning_versus_sharding).", "id": "GoogleCloudDatacatalogV1BigQueryDateShardedSpec", "properties": {"dataset": {"description": "Output only. The Data Catalog resource name of the dataset entry the current table belongs to. For example: `projects/{PROJECT_ID}/locations/{LOCATION}/entrygroups/{ENTRY_GROUP_ID}/entries/{ENTRY_ID}`.", "readOnly": true, "type": "string"}, "latestShardResource": {"description": "Output only. BigQuery resource name of the latest shard.", "readOnly": true, "type": "string"}, "shardCount": {"description": "Output only. Total number of shards.", "format": "int64", "readOnly": true, "type": "string"}, "tablePrefix": {"description": "Output only. The table name prefix of the shards. The name of any given shard is `[table_prefix]YYYYMMDD`. For example, for the `MyTable20180101` shard, the `table_prefix` is `MyTable`.", "readOnly": true, "type": "string"}}, "type": "object"}, "GoogleCloudDatacatalogV1BigQueryRoutineSpec": {"description": "Fields specific for BigQuery routines.", "id": "GoogleCloudDatacatalogV1BigQueryRoutineSpec", "properties": {"importedLibraries": {"description": "Paths of the imported libraries.", "items": {"type": "string"}, "type": "array"}}, "type": "object"}, "GoogleCloudDatacatalogV1BigQueryTableSpec": {"description": "Describes a BigQuery table.", "id": "GoogleCloudDatacatalogV1BigQueryTableSpec", "properties": {"tableSourceType": {"description": "Output only. The table source type.", "enum": ["TABLE_SOURCE_TYPE_UNSPECIFIED", "BIGQUERY_VIEW", "BIGQUERY_TABLE", "BIGQUERY_MATERIALIZED_VIEW"], "enumDescriptions": ["Default unknown type.", "Table view.", "BigQuery native table.", "Big<PERSON><PERSON>y materialized view."], "readOnly": true, "type": "string"}, "tableSpec": {"$ref": "GoogleCloudDatacatalogV1TableSpec", "description": "Specification of a BigQuery table. Populated only if the `table_source_type` is `BIGQUERY_TABLE`."}, "viewSpec": {"$ref": "GoogleCloudDatacatalogV1ViewSpec", "description": "Table view specification. Populated only if the `table_source_type` is `BIGQUERY_VIEW`."}}, "type": "object"}, "GoogleCloudDatacatalogV1BusinessContext": {"description": "Business Context of the entry.", "id": "GoogleCloudDatacatalogV1BusinessContext", "properties": {"contacts": {"$ref": "GoogleCloudDatacatalogV1Contacts", "description": "Contact people for the entry."}, "entryOverview": {"$ref": "GoogleCloudDatacatalogV1EntryOverview", "description": "Entry overview fields for rich text descriptions of entries."}}, "type": "object"}, "GoogleCloudDatacatalogV1CloudBigtableInstanceSpec": {"description": "Specification that applies to Instance entries that are part of `CLOUD_BIGTABLE` system. (user_specified_type)", "id": "GoogleCloudDatacatalogV1CloudBigtableInstanceSpec", "properties": {"cloudBigtableClusterSpecs": {"description": "The list of clusters for the Instance.", "items": {"$ref": "GoogleCloudDatacatalogV1CloudBigtableInstanceSpecCloudBigtableClusterSpec"}, "type": "array"}}, "type": "object"}, "GoogleCloudDatacatalogV1CloudBigtableInstanceSpecCloudBigtableClusterSpec": {"description": "Spec that applies to clusters of an Instance of Cloud Bigtable.", "id": "GoogleCloudDatacatalogV1CloudBigtableInstanceSpecCloudBigtableClusterSpec", "properties": {"displayName": {"description": "Name of the cluster.", "type": "string"}, "linkedResource": {"description": "A link back to the parent resource, in this case Instance.", "type": "string"}, "location": {"description": "Location of the cluster, typically a Cloud zone.", "type": "string"}, "type": {"description": "Type of the resource. For a cluster this would be \"CLUSTER\".", "type": "string"}}, "type": "object"}, "GoogleCloudDatacatalogV1CloudBigtableSystemSpec": {"description": "Specification that applies to all entries that are part of `CLOUD_BIGTABLE` system (user_specified_type)", "id": "GoogleCloudDatacatalogV1CloudBigtableSystemSpec", "properties": {"instanceDisplayName": {"description": "Display name of the Instance. This is user specified and different from the resource name.", "type": "string"}}, "type": "object"}, "GoogleCloudDatacatalogV1CloudSqlBigQueryConnectionSpec": {"description": "Specification for the BigQuery connection to a Cloud SQL instance.", "id": "GoogleCloudDatacatalogV1CloudSqlBigQueryConnectionSpec", "properties": {"database": {"description": "Database name.", "type": "string"}, "instanceId": {"description": "Cloud SQL instance ID in the format of `project:location:instance`.", "type": "string"}, "type": {"description": "Type of the Cloud SQL database.", "enum": ["DATABASE_TYPE_UNSPECIFIED", "POSTGRES", "MYSQL"], "enumDescriptions": ["Unspecified database type.", "Cloud SQL for PostgreSQL.", "Cloud SQL for MySQL."], "type": "string"}}, "type": "object"}, "GoogleCloudDatacatalogV1ColumnSchema": {"description": "A column within a schema. Columns can be nested inside other columns.", "id": "GoogleCloudDatacatalogV1ColumnSchema", "properties": {"column": {"description": "Required. Name of the column. Must be a UTF-8 string without dots (.). The maximum size is 64 bytes.", "type": "string"}, "defaultValue": {"description": "Optional. Default value for the column.", "type": "string"}, "description": {"description": "Optional. Description of the column. Default value is an empty string. The description must be a UTF-8 string with the maximum size of 2000 bytes.", "type": "string"}, "gcRule": {"description": "Optional. Garbage collection policy for the column or column family. Applies to systems like Cloud Bigtable.", "type": "string"}, "highestIndexingType": {"description": "Optional. Most important inclusion of this column.", "enum": ["INDEXING_TYPE_UNSPECIFIED", "INDEXING_TYPE_NONE", "INDEXING_TYPE_NON_UNIQUE", "INDEXING_TYPE_UNIQUE", "INDEXING_TYPE_PRIMARY_KEY"], "enumDescriptions": ["Unspecified.", "Column not a part of an index.", "Column Part of non unique index.", "Column part of unique index.", "Column part of the primary key."], "type": "string"}, "lookerColumnSpec": {"$ref": "GoogleCloudDatacatalogV1ColumnSchemaLookerColumnSpec", "description": "Looker specific column info of this column."}, "mode": {"description": "Optional. A column's mode indicates whether values in this column are required, nullable, or repeated. Only `NULLABLE`, `REQUIRED`, and `REPEATED` values are supported. Default mode is `NULLABLE`.", "type": "string"}, "ordinalPosition": {"description": "Optional. Ordinal position", "format": "int32", "type": "integer"}, "rangeElementType": {"$ref": "GoogleCloudDatacatalogV1ColumnSchemaFieldElementType", "description": "Optional. The subtype of the RANGE, if the type of this field is RANGE. If the type is RANGE, this field is required. Possible values for the field element type of a RANGE include: * DATE * DATETIME * TIMESTAMP"}, "subcolumns": {"description": "Optional. Schema of sub-columns. A column can have zero or more sub-columns.", "items": {"$ref": "GoogleCloudDatacatalogV1ColumnSchema"}, "type": "array"}, "type": {"description": "Required. Type of the column. Must be a UTF-8 string with the maximum size of 128 bytes.", "type": "string"}}, "type": "object"}, "GoogleCloudDatacatalogV1ColumnSchemaFieldElementType": {"description": "Represents the type of a field element.", "id": "GoogleCloudDatacatalogV1ColumnSchemaFieldElementType", "properties": {"type": {"description": "Required. The type of a field element. See ColumnSchema.type.", "type": "string"}}, "type": "object"}, "GoogleCloudDatacatalogV1ColumnSchemaLookerColumnSpec": {"description": "Column info specific to Looker System.", "id": "GoogleCloudDatacatalogV1ColumnSchemaLookerColumnSpec", "properties": {"type": {"description": "Looker specific column type of this column.", "enum": ["LOOKER_COLUMN_TYPE_UNSPECIFIED", "DIMENSION", "DIMENSION_GROUP", "FILTER", "MEASURE", "PARAMETER"], "enumDescriptions": ["Unspecified.", "Dimension.", "Dimension group - parent for Dimension.", "Filter.", "Measure.", "Parameter."], "type": "string"}}, "type": "object"}, "GoogleCloudDatacatalogV1CommonUsageStats": {"description": "Common statistics on the entry's usage. They can be set on any system.", "id": "GoogleCloudDatacatalogV1CommonUsageStats", "properties": {"viewCount": {"description": "View count in source system.", "format": "int64", "type": "string"}}, "type": "object"}, "GoogleCloudDatacatalogV1Contacts": {"description": "Contact people for the entry.", "id": "GoogleCloudDatacatalogV1Contacts", "properties": {"people": {"description": "The list of contact people for the entry.", "items": {"$ref": "GoogleCloudDatacatalogV1ContactsPerson"}, "type": "array"}}, "type": "object"}, "GoogleCloudDatacatalogV1ContactsPerson": {"description": "A contact person for the entry.", "id": "GoogleCloudDatacatalogV1ContactsPerson", "properties": {"designation": {"description": "Designation of the person, for example, Data Steward.", "type": "string"}, "email": {"description": "Email of the person in the format of `john.doe@xyz`, ``, or `<PERSON>`.", "type": "string"}}, "type": "object"}, "GoogleCloudDatacatalogV1DataSource": {"description": "Physical location of an entry.", "id": "GoogleCloudDatacatalogV1DataSource", "properties": {"resource": {"description": "Full name of a resource as defined by the service. For example: `//bigquery.googleapis.com/projects/{PROJECT_ID}/locations/{LOCATION}/datasets/{DATASET_ID}/tables/{TABLE_ID}`", "type": "string"}, "service": {"description": "Service that physically stores the data.", "enum": ["SERVICE_UNSPECIFIED", "CLOUD_STORAGE", "BIGQUERY"], "enumDescriptions": ["Default unknown service.", "Google Cloud Storage service.", "BigQuery service."], "type": "string"}, "sourceEntry": {"description": "Output only. Data Catalog entry name, if applicable.", "readOnly": true, "type": "string"}, "storageProperties": {"$ref": "GoogleCloudDatacatalogV1StorageProperties", "description": "Detailed properties of the underlying storage."}}, "type": "object"}, "GoogleCloudDatacatalogV1DataSourceConnectionSpec": {"description": "Specification that applies to a data source connection. Valid only for entries with the `DATA_SOURCE_CONNECTION` type. Only one of internal specs can be set at the time, and cannot be changed later.", "id": "GoogleCloudDatacatalogV1DataSourceConnectionSpec", "properties": {"bigqueryConnectionSpec": {"$ref": "GoogleCloudDatacatalogV1BigQueryConnectionSpec", "description": "Output only. Fields specific to BigQuery connections."}}, "type": "object"}, "GoogleCloudDatacatalogV1DatabaseTableSpec": {"description": "Specification that applies to a table resource. Valid only for entries with the `TABLE` type.", "id": "GoogleCloudDatacatalogV1DatabaseTableSpec", "properties": {"databaseViewSpec": {"$ref": "GoogleCloudDatacatalogV1DatabaseTableSpecDatabaseViewSpec", "description": "Spec what applies to tables that are actually views. Not set for \"real\" tables."}, "dataplexTable": {"$ref": "GoogleCloudDatacatalogV1DataplexTableSpec", "description": "Output only. Fields specific to a Dataplex Universal Catalog table and present only in the Dataplex Universal Catalog table entries.", "readOnly": true}, "type": {"description": "Type of this table.", "enum": ["TABLE_TYPE_UNSPECIFIED", "NATIVE", "EXTERNAL"], "enumDescriptions": ["Default unknown table type.", "Native table.", "External table."], "type": "string"}}, "type": "object"}, "GoogleCloudDatacatalogV1DatabaseTableSpecDatabaseViewSpec": {"description": "Specification that applies to database view.", "id": "GoogleCloudDatacatalogV1DatabaseTableSpecDatabaseViewSpec", "properties": {"baseTable": {"description": "Name of a singular table this view reflects one to one.", "type": "string"}, "sqlQuery": {"description": "SQL query used to generate this view.", "type": "string"}, "viewType": {"description": "Type of this view.", "enum": ["VIEW_TYPE_UNSPECIFIED", "STANDARD_VIEW", "MATERIALIZED_VIEW"], "enumDescriptions": ["Default unknown view type.", "Standard view.", "Materialized view."], "type": "string"}}, "type": "object"}, "GoogleCloudDatacatalogV1DataplexExternalTable": {"description": "External table registered by Dataplex Universal Catalog. Dataplex Universal Catalog publishes data discovered from an asset into multiple other systems (BigQuery, DPMS) in form of tables. We call them \"external tables\". External tables are also synced into the Data Catalog. This message contains pointers to those external tables (fully qualified name, resource name et cetera) within the Data Catalog.", "id": "GoogleCloudDatacatalogV1DataplexExternalTable", "properties": {"dataCatalogEntry": {"description": "Name of the Data Catalog entry representing the external table.", "type": "string"}, "fullyQualifiedName": {"description": "Fully qualified name (FQN) of the external table.", "type": "string"}, "googleCloudResource": {"description": "Google Cloud resource name of the external table.", "type": "string"}, "system": {"description": "Service in which the external table is registered.", "enum": ["INTEGRATED_SYSTEM_UNSPECIFIED", "BIGQUERY", "CLOUD_PUBSUB", "DATAPROC_METASTORE", "DATAPLEX", "CLOUD_SPANNER", "CLOUD_BIGTABLE", "CLOUD_SQL", "LOOKER", "VERTEX_AI"], "enumDescriptions": ["Default unknown system.", "Big<PERSON>uery.", "Cloud Pub/Sub.", "Dataproc Metastore.", "Dataplex Universal Catalog.", "<PERSON> Spanner", "Cloud Bigtable", "Cloud Sql", "Looker", "Vertex AI"], "type": "string"}}, "type": "object"}, "GoogleCloudDatacatalogV1DataplexFilesetSpec": {"description": "Entry specification for a Dataplex Universal Catalog fileset.", "id": "GoogleCloudDatacatalogV1DataplexFilesetSpec", "properties": {"dataplexSpec": {"$ref": "GoogleCloudDatacatalogV1DataplexSpec", "description": "Common Dataplex Universal Catalog fields."}}, "type": "object"}, "GoogleCloudDatacatalogV1DataplexSpec": {"description": "Common Dataplex Universal Catalog fields.", "id": "GoogleCloudDatacatalogV1DataplexSpec", "properties": {"asset": {"description": "Fully qualified resource name of an asset in Dataplex Universal Catalog, to which the underlying data source (Cloud Storage bucket or BigQuery dataset) of the entity is attached.", "type": "string"}, "compressionFormat": {"description": "Compression format of the data, e.g., zip, gzip etc.", "type": "string"}, "dataFormat": {"$ref": "GoogleCloudDatacatalogV1PhysicalSchema", "description": "Format of the data."}, "projectId": {"description": "Project ID of the underlying Cloud Storage or BigQuery data. Note that this may not be the same project as the corresponding Dataplex Universal Catalog lake / zone / asset.", "type": "string"}}, "type": "object"}, "GoogleCloudDatacatalogV1DataplexTableSpec": {"description": "Entry specification for a Dataplex Universal Catalog table.", "id": "GoogleCloudDatacatalogV1DataplexTableSpec", "properties": {"dataplexSpec": {"$ref": "GoogleCloudDatacatalogV1DataplexSpec", "description": "Common Dataplex Universal Catalog fields."}, "externalTables": {"description": "List of external tables registered by Dataplex Universal Catalog in other systems based on the same underlying data. External tables allow to query this data in those systems.", "items": {"$ref": "GoogleCloudDatacatalogV1DataplexExternalTable"}, "type": "array"}, "userManaged": {"description": "Indicates if the table schema is managed by the user or not.", "type": "boolean"}}, "type": "object"}, "GoogleCloudDatacatalogV1DatasetSpec": {"description": "Specification that applies to a dataset. Valid only for entries with the `DATASET` type.", "id": "GoogleCloudDatacatalogV1DatasetSpec", "properties": {"vertexDatasetSpec": {"$ref": "GoogleCloudDatacatalogV1VertexDatasetSpec", "description": "Vertex AI Dataset specific fields"}}, "type": "object"}, "GoogleCloudDatacatalogV1DumpItem": {"description": "Wrapper for any item that can be contained in the dump.", "id": "GoogleCloudDatacatalogV1DumpItem", "properties": {"taggedEntry": {"$ref": "GoogleCloudDatacatalogV1TaggedEntry", "description": "Entry and its tags."}}, "type": "object"}, "GoogleCloudDatacatalogV1Entry": {"description": "Entry metadata. A Data Catalog entry represents another resource in Google Cloud Platform (such as a BigQuery dataset or a Pub/Sub topic) or outside of it. You can use the `linked_resource` field in the entry resource to refer to the original resource ID of the source system. An entry resource contains resource details, for example, its schema. Additionally, you can attach flexible metadata to an entry in the form of a Tag.", "id": "GoogleCloudDatacatalogV1Entry", "properties": {"bigqueryDateShardedSpec": {"$ref": "GoogleCloudDatacatalogV1BigQueryDateShardedSpec", "description": "Output only. Specification for a group of BigQuery tables with the `[prefix]YYYYMMDD` name pattern. For more information, see [Introduction to partitioned tables] (https://cloud.google.com/bigquery/docs/partitioned-tables#partitioning_versus_sharding).", "readOnly": true}, "bigqueryTableSpec": {"$ref": "GoogleCloudDatacatalogV1BigQueryTableSpec", "description": "Output only. Specification that applies to a BigQuery table. Valid only for entries with the `TABLE` type.", "readOnly": true}, "businessContext": {"$ref": "GoogleCloudDatacatalogV1BusinessContext", "description": "Business Context of the entry. Not supported for BigQuery datasets"}, "cloudBigtableSystemSpec": {"$ref": "GoogleCloudDatacatalogV1CloudBigtableSystemSpec", "description": "Specification that applies to Cloud Bigtable system. Only settable when `integrated_system` is equal to `CLOUD_BIGTABLE`"}, "dataSource": {"$ref": "GoogleCloudDatacatalogV1DataSource", "description": "Output only. Physical location of the entry.", "readOnly": true}, "dataSourceConnectionSpec": {"$ref": "GoogleCloudDatacatalogV1DataSourceConnectionSpec", "description": "Specification that applies to a data source connection. Valid only for entries with the `DATA_SOURCE_CONNECTION` type."}, "databaseTableSpec": {"$ref": "GoogleCloudDatacatalogV1DatabaseTableSpec", "description": "Specification that applies to a table resource. Valid only for entries with the `TABLE` or `EXPLORE` type."}, "datasetSpec": {"$ref": "GoogleCloudDatacatalogV1DatasetSpec", "description": "Specification that applies to a dataset."}, "description": {"description": "Entry description that can consist of several sentences or paragraphs that describe entry contents. The description must not contain Unicode non-characters as well as C0 and C1 control codes except tabs (HT), new lines (LF), carriage returns (CR), and page breaks (FF). The maximum size is 2000 bytes when encoded in UTF-8. Default value is an empty string.", "type": "string"}, "displayName": {"description": "Display name of an entry. The maximum size is 500 bytes when encoded in UTF-8. Default value is an empty string.", "type": "string"}, "featureOnlineStoreSpec": {"$ref": "GoogleCloudDatacatalogV1FeatureOnlineStoreSpec", "description": "FeatureonlineStore spec for Vertex AI Feature Store."}, "filesetSpec": {"$ref": "GoogleCloudDatacatalogV1FilesetSpec", "description": "Specification that applies to a fileset resource. Valid only for entries with the `FILESET` type."}, "fullyQualifiedName": {"description": "[Fully Qualified Name (FQN)](https://cloud.google.com//data-catalog/docs/fully-qualified-names) of the resource. Set automatically for entries representing resources from synced systems. Settable only during creation, and read-only later. Can be used for search and lookup of the entries. ", "type": "string"}, "gcsFilesetSpec": {"$ref": "GoogleCloudDatacatalogV1GcsFilesetSpec", "description": "Specification that applies to a Cloud Storage fileset. Valid only for entries with the `FILESET` type."}, "integratedSystem": {"description": "Output only. Indicates the entry's source system that Data Catalog integrates with, such as BigQuery, Pub/Sub, or Dataproc Metastore.", "enum": ["INTEGRATED_SYSTEM_UNSPECIFIED", "BIGQUERY", "CLOUD_PUBSUB", "DATAPROC_METASTORE", "DATAPLEX", "CLOUD_SPANNER", "CLOUD_BIGTABLE", "CLOUD_SQL", "LOOKER", "VERTEX_AI"], "enumDescriptions": ["Default unknown system.", "Big<PERSON>uery.", "Cloud Pub/Sub.", "Dataproc Metastore.", "Dataplex Universal Catalog.", "<PERSON> Spanner", "Cloud Bigtable", "Cloud Sql", "Looker", "Vertex AI"], "readOnly": true, "type": "string"}, "labels": {"additionalProperties": {"type": "string"}, "description": "Cloud labels attached to the entry. In Data Catalog, you can create and modify labels attached only to custom entries. Synced entries have unmodifiable labels that come from the source system.", "type": "object"}, "linkedResource": {"description": "The resource this metadata entry refers to. For Google Cloud Platform resources, `linked_resource` is the [Full Resource Name] (https://cloud.google.com/apis/design/resource_names#full_resource_name). For example, the `linked_resource` for a table resource from BigQuery is: `//bigquery.googleapis.com/projects/{PROJECT_ID}/datasets/{DATASET_ID}/tables/{TABLE_ID}` Output only when the entry is one of the types in the `EntryType` enum. For entries with a `user_specified_type`, this field is optional and defaults to an empty string. The resource string must contain only letters (a-z, A-Z), numbers (0-9), underscores (_), periods (.), colons (:), slashes (/), dashes (-), and hashes (#). The maximum size is 200 bytes when encoded in UTF-8.", "type": "string"}, "lookerSystemSpec": {"$ref": "GoogleCloudDatacatalogV1LookerSystemSpec", "description": "Specification that applies to Looker sysstem. Only settable when `user_specified_system` is equal to `LOOKER`"}, "modelSpec": {"$ref": "GoogleCloudDatacatalogV1ModelSpec", "description": "Model specification."}, "name": {"description": "Output only. Identifier. The resource name of an entry in URL format. Note: The entry itself and its child resources might not be stored in the location specified in its name.", "readOnly": true, "type": "string"}, "personalDetails": {"$ref": "GoogleCloudDatacatalogV1PersonalDetails", "description": "Output only. Additional information related to the entry. Private to the current user.", "readOnly": true}, "routineSpec": {"$ref": "GoogleCloudDatacatalogV1RoutineSpec", "description": "Specification that applies to a user-defined function or procedure. Valid only for entries with the `ROUTINE` type."}, "schema": {"$ref": "GoogleCloudDatacatalogV1Schema", "description": "Schema of the entry. An entry might not have any schema attached to it."}, "serviceSpec": {"$ref": "GoogleCloudDatacatalogV1ServiceSpec", "description": "Specification that applies to a Service resource."}, "sourceSystemTimestamps": {"$ref": "GoogleCloudDatacatalogV1SystemTimestamps", "description": "Timestamps from the underlying resource, not from the Data Catalog entry. Output only when the entry has a system listed in the `IntegratedSystem` enum. For entries with `user_specified_system`, this field is optional and defaults to an empty timestamp."}, "sqlDatabaseSystemSpec": {"$ref": "GoogleCloudDatacatalogV1SqlDatabaseSystemSpec", "description": "Specification that applies to a relational database system. Only settable when `user_specified_system` is equal to `SQL_DATABASE`"}, "type": {"description": "The type of the entry. For details, see [`EntryType`](#entrytype).", "enum": ["ENTRY_TYPE_UNSPECIFIED", "TABLE", "MODEL", "DATA_STREAM", "FILESET", "CLUSTER", "DATABASE", "DATA_SOURCE_CONNECTION", "ROUTINE", "LAKE", "ZONE", "SERVICE", "DATABASE_SCHEMA", "DASHBOARD", "EXPLORE", "LOOK", "FEATURE_ONLINE_STORE", "FEATURE_VIEW", "FEATURE_GROUP"], "enumDescriptions": ["Default unknown type.", "The entry type that has a GoogleSQL schema, including logical views.", "The type of models. For more information, see [Supported models in BigQuery ML](/bigquery/docs/bqml-introduction#supported_models).", "An entry type for streaming entries. For example, a Pub/Sub topic.", "An entry type for a set of files or objects. For example, a Cloud Storage fileset.", "A group of servers that work together. For example, a Kafka cluster.", "A database.", "Connection to a data source. For example, a BigQuery connection.", "Routine, for example, a BigQuery routine.", "A Dataplex Universal Catalog lake.", "A Dataplex Universal Catalog zone.", "A service, for example, a Dataproc Metastore service.", "Schema within a relational database.", "A Dashboard, for example from Looker.", "A Looker Explore. For more information, see [Looker Explore API] (https://developers.looker.com/api/explorer/4.0/methods/LookmlModel/lookml_model_explore).", "A Looker Look. For more information, see [Looker Look API] (https://developers.looker.com/api/explorer/4.0/methods/Look).", "Feature Online Store resource in Vertex AI Feature Store.", "Feature View resource in Vertex AI Feature Store.", "Feature Group resource in Vertex AI Feature Store."], "type": "string"}, "usageSignal": {"$ref": "GoogleCloudDatacatalogV1UsageSignal", "description": "Resource usage statistics."}, "userSpecifiedSystem": {"description": "Indicates the entry's source system that Data Catalog doesn't automatically integrate with. The `user_specified_system` string has the following limitations: * Is case insensitive. * Must begin with a letter or underscore. * Can only contain letters, numbers, and underscores. * Must be at least 1 character and at most 64 characters long.", "type": "string"}, "userSpecifiedType": {"description": "Custom entry type that doesn't match any of the values allowed for input and listed in the `EntryType` enum. When creating an entry, first check the type values in the enum. If there are no appropriate types for the new entry, provide a custom value, for example, `my_special_type`. The `user_specified_type` string has the following limitations: * Is case insensitive. * Must begin with a letter or underscore. * Can only contain letters, numbers, and underscores. * Must be at least 1 character and at most 64 characters long.", "type": "string"}}, "type": "object"}, "GoogleCloudDatacatalogV1EntryOverview": {"description": "Entry overview fields for rich text descriptions of entries.", "id": "GoogleCloudDatacatalogV1EntryOverview", "properties": {"overview": {"description": "Entry overview with support for rich text. The overview must only contain Unicode characters, and should be formatted using HTML. The maximum length is 10 MiB as this value holds HTML descriptions including encoded images. The maximum length of the text without images is 100 KiB.", "type": "string"}}, "type": "object"}, "GoogleCloudDatacatalogV1FeatureOnlineStoreSpec": {"description": "Detail description of the source information of a Vertex Feature Online Store.", "id": "GoogleCloudDatacatalogV1FeatureOnlineStoreSpec", "properties": {"storageType": {"description": "Output only. Type of underlying storage for the FeatureOnlineStore.", "enum": ["STORAGE_TYPE_UNSPECIFIED", "BIGTABLE", "OPTIMIZED"], "enumDescriptions": ["Should not be used.", "Underlsying storgae is Bigtable.", "Underlying is optimized online server (Lightning)."], "readOnly": true, "type": "string"}}, "type": "object"}, "GoogleCloudDatacatalogV1FilesetSpec": {"description": "Specification that applies to a fileset. Valid only for entries with the 'FILESET' type.", "id": "GoogleCloudDatacatalogV1FilesetSpec", "properties": {"dataplexFileset": {"$ref": "GoogleCloudDatacatalogV1DataplexFilesetSpec", "description": "Fields specific to a Dataplex Universal Catalog fileset and present only in the Dataplex Universal Catalog fileset entries."}}, "type": "object"}, "GoogleCloudDatacatalogV1GcsFileSpec": {"description": "Specification of a single file in Cloud Storage.", "id": "GoogleCloudDatacatalogV1GcsFileSpec", "properties": {"filePath": {"description": "Required. Full file path. Example: `gs://bucket_name/a/b.txt`.", "type": "string"}, "gcsTimestamps": {"$ref": "GoogleCloudDatacatalogV1SystemTimestamps", "description": "Output only. Creation, modification, and expiration timestamps of a Cloud Storage file.", "readOnly": true}, "sizeBytes": {"description": "Output only. File size in bytes.", "format": "int64", "readOnly": true, "type": "string"}}, "type": "object"}, "GoogleCloudDatacatalogV1GcsFilesetSpec": {"description": "Describes a Cloud Storage fileset entry.", "id": "GoogleCloudDatacatalogV1GcsFilesetSpec", "properties": {"filePatterns": {"description": "Required. Patterns to identify a set of files in Google Cloud Storage. For more information, see [Wildcard Names] (https://cloud.google.com/storage/docs/wildcards). Note: Currently, bucket wildcards are not supported. Examples of valid `file_patterns`: * `gs://bucket_name/dir/*`: matches all files in `bucket_name/dir` directory * `gs://bucket_name/dir/**`: matches all files in `bucket_name/dir` and all subdirectories * `gs://bucket_name/file*`: matches files prefixed by `file` in `bucket_name` * `gs://bucket_name/??.txt`: matches files with two characters followed by `.txt` in `bucket_name` * `gs://bucket_name/[aeiou].txt`: matches files that contain a single vowel character followed by `.txt` in `bucket_name` * `gs://bucket_name/[a-m].txt`: matches files that contain `a`, `b`, ... or `m` followed by `.txt` in `bucket_name` * `gs://bucket_name/a/*/b`: matches all files in `bucket_name` that match the `a/*/b` pattern, such as `a/c/b`, `a/d/b` * `gs://another_bucket/a.txt`: matches `gs://another_bucket/a.txt` You can combine wildcards to match complex sets of files, for example: `gs://bucket_name/[a-m]??.j*g`", "items": {"type": "string"}, "type": "array"}, "sampleGcsFileSpecs": {"description": "Output only. Sample files contained in this fileset, not all files contained in this fileset are represented here.", "items": {"$ref": "GoogleCloudDatacatalogV1GcsFileSpec"}, "readOnly": true, "type": "array"}}, "type": "object"}, "GoogleCloudDatacatalogV1ImportEntriesMetadata": {"description": "Metadata message for long-running operation returned by the ImportEntries.", "id": "GoogleCloudDatacatalogV1ImportEntriesMetadata", "properties": {"errors": {"description": "Partial errors that are encountered during the ImportEntries operation. There is no guarantee that all the encountered errors are reported. However, if no errors are reported, it means that no errors were encountered.", "items": {"$ref": "Status"}, "type": "array"}, "state": {"description": "State of the import operation.", "enum": ["IMPORT_STATE_UNSPECIFIED", "IMPORT_QUEUED", "IMPORT_IN_PROGRESS", "IMPORT_DONE", "IMPORT_OBSOLETE"], "enumDescriptions": ["Default value. This value is unused.", "The dump with entries has been queued for import.", "The import of entries is in progress.", "The import of entries has been finished.", "The import of entries has been abandoned in favor of a newer request."], "type": "string"}}, "type": "object"}, "GoogleCloudDatacatalogV1ImportEntriesResponse": {"description": "Response message for long-running operation returned by the ImportEntries.", "id": "GoogleCloudDatacatalogV1ImportEntriesResponse", "properties": {"deletedEntriesCount": {"description": "Number of entries deleted as a result of import operation.", "format": "int64", "type": "string"}, "upsertedEntriesCount": {"description": "Cumulative number of entries created and entries updated as a result of import operation.", "format": "int64", "type": "string"}}, "type": "object"}, "GoogleCloudDatacatalogV1LookerSystemSpec": {"description": "Specification that applies to entries that are part `LOOKER` system (user_specified_type)", "id": "GoogleCloudDatacatalogV1LookerSystemSpec", "properties": {"parentInstanceDisplayName": {"description": "Name of the parent Looker Instance. Empty if it does not exist.", "type": "string"}, "parentInstanceId": {"description": "ID of the parent Looker Instance. Empty if it does not exist. Example value: `someinstance.looker.com`", "type": "string"}, "parentModelDisplayName": {"description": "Name of the parent Model. Empty if it does not exist.", "type": "string"}, "parentModelId": {"description": "ID of the parent Model. Empty if it does not exist.", "type": "string"}, "parentViewDisplayName": {"description": "Name of the parent View. Empty if it does not exist.", "type": "string"}, "parentViewId": {"description": "ID of the parent View. Empty if it does not exist.", "type": "string"}}, "type": "object"}, "GoogleCloudDatacatalogV1ModelSpec": {"description": "Specification that applies to a model. Valid only for entries with the `MODEL` type.", "id": "GoogleCloudDatacatalogV1ModelSpec", "properties": {"vertexModelSpec": {"$ref": "GoogleCloudDatacatalogV1VertexModelSpec", "description": "Specification for vertex model resources."}}, "type": "object"}, "GoogleCloudDatacatalogV1PersonalDetails": {"description": "Entry metadata relevant only to the user and private to them.", "id": "GoogleCloudDatacatalogV1PersonalDetails", "properties": {"starTime": {"description": "Set if the entry is starred; unset otherwise.", "format": "google-datetime", "type": "string"}, "starred": {"description": "True if the entry is starred by the user; false otherwise.", "type": "boolean"}}, "type": "object"}, "GoogleCloudDatacatalogV1PhysicalSchema": {"description": "Native schema used by a resource represented as an entry. Used by query engines for deserializing and parsing source data.", "id": "GoogleCloudDatacatalogV1PhysicalSchema", "properties": {"avro": {"$ref": "GoogleCloudDatacatalogV1PhysicalSchemaAvroSchema", "description": "Schema in Avro JSON format."}, "csv": {"$ref": "GoogleCloudDatacatalogV1PhysicalSchemaCsvSchema", "description": "Marks a CSV-encoded data source."}, "orc": {"$ref": "GoogleCloudDatacatalogV1PhysicalSchemaOrcSchema", "description": "Marks an ORC-encoded data source."}, "parquet": {"$ref": "GoogleCloudDatacatalogV1PhysicalSchemaParquetSchema", "description": "Marks a Parquet-encoded data source."}, "protobuf": {"$ref": "GoogleCloudDatacatalogV1PhysicalSchemaProtobufSchema", "description": "Schema in protocol buffer format."}, "thrift": {"$ref": "GoogleCloudDatacatalogV1PhysicalSchemaThriftSchema", "description": "Schema in Thrift format."}}, "type": "object"}, "GoogleCloudDatacatalogV1PhysicalSchemaAvroSchema": {"description": "Schema in Avro JSON format.", "id": "GoogleCloudDatacatalogV1PhysicalSchemaAvroSchema", "properties": {"text": {"description": "JSON source of the Avro schema.", "type": "string"}}, "type": "object"}, "GoogleCloudDatacatalogV1PhysicalSchemaCsvSchema": {"description": "Marks a CSV-encoded data source.", "id": "GoogleCloudDatacatalogV1PhysicalSchemaCsvSchema", "properties": {}, "type": "object"}, "GoogleCloudDatacatalogV1PhysicalSchemaOrcSchema": {"description": "Marks an ORC-encoded data source.", "id": "GoogleCloudDatacatalogV1PhysicalSchemaOrcSchema", "properties": {}, "type": "object"}, "GoogleCloudDatacatalogV1PhysicalSchemaParquetSchema": {"description": "Marks a Parquet-encoded data source.", "id": "GoogleCloudDatacatalogV1PhysicalSchemaParquetSchema", "properties": {}, "type": "object"}, "GoogleCloudDatacatalogV1PhysicalSchemaProtobufSchema": {"description": "Schema in protocol buffer format.", "id": "GoogleCloudDatacatalogV1PhysicalSchemaProtobufSchema", "properties": {"text": {"description": "Protocol buffer source of the schema.", "type": "string"}}, "type": "object"}, "GoogleCloudDatacatalogV1PhysicalSchemaThriftSchema": {"description": "Schema in Thrift format.", "id": "GoogleCloudDatacatalogV1PhysicalSchemaThriftSchema", "properties": {"text": {"description": "Thrift IDL source of the schema.", "type": "string"}}, "type": "object"}, "GoogleCloudDatacatalogV1ReconcileTagsMetadata": {"description": "Long-running operation metadata message returned by the ReconcileTags.", "id": "GoogleCloudDatacatalogV1ReconcileTagsMetadata", "properties": {"errors": {"additionalProperties": {"$ref": "Status"}, "description": "Maps the name of each tagged column (or empty string for a sole entry) to tagging operation status.", "type": "object"}, "state": {"description": "State of the reconciliation operation.", "enum": ["RECONCILIATION_STATE_UNSPECIFIED", "RECONCILIATION_QUEUED", "RECONCILIATION_IN_PROGRESS", "RECONCILIATION_DONE"], "enumDescriptions": ["Default value. This value is unused.", "The reconciliation has been queued and awaits for execution.", "The reconciliation is in progress.", "The reconciliation has been finished."], "type": "string"}}, "type": "object"}, "GoogleCloudDatacatalogV1ReconcileTagsResponse": {"description": "Long-running operation response message returned by ReconcileTags.", "id": "GoogleCloudDatacatalogV1ReconcileTagsResponse", "properties": {"createdTagsCount": {"description": "Number of tags created in the request.", "format": "int64", "type": "string"}, "deletedTagsCount": {"description": "Number of tags deleted in the request.", "format": "int64", "type": "string"}, "updatedTagsCount": {"description": "Number of tags updated in the request.", "format": "int64", "type": "string"}}, "type": "object"}, "GoogleCloudDatacatalogV1RoutineSpec": {"description": "Specification that applies to a routine. Valid only for entries with the `ROUTINE` type.", "id": "GoogleCloudDatacatalogV1RoutineSpec", "properties": {"bigqueryRoutineSpec": {"$ref": "GoogleCloudDatacatalogV1BigQueryRoutineSpec", "description": "Fields specific for BigQuery routines."}, "definitionBody": {"description": "The body of the routine.", "type": "string"}, "language": {"description": "The language the routine is written in. The exact value depends on the source system. For BigQuery routines, possible values are: * `SQL` * `JAVASCRIPT`", "type": "string"}, "returnType": {"description": "Return type of the argument. The exact value depends on the source system and the language.", "type": "string"}, "routineArguments": {"description": "Arguments of the routine.", "items": {"$ref": "GoogleCloudDatacatalogV1RoutineSpecArgument"}, "type": "array"}, "routineType": {"description": "The type of the routine.", "enum": ["ROUTINE_TYPE_UNSPECIFIED", "SCALAR_FUNCTION", "PROCEDURE"], "enumDescriptions": ["Unspecified type.", "Non-builtin permanent scalar function.", "Stored procedure."], "type": "string"}}, "type": "object"}, "GoogleCloudDatacatalogV1RoutineSpecArgument": {"description": "Input or output argument of a function or stored procedure.", "id": "GoogleCloudDatacatalogV1RoutineSpecArgument", "properties": {"mode": {"description": "Specifies whether the argument is input or output.", "enum": ["MODE_UNSPECIFIED", "IN", "OUT", "INOUT"], "enumDescriptions": ["Unspecified mode.", "The argument is input-only.", "The argument is output-only.", "The argument is both an input and an output."], "type": "string"}, "name": {"description": "The name of the argument. A return argument of a function might not have a name.", "type": "string"}, "type": {"description": "Type of the argument. The exact value depends on the source system and the language.", "type": "string"}}, "type": "object"}, "GoogleCloudDatacatalogV1Schema": {"description": "Represents a schema, for example, a BigQuery, GoogleSQL, or Avro schema.", "id": "GoogleCloudDatacatalogV1Schema", "properties": {"columns": {"description": "The unified GoogleSQL-like schema of columns. The overall maximum number of columns and nested columns is 10,000. The maximum nested depth is 15 levels.", "items": {"$ref": "GoogleCloudDatacatalogV1ColumnSchema"}, "type": "array"}}, "type": "object"}, "GoogleCloudDatacatalogV1ServiceSpec": {"description": "Specification that applies to a Service resource. Valid only for entries with the `SERVICE` type.", "id": "GoogleCloudDatacatalogV1ServiceSpec", "properties": {"cloudBigtableInstanceSpec": {"$ref": "GoogleCloudDatacatalogV1CloudBigtableInstanceSpec", "description": "Specification that applies to Instance entries of `CLOUD_BIGTABLE` system."}}, "type": "object"}, "GoogleCloudDatacatalogV1SqlDatabaseSystemSpec": {"description": "Specification that applies to entries that are part `SQL_DATABASE` system (user_specified_type)", "id": "GoogleCloudDatacatalogV1SqlDatabaseSystemSpec", "properties": {"databaseVersion": {"description": "Version of the database engine.", "type": "string"}, "instanceHost": {"description": "Host of the SQL database enum InstanceHost { UNDEFINED = 0; SELF_HOSTED = 1; CLOUD_SQL = 2; AMAZON_RDS = 3; AZURE_SQL = 4; } Host of the enclousing database instance.", "type": "string"}, "sqlEngine": {"description": "SQL Database Engine. enum SqlEngine { UNDEFINED = 0; MY_SQL = 1; POSTGRE_SQL = 2; SQL_SERVER = 3; } Engine of the enclosing database instance.", "type": "string"}}, "type": "object"}, "GoogleCloudDatacatalogV1StorageProperties": {"description": "Details the properties of the underlying storage.", "id": "GoogleCloudDatacatalogV1StorageProperties", "properties": {"filePattern": {"description": "Patterns to identify a set of files for this fileset. Examples of a valid `file_pattern`: * `gs://bucket_name/dir/*`: matches all files in the `bucket_name/dir` directory * `gs://bucket_name/dir/**`: matches all files in the `bucket_name/dir` and all subdirectories recursively * `gs://bucket_name/file*`: matches files prefixed by `file` in `bucket_name` * `gs://bucket_name/??.txt`: matches files with two characters followed by `.txt` in `bucket_name` * `gs://bucket_name/[aeiou].txt`: matches files that contain a single vowel character followed by `.txt` in `bucket_name` * `gs://bucket_name/[a-m].txt`: matches files that contain `a`, `b`, ... or `m` followed by `.txt` in `bucket_name` * `gs://bucket_name/a/*/b`: matches all files in `bucket_name` that match the `a/*/b` pattern, such as `a/c/b`, `a/d/b` * `gs://another_bucket/a.txt`: matches `gs://another_bucket/a.txt`", "items": {"type": "string"}, "type": "array"}, "fileType": {"description": "File type in MIME format, for example, `text/plain`.", "type": "string"}}, "type": "object"}, "GoogleCloudDatacatalogV1SystemTimestamps": {"description": "Timestamps associated with this resource in a particular system.", "id": "GoogleCloudDatacatalogV1SystemTimestamps", "properties": {"createTime": {"description": "Creation timestamp of the resource within the given system.", "format": "google-datetime", "type": "string"}, "expireTime": {"description": "Output only. Expiration timestamp of the resource within the given system. Currently only applicable to BigQuery resources.", "format": "google-datetime", "readOnly": true, "type": "string"}, "updateTime": {"description": "Timestamp of the last modification of the resource or its metadata within a given system. Note: Depending on the source system, not every modification updates this timestamp. For example, BigQuery timestamps every metadata modification but not data or permission changes.", "format": "google-datetime", "type": "string"}}, "type": "object"}, "GoogleCloudDatacatalogV1TableSpec": {"description": "Normal BigQuery table specification.", "id": "GoogleCloudDatacatalogV1TableSpec", "properties": {"groupedEntry": {"description": "Output only. If the table is date-sharded, that is, it matches the `[prefix]YYYYMMDD` name pattern, this field is the Data Catalog resource name of the date-sharded grouped entry. For example: `projects/{PROJECT_ID}/locations/{LOCATION}/entrygroups/{ENTRY_GROUP_ID}/entries/{ENTRY_ID}`. Otherwise, `grouped_entry` is empty.", "readOnly": true, "type": "string"}}, "type": "object"}, "GoogleCloudDatacatalogV1Tag": {"description": "Tags contain custom metadata and are attached to Data Catalog resources. Tags conform with the specification of their tag template. See [Data Catalog IAM](https://cloud.google.com/data-catalog/docs/concepts/iam) for information on the permissions needed to create or view tags.", "id": "GoogleCloudDatacatalogV1Tag", "properties": {"column": {"description": "Resources like entry can have schemas associated with them. This scope allows you to attach tags to an individual column based on that schema. To attach a tag to a nested column, separate column names with a dot (`.`). Example: `column.nested_column`.", "type": "string"}, "dataplexTransferStatus": {"description": "Output only. Denotes the transfer status of the Tag Template.", "enum": ["DATAPLEX_TRANSFER_STATUS_UNSPECIFIED", "MIGRATED", "TRANSFERRED"], "enumDeprecated": [false, true, false], "enumDescriptions": ["Default value. TagTemplate and its tags are only visible and editable in Data Catalog.", "TagTemplate and its tags are auto-copied to Dataplex Universal Catalog service. Visible in both services. Editable in Data Catalog, read-only in Dataplex Universal Catalog. Deprecated: Individual TagTemplate migration is deprecated in favor of organization or project wide TagTemplate migration opt-in.", "TagTemplate and its tags are auto-copied to Dataplex Universal Catalog service. Visible in both services. Editable in Dataplex Universal Catalog, read-only in Data Catalog."], "readOnly": true, "type": "string"}, "fields": {"additionalProperties": {"$ref": "GoogleCloudDatacatalogV1TagField"}, "description": "Required. Maps the ID of a tag field to its value and additional information about that field. Tag template defines valid field IDs. A tag must have at least 1 field and at most 500 fields.", "type": "object"}, "name": {"description": "Identifier. The resource name of the tag in URL format where tag ID is a system-generated identifier. Note: The tag itself might not be stored in the location specified in its name.", "type": "string"}, "template": {"description": "Required. The resource name of the tag template this tag uses. Example: `projects/{PROJECT_ID}/locations/{LOCATION}/tagTemplates/{TAG_TEMPLATE_ID}` This field cannot be modified after creation.", "type": "string"}, "templateDisplayName": {"description": "Output only. The display name of the tag template.", "readOnly": true, "type": "string"}}, "type": "object"}, "GoogleCloudDatacatalogV1TagField": {"description": "Contains the value and additional information on a field within a Tag.", "id": "GoogleCloudDatacatalogV1TagField", "properties": {"boolValue": {"description": "The value of a tag field with a boolean type.", "type": "boolean"}, "displayName": {"description": "Output only. The display name of this field.", "readOnly": true, "type": "string"}, "doubleValue": {"description": "The value of a tag field with a double type.", "format": "double", "type": "number"}, "enumValue": {"$ref": "GoogleCloudDatacatalogV1TagFieldEnumValue", "description": "The value of a tag field with an enum type. This value must be one of the allowed values listed in this enum."}, "order": {"description": "Output only. The order of this field with respect to other fields in this tag. Can be set by Tag. For example, a higher value can indicate a more important field. The value can be negative. Multiple fields can have the same order, and field orders within a tag don't have to be sequential.", "format": "int32", "readOnly": true, "type": "integer"}, "richtextValue": {"description": "The value of a tag field with a rich text type. The maximum length is 10 MiB as this value holds HTML descriptions including encoded images. The maximum length of the text without images is 100 KiB.", "type": "string"}, "stringValue": {"description": "The value of a tag field with a string type. The maximum length is 2000 UTF-8 characters.", "type": "string"}, "timestampValue": {"description": "The value of a tag field with a timestamp type.", "format": "google-datetime", "type": "string"}}, "type": "object"}, "GoogleCloudDatacatalogV1TagFieldEnumValue": {"description": "An enum value.", "id": "GoogleCloudDatacatalogV1TagFieldEnumValue", "properties": {"displayName": {"description": "The display name of the enum value.", "type": "string"}}, "type": "object"}, "GoogleCloudDatacatalogV1TaggedEntry": {"description": "Wrapper containing Entry and information about Tags that should and should not be attached to it.", "id": "GoogleCloudDatacatalogV1TaggedEntry", "properties": {"absentTags": {"description": "Optional. Tags that should be deleted from the Data Catalog. Caller should populate template name and column only.", "items": {"$ref": "GoogleCloudDatacatalogV1Tag"}, "type": "array"}, "presentTags": {"description": "Optional. Tags that should be ingested into the Data Catalog. Caller should populate template name, column and fields.", "items": {"$ref": "GoogleCloudDatacatalogV1Tag"}, "type": "array"}, "v1Entry": {"$ref": "GoogleCloudDatacatalogV1Entry", "description": "Non-encrypted Data Catalog v1 Entry."}}, "type": "object"}, "GoogleCloudDatacatalogV1UsageSignal": {"description": "The set of all usage signals that Data Catalog stores. Note: Usually, these signals are updated daily. In rare cases, an update may fail but will be performed again on the next day.", "id": "GoogleCloudDatacatalogV1UsageSignal", "properties": {"commonUsageWithinTimeRange": {"additionalProperties": {"$ref": "GoogleCloudDatacatalogV1CommonUsageStats"}, "description": "Common usage statistics over each of the predefined time ranges. Supported time ranges are `{\"24H\", \"7D\", \"30D\", \"Lifetime\"}`.", "type": "object"}, "favoriteCount": {"description": "Favorite count in the source system.", "format": "int64", "type": "string"}, "updateTime": {"description": "The end timestamp of the duration of usage statistics.", "format": "google-datetime", "type": "string"}, "usageWithinTimeRange": {"additionalProperties": {"$ref": "GoogleCloudDatacatalogV1UsageStats"}, "description": "Output only. BigQuery usage statistics over each of the predefined time ranges. Supported time ranges are `{\"24H\", \"7D\", \"30D\"}`.", "readOnly": true, "type": "object"}}, "type": "object"}, "GoogleCloudDatacatalogV1UsageStats": {"description": "Detailed statistics on the entry's usage. Usage statistics have the following limitations: - Only BigQuery tables have them. - They only include BigQuery query jobs. - They might be underestimated because wildcard table references are not yet counted. For more information, see [Querying multiple tables using a wildcard table] (https://cloud.google.com/bigquery/docs/querying-wildcard-tables)", "id": "GoogleCloudDatacatalogV1UsageStats", "properties": {"totalCancellations": {"description": "The number of cancelled attempts to use the underlying entry.", "format": "float", "type": "number"}, "totalCompletions": {"description": "The number of successful uses of the underlying entry.", "format": "float", "type": "number"}, "totalExecutionTimeForCompletionsMillis": {"description": "Total time spent only on successful uses, in milliseconds.", "format": "float", "type": "number"}, "totalFailures": {"description": "The number of failed attempts to use the underlying entry.", "format": "float", "type": "number"}}, "type": "object"}, "GoogleCloudDatacatalogV1VertexDatasetSpec": {"description": "Specification for vertex dataset resources.", "id": "GoogleCloudDatacatalogV1VertexDatasetSpec", "properties": {"dataItemCount": {"description": "The number of DataItems in this Dataset. Only apply for non-structured Dataset.", "format": "int64", "type": "string"}, "dataType": {"description": "Type of the dataset.", "enum": ["DATA_TYPE_UNSPECIFIED", "TABLE", "IMAGE", "TEXT", "VIDEO", "CONVERSATION", "TIME_SERIES", "DOCUMENT", "TEXT_TO_SPEECH", "TRANSLATION", "STORE_VISION", "ENTERPRISE_KNOWLEDGE_GRAPH", "TEXT_PROMPT"], "enumDescriptions": ["Should not be used.", "Structured data dataset.", "Image dataset which supports ImageClassification, ImageObjectDetection and ImageSegmentation problems.", "Document dataset which supports TextClassification, TextExtraction and TextSentiment problems.", "Video dataset which supports VideoClassification, VideoObjectTracking and VideoActionRecognition problems.", "Conversation dataset which supports conversation problems.", "TimeSeries dataset.", "Document dataset which supports DocumentAnnotation problems.", "TextToSpeech dataset which supports TextToSpeech problems.", "Translation dataset which supports Translation problems.", "Store Vision dataset which is used for HITL integration.", "Enterprise Knowledge Graph dataset which is used for HITL labeling integration.", "Text prompt dataset which supports Large Language Models."], "type": "string"}}, "type": "object"}, "GoogleCloudDatacatalogV1VertexModelSourceInfo": {"description": "Detail description of the source information of a Vertex model.", "id": "GoogleCloudDatacatalogV1VertexModelSourceInfo", "properties": {"copy": {"description": "If this Model is copy of another Model. If true then source_type pertains to the original.", "type": "boolean"}, "sourceType": {"description": "Type of the model source.", "enum": ["MODEL_SOURCE_TYPE_UNSPECIFIED", "AUTOML", "CUSTOM", "BQML", "MODEL_GARDEN", "GENIE", "CUSTOM_TEXT_EMBEDDING", "MARKETPLACE"], "enumDescriptions": ["Should not be used.", "The Model is uploaded by automl training pipeline.", "The Model is uploaded by user or custom training pipeline.", "The Model is registered and sync'ed from BigQuery ML.", "The Model is saved or tuned from Model Garden.", "The Model is saved or tuned from Genie.", "The Model is uploaded by text embedding finetuning pipeline.", "The Model is saved or tuned from Marketplace."], "type": "string"}}, "type": "object"}, "GoogleCloudDatacatalogV1VertexModelSpec": {"description": "Specification for vertex model resources.", "id": "GoogleCloudDatacatalogV1VertexModelSpec", "properties": {"containerImageUri": {"description": "URI of the Docker image to be used as the custom container for serving predictions.", "type": "string"}, "versionAliases": {"description": "User provided version aliases so that a model version can be referenced via alias", "items": {"type": "string"}, "type": "array"}, "versionDescription": {"description": "The description of this version.", "type": "string"}, "versionId": {"description": "The version ID of the model.", "type": "string"}, "vertexModelSourceInfo": {"$ref": "GoogleCloudDatacatalogV1VertexModelSourceInfo", "description": "Source of a Vertex model."}}, "type": "object"}, "GoogleCloudDatacatalogV1ViewSpec": {"description": "Table view specification.", "id": "GoogleCloudDatacatalogV1ViewSpec", "properties": {"viewQuery": {"description": "Output only. The query that defines the table view.", "readOnly": true, "type": "string"}}, "type": "object"}, "GoogleCloudDatacatalogV1beta1BigQueryDateShardedSpec": {"description": "Spec for a group of BigQuery tables with name pattern `[prefix]YYYYMMDD`. Context: https://cloud.google.com/bigquery/docs/partitioned-tables#partitioning_versus_sharding", "id": "GoogleCloudDatacatalogV1beta1BigQueryDateShardedSpec", "properties": {"dataset": {"description": "Output only. The Data Catalog resource name of the dataset entry the current table belongs to, for example, `projects/{project_id}/locations/{location}/entrygroups/{entry_group_id}/entries/{entry_id}`.", "readOnly": true, "type": "string"}, "shardCount": {"description": "Output only. Total number of shards.", "format": "int64", "readOnly": true, "type": "string"}, "tablePrefix": {"description": "Output only. The table name prefix of the shards. The name of any given shard is `[table_prefix]YYYYMMDD`, for example, for shard `MyTable20180101`, the `table_prefix` is `MyTable`.", "readOnly": true, "type": "string"}}, "type": "object"}, "GoogleCloudDatacatalogV1beta1BigQueryTableSpec": {"description": "Describes a BigQuery table.", "id": "GoogleCloudDatacatalogV1beta1BigQueryTableSpec", "properties": {"tableSourceType": {"description": "Output only. The table source type.", "enum": ["TABLE_SOURCE_TYPE_UNSPECIFIED", "BIGQUERY_VIEW", "BIGQUERY_TABLE", "BIGQUERY_MATERIALIZED_VIEW"], "enumDescriptions": ["Default unknown type.", "Table view.", "BigQuery native table.", "Big<PERSON><PERSON>y materialized view."], "readOnly": true, "type": "string"}, "tableSpec": {"$ref": "GoogleCloudDatacatalogV1beta1TableSpec", "description": "Spec of a BigQuery table. This field should only be populated if `table_source_type` is `BIGQUERY_TABLE`."}, "viewSpec": {"$ref": "GoogleCloudDatacatalogV1beta1ViewSpec", "description": "Table view specification. This field should only be populated if `table_source_type` is `BIGQUERY_VIEW`."}}, "type": "object"}, "GoogleCloudDatacatalogV1beta1ColumnSchema": {"description": "Representation of a column within a schema. Columns could be nested inside other columns.", "id": "GoogleCloudDatacatalogV1beta1ColumnSchema", "properties": {"column": {"description": "Required. Name of the column.", "type": "string"}, "description": {"description": "Optional. Description of the column. Default value is an empty string.", "type": "string"}, "mode": {"description": "Optional. A column's mode indicates whether the values in this column are required, nullable, etc. Only `NULLABLE`, `REQUIRED` and `REPEATED` are supported. Default mode is `NULLABLE`.", "type": "string"}, "subcolumns": {"description": "Optional. Schema of sub-columns. A column can have zero or more sub-columns.", "items": {"$ref": "GoogleCloudDatacatalogV1beta1ColumnSchema"}, "type": "array"}, "type": {"description": "Required. Type of the column.", "type": "string"}}, "type": "object"}, "GoogleCloudDatacatalogV1beta1Entry": {"description": "Entry Metadata. A Data Catalog Entry resource represents another resource in Google Cloud Platform (such as a BigQuery dataset or a Pub/Sub topic), or outside of Google Cloud Platform. Clients can use the `linked_resource` field in the Entry resource to refer to the original resource ID of the source system. An Entry resource contains resource details, such as its schema. An Entry can also be used to attach flexible metadata, such as a Tag.", "id": "GoogleCloudDatacatalogV1beta1Entry", "properties": {"bigqueryDateShardedSpec": {"$ref": "GoogleCloudDatacatalogV1beta1BigQueryDateShardedSpec", "description": "Specification for a group of BigQuery tables with name pattern `[prefix]YYYYMMDD`. Context: https://cloud.google.com/bigquery/docs/partitioned-tables#partitioning_versus_sharding."}, "bigqueryTableSpec": {"$ref": "GoogleCloudDatacatalogV1beta1BigQueryTableSpec", "description": "Specification that applies to a BigQuery table. This is only valid on entries of type `TABLE`."}, "description": {"description": "Entry description, which can consist of several sentences or paragraphs that describe entry contents. Default value is an empty string.", "type": "string"}, "displayName": {"description": "Display information such as title and description. A short name to identify the entry, for example, \"Analytics Data - Jan 2011\". Default value is an empty string.", "type": "string"}, "gcsFilesetSpec": {"$ref": "GoogleCloudDatacatalogV1beta1GcsFilesetSpec", "description": "Specification that applies to a Cloud Storage fileset. This is only valid on entries of type FILESET."}, "integratedSystem": {"description": "Output only. This field indicates the entry's source system that Data Catalog integrates with, such as BigQuery or Pub/Sub.", "enum": ["INTEGRATED_SYSTEM_UNSPECIFIED", "BIGQUERY", "CLOUD_PUBSUB"], "enumDescriptions": ["Default unknown system.", "Big<PERSON>uery.", "Cloud Pub/Sub."], "readOnly": true, "type": "string"}, "linkedResource": {"description": "The resource this metadata entry refers to. For Google Cloud Platform resources, `linked_resource` is the [full name of the resource](https://cloud.google.com/apis/design/resource_names#full_resource_name). For example, the `linked_resource` for a table resource from BigQuery is: * //bigquery.googleapis.com/projects/projectId/datasets/datasetId/tables/tableId Output only when Entry is of type in the EntryType enum. For entries with user_specified_type, this field is optional and defaults to an empty string.", "type": "string"}, "name": {"description": "Output only. Identifier. The Data Catalog resource name of the entry in URL format. Example: * projects/{project_id}/locations/{location}/entryGroups/{entry_group_id}/entries/{entry_id} Note that this Entry and its child resources may not actually be stored in the location in this name.", "readOnly": true, "type": "string"}, "schema": {"$ref": "GoogleCloudDatacatalogV1beta1Schema", "description": "Schema of the entry. An entry might not have any schema attached to it."}, "sourceSystemTimestamps": {"$ref": "GoogleCloudDatacatalogV1beta1SystemTimestamps", "description": "Output only. Timestamps about the underlying resource, not about this Data Catalog entry. Output only when Entry is of type in the EntryType enum. For entries with user_specified_type, this field is optional and defaults to an empty timestamp.", "readOnly": true}, "type": {"description": "The type of the entry. Only used for Entries with types in the EntryType enum.", "enum": ["ENTRY_TYPE_UNSPECIFIED", "TABLE", "MODEL", "DATA_STREAM", "FILESET"], "enumDescriptions": ["Default unknown type.", "Output only. The type of entry that has a GoogleSQL schema, including logical views.", "Output only. The type of models. https://cloud.google.com/bigquery-ml/docs/bigqueryml-intro", "Output only. An entry type which is used for streaming entries. Example: Pub/Sub topic.", "An entry type which is a set of files or objects. Example: Cloud Storage fileset."], "type": "string"}, "usageSignal": {"$ref": "GoogleCloudDatacatalogV1beta1UsageSignal", "description": "Output only. Statistics on the usage level of the resource.", "readOnly": true}, "userSpecifiedSystem": {"description": "This field indicates the entry's source system that Data Catalog does not integrate with. `user_specified_system` strings must begin with a letter or underscore and can only contain letters, numbers, and underscores; are case insensitive; must be at least 1 character and at most 64 characters long.", "type": "string"}, "userSpecifiedType": {"description": "Entry type if it does not fit any of the input-allowed values listed in `EntryType` enum above. When creating an entry, users should check the enum values first, if nothing matches the entry to be created, then provide a custom value, for example \"my_special_type\". `user_specified_type` strings must begin with a letter or underscore and can only contain letters, numbers, and underscores; are case insensitive; must be at least 1 character and at most 64 characters long. Currently, only FILESET enum value is allowed. All other entries created through Data Catalog must use `user_specified_type`.", "type": "string"}}, "type": "object"}, "GoogleCloudDatacatalogV1beta1EntryGroup": {"description": "EntryGroup Metadata. An EntryGroup resource represents a logical grouping of zero or more Data Catalog Entry resources.", "id": "GoogleCloudDatacatalogV1beta1EntryGroup", "properties": {"dataCatalogTimestamps": {"$ref": "GoogleCloudDatacatalogV1beta1SystemTimestamps", "description": "Output only. Timestamps about this EntryGroup. Default value is empty timestamps.", "readOnly": true}, "description": {"description": "Entry group description, which can consist of several sentences or paragraphs that describe entry group contents. Default value is an empty string.", "type": "string"}, "displayName": {"description": "A short name to identify the entry group, for example, \"analytics data - jan 2011\". Default value is an empty string.", "type": "string"}, "name": {"description": "Identifier. The resource name of the entry group in URL format. Example: * projects/{project_id}/locations/{location}/entryGroups/{entry_group_id} Note that this EntryGroup and its child resources may not actually be stored in the location in this name.", "type": "string"}}, "type": "object"}, "GoogleCloudDatacatalogV1beta1ExportTaxonomiesResponse": {"description": "Response message for ExportTaxonomies.", "id": "GoogleCloudDatacatalogV1beta1ExportTaxonomiesResponse", "properties": {"taxonomies": {"description": "List of taxonomies and policy tags in a tree structure.", "items": {"$ref": "GoogleCloudDatacatalogV1beta1SerializedTaxonomy"}, "type": "array"}}, "type": "object"}, "GoogleCloudDatacatalogV1beta1FieldType": {"id": "GoogleCloudDatacatalogV1beta1FieldType", "properties": {"enumType": {"$ref": "GoogleCloudDatacatalogV1beta1FieldTypeEnumType", "description": "Represents an enum type."}, "primitiveType": {"description": "Represents primitive types - string, bool etc.", "enum": ["PRIMITIVE_TYPE_UNSPECIFIED", "DOUBLE", "STRING", "BOOL", "TIMESTAMP"], "enumDescriptions": ["This is the default invalid value for a type.", "A double precision number.", "An UTF-8 string.", "A boolean value.", "A timestamp."], "type": "string"}}, "type": "object"}, "GoogleCloudDatacatalogV1beta1FieldTypeEnumType": {"id": "GoogleCloudDatacatalogV1beta1FieldTypeEnumType", "properties": {"allowedValues": {"items": {"$ref": "GoogleCloudDatacatalogV1beta1FieldTypeEnumTypeEnumValue"}, "type": "array"}}, "type": "object"}, "GoogleCloudDatacatalogV1beta1FieldTypeEnumTypeEnumValue": {"id": "GoogleCloudDatacatalogV1beta1FieldTypeEnumTypeEnumValue", "properties": {"displayName": {"description": "Required. The display name of the enum value. Must not be an empty string.", "type": "string"}}, "type": "object"}, "GoogleCloudDatacatalogV1beta1GcsFileSpec": {"description": "Specifications of a single file in Cloud Storage.", "id": "GoogleCloudDatacatalogV1beta1GcsFileSpec", "properties": {"filePath": {"description": "Required. The full file path. Example: `gs://bucket_name/a/b.txt`.", "type": "string"}, "gcsTimestamps": {"$ref": "GoogleCloudDatacatalogV1beta1SystemTimestamps", "description": "Output only. Timestamps about the Cloud Storage file.", "readOnly": true}, "sizeBytes": {"description": "Output only. The size of the file, in bytes.", "format": "int64", "readOnly": true, "type": "string"}}, "type": "object"}, "GoogleCloudDatacatalogV1beta1GcsFilesetSpec": {"description": "Describes a Cloud Storage fileset entry.", "id": "GoogleCloudDatacatalogV1beta1GcsFilesetSpec", "properties": {"filePatterns": {"description": "Required. Patterns to identify a set of files in Google Cloud Storage. See [Cloud Storage documentation](https://cloud.google.com/storage/docs/wildcards) for more information. Note that bucket wildcards are currently not supported. Examples of valid file_patterns: * `gs://bucket_name/dir/*`: matches all files within `bucket_name/dir` directory. * `gs://bucket_name/dir/**`: matches all files in `bucket_name/dir` spanning all subdirectories. * `gs://bucket_name/file*`: matches files prefixed by `file` in `bucket_name` * `gs://bucket_name/??.txt`: matches files with two characters followed by `.txt` in `bucket_name` * `gs://bucket_name/[aeiou].txt`: matches files that contain a single vowel character followed by `.txt` in `bucket_name` * `gs://bucket_name/[a-m].txt`: matches files that contain `a`, `b`, ... or `m` followed by `.txt` in `bucket_name` * `gs://bucket_name/a/*/b`: matches all files in `bucket_name` that match `a/*/b` pattern, such as `a/c/b`, `a/d/b` * `gs://another_bucket/a.txt`: matches `gs://another_bucket/a.txt` You can combine wildcards to provide more powerful matches, for example: * `gs://bucket_name/[a-m]??.j*g`", "items": {"type": "string"}, "type": "array"}, "sampleGcsFileSpecs": {"description": "Output only. Sample files contained in this fileset, not all files contained in this fileset are represented here.", "items": {"$ref": "GoogleCloudDatacatalogV1beta1GcsFileSpec"}, "readOnly": true, "type": "array"}}, "type": "object"}, "GoogleCloudDatacatalogV1beta1ImportTaxonomiesRequest": {"description": "Request message for ImportTaxonomies.", "id": "GoogleCloudDatacatalogV1beta1ImportTaxonomiesRequest", "properties": {"inlineSource": {"$ref": "GoogleCloudDatacatalogV1beta1InlineSource", "description": "Inline source used for taxonomies to be imported."}}, "type": "object"}, "GoogleCloudDatacatalogV1beta1ImportTaxonomiesResponse": {"description": "Response message for ImportTaxonomies.", "id": "GoogleCloudDatacatalogV1beta1ImportTaxonomiesResponse", "properties": {"taxonomies": {"description": "Taxonomies that were imported.", "items": {"$ref": "GoogleCloudDatacatalogV1beta1Taxonomy"}, "type": "array"}}, "type": "object"}, "GoogleCloudDatacatalogV1beta1InlineSource": {"description": "Inline source used for taxonomies import.", "id": "GoogleCloudDatacatalogV1beta1InlineSource", "properties": {"taxonomies": {"description": "Required. Taxonomies to be imported.", "items": {"$ref": "GoogleCloudDatacatalogV1beta1SerializedTaxonomy"}, "type": "array"}}, "type": "object"}, "GoogleCloudDatacatalogV1beta1ListEntriesResponse": {"description": "Response message for ListEntries.", "id": "GoogleCloudDatacatalogV1beta1ListEntriesResponse", "properties": {"entries": {"description": "Entry details.", "items": {"$ref": "GoogleCloudDatacatalogV1beta1Entry"}, "type": "array"}, "nextPageToken": {"description": "Token to retrieve the next page of results. It is set to empty if no items remain in results.", "type": "string"}}, "type": "object"}, "GoogleCloudDatacatalogV1beta1ListEntryGroupsResponse": {"description": "Response message for ListEntryGroups.", "id": "GoogleCloudDatacatalogV1beta1ListEntryGroupsResponse", "properties": {"entryGroups": {"description": "EntryGroup details.", "items": {"$ref": "GoogleCloudDatacatalogV1beta1EntryGroup"}, "type": "array"}, "nextPageToken": {"description": "Token to retrieve the next page of results. It is set to empty if no items remain in results.", "type": "string"}}, "type": "object"}, "GoogleCloudDatacatalogV1beta1ListPolicyTagsResponse": {"description": "Response message for ListPolicyTags.", "id": "GoogleCloudDatacatalogV1beta1ListPolicyTagsResponse", "properties": {"nextPageToken": {"description": "<PERSON><PERSON> used to retrieve the next page of results, or empty if there are no more results in the list.", "type": "string"}, "policyTags": {"description": "The policy tags that are in the requested taxonomy.", "items": {"$ref": "GoogleCloudDatacatalogV1beta1PolicyTag"}, "type": "array"}}, "type": "object"}, "GoogleCloudDatacatalogV1beta1ListTagsResponse": {"description": "Response message for ListTags.", "id": "GoogleCloudDatacatalogV1beta1ListTagsResponse", "properties": {"nextPageToken": {"description": "Token to retrieve the next page of results. It is set to empty if no items remain in results.", "type": "string"}, "tags": {"description": "Tag details.", "items": {"$ref": "GoogleCloudDatacatalogV1beta1Tag"}, "type": "array"}}, "type": "object"}, "GoogleCloudDatacatalogV1beta1ListTaxonomiesResponse": {"description": "Response message for ListTaxonomies.", "id": "GoogleCloudDatacatalogV1beta1ListTaxonomiesResponse", "properties": {"nextPageToken": {"description": "<PERSON><PERSON> used to retrieve the next page of results, or empty if there are no more results in the list.", "type": "string"}, "taxonomies": {"description": "Taxonomies that the project contains.", "items": {"$ref": "GoogleCloudDatacatalogV1beta1Taxonomy"}, "type": "array"}}, "type": "object"}, "GoogleCloudDatacatalogV1beta1PolicyTag": {"description": "Denotes one policy tag in a taxonomy (e.g. ssn). Policy Tags can be defined in a hierarchy. For example, consider the following hierarchy: Geolocation -> (LatLong, City, ZipCode). PolicyTag \"Geolocation\" contains three child policy tags: \"LatLong\", \"City\", and \"ZipCode\".", "id": "GoogleCloudDatacatalogV1beta1PolicyTag", "properties": {"childPolicyTags": {"description": "Output only. Resource names of child policy tags of this policy tag.", "items": {"type": "string"}, "readOnly": true, "type": "array"}, "description": {"description": "Description of this policy tag. It must: contain only unicode characters, tabs, newlines, carriage returns and page breaks; and be at most 2000 bytes long when encoded in UTF-8. If not set, defaults to an empty description. If not set, defaults to an empty description.", "type": "string"}, "displayName": {"description": "Required. User defined name of this policy tag. It must: be unique within the parent taxonomy; contain only unicode letters, numbers, underscores, dashes and spaces; not start or end with spaces; and be at most 200 bytes long when encoded in UTF-8.", "type": "string"}, "name": {"description": "Identifier. Resource name of this policy tag, whose format is: \"projects/{project_number}/locations/{location_id}/taxonomies/{taxonomy_id}/policyTags/{id}\".", "type": "string"}, "parentPolicyTag": {"description": "Resource name of this policy tag's parent policy tag (e.g. for the \"LatLong\" policy tag in the example above, this field contains the resource name of the \"Geolocation\" policy tag). If empty, it means this policy tag is a top level policy tag (e.g. this field is empty for the \"Geolocation\" policy tag in the example above). If not set, defaults to an empty string.", "type": "string"}}, "type": "object"}, "GoogleCloudDatacatalogV1beta1RenameTagTemplateFieldEnumValueRequest": {"description": "Request message for RenameTagTemplateFieldEnumValue.", "id": "GoogleCloudDatacatalogV1beta1RenameTagTemplateFieldEnumValueRequest", "properties": {"newEnumValueDisplayName": {"description": "Required. The new display name of the enum value. For example, `my_new_enum_value`.", "type": "string"}}, "type": "object"}, "GoogleCloudDatacatalogV1beta1RenameTagTemplateFieldRequest": {"description": "Request message for RenameTagTemplateField.", "id": "GoogleCloudDatacatalogV1beta1RenameTagTemplateFieldRequest", "properties": {"newTagTemplateFieldId": {"description": "Required. The new ID of this tag template field. For example, `my_new_field`.", "type": "string"}}, "type": "object"}, "GoogleCloudDatacatalogV1beta1Schema": {"description": "Represents a schema (e.g. BigQuery, GoogleSQL, Avro schema).", "id": "GoogleCloudDatacatalogV1beta1Schema", "properties": {"columns": {"description": "Required. Schema of columns. A maximum of 10,000 columns and sub-columns can be specified.", "items": {"$ref": "GoogleCloudDatacatalogV1beta1ColumnSchema"}, "type": "array"}}, "type": "object"}, "GoogleCloudDatacatalogV1beta1SearchCatalogRequest": {"description": "Request message for SearchCatalog.", "id": "GoogleCloudDatacatalogV1beta1SearchCatalogRequest", "properties": {"orderBy": {"description": "Specifies the ordering of results, currently supported case-sensitive choices are: * `relevance`, only supports descending * `last_modified_timestamp [asc|desc]`, defaults to descending if not specified * `default` that can only be descending If not specified, defaults to `relevance` descending.", "type": "string"}, "pageSize": {"description": "Number of results in the search page. If <=0 then defaults to 10. Max limit for page_size is 1000. Throws an invalid argument for page_size > 1000.", "format": "int32", "type": "integer"}, "pageToken": {"description": "Optional. Pagination token returned in an earlier SearchCatalogResponse.next_page_token, which indicates that this is a continuation of a prior SearchCatalogRequest call, and that the system should return the next page of data. If empty, the first page is returned.", "type": "string"}, "query": {"description": "Optional. The query string in search query syntax. An empty query string will result in all data assets (in the specified scope) that the user has access to. Query strings can be simple as \"x\" or more qualified as: * name:x * column:x * description:y Note: Query tokens need to have a minimum of 3 characters for substring matching to work correctly. See [Data Catalog Search Syntax](https://cloud.google.com/data-catalog/docs/how-to/search-reference) for more information.", "type": "string"}, "scope": {"$ref": "GoogleCloudDatacatalogV1beta1SearchCatalogRequestScope", "description": "Required. The scope of this search request. A `scope` that has empty `include_org_ids`, `include_project_ids` AND false `include_gcp_public_datasets` is considered invalid. Data Catalog will return an error in such a case."}}, "type": "object"}, "GoogleCloudDatacatalogV1beta1SearchCatalogRequestScope": {"description": "The criteria that select the subspace used for query matching.", "id": "GoogleCloudDatacatalogV1beta1SearchCatalogRequestScope", "properties": {"includeGcpPublicDatasets": {"description": "If `true`, include Google Cloud public datasets in the search results. Info on Google Cloud public datasets is available at https://cloud.google.com/public-datasets/. By default, Google Cloud public datasets are excluded.", "type": "boolean"}, "includeOrgIds": {"description": "The list of organization IDs to search within. To find your organization ID, follow instructions in https://cloud.google.com/resource-manager/docs/creating-managing-organization.", "items": {"type": "string"}, "type": "array"}, "includeProjectIds": {"description": "The list of project IDs to search within. To learn more about the distinction between project names/IDs/numbers, go to https://cloud.google.com/docs/overview/#projects.", "items": {"type": "string"}, "type": "array"}, "restrictedLocations": {"description": "Optional. The list of locations to search within. 1. If empty, search will be performed in all locations; 2. If any of the locations are NOT in the valid locations list, error will be returned; 3. Otherwise, search only the given locations for matching results. Typical usage is to leave this field empty. When a location is unreachable as returned in the `SearchCatalogResponse.unreachable` field, users can repeat the search request with this parameter set to get additional information on the error. Valid locations: * asia-east1 * asia-east2 * asia-northeast1 * asia-northeast2 * asia-northeast3 * asia-south1 * asia-southeast1 * australia-southeast1 * eu * europe-north1 * europe-west1 * europe-west2 * europe-west3 * europe-west4 * europe-west6 * global * northamerica-northeast1 * southamerica-east1 * us * us-central1 * us-east1 * us-east4 * us-west1 * us-west2", "items": {"type": "string"}, "type": "array"}}, "type": "object"}, "GoogleCloudDatacatalogV1beta1SearchCatalogResponse": {"description": "Response message for SearchCatalog.", "id": "GoogleCloudDatacatalogV1beta1SearchCatalogResponse", "properties": {"nextPageToken": {"description": "The token that can be used to retrieve the next page of results.", "type": "string"}, "results": {"description": "Search results.", "items": {"$ref": "GoogleCloudDatacatalogV1beta1SearchCatalogResult"}, "type": "array"}, "totalSize": {"description": "The approximate total number of entries matched by the query.", "format": "int32", "type": "integer"}, "unreachable": {"description": "Unreachable locations. Search result does not include data from those locations. Users can get additional information on the error by repeating the search request with a more restrictive parameter -- setting the value for `SearchDataCatalogRequest.scope.restricted_locations`.", "items": {"type": "string"}, "type": "array"}}, "type": "object"}, "GoogleCloudDatacatalogV1beta1SearchCatalogResult": {"description": "A result that appears in the response of a search request. Each result captures details of one entry that matches the search.", "id": "GoogleCloudDatacatalogV1beta1SearchCatalogResult", "properties": {"linkedResource": {"description": "The full name of the cloud resource the entry belongs to. See: https://cloud.google.com/apis/design/resource_names#full_resource_name. Example: * `//bigquery.googleapis.com/projects/projectId/datasets/datasetId/tables/tableId`", "type": "string"}, "modifyTime": {"description": "Last-modified timestamp of the entry from the managing system.", "format": "google-datetime", "type": "string"}, "relativeResourceName": {"description": "The relative resource name of the resource in URL format. Examples: * `projects/{project_id}/locations/{location_id}/entryGroups/{entry_group_id}/entries/{entry_id}` * `projects/{project_id}/tagTemplates/{tag_template_id}`", "type": "string"}, "searchResultSubtype": {"description": "Sub-type of the search result. This is a dot-delimited description of the resource's full type, and is the same as the value callers would provide in the \"type\" search facet. Examples: `entry.table`, `entry.dataStream`, `tagTemplate`.", "type": "string"}, "searchResultType": {"description": "Type of the search result. This field can be used to determine which Get method to call to fetch the full resource.", "enum": ["SEARCH_RESULT_TYPE_UNSPECIFIED", "ENTRY", "TAG_TEMPLATE", "ENTRY_GROUP"], "enumDescriptions": ["Default unknown type.", "An Entry.", "A TagTemplate.", "An EntryGroup."], "type": "string"}}, "type": "object"}, "GoogleCloudDatacatalogV1beta1SerializedPolicyTag": {"description": "Message representing one policy tag when exported as a nested proto.", "id": "GoogleCloudDatacatalogV1beta1SerializedPolicyTag", "properties": {"childPolicyTags": {"description": "Children of the policy tag if any.", "items": {"$ref": "GoogleCloudDatacatalogV1beta1SerializedPolicyTag"}, "type": "array"}, "description": {"description": "Description of the serialized policy tag. The length of the description is limited to 2000 bytes when encoded in UTF-8. If not set, defaults to an empty description.", "type": "string"}, "displayName": {"description": "Required. Display name of the policy tag. Max 200 bytes when encoded in UTF-8.", "type": "string"}, "policyTag": {"description": "Resource name of the policy tag. This field will be ignored when calling ImportTaxonomies.", "type": "string"}}, "type": "object"}, "GoogleCloudDatacatalogV1beta1SerializedTaxonomy": {"description": "Message capturing a taxonomy and its policy tag hierarchy as a nested proto. Used for taxonomy import/export and mutation.", "id": "GoogleCloudDatacatalogV1beta1SerializedTaxonomy", "properties": {"activatedPolicyTypes": {"description": "A list of policy types that are activated for a taxonomy.", "items": {"enum": ["POLICY_TYPE_UNSPECIFIED", "FINE_GRAINED_ACCESS_CONTROL"], "enumDescriptions": ["Unspecified policy type.", "Fine grained access control policy, which enables access control on tagged resources."], "type": "string"}, "type": "array"}, "description": {"description": "Description of the serialized taxonomy. The length of the description is limited to 2000 bytes when encoded in UTF-8. If not set, defaults to an empty description.", "type": "string"}, "displayName": {"description": "Required. Display name of the taxonomy. Max 200 bytes when encoded in UTF-8.", "type": "string"}, "policyTags": {"description": "Top level policy tags associated with the taxonomy if any.", "items": {"$ref": "GoogleCloudDatacatalogV1beta1SerializedPolicyTag"}, "type": "array"}}, "type": "object"}, "GoogleCloudDatacatalogV1beta1SystemTimestamps": {"description": "Timestamps about this resource according to a particular system.", "id": "GoogleCloudDatacatalogV1beta1SystemTimestamps", "properties": {"createTime": {"description": "The creation time of the resource within the given system.", "format": "google-datetime", "type": "string"}, "expireTime": {"description": "Output only. The expiration time of the resource within the given system. Currently only apllicable to BigQuery resources.", "format": "google-datetime", "readOnly": true, "type": "string"}, "updateTime": {"description": "The last-modified time of the resource within the given system.", "format": "google-datetime", "type": "string"}}, "type": "object"}, "GoogleCloudDatacatalogV1beta1TableSpec": {"description": "Normal BigQuery table spec.", "id": "GoogleCloudDatacatalogV1beta1TableSpec", "properties": {"groupedEntry": {"description": "Output only. If the table is a dated shard, i.e., with name pattern `[prefix]YYYYMMDD`, `grouped_entry` is the Data Catalog resource name of the date sharded grouped entry, for example, `projects/{project_id}/locations/{location}/entrygroups/{entry_group_id}/entries/{entry_id}`. Otherwise, `grouped_entry` is empty.", "readOnly": true, "type": "string"}}, "type": "object"}, "GoogleCloudDatacatalogV1beta1Tag": {"description": "Tags are used to attach custom metadata to Data Catalog resources. Tags conform to the specifications within their tag template. See [Data Catalog IAM](https://cloud.google.com/data-catalog/docs/concepts/iam) for information on the permissions needed to create or view tags.", "id": "GoogleCloudDatacatalogV1beta1Tag", "properties": {"column": {"description": "Resources like Entry can have schemas associated with them. This scope allows users to attach tags to an individual column based on that schema. For attaching a tag to a nested column, use `.` to separate the column names. Example: * `outer_column.inner_column`", "type": "string"}, "fields": {"additionalProperties": {"$ref": "GoogleCloudDatacatalogV1beta1TagField"}, "description": "Required. This maps the ID of a tag field to the value of and additional information about that field. Valid field IDs are defined by the tag's template. A tag must have at least 1 field and at most 500 fields.", "type": "object"}, "name": {"description": "Identifier. The resource name of the tag in URL format. Example: * projects/{project_id}/locations/{location}/entrygroups/{entry_group_id}/entries/{entry_id}/tags/{tag_id} where `tag_id` is a system-generated identifier. Note that this Tag may not actually be stored in the location in this name.", "type": "string"}, "template": {"description": "Required. The resource name of the tag template that this tag uses. Example: * projects/{project_id}/locations/{location}/tagTemplates/{tag_template_id} This field cannot be modified after creation.", "type": "string"}, "templateDisplayName": {"description": "Output only. The display name of the tag template.", "readOnly": true, "type": "string"}}, "type": "object"}, "GoogleCloudDatacatalogV1beta1TagField": {"description": "Contains the value and supporting information for a field within a Tag.", "id": "GoogleCloudDatacatalogV1beta1TagField", "properties": {"boolValue": {"description": "Holds the value for a tag field with boolean type.", "type": "boolean"}, "displayName": {"description": "Output only. The display name of this field.", "readOnly": true, "type": "string"}, "doubleValue": {"description": "Holds the value for a tag field with double type.", "format": "double", "type": "number"}, "enumValue": {"$ref": "GoogleCloudDatacatalogV1beta1TagFieldEnumValue", "description": "Holds the value for a tag field with enum type. This value must be one of the allowed values in the definition of this enum."}, "order": {"description": "Output only. The order of this field with respect to other fields in this tag. It can be set in Tag. For example, a higher value can indicate a more important field. The value can be negative. Multiple fields can have the same order, and field orders within a tag do not have to be sequential.", "format": "int32", "readOnly": true, "type": "integer"}, "stringValue": {"description": "Holds the value for a tag field with string type.", "type": "string"}, "timestampValue": {"description": "Holds the value for a tag field with timestamp type.", "format": "google-datetime", "type": "string"}}, "type": "object"}, "GoogleCloudDatacatalogV1beta1TagFieldEnumValue": {"description": "Holds an enum value.", "id": "GoogleCloudDatacatalogV1beta1TagFieldEnumValue", "properties": {"displayName": {"description": "The display name of the enum value.", "type": "string"}}, "type": "object"}, "GoogleCloudDatacatalogV1beta1TagTemplate": {"description": "A tag template defines a tag, which can have one or more typed fields. The template is used to create and attach the tag to Google Cloud resources. [Tag template roles](https://cloud.google.com/iam/docs/understanding-roles#data-catalog-roles) provide permissions to create, edit, and use the template. See, for example, the [TagTemplate User](https://cloud.google.com/data-catalog/docs/how-to/template-user) role, which includes permission to use the tag template to tag resources.", "id": "GoogleCloudDatacatalogV1beta1TagTemplate", "properties": {"dataplexTransferStatus": {"description": "Output only. Transfer status of the TagTemplate", "enum": ["DATAPLEX_TRANSFER_STATUS_UNSPECIFIED", "MIGRATED"], "enumDeprecated": [false, true], "enumDescriptions": ["Default value. TagTemplate and its tags are only visible and editable in DataCatalog.", "TagTemplate and its tags are auto-copied to Dataplex Universal Catalog service. Visible in both services. Editable in Data Catalog, read-only in Dataplex Universal Catalog. Deprecated: Individual TagTemplate migration is deprecated in favor of organization or project wide TagTemplate migration opt-in."], "readOnly": true, "type": "string"}, "displayName": {"description": "The display name for this template. Defaults to an empty string.", "type": "string"}, "fields": {"additionalProperties": {"$ref": "GoogleCloudDatacatalogV1beta1TagTemplateField"}, "description": "Required. Map of tag template field IDs to the settings for the field. This map is an exhaustive list of the allowed fields. This map must contain at least one field and at most 500 fields. The keys to this map are tag template field IDs. Field IDs can contain letters (both uppercase and lowercase), numbers (0-9) and underscores (_). Field IDs must be at least 1 character long and at most 64 characters long. Field IDs must start with a letter or underscore.", "type": "object"}, "name": {"description": "Identifier. The resource name of the tag template in URL format. Example: * projects/{project_id}/locations/{location}/tagTemplates/{tag_template_id} Note that this TagTemplate and its child resources may not actually be stored in the location in this name.", "type": "string"}}, "type": "object"}, "GoogleCloudDatacatalogV1beta1TagTemplateField": {"description": "The template for an individual field within a tag template.", "id": "GoogleCloudDatacatalogV1beta1TagTemplateField", "properties": {"description": {"description": "The description for this field. Defaults to an empty string.", "type": "string"}, "displayName": {"description": "The display name for this field. Defaults to an empty string.", "type": "string"}, "isRequired": {"description": "Whether this is a required field. Defaults to false.", "type": "boolean"}, "name": {"description": "Output only. Identifier. The resource name of the tag template field in URL format. Example: * projects/{project_id}/locations/{location}/tagTemplates/{tag_template}/fields/{field} Note that this TagTemplateField may not actually be stored in the location in this name.", "readOnly": true, "type": "string"}, "order": {"description": "The order of this field with respect to other fields in this tag template. A higher value indicates a more important field. The value can be negative. Multiple fields can have the same order, and field orders within a tag do not have to be sequential.", "format": "int32", "type": "integer"}, "type": {"$ref": "GoogleCloudDatacatalogV1beta1FieldType", "description": "Required. The type of value this tag field can contain."}}, "type": "object"}, "GoogleCloudDatacatalogV1beta1Taxonomy": {"description": "A taxonomy is a collection of policy tags that classify data along a common axis. For instance a data *sensitivity* taxonomy could contain policy tags denoting PII such as age, zipcode, and SSN. A data *origin* taxonomy could contain policy tags to distinguish user data, employee data, partner data, public data.", "id": "GoogleCloudDatacatalogV1beta1Taxonomy", "properties": {"activatedPolicyTypes": {"description": "Optional. A list of policy types that are activated for this taxonomy. If not set, defaults to an empty list.", "items": {"enum": ["POLICY_TYPE_UNSPECIFIED", "FINE_GRAINED_ACCESS_CONTROL"], "enumDescriptions": ["Unspecified policy type.", "Fine grained access control policy, which enables access control on tagged resources."], "type": "string"}, "type": "array"}, "description": {"description": "Optional. Description of this taxonomy. It must: contain only unicode characters, tabs, newlines, carriage returns and page breaks; and be at most 2000 bytes long when encoded in UTF-8. If not set, defaults to an empty description.", "type": "string"}, "displayName": {"description": "Required. User defined name of this taxonomy. It must: contain only unicode letters, numbers, underscores, dashes and spaces; not start or end with spaces; and be at most 200 bytes long when encoded in UTF-8. The taxonomy display name must be unique within an organization.", "type": "string"}, "name": {"description": "Identifier. Resource name of this taxonomy, whose format is: \"projects/{project_number}/locations/{location_id}/taxonomies/{id}\".", "type": "string"}, "policyTagCount": {"description": "Output only. Number of policy tags contained in this taxonomy.", "format": "int32", "readOnly": true, "type": "integer"}, "service": {"$ref": "GoogleCloudDatacatalogV1beta1TaxonomyService", "description": "Output only. Identity of the service which owns the Taxonomy. This field is only populated when the taxonomy is created by a Google Cloud service. Currently only 'DATAPLEX' is supported.", "readOnly": true}, "taxonomyTimestamps": {"$ref": "GoogleCloudDatacatalogV1beta1SystemTimestamps", "description": "Output only. Timestamps about this taxonomy. Only create_time and update_time are used.", "readOnly": true}}, "type": "object"}, "GoogleCloudDatacatalogV1beta1TaxonomyService": {"description": "The source system of the Taxonomy.", "id": "GoogleCloudDatacatalogV1beta1TaxonomyService", "properties": {"identity": {"description": "The service agent for the service.", "type": "string"}, "name": {"description": "The Google Cloud service name.", "enum": ["MANAGING_SYSTEM_UNSPECIFIED", "MANAGING_SYSTEM_DATAPLEX", "MANAGING_SYSTEM_OTHER"], "enumDescriptions": ["Default value", "Dataplex Universal Catalog.", "Other"], "type": "string"}}, "type": "object"}, "GoogleCloudDatacatalogV1beta1UsageSignal": {"description": "The set of all usage signals that we store in Data Catalog.", "id": "GoogleCloudDatacatalogV1beta1UsageSignal", "properties": {"updateTime": {"description": "The timestamp of the end of the usage statistics duration.", "format": "google-datetime", "type": "string"}, "usageWithinTimeRange": {"additionalProperties": {"$ref": "GoogleCloudDatacatalogV1beta1UsageStats"}, "description": "Usage statistics over each of the pre-defined time ranges, supported strings for time ranges are {\"24H\", \"7D\", \"30D\"}.", "type": "object"}}, "type": "object"}, "GoogleCloudDatacatalogV1beta1UsageStats": {"description": "Detailed counts on the entry's usage. Caveats: - Only BigQuery tables have usage stats - The usage stats only include BigQuery query jobs - The usage stats might be underestimated, e.g. wildcard table references are not yet counted in usage computation https://cloud.google.com/bigquery/docs/querying-wildcard-tables", "id": "GoogleCloudDatacatalogV1beta1UsageStats", "properties": {"totalCancellations": {"description": "The number of times that the underlying entry was attempted to be used but was cancelled by the user.", "format": "float", "type": "number"}, "totalCompletions": {"description": "The number of times that the underlying entry was successfully used.", "format": "float", "type": "number"}, "totalExecutionTimeForCompletionsMillis": {"description": "Total time spent (in milliseconds) during uses the resulted in completions.", "format": "float", "type": "number"}, "totalFailures": {"description": "The number of times that the underlying entry was attempted to be used but failed.", "format": "float", "type": "number"}}, "type": "object"}, "GoogleCloudDatacatalogV1beta1ViewSpec": {"description": "Table view specification.", "id": "GoogleCloudDatacatalogV1beta1ViewSpec", "properties": {"viewQuery": {"description": "Output only. The query that defines the table view.", "readOnly": true, "type": "string"}}, "type": "object"}, "Policy": {"description": "An Identity and Access Management (IAM) policy, which specifies access controls for Google Cloud resources. A `Policy` is a collection of `bindings`. A `binding` binds one or more `members`, or principals, to a single `role`. Principals can be user accounts, service accounts, Google groups, and domains (such as G Suite). A `role` is a named list of permissions; each `role` can be an IAM predefined role or a user-created custom role. For some types of Google Cloud resources, a `binding` can also specify a `condition`, which is a logical expression that allows access to a resource only if the expression evaluates to `true`. A condition can add constraints based on attributes of the request, the resource, or both. To learn which resources support conditions in their IAM policies, see the [IAM documentation](https://cloud.google.com/iam/help/conditions/resource-policies). **JSON example:** ``` { \"bindings\": [ { \"role\": \"roles/resourcemanager.organizationAdmin\", \"members\": [ \"user:<EMAIL>\", \"group:<EMAIL>\", \"domain:google.com\", \"serviceAccount:<EMAIL>\" ] }, { \"role\": \"roles/resourcemanager.organizationViewer\", \"members\": [ \"user:<EMAIL>\" ], \"condition\": { \"title\": \"expirable access\", \"description\": \"Does not grant access after Sep 2020\", \"expression\": \"request.time < timestamp('2020-10-01T00:00:00.000Z')\", } } ], \"etag\": \"BwWWja0YfJA=\", \"version\": 3 } ``` **YAML example:** ``` bindings: - members: - user:<EMAIL> - group:<EMAIL> - domain:google.com - serviceAccount:<EMAIL> role: roles/resourcemanager.organizationAdmin - members: - user:<EMAIL> role: roles/resourcemanager.organizationViewer condition: title: expirable access description: Does not grant access after Sep 2020 expression: request.time < timestamp('2020-10-01T00:00:00.000Z') etag: BwWWja0YfJA= version: 3 ``` For a description of IAM and its features, see the [IAM documentation](https://cloud.google.com/iam/docs/).", "id": "Policy", "properties": {"bindings": {"description": "Associates a list of `members`, or principals, with a `role`. Optionally, may specify a `condition` that determines how and when the `bindings` are applied. Each of the `bindings` must contain at least one principal. The `bindings` in a `Policy` can refer to up to 1,500 principals; up to 250 of these principals can be Google groups. Each occurrence of a principal counts towards these limits. For example, if the `bindings` grant 50 different roles to `user:<EMAIL>`, and not to any other principal, then you can add another 1,450 principals to the `bindings` in the `Policy`.", "items": {"$ref": "Binding"}, "type": "array"}, "etag": {"description": "`etag` is used for optimistic concurrency control as a way to help prevent simultaneous updates of a policy from overwriting each other. It is strongly suggested that systems make use of the `etag` in the read-modify-write cycle to perform policy updates in order to avoid race conditions: An `etag` is returned in the response to `getIamPolicy`, and systems are expected to put that etag in the request to `setIamPolicy` to ensure that their change will be applied to the same version of the policy. **Important:** If you use IAM Conditions, you must include the `etag` field whenever you call `setIamPolicy`. If you omit this field, then IAM allows you to overwrite a version `3` policy with a version `1` policy, and all of the conditions in the version `3` policy are lost.", "format": "byte", "type": "string"}, "version": {"description": "Specifies the format of the policy. Valid values are `0`, `1`, and `3`. Requests that specify an invalid value are rejected. Any operation that affects conditional role bindings must specify version `3`. This requirement applies to the following operations: * Getting a policy that includes a conditional role binding * Adding a conditional role binding to a policy * Changing a conditional role binding in a policy * Removing any role binding, with or without a condition, from a policy that includes conditions **Important:** If you use IAM Conditions, you must include the `etag` field whenever you call `setIamPolicy`. If you omit this field, then IAM allows you to overwrite a version `3` policy with a version `1` policy, and all of the conditions in the version `3` policy are lost. If a policy does not include any conditions, operations on that policy may specify any valid version or leave the field unset. To learn which resources support conditions in their IAM policies, see the [IAM documentation](https://cloud.google.com/iam/help/conditions/resource-policies).", "format": "int32", "type": "integer"}}, "type": "object"}, "SetIamPolicyRequest": {"description": "Request message for `SetIamPolicy` method.", "id": "SetIamPolicyRequest", "properties": {"policy": {"$ref": "Policy", "description": "REQUIRED: The complete policy to be applied to the `resource`. The size of the policy is limited to a few 10s of KB. An empty policy is a valid policy but certain Google Cloud services (such as Projects) might reject them."}}, "type": "object"}, "Status": {"description": "The `Status` type defines a logical error model that is suitable for different programming environments, including REST APIs and RPC APIs. It is used by [gRPC](https://github.com/grpc). Each `Status` message contains three pieces of data: error code, error message, and error details. You can find out more about this error model and how to work with it in the [API Design Guide](https://cloud.google.com/apis/design/errors).", "id": "Status", "properties": {"code": {"description": "The status code, which should be an enum value of google.rpc.Code.", "format": "int32", "type": "integer"}, "details": {"description": "A list of messages that carry the error details. There is a common set of message types for APIs to use.", "items": {"additionalProperties": {"description": "Properties of the object. Contains field @type with type URL.", "type": "any"}, "type": "object"}, "type": "array"}, "message": {"description": "A developer-facing error message, which should be in English. Any user-facing error message should be localized and sent in the google.rpc.Status.details field, or localized by the client.", "type": "string"}}, "type": "object"}, "TestIamPermissionsRequest": {"description": "Request message for `TestIamPermissions` method.", "id": "TestIamPermissionsRequest", "properties": {"permissions": {"description": "The set of permissions to check for the `resource`. Permissions with wildcards (such as `*` or `storage.*`) are not allowed. For more information see [IAM Overview](https://cloud.google.com/iam/docs/overview#permissions).", "items": {"type": "string"}, "type": "array"}}, "type": "object"}, "TestIamPermissionsResponse": {"description": "Response message for `TestIamPermissions` method.", "id": "TestIamPermissionsResponse", "properties": {"permissions": {"description": "A subset of `TestPermissionsRequest.permissions` that the caller is allowed.", "items": {"type": "string"}, "type": "array"}}, "type": "object"}}, "servicePath": "", "title": "Google Cloud Data Catalog API", "version": "v1beta1", "version_module": true}