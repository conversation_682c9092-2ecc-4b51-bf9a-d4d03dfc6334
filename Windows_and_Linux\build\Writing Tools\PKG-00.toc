('c:\\Users\\<USER>\\Desktop\\Mes_projets\\WritingToolsClean\\Windows_and_Linux\\build\\Writing '
 'Tools\\Writing Tools.pkg',
 {'BINARY': True,
  'DATA': True,
  'EXECUTABLE': True,
  'EXTENSION': True,
  'PYMODULE': True,
  'PYSOURCE': True,
  'PYZ': False,
  'SPLASH': True,
  'SYMLINK': False},
 [('pyi-contents-directory _internal', '', 'OPTION'),
  ('PYZ-00.pyz',
   'c:\\Users\\<USER>\\Desktop\\Mes_projets\\WritingToolsClean\\Windows_and_Linux\\build\\Writing '
   'Tools\\PYZ-00.pyz',
   'PYZ'),
  ('struct',
   'c:\\Users\\<USER>\\Desktop\\Mes_projets\\WritingToolsClean\\Windows_and_Linux\\build\\Writing '
   'Tools\\localpycs\\struct.pyc',
   'PYMODULE'),
  ('pyimod01_archive',
   'c:\\Users\\<USER>\\Desktop\\Mes_projets\\WritingToolsClean\\Windows_and_Linux\\build\\Writing '
   'Tools\\localpycs\\pyimod01_archive.pyc',
   'PYMODULE'),
  ('pyimod02_importers',
   'c:\\Users\\<USER>\\Desktop\\Mes_projets\\WritingToolsClean\\Windows_and_Linux\\build\\Writing '
   'Tools\\localpycs\\pyimod02_importers.pyc',
   'PYMODULE'),
  ('pyimod03_ctypes',
   'c:\\Users\\<USER>\\Desktop\\Mes_projets\\WritingToolsClean\\Windows_and_Linux\\build\\Writing '
   'Tools\\localpycs\\pyimod03_ctypes.pyc',
   'PYMODULE'),
  ('pyimod04_pywin32',
   'c:\\Users\\<USER>\\Desktop\\Mes_projets\\WritingToolsClean\\Windows_and_Linux\\build\\Writing '
   'Tools\\localpycs\\pyimod04_pywin32.pyc',
   'PYMODULE'),
  ('pyiboot01_bootstrap',
   'c:\\Users\\<USER>\\Desktop\\Mes_projets\\WritingToolsClean\\Windows_and_Linux\\myvenv\\Lib\\site-packages\\PyInstaller\\loader\\pyiboot01_bootstrap.py',
   'PYSOURCE'),
  ('pyi_rth_inspect',
   'C:\\Users\\<USER>\\Desktop\\Mes_projets\\WritingToolsClean\\Windows_and_Linux\\myvenv\\Lib\\site-packages\\PyInstaller\\hooks\\rthooks\\pyi_rth_inspect.py',
   'PYSOURCE'),
  ('pyi_rth_setuptools',
   'C:\\Users\\<USER>\\Desktop\\Mes_projets\\WritingToolsClean\\Windows_and_Linux\\myvenv\\Lib\\site-packages\\PyInstaller\\hooks\\rthooks\\pyi_rth_setuptools.py',
   'PYSOURCE'),
  ('pyi_rth_pkgutil',
   'C:\\Users\\<USER>\\Desktop\\Mes_projets\\WritingToolsClean\\Windows_and_Linux\\myvenv\\Lib\\site-packages\\PyInstaller\\hooks\\rthooks\\pyi_rth_pkgutil.py',
   'PYSOURCE'),
  ('pyi_rth_multiprocessing',
   'C:\\Users\\<USER>\\Desktop\\Mes_projets\\WritingToolsClean\\Windows_and_Linux\\myvenv\\Lib\\site-packages\\PyInstaller\\hooks\\rthooks\\pyi_rth_multiprocessing.py',
   'PYSOURCE'),
  ('pyi_rth_pyside6',
   'C:\\Users\\<USER>\\Desktop\\Mes_projets\\WritingToolsClean\\Windows_and_Linux\\myvenv\\Lib\\site-packages\\PyInstaller\\hooks\\rthooks\\pyi_rth_pyside6.py',
   'PYSOURCE'),
  ('main',
   'C:\\Users\\<USER>\\Desktop\\Mes_projets\\WritingToolsClean\\Windows_and_Linux\\main.py',
   'PYSOURCE'),
  ('python312.dll',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\python312.dll',
   'BINARY'),
  ('PySide6\\plugins\\styles\\qmodernwindowsstyle.dll',
   'C:\\Users\\<USER>\\Desktop\\Mes_projets\\WritingToolsClean\\Windows_and_Linux\\myvenv\\Lib\\site-packages\\PySide6\\plugins\\styles\\qmodernwindowsstyle.dll',
   'BINARY'),
  ('PySide6\\plugins\\imageformats\\qico.dll',
   'C:\\Users\\<USER>\\Desktop\\Mes_projets\\WritingToolsClean\\Windows_and_Linux\\myvenv\\Lib\\site-packages\\PySide6\\plugins\\imageformats\\qico.dll',
   'BINARY'),
  ('PySide6\\plugins\\imageformats\\qjpeg.dll',
   'C:\\Users\\<USER>\\Desktop\\Mes_projets\\WritingToolsClean\\Windows_and_Linux\\myvenv\\Lib\\site-packages\\PySide6\\plugins\\imageformats\\qjpeg.dll',
   'BINARY'),
  ('PySide6\\plugins\\iconengines\\qsvgicon.dll',
   'C:\\Users\\<USER>\\Desktop\\Mes_projets\\WritingToolsClean\\Windows_and_Linux\\myvenv\\Lib\\site-packages\\PySide6\\plugins\\iconengines\\qsvgicon.dll',
   'BINARY'),
  ('PySide6\\plugins\\platforms\\qwindows.dll',
   'C:\\Users\\<USER>\\Desktop\\Mes_projets\\WritingToolsClean\\Windows_and_Linux\\myvenv\\Lib\\site-packages\\PySide6\\plugins\\platforms\\qwindows.dll',
   'BINARY'),
  ('PySide6\\plugins\\imageformats\\qsvg.dll',
   'C:\\Users\\<USER>\\Desktop\\Mes_projets\\WritingToolsClean\\Windows_and_Linux\\myvenv\\Lib\\site-packages\\PySide6\\plugins\\imageformats\\qsvg.dll',
   'BINARY'),
  ('PySide6\\plugins\\generic\\qtuiotouchplugin.dll',
   'C:\\Users\\<USER>\\Desktop\\Mes_projets\\WritingToolsClean\\Windows_and_Linux\\myvenv\\Lib\\site-packages\\PySide6\\plugins\\generic\\qtuiotouchplugin.dll',
   'BINARY'),
  ('PySide6\\plugins\\imageformats\\qgif.dll',
   'C:\\Users\\<USER>\\Desktop\\Mes_projets\\WritingToolsClean\\Windows_and_Linux\\myvenv\\Lib\\site-packages\\PySide6\\plugins\\imageformats\\qgif.dll',
   'BINARY'),
  ('PySide6\\plugins\\imageformats\\qwbmp.dll',
   'C:\\Users\\<USER>\\Desktop\\Mes_projets\\WritingToolsClean\\Windows_and_Linux\\myvenv\\Lib\\site-packages\\PySide6\\plugins\\imageformats\\qwbmp.dll',
   'BINARY'),
  ('PySide6\\plugins\\imageformats\\qtga.dll',
   'C:\\Users\\<USER>\\Desktop\\Mes_projets\\WritingToolsClean\\Windows_and_Linux\\myvenv\\Lib\\site-packages\\PySide6\\plugins\\imageformats\\qtga.dll',
   'BINARY'),
  ('PySide6\\plugins\\imageformats\\qpdf.dll',
   'C:\\Users\\<USER>\\Desktop\\Mes_projets\\WritingToolsClean\\Windows_and_Linux\\myvenv\\Lib\\site-packages\\PySide6\\plugins\\imageformats\\qpdf.dll',
   'BINARY'),
  ('PySide6\\plugins\\imageformats\\qicns.dll',
   'C:\\Users\\<USER>\\Desktop\\Mes_projets\\WritingToolsClean\\Windows_and_Linux\\myvenv\\Lib\\site-packages\\PySide6\\plugins\\imageformats\\qicns.dll',
   'BINARY'),
  ('PySide6\\plugins\\platforms\\qoffscreen.dll',
   'C:\\Users\\<USER>\\Desktop\\Mes_projets\\WritingToolsClean\\Windows_and_Linux\\myvenv\\Lib\\site-packages\\PySide6\\plugins\\platforms\\qoffscreen.dll',
   'BINARY'),
  ('PySide6\\plugins\\platforms\\qminimal.dll',
   'C:\\Users\\<USER>\\Desktop\\Mes_projets\\WritingToolsClean\\Windows_and_Linux\\myvenv\\Lib\\site-packages\\PySide6\\plugins\\platforms\\qminimal.dll',
   'BINARY'),
  ('PySide6\\plugins\\imageformats\\qtiff.dll',
   'C:\\Users\\<USER>\\Desktop\\Mes_projets\\WritingToolsClean\\Windows_and_Linux\\myvenv\\Lib\\site-packages\\PySide6\\plugins\\imageformats\\qtiff.dll',
   'BINARY'),
  ('PySide6\\plugins\\imageformats\\qwebp.dll',
   'C:\\Users\\<USER>\\Desktop\\Mes_projets\\WritingToolsClean\\Windows_and_Linux\\myvenv\\Lib\\site-packages\\PySide6\\plugins\\imageformats\\qwebp.dll',
   'BINARY'),
  ('PySide6\\plugins\\platforms\\qdirect2d.dll',
   'C:\\Users\\<USER>\\Desktop\\Mes_projets\\WritingToolsClean\\Windows_and_Linux\\myvenv\\Lib\\site-packages\\PySide6\\plugins\\platforms\\qdirect2d.dll',
   'BINARY'),
  ('PySide6\\plugins\\platforminputcontexts\\qtvirtualkeyboardplugin.dll',
   'C:\\Users\\<USER>\\Desktop\\Mes_projets\\WritingToolsClean\\Windows_and_Linux\\myvenv\\Lib\\site-packages\\PySide6\\plugins\\platforminputcontexts\\qtvirtualkeyboardplugin.dll',
   'BINARY'),
  ('PySide6\\opengl32sw.dll',
   'C:\\Users\\<USER>\\Desktop\\Mes_projets\\WritingToolsClean\\Windows_and_Linux\\myvenv\\Lib\\site-packages\\PySide6\\opengl32sw.dll',
   'BINARY'),
  ('_decimal.pyd',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\DLLs\\_decimal.pyd',
   'EXTENSION'),
  ('_hashlib.pyd',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\DLLs\\_hashlib.pyd',
   'EXTENSION'),
  ('_lzma.pyd',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\DLLs\\_lzma.pyd',
   'EXTENSION'),
  ('_bz2.pyd',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\DLLs\\_bz2.pyd',
   'EXTENSION'),
  ('unicodedata.pyd',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\DLLs\\unicodedata.pyd',
   'EXTENSION'),
  ('select.pyd',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\DLLs\\select.pyd',
   'EXTENSION'),
  ('_socket.pyd',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\DLLs\\_socket.pyd',
   'EXTENSION'),
  ('_multiprocessing.pyd',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\DLLs\\_multiprocessing.pyd',
   'EXTENSION'),
  ('pyexpat.pyd',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\DLLs\\pyexpat.pyd',
   'EXTENSION'),
  ('_ssl.pyd',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\DLLs\\_ssl.pyd',
   'EXTENSION'),
  ('_ctypes.pyd',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\DLLs\\_ctypes.pyd',
   'EXTENSION'),
  ('_queue.pyd',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\DLLs\\_queue.pyd',
   'EXTENSION'),
  ('_overlapped.pyd',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\DLLs\\_overlapped.pyd',
   'EXTENSION'),
  ('_asyncio.pyd',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\DLLs\\_asyncio.pyd',
   'EXTENSION'),
  ('_wmi.pyd',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\DLLs\\_wmi.pyd',
   'EXTENSION'),
  ('_uuid.pyd',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\DLLs\\_uuid.pyd',
   'EXTENSION'),
  ('pydantic_core\\_pydantic_core.cp312-win_amd64.pyd',
   'c:\\Users\\<USER>\\Desktop\\Mes_projets\\WritingToolsClean\\Windows_and_Linux\\myvenv\\Lib\\site-packages\\pydantic_core\\_pydantic_core.cp312-win_amd64.pyd',
   'EXTENSION'),
  ('_zoneinfo.pyd',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\DLLs\\_zoneinfo.pyd',
   'EXTENSION'),
  ('jiter\\jiter.cp312-win_amd64.pyd',
   'c:\\Users\\<USER>\\Desktop\\Mes_projets\\WritingToolsClean\\Windows_and_Linux\\myvenv\\Lib\\site-packages\\jiter\\jiter.cp312-win_amd64.pyd',
   'EXTENSION'),
  ('google\\_upb\\_message.pyd',
   'c:\\Users\\<USER>\\Desktop\\Mes_projets\\WritingToolsClean\\Windows_and_Linux\\myvenv\\Lib\\site-packages\\google\\_upb\\_message.pyd',
   'EXTENSION'),
  ('charset_normalizer\\md__mypyc.cp312-win_amd64.pyd',
   'c:\\Users\\<USER>\\Desktop\\Mes_projets\\WritingToolsClean\\Windows_and_Linux\\myvenv\\Lib\\site-packages\\charset_normalizer\\md__mypyc.cp312-win_amd64.pyd',
   'EXTENSION'),
  ('charset_normalizer\\md.cp312-win_amd64.pyd',
   'c:\\Users\\<USER>\\Desktop\\Mes_projets\\WritingToolsClean\\Windows_and_Linux\\myvenv\\Lib\\site-packages\\charset_normalizer\\md.cp312-win_amd64.pyd',
   'EXTENSION'),
  ('grpc\\_cython\\cygrpc.cp312-win_amd64.pyd',
   'c:\\Users\\<USER>\\Desktop\\Mes_projets\\WritingToolsClean\\Windows_and_Linux\\myvenv\\Lib\\site-packages\\grpc\\_cython\\cygrpc.cp312-win_amd64.pyd',
   'EXTENSION'),
  ('PySide6\\QtWidgets.pyd',
   'c:\\Users\\<USER>\\Desktop\\Mes_projets\\WritingToolsClean\\Windows_and_Linux\\myvenv\\Lib\\site-packages\\PySide6\\QtWidgets.pyd',
   'EXTENSION'),
  ('shiboken6\\Shiboken.pyd',
   'c:\\Users\\<USER>\\Desktop\\Mes_projets\\WritingToolsClean\\Windows_and_Linux\\myvenv\\Lib\\site-packages\\shiboken6\\Shiboken.pyd',
   'EXTENSION'),
  ('PySide6\\QtGui.pyd',
   'c:\\Users\\<USER>\\Desktop\\Mes_projets\\WritingToolsClean\\Windows_and_Linux\\myvenv\\Lib\\site-packages\\PySide6\\QtGui.pyd',
   'EXTENSION'),
  ('PySide6\\QtCore.pyd',
   'c:\\Users\\<USER>\\Desktop\\Mes_projets\\WritingToolsClean\\Windows_and_Linux\\myvenv\\Lib\\site-packages\\PySide6\\QtCore.pyd',
   'EXTENSION'),
  ('api-ms-win-crt-conio-l1-1-0.dll',
   'C:\\Program Files (x86)\\Windows Kits\\10\\Windows Performance '
   'Toolkit\\api-ms-win-crt-conio-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-crt-convert-l1-1-0.dll',
   'C:\\Program Files (x86)\\Windows Kits\\10\\Windows Performance '
   'Toolkit\\api-ms-win-crt-convert-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-crt-string-l1-1-0.dll',
   'C:\\Program Files (x86)\\Windows Kits\\10\\Windows Performance '
   'Toolkit\\api-ms-win-crt-string-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-crt-math-l1-1-0.dll',
   'C:\\Program Files (x86)\\Windows Kits\\10\\Windows Performance '
   'Toolkit\\api-ms-win-crt-math-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-crt-time-l1-1-0.dll',
   'C:\\Program Files (x86)\\Windows Kits\\10\\Windows Performance '
   'Toolkit\\api-ms-win-crt-time-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-crt-process-l1-1-0.dll',
   'C:\\Program Files (x86)\\Windows Kits\\10\\Windows Performance '
   'Toolkit\\api-ms-win-crt-process-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-crt-heap-l1-1-0.dll',
   'C:\\Program Files (x86)\\Windows Kits\\10\\Windows Performance '
   'Toolkit\\api-ms-win-crt-heap-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-crt-filesystem-l1-1-0.dll',
   'C:\\Program Files (x86)\\Windows Kits\\10\\Windows Performance '
   'Toolkit\\api-ms-win-crt-filesystem-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-crt-stdio-l1-1-0.dll',
   'C:\\Program Files (x86)\\Windows Kits\\10\\Windows Performance '
   'Toolkit\\api-ms-win-crt-stdio-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-crt-environment-l1-1-0.dll',
   'C:\\Program Files (x86)\\Windows Kits\\10\\Windows Performance '
   'Toolkit\\api-ms-win-crt-environment-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-crt-runtime-l1-1-0.dll',
   'C:\\Program Files (x86)\\Windows Kits\\10\\Windows Performance '
   'Toolkit\\api-ms-win-crt-runtime-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-crt-locale-l1-1-0.dll',
   'C:\\Program Files (x86)\\Windows Kits\\10\\Windows Performance '
   'Toolkit\\api-ms-win-crt-locale-l1-1-0.dll',
   'BINARY'),
  ('VCRUNTIME140.dll',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\VCRUNTIME140.dll',
   'BINARY'),
  ('shiboken6\\MSVCP140.dll',
   'C:\\Users\\<USER>\\Desktop\\Mes_projets\\WritingToolsClean\\Windows_and_Linux\\myvenv\\Lib\\site-packages\\shiboken6\\MSVCP140.dll',
   'BINARY'),
  ('PySide6\\Qt6Core.dll',
   'C:\\Users\\<USER>\\Desktop\\Mes_projets\\WritingToolsClean\\Windows_and_Linux\\myvenv\\Lib\\site-packages\\PySide6\\Qt6Core.dll',
   'BINARY'),
  ('PySide6\\Qt6Widgets.dll',
   'C:\\Users\\<USER>\\Desktop\\Mes_projets\\WritingToolsClean\\Windows_and_Linux\\myvenv\\Lib\\site-packages\\PySide6\\Qt6Widgets.dll',
   'BINARY'),
  ('PySide6\\Qt6Gui.dll',
   'C:\\Users\\<USER>\\Desktop\\Mes_projets\\WritingToolsClean\\Windows_and_Linux\\myvenv\\Lib\\site-packages\\PySide6\\Qt6Gui.dll',
   'BINARY'),
  ('PySide6\\Qt6Svg.dll',
   'C:\\Users\\<USER>\\Desktop\\Mes_projets\\WritingToolsClean\\Windows_and_Linux\\myvenv\\Lib\\site-packages\\PySide6\\Qt6Svg.dll',
   'BINARY'),
  ('VCRUNTIME140_1.dll',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\VCRUNTIME140_1.dll',
   'BINARY'),
  ('PySide6\\Qt6Network.dll',
   'C:\\Users\\<USER>\\Desktop\\Mes_projets\\WritingToolsClean\\Windows_and_Linux\\myvenv\\Lib\\site-packages\\PySide6\\Qt6Network.dll',
   'BINARY'),
  ('PySide6\\Qt6Pdf.dll',
   'C:\\Users\\<USER>\\Desktop\\Mes_projets\\WritingToolsClean\\Windows_and_Linux\\myvenv\\Lib\\site-packages\\PySide6\\Qt6Pdf.dll',
   'BINARY'),
  ('api-ms-win-crt-utility-l1-1-0.dll',
   'C:\\Program Files (x86)\\Windows Kits\\10\\Windows Performance '
   'Toolkit\\api-ms-win-crt-utility-l1-1-0.dll',
   'BINARY'),
  ('PySide6\\Qt6VirtualKeyboard.dll',
   'C:\\Users\\<USER>\\Desktop\\Mes_projets\\WritingToolsClean\\Windows_and_Linux\\myvenv\\Lib\\site-packages\\PySide6\\Qt6VirtualKeyboard.dll',
   'BINARY'),
  ('libcrypto-3.dll',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\DLLs\\libcrypto-3.dll',
   'BINARY'),
  ('libssl-3.dll',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\DLLs\\libssl-3.dll',
   'BINARY'),
  ('libffi-8.dll',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\DLLs\\libffi-8.dll',
   'BINARY'),
  ('api-ms-win-core-synch-l1-2-0.dll',
   'C:\\Program Files (x86)\\Windows Kits\\10\\Windows Performance '
   'Toolkit\\api-ms-win-core-synch-l1-2-0.dll',
   'BINARY'),
  ('python3.dll',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\python3.dll',
   'BINARY'),
  ('shiboken6\\shiboken6.abi3.dll',
   'C:\\Users\\<USER>\\Desktop\\Mes_projets\\WritingToolsClean\\Windows_and_Linux\\myvenv\\Lib\\site-packages\\shiboken6\\shiboken6.abi3.dll',
   'BINARY'),
  ('PySide6\\VCRUNTIME140.dll',
   'c:\\Users\\<USER>\\Desktop\\Mes_projets\\WritingToolsClean\\Windows_and_Linux\\myvenv\\Lib\\site-packages\\PySide6\\VCRUNTIME140.dll',
   'BINARY'),
  ('PySide6\\pyside6.abi3.dll',
   'c:\\Users\\<USER>\\Desktop\\Mes_projets\\WritingToolsClean\\Windows_and_Linux\\myvenv\\Lib\\site-packages\\PySide6\\pyside6.abi3.dll',
   'BINARY'),
  ('PySide6\\MSVCP140.dll',
   'c:\\Users\\<USER>\\Desktop\\Mes_projets\\WritingToolsClean\\Windows_and_Linux\\myvenv\\Lib\\site-packages\\PySide6\\MSVCP140.dll',
   'BINARY'),
  ('PySide6\\VCRUNTIME140_1.dll',
   'c:\\Users\\<USER>\\Desktop\\Mes_projets\\WritingToolsClean\\Windows_and_Linux\\myvenv\\Lib\\site-packages\\PySide6\\VCRUNTIME140_1.dll',
   'BINARY'),
  ('shiboken6\\VCRUNTIME140_1.dll',
   'c:\\Users\\<USER>\\Desktop\\Mes_projets\\WritingToolsClean\\Windows_and_Linux\\myvenv\\Lib\\site-packages\\shiboken6\\VCRUNTIME140_1.dll',
   'BINARY'),
  ('shiboken6\\VCRUNTIME140.dll',
   'c:\\Users\\<USER>\\Desktop\\Mes_projets\\WritingToolsClean\\Windows_and_Linux\\myvenv\\Lib\\site-packages\\shiboken6\\VCRUNTIME140.dll',
   'BINARY'),
  ('PySide6\\MSVCP140_2.dll',
   'c:\\Users\\<USER>\\Desktop\\Mes_projets\\WritingToolsClean\\Windows_and_Linux\\myvenv\\Lib\\site-packages\\PySide6\\MSVCP140_2.dll',
   'BINARY'),
  ('ucrtbase.dll',
   'C:\\Program Files (x86)\\Windows Kits\\10\\Windows Performance '
   'Toolkit\\ucrtbase.dll',
   'BINARY'),
  ('PySide6\\MSVCP140_1.dll',
   'C:\\Users\\<USER>\\Desktop\\Mes_projets\\WritingToolsClean\\Windows_and_Linux\\myvenv\\Lib\\site-packages\\PySide6\\MSVCP140_1.dll',
   'BINARY'),
  ('PySide6\\Qt6Qml.dll',
   'C:\\Users\\<USER>\\Desktop\\Mes_projets\\WritingToolsClean\\Windows_and_Linux\\myvenv\\Lib\\site-packages\\PySide6\\Qt6Qml.dll',
   'BINARY'),
  ('PySide6\\Qt6Quick.dll',
   'C:\\Users\\<USER>\\Desktop\\Mes_projets\\WritingToolsClean\\Windows_and_Linux\\myvenv\\Lib\\site-packages\\PySide6\\Qt6Quick.dll',
   'BINARY'),
  ('api-ms-win-core-localization-l1-2-0.dll',
   'C:\\Program Files (x86)\\Windows Kits\\10\\Windows Performance '
   'Toolkit\\api-ms-win-core-localization-l1-2-0.dll',
   'BINARY'),
  ('api-ms-win-core-errorhandling-l1-1-0.dll',
   'C:\\Program Files (x86)\\Windows Kits\\10\\Windows Performance '
   'Toolkit\\api-ms-win-core-errorhandling-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-console-l1-1-0.dll',
   'C:\\Program Files (x86)\\Windows Kits\\10\\Windows Performance '
   'Toolkit\\api-ms-win-core-console-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-file-l2-1-0.dll',
   'C:\\Program Files (x86)\\Windows Kits\\10\\Windows Performance '
   'Toolkit\\api-ms-win-core-file-l2-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-debug-l1-1-0.dll',
   'C:\\Program Files (x86)\\Windows Kits\\10\\Windows Performance '
   'Toolkit\\api-ms-win-core-debug-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-namedpipe-l1-1-0.dll',
   'C:\\Program Files (x86)\\Windows Kits\\10\\Windows Performance '
   'Toolkit\\api-ms-win-core-namedpipe-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-util-l1-1-0.dll',
   'C:\\Program Files (x86)\\Windows Kits\\10\\Windows Performance '
   'Toolkit\\api-ms-win-core-util-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-heap-l1-1-0.dll',
   'C:\\Program Files (x86)\\Windows Kits\\10\\Windows Performance '
   'Toolkit\\api-ms-win-core-heap-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-file-l1-1-0.dll',
   'C:\\Program Files (x86)\\Windows Kits\\10\\Windows Performance '
   'Toolkit\\api-ms-win-core-file-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-rtlsupport-l1-1-0.dll',
   'C:\\Program Files (x86)\\Windows Kits\\10\\Windows Performance '
   'Toolkit\\api-ms-win-core-rtlsupport-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-libraryloader-l1-1-0.dll',
   'C:\\Program Files (x86)\\Windows Kits\\10\\Windows Performance '
   'Toolkit\\api-ms-win-core-libraryloader-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-processenvironment-l1-1-0.dll',
   'C:\\Program Files (x86)\\Windows Kits\\10\\Windows Performance '
   'Toolkit\\api-ms-win-core-processenvironment-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-synch-l1-1-0.dll',
   'C:\\Program Files (x86)\\Windows Kits\\10\\Windows Performance '
   'Toolkit\\api-ms-win-core-synch-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-timezone-l1-1-0.dll',
   'C:\\Program Files (x86)\\Windows Kits\\10\\Windows Performance '
   'Toolkit\\api-ms-win-core-timezone-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-string-l1-1-0.dll',
   'C:\\Program Files (x86)\\Windows Kits\\10\\Windows Performance '
   'Toolkit\\api-ms-win-core-string-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-datetime-l1-1-0.dll',
   'C:\\Program Files (x86)\\Windows Kits\\10\\Windows Performance '
   'Toolkit\\api-ms-win-core-datetime-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-sysinfo-l1-1-0.dll',
   'C:\\Program Files (x86)\\Windows Kits\\10\\Windows Performance '
   'Toolkit\\api-ms-win-core-sysinfo-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-interlocked-l1-1-0.dll',
   'C:\\Program Files (x86)\\Windows Kits\\10\\Windows Performance '
   'Toolkit\\api-ms-win-core-interlocked-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-handle-l1-1-0.dll',
   'C:\\Program Files (x86)\\Windows Kits\\10\\Windows Performance '
   'Toolkit\\api-ms-win-core-handle-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-memory-l1-1-0.dll',
   'C:\\Program Files (x86)\\Windows Kits\\10\\Windows Performance '
   'Toolkit\\api-ms-win-core-memory-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-processthreads-l1-1-0.dll',
   'C:\\Program Files (x86)\\Windows Kits\\10\\Windows Performance '
   'Toolkit\\api-ms-win-core-processthreads-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-file-l1-2-0.dll',
   'C:\\Program Files (x86)\\Windows Kits\\10\\Windows Performance '
   'Toolkit\\api-ms-win-core-file-l1-2-0.dll',
   'BINARY'),
  ('api-ms-win-core-profile-l1-1-0.dll',
   'C:\\Program Files (x86)\\Windows Kits\\10\\Windows Performance '
   'Toolkit\\api-ms-win-core-profile-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-processthreads-l1-1-1.dll',
   'C:\\Program Files (x86)\\Windows Kits\\10\\Windows Performance '
   'Toolkit\\api-ms-win-core-processthreads-l1-1-1.dll',
   'BINARY'),
  ('PySide6\\Qt6QmlModels.dll',
   'C:\\Users\\<USER>\\Desktop\\Mes_projets\\WritingToolsClean\\Windows_and_Linux\\myvenv\\Lib\\site-packages\\PySide6\\Qt6QmlModels.dll',
   'BINARY'),
  ('PySide6\\Qt6QmlMeta.dll',
   'C:\\Users\\<USER>\\Desktop\\Mes_projets\\WritingToolsClean\\Windows_and_Linux\\myvenv\\Lib\\site-packages\\PySide6\\Qt6QmlMeta.dll',
   'BINARY'),
  ('PySide6\\Qt6OpenGL.dll',
   'C:\\Users\\<USER>\\Desktop\\Mes_projets\\WritingToolsClean\\Windows_and_Linux\\myvenv\\Lib\\site-packages\\PySide6\\Qt6OpenGL.dll',
   'BINARY'),
  ('PySide6\\Qt6QmlWorkerScript.dll',
   'C:\\Users\\<USER>\\Desktop\\Mes_projets\\WritingToolsClean\\Windows_and_Linux\\myvenv\\Lib\\site-packages\\PySide6\\Qt6QmlWorkerScript.dll',
   'BINARY'),
  ('setuptools\\_vendor\\importlib_metadata-8.0.0.dist-info\\top_level.txt',
   'c:\\Users\\<USER>\\Desktop\\Mes_projets\\WritingToolsClean\\Windows_and_Linux\\myvenv\\Lib\\site-packages\\setuptools\\_vendor\\importlib_metadata-8.0.0.dist-info\\top_level.txt',
   'DATA'),
  ('setuptools\\_vendor\\importlib_metadata-8.0.0.dist-info\\REQUESTED',
   'c:\\Users\\<USER>\\Desktop\\Mes_projets\\WritingToolsClean\\Windows_and_Linux\\myvenv\\Lib\\site-packages\\setuptools\\_vendor\\importlib_metadata-8.0.0.dist-info\\REQUESTED',
   'DATA'),
  ('setuptools\\_vendor\\importlib_metadata-8.0.0.dist-info\\LICENSE',
   'c:\\Users\\<USER>\\Desktop\\Mes_projets\\WritingToolsClean\\Windows_and_Linux\\myvenv\\Lib\\site-packages\\setuptools\\_vendor\\importlib_metadata-8.0.0.dist-info\\LICENSE',
   'DATA'),
  ('setuptools\\_vendor\\importlib_metadata-8.0.0.dist-info\\RECORD',
   'c:\\Users\\<USER>\\Desktop\\Mes_projets\\WritingToolsClean\\Windows_and_Linux\\myvenv\\Lib\\site-packages\\setuptools\\_vendor\\importlib_metadata-8.0.0.dist-info\\RECORD',
   'DATA'),
  ('setuptools\\_vendor\\importlib_metadata-8.0.0.dist-info\\INSTALLER',
   'c:\\Users\\<USER>\\Desktop\\Mes_projets\\WritingToolsClean\\Windows_and_Linux\\myvenv\\Lib\\site-packages\\setuptools\\_vendor\\importlib_metadata-8.0.0.dist-info\\INSTALLER',
   'DATA'),
  ('setuptools\\_vendor\\importlib_metadata-8.0.0.dist-info\\METADATA',
   'c:\\Users\\<USER>\\Desktop\\Mes_projets\\WritingToolsClean\\Windows_and_Linux\\myvenv\\Lib\\site-packages\\setuptools\\_vendor\\importlib_metadata-8.0.0.dist-info\\METADATA',
   'DATA'),
  ('setuptools\\_vendor\\importlib_metadata-8.0.0.dist-info\\WHEEL',
   'c:\\Users\\<USER>\\Desktop\\Mes_projets\\WritingToolsClean\\Windows_and_Linux\\myvenv\\Lib\\site-packages\\setuptools\\_vendor\\importlib_metadata-8.0.0.dist-info\\WHEEL',
   'DATA'),
  ('setuptools\\_vendor\\jaraco\\text\\Lorem ipsum.txt',
   'c:\\Users\\<USER>\\Desktop\\Mes_projets\\WritingToolsClean\\Windows_and_Linux\\myvenv\\Lib\\site-packages\\setuptools\\_vendor\\jaraco\\text\\Lorem '
   'ipsum.txt',
   'DATA'),
  ('certifi\\cacert.pem',
   'c:\\Users\\<USER>\\Desktop\\Mes_projets\\WritingToolsClean\\Windows_and_Linux\\myvenv\\Lib\\site-packages\\certifi\\cacert.pem',
   'DATA'),
  ('certifi\\py.typed',
   'c:\\Users\\<USER>\\Desktop\\Mes_projets\\WritingToolsClean\\Windows_and_Linux\\myvenv\\Lib\\site-packages\\certifi\\py.typed',
   'DATA'),
  ('grpc\\_cython\\_credentials\\roots.pem',
   'c:\\Users\\<USER>\\Desktop\\Mes_projets\\WritingToolsClean\\Windows_and_Linux\\myvenv\\Lib\\site-packages\\grpc\\_cython\\_credentials\\roots.pem',
   'DATA'),
  ('google_api_core-2.25.1.dist-info\\METADATA',
   'c:\\Users\\<USER>\\Desktop\\Mes_projets\\WritingToolsClean\\Windows_and_Linux\\myvenv\\Lib\\site-packages\\google_api_core-2.25.1.dist-info\\METADATA',
   'DATA'),
  ('google_api_core-2.25.1.dist-info\\licenses\\LICENSE',
   'c:\\Users\\<USER>\\Desktop\\Mes_projets\\WritingToolsClean\\Windows_and_Linux\\myvenv\\Lib\\site-packages\\google_api_core-2.25.1.dist-info\\licenses\\LICENSE',
   'DATA'),
  ('google_api_core-2.25.1.dist-info\\top_level.txt',
   'c:\\Users\\<USER>\\Desktop\\Mes_projets\\WritingToolsClean\\Windows_and_Linux\\myvenv\\Lib\\site-packages\\google_api_core-2.25.1.dist-info\\top_level.txt',
   'DATA'),
  ('google_api_core-2.25.1.dist-info\\INSTALLER',
   'c:\\Users\\<USER>\\Desktop\\Mes_projets\\WritingToolsClean\\Windows_and_Linux\\myvenv\\Lib\\site-packages\\google_api_core-2.25.1.dist-info\\INSTALLER',
   'DATA'),
  ('google_api_core-2.25.1.dist-info\\RECORD',
   'c:\\Users\\<USER>\\Desktop\\Mes_projets\\WritingToolsClean\\Windows_and_Linux\\myvenv\\Lib\\site-packages\\google_api_core-2.25.1.dist-info\\RECORD',
   'DATA'),
  ('google_api_core-2.25.1.dist-info\\WHEEL',
   'c:\\Users\\<USER>\\Desktop\\Mes_projets\\WritingToolsClean\\Windows_and_Linux\\myvenv\\Lib\\site-packages\\google_api_core-2.25.1.dist-info\\WHEEL',
   'DATA'),
  ('googleapiclient\\discovery_cache\\documents\\analyticsdata.v1alpha.json',
   'c:\\Users\\<USER>\\Desktop\\Mes_projets\\WritingToolsClean\\Windows_and_Linux\\myvenv\\Lib\\site-packages\\googleapiclient\\discovery_cache\\documents\\analyticsdata.v1alpha.json',
   'DATA'),
  ('googleapiclient\\discovery_cache\\documents\\domains.v1beta1.json',
   'c:\\Users\\<USER>\\Desktop\\Mes_projets\\WritingToolsClean\\Windows_and_Linux\\myvenv\\Lib\\site-packages\\googleapiclient\\discovery_cache\\documents\\domains.v1beta1.json',
   'DATA'),
  ('googleapiclient\\discovery_cache\\documents\\doubleclickbidmanager.v2.json',
   'c:\\Users\\<USER>\\Desktop\\Mes_projets\\WritingToolsClean\\Windows_and_Linux\\myvenv\\Lib\\site-packages\\googleapiclient\\discovery_cache\\documents\\doubleclickbidmanager.v2.json',
   'DATA'),
  ('googleapiclient\\discovery_cache\\documents\\firebaseml.v1beta2.json',
   'c:\\Users\\<USER>\\Desktop\\Mes_projets\\WritingToolsClean\\Windows_and_Linux\\myvenv\\Lib\\site-packages\\googleapiclient\\discovery_cache\\documents\\firebaseml.v1beta2.json',
   'DATA'),
  ('googleapiclient\\discovery_cache\\documents\\cloudbilling.v1beta.json',
   'c:\\Users\\<USER>\\Desktop\\Mes_projets\\WritingToolsClean\\Windows_and_Linux\\myvenv\\Lib\\site-packages\\googleapiclient\\discovery_cache\\documents\\cloudbilling.v1beta.json',
   'DATA'),
  ('googleapiclient\\discovery_cache\\documents\\domainsrdap.v1.json',
   'c:\\Users\\<USER>\\Desktop\\Mes_projets\\WritingToolsClean\\Windows_and_Linux\\myvenv\\Lib\\site-packages\\googleapiclient\\discovery_cache\\documents\\domainsrdap.v1.json',
   'DATA'),
  ('googleapiclient\\discovery_cache\\documents\\cloudprofiler.v2.json',
   'c:\\Users\\<USER>\\Desktop\\Mes_projets\\WritingToolsClean\\Windows_and_Linux\\myvenv\\Lib\\site-packages\\googleapiclient\\discovery_cache\\documents\\cloudprofiler.v2.json',
   'DATA'),
  ('googleapiclient\\discovery_cache\\documents\\abusiveexperiencereport.v1.json',
   'c:\\Users\\<USER>\\Desktop\\Mes_projets\\WritingToolsClean\\Windows_and_Linux\\myvenv\\Lib\\site-packages\\googleapiclient\\discovery_cache\\documents\\abusiveexperiencereport.v1.json',
   'DATA'),
  ('googleapiclient\\discovery_cache\\documents\\webfonts.v1.json',
   'c:\\Users\\<USER>\\Desktop\\Mes_projets\\WritingToolsClean\\Windows_and_Linux\\myvenv\\Lib\\site-packages\\googleapiclient\\discovery_cache\\documents\\webfonts.v1.json',
   'DATA'),
  ('googleapiclient\\discovery_cache\\documents\\cloudchannel.v1.json',
   'c:\\Users\\<USER>\\Desktop\\Mes_projets\\WritingToolsClean\\Windows_and_Linux\\myvenv\\Lib\\site-packages\\googleapiclient\\discovery_cache\\documents\\cloudchannel.v1.json',
   'DATA'),
  ('googleapiclient\\discovery_cache\\documents\\netapp.v1.json',
   'c:\\Users\\<USER>\\Desktop\\Mes_projets\\WritingToolsClean\\Windows_and_Linux\\myvenv\\Lib\\site-packages\\googleapiclient\\discovery_cache\\documents\\netapp.v1.json',
   'DATA'),
  ('googleapiclient\\discovery_cache\\documents\\homegraph.v1.json',
   'c:\\Users\\<USER>\\Desktop\\Mes_projets\\WritingToolsClean\\Windows_and_Linux\\myvenv\\Lib\\site-packages\\googleapiclient\\discovery_cache\\documents\\homegraph.v1.json',
   'DATA'),
  ('googleapiclient\\discovery_cache\\documents\\speech.v1p1beta1.json',
   'c:\\Users\\<USER>\\Desktop\\Mes_projets\\WritingToolsClean\\Windows_and_Linux\\myvenv\\Lib\\site-packages\\googleapiclient\\discovery_cache\\documents\\speech.v1p1beta1.json',
   'DATA'),
  ('googleapiclient\\discovery_cache\\documents\\healthcare.v1.json',
   'c:\\Users\\<USER>\\Desktop\\Mes_projets\\WritingToolsClean\\Windows_and_Linux\\myvenv\\Lib\\site-packages\\googleapiclient\\discovery_cache\\documents\\healthcare.v1.json',
   'DATA'),
  ('googleapiclient\\discovery_cache\\documents\\speech.v2beta1.json',
   'c:\\Users\\<USER>\\Desktop\\Mes_projets\\WritingToolsClean\\Windows_and_Linux\\myvenv\\Lib\\site-packages\\googleapiclient\\discovery_cache\\documents\\speech.v2beta1.json',
   'DATA'),
  ('googleapiclient\\discovery_cache\\documents\\dialogflow.v2beta1.json',
   'c:\\Users\\<USER>\\Desktop\\Mes_projets\\WritingToolsClean\\Windows_and_Linux\\myvenv\\Lib\\site-packages\\googleapiclient\\discovery_cache\\documents\\dialogflow.v2beta1.json',
   'DATA'),
  ('googleapiclient\\discovery_cache\\documents\\websecurityscanner.v1.json',
   'c:\\Users\\<USER>\\Desktop\\Mes_projets\\WritingToolsClean\\Windows_and_Linux\\myvenv\\Lib\\site-packages\\googleapiclient\\discovery_cache\\documents\\websecurityscanner.v1.json',
   'DATA'),
  ('googleapiclient\\discovery_cache\\documents\\binaryauthorization.v1.json',
   'c:\\Users\\<USER>\\Desktop\\Mes_projets\\WritingToolsClean\\Windows_and_Linux\\myvenv\\Lib\\site-packages\\googleapiclient\\discovery_cache\\documents\\binaryauthorization.v1.json',
   'DATA'),
  ('googleapiclient\\discovery_cache\\documents\\playdeveloperreporting.v1beta1.json',
   'c:\\Users\\<USER>\\Desktop\\Mes_projets\\WritingToolsClean\\Windows_and_Linux\\myvenv\\Lib\\site-packages\\googleapiclient\\discovery_cache\\documents\\playdeveloperreporting.v1beta1.json',
   'DATA'),
  ('googleapiclient\\discovery_cache\\documents\\adsensehost.v4.1.json',
   'c:\\Users\\<USER>\\Desktop\\Mes_projets\\WritingToolsClean\\Windows_and_Linux\\myvenv\\Lib\\site-packages\\googleapiclient\\discovery_cache\\documents\\adsensehost.v4.1.json',
   'DATA'),
  ('googleapiclient\\discovery_cache\\documents\\gkehub.v1alpha.json',
   'c:\\Users\\<USER>\\Desktop\\Mes_projets\\WritingToolsClean\\Windows_and_Linux\\myvenv\\Lib\\site-packages\\googleapiclient\\discovery_cache\\documents\\gkehub.v1alpha.json',
   'DATA'),
  ('googleapiclient\\discovery_cache\\documents\\connectors.v1.json',
   'c:\\Users\\<USER>\\Desktop\\Mes_projets\\WritingToolsClean\\Windows_and_Linux\\myvenv\\Lib\\site-packages\\googleapiclient\\discovery_cache\\documents\\connectors.v1.json',
   'DATA'),
  ('googleapiclient\\discovery_cache\\documents\\compute.alpha.json',
   'c:\\Users\\<USER>\\Desktop\\Mes_projets\\WritingToolsClean\\Windows_and_Linux\\myvenv\\Lib\\site-packages\\googleapiclient\\discovery_cache\\documents\\compute.alpha.json',
   'DATA'),
  ('googleapiclient\\discovery_cache\\documents\\datastore.v1beta1.json',
   'c:\\Users\\<USER>\\Desktop\\Mes_projets\\WritingToolsClean\\Windows_and_Linux\\myvenv\\Lib\\site-packages\\googleapiclient\\discovery_cache\\documents\\datastore.v1beta1.json',
   'DATA'),
  ('googleapiclient\\discovery_cache\\documents\\workstations.v1.json',
   'c:\\Users\\<USER>\\Desktop\\Mes_projets\\WritingToolsClean\\Windows_and_Linux\\myvenv\\Lib\\site-packages\\googleapiclient\\discovery_cache\\documents\\workstations.v1.json',
   'DATA'),
  ('googleapiclient\\discovery_cache\\documents\\deploymentmanager.alpha.json',
   'c:\\Users\\<USER>\\Desktop\\Mes_projets\\WritingToolsClean\\Windows_and_Linux\\myvenv\\Lib\\site-packages\\googleapiclient\\discovery_cache\\documents\\deploymentmanager.alpha.json',
   'DATA'),
  ('googleapiclient\\discovery_cache\\documents\\playdeveloperreporting.v1alpha1.json',
   'c:\\Users\\<USER>\\Desktop\\Mes_projets\\WritingToolsClean\\Windows_and_Linux\\myvenv\\Lib\\site-packages\\googleapiclient\\discovery_cache\\documents\\playdeveloperreporting.v1alpha1.json',
   'DATA'),
  ('googleapiclient\\discovery_cache\\documents\\firebase.v1beta1.json',
   'c:\\Users\\<USER>\\Desktop\\Mes_projets\\WritingToolsClean\\Windows_and_Linux\\myvenv\\Lib\\site-packages\\googleapiclient\\discovery_cache\\documents\\firebase.v1beta1.json',
   'DATA'),
  ('googleapiclient\\discovery_cache\\documents\\iamcredentials.v1.json',
   'c:\\Users\\<USER>\\Desktop\\Mes_projets\\WritingToolsClean\\Windows_and_Linux\\myvenv\\Lib\\site-packages\\googleapiclient\\discovery_cache\\documents\\iamcredentials.v1.json',
   'DATA'),
  ('googleapiclient\\discovery_cache\\documents\\alloydb.v1.json',
   'c:\\Users\\<USER>\\Desktop\\Mes_projets\\WritingToolsClean\\Windows_and_Linux\\myvenv\\Lib\\site-packages\\googleapiclient\\discovery_cache\\documents\\alloydb.v1.json',
   'DATA'),
  ('googleapiclient\\discovery_cache\\documents\\digitalassetlinks.v1.json',
   'c:\\Users\\<USER>\\Desktop\\Mes_projets\\WritingToolsClean\\Windows_and_Linux\\myvenv\\Lib\\site-packages\\googleapiclient\\discovery_cache\\documents\\digitalassetlinks.v1.json',
   'DATA'),
  ('googleapiclient\\discovery_cache\\documents\\composer.v1beta1.json',
   'c:\\Users\\<USER>\\Desktop\\Mes_projets\\WritingToolsClean\\Windows_and_Linux\\myvenv\\Lib\\site-packages\\googleapiclient\\discovery_cache\\documents\\composer.v1beta1.json',
   'DATA'),
  ('googleapiclient\\discovery_cache\\documents\\gkehub.v2beta.json',
   'c:\\Users\\<USER>\\Desktop\\Mes_projets\\WritingToolsClean\\Windows_and_Linux\\myvenv\\Lib\\site-packages\\googleapiclient\\discovery_cache\\documents\\gkehub.v2beta.json',
   'DATA'),
  ('googleapiclient\\discovery_cache\\documents\\admin.reportsv1.json',
   'c:\\Users\\<USER>\\Desktop\\Mes_projets\\WritingToolsClean\\Windows_and_Linux\\myvenv\\Lib\\site-packages\\googleapiclient\\discovery_cache\\documents\\admin.reportsv1.json',
   'DATA'),
  ('googleapiclient\\discovery_cache\\documents\\bigqueryreservation.v1beta1.json',
   'c:\\Users\\<USER>\\Desktop\\Mes_projets\\WritingToolsClean\\Windows_and_Linux\\myvenv\\Lib\\site-packages\\googleapiclient\\discovery_cache\\documents\\bigqueryreservation.v1beta1.json',
   'DATA'),
  ('googleapiclient\\discovery_cache\\documents\\firestore.v1beta1.json',
   'c:\\Users\\<USER>\\Desktop\\Mes_projets\\WritingToolsClean\\Windows_and_Linux\\myvenv\\Lib\\site-packages\\googleapiclient\\discovery_cache\\documents\\firestore.v1beta1.json',
   'DATA'),
  ('googleapiclient\\discovery_cache\\documents\\bigquerydatatransfer.v1.json',
   'c:\\Users\\<USER>\\Desktop\\Mes_projets\\WritingToolsClean\\Windows_and_Linux\\myvenv\\Lib\\site-packages\\googleapiclient\\discovery_cache\\documents\\bigquerydatatransfer.v1.json',
   'DATA'),
  ('google_api_python_client-2.177.0.dist-info\\top_level.txt',
   'c:\\Users\\<USER>\\Desktop\\Mes_projets\\WritingToolsClean\\Windows_and_Linux\\myvenv\\Lib\\site-packages\\google_api_python_client-2.177.0.dist-info\\top_level.txt',
   'DATA'),
  ('googleapiclient\\discovery_cache\\documents\\dns.v1beta2.json',
   'c:\\Users\\<USER>\\Desktop\\Mes_projets\\WritingToolsClean\\Windows_and_Linux\\myvenv\\Lib\\site-packages\\googleapiclient\\discovery_cache\\documents\\dns.v1beta2.json',
   'DATA'),
  ('googleapiclient\\discovery_cache\\documents\\apikeys.v2.json',
   'c:\\Users\\<USER>\\Desktop\\Mes_projets\\WritingToolsClean\\Windows_and_Linux\\myvenv\\Lib\\site-packages\\googleapiclient\\discovery_cache\\documents\\apikeys.v2.json',
   'DATA'),
  ('googleapiclient\\discovery_cache\\documents\\tagmanager.v2.json',
   'c:\\Users\\<USER>\\Desktop\\Mes_projets\\WritingToolsClean\\Windows_and_Linux\\myvenv\\Lib\\site-packages\\googleapiclient\\discovery_cache\\documents\\tagmanager.v2.json',
   'DATA'),
  ('googleapiclient\\discovery_cache\\documents\\factchecktools.v1alpha1.json',
   'c:\\Users\\<USER>\\Desktop\\Mes_projets\\WritingToolsClean\\Windows_and_Linux\\myvenv\\Lib\\site-packages\\googleapiclient\\discovery_cache\\documents\\factchecktools.v1alpha1.json',
   'DATA'),
  ('googleapiclient\\discovery_cache\\documents\\binaryauthorization.v1beta1.json',
   'c:\\Users\\<USER>\\Desktop\\Mes_projets\\WritingToolsClean\\Windows_and_Linux\\myvenv\\Lib\\site-packages\\googleapiclient\\discovery_cache\\documents\\binaryauthorization.v1beta1.json',
   'DATA'),
  ('googleapiclient\\discovery_cache\\documents\\aiplatform.v1beta1.json',
   'c:\\Users\\<USER>\\Desktop\\Mes_projets\\WritingToolsClean\\Windows_and_Linux\\myvenv\\Lib\\site-packages\\googleapiclient\\discovery_cache\\documents\\aiplatform.v1beta1.json',
   'DATA'),
  ('googleapiclient\\discovery_cache\\documents\\cloudcontrolspartner.v1.json',
   'c:\\Users\\<USER>\\Desktop\\Mes_projets\\WritingToolsClean\\Windows_and_Linux\\myvenv\\Lib\\site-packages\\googleapiclient\\discovery_cache\\documents\\cloudcontrolspartner.v1.json',
   'DATA'),
  ('googleapiclient\\discovery_cache\\documents\\cloudresourcemanager.v1.json',
   'c:\\Users\\<USER>\\Desktop\\Mes_projets\\WritingToolsClean\\Windows_and_Linux\\myvenv\\Lib\\site-packages\\googleapiclient\\discovery_cache\\documents\\cloudresourcemanager.v1.json',
   'DATA'),
  ('googleapiclient\\discovery_cache\\documents\\mybusinessqanda.v1.json',
   'c:\\Users\\<USER>\\Desktop\\Mes_projets\\WritingToolsClean\\Windows_and_Linux\\myvenv\\Lib\\site-packages\\googleapiclient\\discovery_cache\\documents\\mybusinessqanda.v1.json',
   'DATA'),
  ('googleapiclient\\discovery_cache\\documents\\lifesciences.v2beta.json',
   'c:\\Users\\<USER>\\Desktop\\Mes_projets\\WritingToolsClean\\Windows_and_Linux\\myvenv\\Lib\\site-packages\\googleapiclient\\discovery_cache\\documents\\lifesciences.v2beta.json',
   'DATA'),
  ('googleapiclient\\discovery_cache\\documents\\run.v1.json',
   'c:\\Users\\<USER>\\Desktop\\Mes_projets\\WritingToolsClean\\Windows_and_Linux\\myvenv\\Lib\\site-packages\\googleapiclient\\discovery_cache\\documents\\run.v1.json',
   'DATA'),
  ('googleapiclient\\discovery_cache\\documents\\admin.directoryv1.json',
   'c:\\Users\\<USER>\\Desktop\\Mes_projets\\WritingToolsClean\\Windows_and_Linux\\myvenv\\Lib\\site-packages\\googleapiclient\\discovery_cache\\documents\\admin.directoryv1.json',
   'DATA'),
  ('googleapiclient\\discovery_cache\\documents\\saasservicemgmt.v1beta1.json',
   'c:\\Users\\<USER>\\Desktop\\Mes_projets\\WritingToolsClean\\Windows_and_Linux\\myvenv\\Lib\\site-packages\\googleapiclient\\discovery_cache\\documents\\saasservicemgmt.v1beta1.json',
   'DATA'),
  ('googleapiclient\\discovery_cache\\documents\\gkehub.v1alpha2.json',
   'c:\\Users\\<USER>\\Desktop\\Mes_projets\\WritingToolsClean\\Windows_and_Linux\\myvenv\\Lib\\site-packages\\googleapiclient\\discovery_cache\\documents\\gkehub.v1alpha2.json',
   'DATA'),
  ('googleapiclient\\discovery_cache\\documents\\contactcenteraiplatform.v1alpha1.json',
   'c:\\Users\\<USER>\\Desktop\\Mes_projets\\WritingToolsClean\\Windows_and_Linux\\myvenv\\Lib\\site-packages\\googleapiclient\\discovery_cache\\documents\\contactcenteraiplatform.v1alpha1.json',
   'DATA'),
  ('googleapiclient\\discovery_cache\\documents\\run.v1beta1.json',
   'c:\\Users\\<USER>\\Desktop\\Mes_projets\\WritingToolsClean\\Windows_and_Linux\\myvenv\\Lib\\site-packages\\googleapiclient\\discovery_cache\\documents\\run.v1beta1.json',
   'DATA'),
  ('googleapiclient\\discovery_cache\\documents\\storagebatchoperations.v1.json',
   'c:\\Users\\<USER>\\Desktop\\Mes_projets\\WritingToolsClean\\Windows_and_Linux\\myvenv\\Lib\\site-packages\\googleapiclient\\discovery_cache\\documents\\storagebatchoperations.v1.json',
   'DATA'),
  ('googleapiclient\\discovery_cache\\documents\\securitycenter.v1.json',
   'c:\\Users\\<USER>\\Desktop\\Mes_projets\\WritingToolsClean\\Windows_and_Linux\\myvenv\\Lib\\site-packages\\googleapiclient\\discovery_cache\\documents\\securitycenter.v1.json',
   'DATA'),
  ('googleapiclient\\discovery_cache\\documents\\notebooks.v1.json',
   'c:\\Users\\<USER>\\Desktop\\Mes_projets\\WritingToolsClean\\Windows_and_Linux\\myvenv\\Lib\\site-packages\\googleapiclient\\discovery_cache\\documents\\notebooks.v1.json',
   'DATA'),
  ('googleapiclient\\discovery_cache\\documents\\healthcare.v1beta1.json',
   'c:\\Users\\<USER>\\Desktop\\Mes_projets\\WritingToolsClean\\Windows_and_Linux\\myvenv\\Lib\\site-packages\\googleapiclient\\discovery_cache\\documents\\healthcare.v1beta1.json',
   'DATA'),
  ('google_api_python_client-2.177.0.dist-info\\INSTALLER',
   'c:\\Users\\<USER>\\Desktop\\Mes_projets\\WritingToolsClean\\Windows_and_Linux\\myvenv\\Lib\\site-packages\\google_api_python_client-2.177.0.dist-info\\INSTALLER',
   'DATA'),
  ('googleapiclient\\discovery_cache\\documents\\cloudlocationfinder.v1alpha.json',
   'c:\\Users\\<USER>\\Desktop\\Mes_projets\\WritingToolsClean\\Windows_and_Linux\\myvenv\\Lib\\site-packages\\googleapiclient\\discovery_cache\\documents\\cloudlocationfinder.v1alpha.json',
   'DATA'),
  ('googleapiclient\\discovery_cache\\documents\\publicca.v1alpha1.json',
   'c:\\Users\\<USER>\\Desktop\\Mes_projets\\WritingToolsClean\\Windows_and_Linux\\myvenv\\Lib\\site-packages\\googleapiclient\\discovery_cache\\documents\\publicca.v1alpha1.json',
   'DATA'),
  ('googleapiclient\\discovery_cache\\documents\\merchantapi.reviews_v1beta.json',
   'c:\\Users\\<USER>\\Desktop\\Mes_projets\\WritingToolsClean\\Windows_and_Linux\\myvenv\\Lib\\site-packages\\googleapiclient\\discovery_cache\\documents\\merchantapi.reviews_v1beta.json',
   'DATA'),
  ('googleapiclient\\discovery_cache\\documents\\networkmanagement.v1beta1.json',
   'c:\\Users\\<USER>\\Desktop\\Mes_projets\\WritingToolsClean\\Windows_and_Linux\\myvenv\\Lib\\site-packages\\googleapiclient\\discovery_cache\\documents\\networkmanagement.v1beta1.json',
   'DATA'),
  ('googleapiclient\\discovery_cache\\documents\\clouderrorreporting.v1beta1.json',
   'c:\\Users\\<USER>\\Desktop\\Mes_projets\\WritingToolsClean\\Windows_and_Linux\\myvenv\\Lib\\site-packages\\googleapiclient\\discovery_cache\\documents\\clouderrorreporting.v1beta1.json',
   'DATA'),
  ('googleapiclient\\discovery_cache\\documents\\adexchangebuyer.v1.2.json',
   'c:\\Users\\<USER>\\Desktop\\Mes_projets\\WritingToolsClean\\Windows_and_Linux\\myvenv\\Lib\\site-packages\\googleapiclient\\discovery_cache\\documents\\adexchangebuyer.v1.2.json',
   'DATA'),
  ('googleapiclient\\discovery_cache\\documents\\cloudfunctions.v2beta.json',
   'c:\\Users\\<USER>\\Desktop\\Mes_projets\\WritingToolsClean\\Windows_and_Linux\\myvenv\\Lib\\site-packages\\googleapiclient\\discovery_cache\\documents\\cloudfunctions.v2beta.json',
   'DATA'),
  ('googleapiclient\\discovery_cache\\documents\\drive.v2.json',
   'c:\\Users\\<USER>\\Desktop\\Mes_projets\\WritingToolsClean\\Windows_and_Linux\\myvenv\\Lib\\site-packages\\googleapiclient\\discovery_cache\\documents\\drive.v2.json',
   'DATA'),
  ('googleapiclient\\discovery_cache\\documents\\metastore.v2.json',
   'c:\\Users\\<USER>\\Desktop\\Mes_projets\\WritingToolsClean\\Windows_and_Linux\\myvenv\\Lib\\site-packages\\googleapiclient\\discovery_cache\\documents\\metastore.v2.json',
   'DATA'),
  ('googleapiclient\\discovery_cache\\documents\\slides.v1.json',
   'c:\\Users\\<USER>\\Desktop\\Mes_projets\\WritingToolsClean\\Windows_and_Linux\\myvenv\\Lib\\site-packages\\googleapiclient\\discovery_cache\\documents\\slides.v1.json',
   'DATA'),
  ('googleapiclient\\discovery_cache\\documents\\osconfig.v2.json',
   'c:\\Users\\<USER>\\Desktop\\Mes_projets\\WritingToolsClean\\Windows_and_Linux\\myvenv\\Lib\\site-packages\\googleapiclient\\discovery_cache\\documents\\osconfig.v2.json',
   'DATA'),
  ('googleapiclient\\discovery_cache\\documents\\apigateway.v1beta.json',
   'c:\\Users\\<USER>\\Desktop\\Mes_projets\\WritingToolsClean\\Windows_and_Linux\\myvenv\\Lib\\site-packages\\googleapiclient\\discovery_cache\\documents\\apigateway.v1beta.json',
   'DATA'),
  ('googleapiclient\\discovery_cache\\documents\\osconfig.v1alpha.json',
   'c:\\Users\\<USER>\\Desktop\\Mes_projets\\WritingToolsClean\\Windows_and_Linux\\myvenv\\Lib\\site-packages\\googleapiclient\\discovery_cache\\documents\\osconfig.v1alpha.json',
   'DATA'),
  ('googleapiclient\\discovery_cache\\documents\\remotebuildexecution.v1alpha.json',
   'c:\\Users\\<USER>\\Desktop\\Mes_projets\\WritingToolsClean\\Windows_and_Linux\\myvenv\\Lib\\site-packages\\googleapiclient\\discovery_cache\\documents\\remotebuildexecution.v1alpha.json',
   'DATA'),
  ('googleapiclient\\discovery_cache\\documents\\translate.v3.json',
   'c:\\Users\\<USER>\\Desktop\\Mes_projets\\WritingToolsClean\\Windows_and_Linux\\myvenv\\Lib\\site-packages\\googleapiclient\\discovery_cache\\documents\\translate.v3.json',
   'DATA'),
  ('googleapiclient\\discovery_cache\\documents\\gkebackup.v1.json',
   'c:\\Users\\<USER>\\Desktop\\Mes_projets\\WritingToolsClean\\Windows_and_Linux\\myvenv\\Lib\\site-packages\\googleapiclient\\discovery_cache\\documents\\gkebackup.v1.json',
   'DATA'),
  ('googleapiclient\\discovery_cache\\documents\\adsenseplatform.v1.json',
   'c:\\Users\\<USER>\\Desktop\\Mes_projets\\WritingToolsClean\\Windows_and_Linux\\myvenv\\Lib\\site-packages\\googleapiclient\\discovery_cache\\documents\\adsenseplatform.v1.json',
   'DATA'),
  ('googleapiclient\\discovery_cache\\documents\\monitoring.v3.json',
   'c:\\Users\\<USER>\\Desktop\\Mes_projets\\WritingToolsClean\\Windows_and_Linux\\myvenv\\Lib\\site-packages\\googleapiclient\\discovery_cache\\documents\\monitoring.v3.json',
   'DATA'),
  ('googleapiclient\\discovery_cache\\documents\\localservices.v1.json',
   'c:\\Users\\<USER>\\Desktop\\Mes_projets\\WritingToolsClean\\Windows_and_Linux\\myvenv\\Lib\\site-packages\\googleapiclient\\discovery_cache\\documents\\localservices.v1.json',
   'DATA'),
  ('googleapiclient\\discovery_cache\\documents\\admob.v1.json',
   'c:\\Users\\<USER>\\Desktop\\Mes_projets\\WritingToolsClean\\Windows_and_Linux\\myvenv\\Lib\\site-packages\\googleapiclient\\discovery_cache\\documents\\admob.v1.json',
   'DATA'),
  ('googleapiclient\\discovery_cache\\documents\\managedkafka.v1.json',
   'c:\\Users\\<USER>\\Desktop\\Mes_projets\\WritingToolsClean\\Windows_and_Linux\\myvenv\\Lib\\site-packages\\googleapiclient\\discovery_cache\\documents\\managedkafka.v1.json',
   'DATA'),
  ('googleapiclient\\discovery_cache\\documents\\clouddebugger.v2.json',
   'c:\\Users\\<USER>\\Desktop\\Mes_projets\\WritingToolsClean\\Windows_and_Linux\\myvenv\\Lib\\site-packages\\googleapiclient\\discovery_cache\\documents\\clouddebugger.v2.json',
   'DATA'),
  ('googleapiclient\\discovery_cache\\documents\\iam.v2.json',
   'c:\\Users\\<USER>\\Desktop\\Mes_projets\\WritingToolsClean\\Windows_and_Linux\\myvenv\\Lib\\site-packages\\googleapiclient\\discovery_cache\\documents\\iam.v2.json',
   'DATA'),
  ('googleapiclient\\discovery_cache\\documents\\eventarc.v1beta1.json',
   'c:\\Users\\<USER>\\Desktop\\Mes_projets\\WritingToolsClean\\Windows_and_Linux\\myvenv\\Lib\\site-packages\\googleapiclient\\discovery_cache\\documents\\eventarc.v1beta1.json',
   'DATA'),
  ('googleapiclient\\discovery_cache\\documents\\dns.v1.json',
   'c:\\Users\\<USER>\\Desktop\\Mes_projets\\WritingToolsClean\\Windows_and_Linux\\myvenv\\Lib\\site-packages\\googleapiclient\\discovery_cache\\documents\\dns.v1.json',
   'DATA'),
  ('googleapiclient\\discovery_cache\\documents\\bigtableadmin.v1.json',
   'c:\\Users\\<USER>\\Desktop\\Mes_projets\\WritingToolsClean\\Windows_and_Linux\\myvenv\\Lib\\site-packages\\googleapiclient\\discovery_cache\\documents\\bigtableadmin.v1.json',
   'DATA'),
  ('googleapiclient\\discovery_cache\\documents\\domains.v1alpha2.json',
   'c:\\Users\\<USER>\\Desktop\\Mes_projets\\WritingToolsClean\\Windows_and_Linux\\myvenv\\Lib\\site-packages\\googleapiclient\\discovery_cache\\documents\\domains.v1alpha2.json',
   'DATA'),
  ('googleapiclient\\discovery_cache\\documents\\places.v1.json',
   'c:\\Users\\<USER>\\Desktop\\Mes_projets\\WritingToolsClean\\Windows_and_Linux\\myvenv\\Lib\\site-packages\\googleapiclient\\discovery_cache\\documents\\places.v1.json',
   'DATA'),
  ('googleapiclient\\discovery_cache\\documents\\servicemanagement.v1.json',
   'c:\\Users\\<USER>\\Desktop\\Mes_projets\\WritingToolsClean\\Windows_and_Linux\\myvenv\\Lib\\site-packages\\googleapiclient\\discovery_cache\\documents\\servicemanagement.v1.json',
   'DATA'),
  ('googleapiclient\\discovery_cache\\documents\\displayvideo.v4.json',
   'c:\\Users\\<USER>\\Desktop\\Mes_projets\\WritingToolsClean\\Windows_and_Linux\\myvenv\\Lib\\site-packages\\googleapiclient\\discovery_cache\\documents\\displayvideo.v4.json',
   'DATA'),
  ('googleapiclient\\discovery_cache\\documents\\accessapproval.v1.json',
   'c:\\Users\\<USER>\\Desktop\\Mes_projets\\WritingToolsClean\\Windows_and_Linux\\myvenv\\Lib\\site-packages\\googleapiclient\\discovery_cache\\documents\\accessapproval.v1.json',
   'DATA'),
  ('googleapiclient\\discovery_cache\\documents\\adsenseplatform.v1alpha.json',
   'c:\\Users\\<USER>\\Desktop\\Mes_projets\\WritingToolsClean\\Windows_and_Linux\\myvenv\\Lib\\site-packages\\googleapiclient\\discovery_cache\\documents\\adsenseplatform.v1alpha.json',
   'DATA'),
  ('googleapiclient\\discovery_cache\\documents\\videointelligence.v1p2beta1.json',
   'c:\\Users\\<USER>\\Desktop\\Mes_projets\\WritingToolsClean\\Windows_and_Linux\\myvenv\\Lib\\site-packages\\googleapiclient\\discovery_cache\\documents\\videointelligence.v1p2beta1.json',
   'DATA'),
  ('googleapiclient\\discovery_cache\\documents\\networkmanagement.v1.json',
   'c:\\Users\\<USER>\\Desktop\\Mes_projets\\WritingToolsClean\\Windows_and_Linux\\myvenv\\Lib\\site-packages\\googleapiclient\\discovery_cache\\documents\\networkmanagement.v1.json',
   'DATA'),
  ('googleapiclient\\discovery_cache\\documents\\policytroubleshooter.v1beta.json',
   'c:\\Users\\<USER>\\Desktop\\Mes_projets\\WritingToolsClean\\Windows_and_Linux\\myvenv\\Lib\\site-packages\\googleapiclient\\discovery_cache\\documents\\policytroubleshooter.v1beta.json',
   'DATA'),
  ('googleapiclient\\discovery_cache\\documents\\solar.v1.json',
   'c:\\Users\\<USER>\\Desktop\\Mes_projets\\WritingToolsClean\\Windows_and_Linux\\myvenv\\Lib\\site-packages\\googleapiclient\\discovery_cache\\documents\\solar.v1.json',
   'DATA'),
  ('googleapiclient\\discovery_cache\\documents\\container.v1beta1.json',
   'c:\\Users\\<USER>\\Desktop\\Mes_projets\\WritingToolsClean\\Windows_and_Linux\\myvenv\\Lib\\site-packages\\googleapiclient\\discovery_cache\\documents\\container.v1beta1.json',
   'DATA'),
  ('googleapiclient\\discovery_cache\\documents\\fcmdata.v1beta1.json',
   'c:\\Users\\<USER>\\Desktop\\Mes_projets\\WritingToolsClean\\Windows_and_Linux\\myvenv\\Lib\\site-packages\\googleapiclient\\discovery_cache\\documents\\fcmdata.v1beta1.json',
   'DATA'),
  ('googleapiclient\\discovery_cache\\documents\\websecurityscanner.v1alpha.json',
   'c:\\Users\\<USER>\\Desktop\\Mes_projets\\WritingToolsClean\\Windows_and_Linux\\myvenv\\Lib\\site-packages\\googleapiclient\\discovery_cache\\documents\\websecurityscanner.v1alpha.json',
   'DATA'),
  ('googleapiclient\\discovery_cache\\documents\\cloudbuild.v1beta1.json',
   'c:\\Users\\<USER>\\Desktop\\Mes_projets\\WritingToolsClean\\Windows_and_Linux\\myvenv\\Lib\\site-packages\\googleapiclient\\discovery_cache\\documents\\cloudbuild.v1beta1.json',
   'DATA'),
  ('googleapiclient\\discovery_cache\\documents\\books.v1.json',
   'c:\\Users\\<USER>\\Desktop\\Mes_projets\\WritingToolsClean\\Windows_and_Linux\\myvenv\\Lib\\site-packages\\googleapiclient\\discovery_cache\\documents\\books.v1.json',
   'DATA'),
  ('googleapiclient\\discovery_cache\\documents\\vmmigration.v1alpha1.json',
   'c:\\Users\\<USER>\\Desktop\\Mes_projets\\WritingToolsClean\\Windows_and_Linux\\myvenv\\Lib\\site-packages\\googleapiclient\\discovery_cache\\documents\\vmmigration.v1alpha1.json',
   'DATA'),
  ('googleapiclient\\discovery_cache\\documents\\walletobjects.v1.json',
   'c:\\Users\\<USER>\\Desktop\\Mes_projets\\WritingToolsClean\\Windows_and_Linux\\myvenv\\Lib\\site-packages\\googleapiclient\\discovery_cache\\documents\\walletobjects.v1.json',
   'DATA'),
  ('google_api_python_client-2.177.0.dist-info\\METADATA',
   'c:\\Users\\<USER>\\Desktop\\Mes_projets\\WritingToolsClean\\Windows_and_Linux\\myvenv\\Lib\\site-packages\\google_api_python_client-2.177.0.dist-info\\METADATA',
   'DATA'),
  ('googleapiclient\\discovery_cache\\documents\\vmwareengine.v1.json',
   'c:\\Users\\<USER>\\Desktop\\Mes_projets\\WritingToolsClean\\Windows_and_Linux\\myvenv\\Lib\\site-packages\\googleapiclient\\discovery_cache\\documents\\vmwareengine.v1.json',
   'DATA'),
  ('googleapiclient\\discovery_cache\\documents\\iap.v1beta1.json',
   'c:\\Users\\<USER>\\Desktop\\Mes_projets\\WritingToolsClean\\Windows_and_Linux\\myvenv\\Lib\\site-packages\\googleapiclient\\discovery_cache\\documents\\iap.v1beta1.json',
   'DATA'),
  ('googleapiclient\\discovery_cache\\documents\\paymentsresellersubscription.v1.json',
   'c:\\Users\\<USER>\\Desktop\\Mes_projets\\WritingToolsClean\\Windows_and_Linux\\myvenv\\Lib\\site-packages\\googleapiclient\\discovery_cache\\documents\\paymentsresellersubscription.v1.json',
   'DATA'),
  ('googleapiclient\\discovery_cache\\documents\\realtimebidding.v1.json',
   'c:\\Users\\<USER>\\Desktop\\Mes_projets\\WritingToolsClean\\Windows_and_Linux\\myvenv\\Lib\\site-packages\\googleapiclient\\discovery_cache\\documents\\realtimebidding.v1.json',
   'DATA'),
  ('googleapiclient\\discovery_cache\\documents\\pollen.v1.json',
   'c:\\Users\\<USER>\\Desktop\\Mes_projets\\WritingToolsClean\\Windows_and_Linux\\myvenv\\Lib\\site-packages\\googleapiclient\\discovery_cache\\documents\\pollen.v1.json',
   'DATA'),
  ('googleapiclient\\discovery_cache\\documents\\servicecontrol.v2.json',
   'c:\\Users\\<USER>\\Desktop\\Mes_projets\\WritingToolsClean\\Windows_and_Linux\\myvenv\\Lib\\site-packages\\googleapiclient\\discovery_cache\\documents\\servicecontrol.v2.json',
   'DATA'),
  ('googleapiclient\\discovery_cache\\documents\\memcache.v1beta2.json',
   'c:\\Users\\<USER>\\Desktop\\Mes_projets\\WritingToolsClean\\Windows_and_Linux\\myvenv\\Lib\\site-packages\\googleapiclient\\discovery_cache\\documents\\memcache.v1beta2.json',
   'DATA'),
  ('googleapiclient\\discovery_cache\\documents\\cloudscheduler.v1beta1.json',
   'c:\\Users\\<USER>\\Desktop\\Mes_projets\\WritingToolsClean\\Windows_and_Linux\\myvenv\\Lib\\site-packages\\googleapiclient\\discovery_cache\\documents\\cloudscheduler.v1beta1.json',
   'DATA'),
  ('googleapiclient\\discovery_cache\\documents\\apigateway.v1.json',
   'c:\\Users\\<USER>\\Desktop\\Mes_projets\\WritingToolsClean\\Windows_and_Linux\\myvenv\\Lib\\site-packages\\googleapiclient\\discovery_cache\\documents\\apigateway.v1.json',
   'DATA'),
  ('googleapiclient\\discovery_cache\\documents\\versionhistory.v1.json',
   'c:\\Users\\<USER>\\Desktop\\Mes_projets\\WritingToolsClean\\Windows_and_Linux\\myvenv\\Lib\\site-packages\\googleapiclient\\discovery_cache\\documents\\versionhistory.v1.json',
   'DATA'),
  ('googleapiclient\\discovery_cache\\documents\\publicca.v1.json',
   'c:\\Users\\<USER>\\Desktop\\Mes_projets\\WritingToolsClean\\Windows_and_Linux\\myvenv\\Lib\\site-packages\\googleapiclient\\discovery_cache\\documents\\publicca.v1.json',
   'DATA'),
  ('googleapiclient\\discovery_cache\\documents\\developerconnect.v1.json',
   'c:\\Users\\<USER>\\Desktop\\Mes_projets\\WritingToolsClean\\Windows_and_Linux\\myvenv\\Lib\\site-packages\\googleapiclient\\discovery_cache\\documents\\developerconnect.v1.json',
   'DATA'),
  ('googleapiclient\\discovery_cache\\documents\\datacatalog.v1beta1.json',
   'c:\\Users\\<USER>\\Desktop\\Mes_projets\\WritingToolsClean\\Windows_and_Linux\\myvenv\\Lib\\site-packages\\googleapiclient\\discovery_cache\\documents\\datacatalog.v1beta1.json',
   'DATA'),
  ('googleapiclient\\discovery_cache\\documents\\bigqueryreservation.v1.json',
   'c:\\Users\\<USER>\\Desktop\\Mes_projets\\WritingToolsClean\\Windows_and_Linux\\myvenv\\Lib\\site-packages\\googleapiclient\\discovery_cache\\documents\\bigqueryreservation.v1.json',
   'DATA'),
  ('googleapiclient\\discovery_cache\\documents\\admin.datatransferv1.json',
   'c:\\Users\\<USER>\\Desktop\\Mes_projets\\WritingToolsClean\\Windows_and_Linux\\myvenv\\Lib\\site-packages\\googleapiclient\\discovery_cache\\documents\\admin.datatransferv1.json',
   'DATA'),
  ('googleapiclient\\discovery_cache\\documents\\playcustomapp.v1.json',
   'c:\\Users\\<USER>\\Desktop\\Mes_projets\\WritingToolsClean\\Windows_and_Linux\\myvenv\\Lib\\site-packages\\googleapiclient\\discovery_cache\\documents\\playcustomapp.v1.json',
   'DATA'),
  ('googleapiclient\\discovery_cache\\documents\\blogger.v3.json',
   'c:\\Users\\<USER>\\Desktop\\Mes_projets\\WritingToolsClean\\Windows_and_Linux\\myvenv\\Lib\\site-packages\\googleapiclient\\discovery_cache\\documents\\blogger.v3.json',
   'DATA'),
  ('googleapiclient\\discovery_cache\\documents\\searchconsole.v1.json',
   'c:\\Users\\<USER>\\Desktop\\Mes_projets\\WritingToolsClean\\Windows_and_Linux\\myvenv\\Lib\\site-packages\\googleapiclient\\discovery_cache\\documents\\searchconsole.v1.json',
   'DATA'),
  ('googleapiclient\\discovery_cache\\documents\\policysimulator.v1.json',
   'c:\\Users\\<USER>\\Desktop\\Mes_projets\\WritingToolsClean\\Windows_and_Linux\\myvenv\\Lib\\site-packages\\googleapiclient\\discovery_cache\\documents\\policysimulator.v1.json',
   'DATA'),
  ('googleapiclient\\discovery_cache\\documents\\securityposture.v1.json',
   'c:\\Users\\<USER>\\Desktop\\Mes_projets\\WritingToolsClean\\Windows_and_Linux\\myvenv\\Lib\\site-packages\\googleapiclient\\discovery_cache\\documents\\securityposture.v1.json',
   'DATA'),
  ('googleapiclient\\discovery_cache\\documents\\metastore.v2beta.json',
   'c:\\Users\\<USER>\\Desktop\\Mes_projets\\WritingToolsClean\\Windows_and_Linux\\myvenv\\Lib\\site-packages\\googleapiclient\\discovery_cache\\documents\\metastore.v2beta.json',
   'DATA'),
  ('googleapiclient\\discovery_cache\\documents\\batch.v1.json',
   'c:\\Users\\<USER>\\Desktop\\Mes_projets\\WritingToolsClean\\Windows_and_Linux\\myvenv\\Lib\\site-packages\\googleapiclient\\discovery_cache\\documents\\batch.v1.json',
   'DATA'),
  ('googleapiclient\\discovery_cache\\documents\\mybusinessbusinessinformation.v1.json',
   'c:\\Users\\<USER>\\Desktop\\Mes_projets\\WritingToolsClean\\Windows_and_Linux\\myvenv\\Lib\\site-packages\\googleapiclient\\discovery_cache\\documents\\mybusinessbusinessinformation.v1.json',
   'DATA'),
  ('googleapiclient\\discovery_cache\\documents\\securitycenter.v1beta2.json',
   'c:\\Users\\<USER>\\Desktop\\Mes_projets\\WritingToolsClean\\Windows_and_Linux\\myvenv\\Lib\\site-packages\\googleapiclient\\discovery_cache\\documents\\securitycenter.v1beta2.json',
   'DATA'),
  ('googleapiclient\\discovery_cache\\documents\\cloudidentity.v1.json',
   'c:\\Users\\<USER>\\Desktop\\Mes_projets\\WritingToolsClean\\Windows_and_Linux\\myvenv\\Lib\\site-packages\\googleapiclient\\discovery_cache\\documents\\cloudidentity.v1.json',
   'DATA'),
  ('googleapiclient\\discovery_cache\\documents\\blogger.v2.json',
   'c:\\Users\\<USER>\\Desktop\\Mes_projets\\WritingToolsClean\\Windows_and_Linux\\myvenv\\Lib\\site-packages\\googleapiclient\\discovery_cache\\documents\\blogger.v2.json',
   'DATA'),
  ('googleapiclient\\discovery_cache\\documents\\mybusinessplaceactions.v1.json',
   'c:\\Users\\<USER>\\Desktop\\Mes_projets\\WritingToolsClean\\Windows_and_Linux\\myvenv\\Lib\\site-packages\\googleapiclient\\discovery_cache\\documents\\mybusinessplaceactions.v1.json',
   'DATA'),
  ('googleapiclient\\discovery_cache\\documents\\servicedirectory.v1.json',
   'c:\\Users\\<USER>\\Desktop\\Mes_projets\\WritingToolsClean\\Windows_and_Linux\\myvenv\\Lib\\site-packages\\googleapiclient\\discovery_cache\\documents\\servicedirectory.v1.json',
   'DATA'),
  ('googleapiclient\\discovery_cache\\documents\\indexing.v3.json',
   'c:\\Users\\<USER>\\Desktop\\Mes_projets\\WritingToolsClean\\Windows_and_Linux\\myvenv\\Lib\\site-packages\\googleapiclient\\discovery_cache\\documents\\indexing.v3.json',
   'DATA'),
  ('googleapiclient\\discovery_cache\\documents\\firebaseappcheck.v1beta.json',
   'c:\\Users\\<USER>\\Desktop\\Mes_projets\\WritingToolsClean\\Windows_and_Linux\\myvenv\\Lib\\site-packages\\googleapiclient\\discovery_cache\\documents\\firebaseappcheck.v1beta.json',
   'DATA'),
  ('googleapiclient\\discovery_cache\\documents\\gkeonprem.v1.json',
   'c:\\Users\\<USER>\\Desktop\\Mes_projets\\WritingToolsClean\\Windows_and_Linux\\myvenv\\Lib\\site-packages\\googleapiclient\\discovery_cache\\documents\\gkeonprem.v1.json',
   'DATA'),
  ('googleapiclient\\discovery_cache\\documents\\merchantapi.quota_v1beta.json',
   'c:\\Users\\<USER>\\Desktop\\Mes_projets\\WritingToolsClean\\Windows_and_Linux\\myvenv\\Lib\\site-packages\\googleapiclient\\discovery_cache\\documents\\merchantapi.quota_v1beta.json',
   'DATA'),
  ('googleapiclient\\discovery_cache\\documents\\firebasedatabase.v1beta.json',
   'c:\\Users\\<USER>\\Desktop\\Mes_projets\\WritingToolsClean\\Windows_and_Linux\\myvenv\\Lib\\site-packages\\googleapiclient\\discovery_cache\\documents\\firebasedatabase.v1beta.json',
   'DATA'),
  ('googleapiclient\\discovery_cache\\documents\\displayvideo.v1.json',
   'c:\\Users\\<USER>\\Desktop\\Mes_projets\\WritingToolsClean\\Windows_and_Linux\\myvenv\\Lib\\site-packages\\googleapiclient\\discovery_cache\\documents\\displayvideo.v1.json',
   'DATA'),
  ('googleapiclient\\discovery_cache\\documents\\index.json',
   'c:\\Users\\<USER>\\Desktop\\Mes_projets\\WritingToolsClean\\Windows_and_Linux\\myvenv\\Lib\\site-packages\\googleapiclient\\discovery_cache\\documents\\index.json',
   'DATA'),
  ('googleapiclient\\discovery_cache\\documents\\cloudsearch.v1.json',
   'c:\\Users\\<USER>\\Desktop\\Mes_projets\\WritingToolsClean\\Windows_and_Linux\\myvenv\\Lib\\site-packages\\googleapiclient\\discovery_cache\\documents\\cloudsearch.v1.json',
   'DATA'),
  ('googleapiclient\\discovery_cache\\documents\\managedidentities.v1alpha1.json',
   'c:\\Users\\<USER>\\Desktop\\Mes_projets\\WritingToolsClean\\Windows_and_Linux\\myvenv\\Lib\\site-packages\\googleapiclient\\discovery_cache\\documents\\managedidentities.v1alpha1.json',
   'DATA'),
  ('googleapiclient\\discovery_cache\\documents\\merchantapi.notifications_v1beta.json',
   'c:\\Users\\<USER>\\Desktop\\Mes_projets\\WritingToolsClean\\Windows_and_Linux\\myvenv\\Lib\\site-packages\\googleapiclient\\discovery_cache\\documents\\merchantapi.notifications_v1beta.json',
   'DATA'),
  ('googleapiclient\\discovery_cache\\documents\\identitytoolkit.v2.json',
   'c:\\Users\\<USER>\\Desktop\\Mes_projets\\WritingToolsClean\\Windows_and_Linux\\myvenv\\Lib\\site-packages\\googleapiclient\\discovery_cache\\documents\\identitytoolkit.v2.json',
   'DATA'),
  ('googleapiclient\\discovery_cache\\documents\\chromepolicy.v1.json',
   'c:\\Users\\<USER>\\Desktop\\Mes_projets\\WritingToolsClean\\Windows_and_Linux\\myvenv\\Lib\\site-packages\\googleapiclient\\discovery_cache\\documents\\chromepolicy.v1.json',
   'DATA'),
  ('googleapiclient\\discovery_cache\\documents\\acceleratedmobilepageurl.v1.json',
   'c:\\Users\\<USER>\\Desktop\\Mes_projets\\WritingToolsClean\\Windows_and_Linux\\myvenv\\Lib\\site-packages\\googleapiclient\\discovery_cache\\documents\\acceleratedmobilepageurl.v1.json',
   'DATA'),
  ('googleapiclient\\discovery_cache\\documents\\videointelligence.v1p3beta1.json',
   'c:\\Users\\<USER>\\Desktop\\Mes_projets\\WritingToolsClean\\Windows_and_Linux\\myvenv\\Lib\\site-packages\\googleapiclient\\discovery_cache\\documents\\videointelligence.v1p3beta1.json',
   'DATA'),
  ('googleapiclient\\discovery_cache\\documents\\merchantapi.inventories_v1beta.json',
   'c:\\Users\\<USER>\\Desktop\\Mes_projets\\WritingToolsClean\\Windows_and_Linux\\myvenv\\Lib\\site-packages\\googleapiclient\\discovery_cache\\documents\\merchantapi.inventories_v1beta.json',
   'DATA'),
  ('googleapiclient\\discovery_cache\\documents\\orgpolicy.v2.json',
   'c:\\Users\\<USER>\\Desktop\\Mes_projets\\WritingToolsClean\\Windows_and_Linux\\myvenv\\Lib\\site-packages\\googleapiclient\\discovery_cache\\documents\\orgpolicy.v2.json',
   'DATA'),
  ('googleapiclient\\discovery_cache\\documents\\discovery.v1.json',
   'c:\\Users\\<USER>\\Desktop\\Mes_projets\\WritingToolsClean\\Windows_and_Linux\\myvenv\\Lib\\site-packages\\googleapiclient\\discovery_cache\\documents\\discovery.v1.json',
   'DATA'),
  ('googleapiclient\\discovery_cache\\documents\\cloudresourcemanager.v2beta1.json',
   'c:\\Users\\<USER>\\Desktop\\Mes_projets\\WritingToolsClean\\Windows_and_Linux\\myvenv\\Lib\\site-packages\\googleapiclient\\discovery_cache\\documents\\cloudresourcemanager.v2beta1.json',
   'DATA'),
  ('googleapiclient\\discovery_cache\\documents\\content.v2.json',
   'c:\\Users\\<USER>\\Desktop\\Mes_projets\\WritingToolsClean\\Windows_and_Linux\\myvenv\\Lib\\site-packages\\googleapiclient\\discovery_cache\\documents\\content.v2.json',
   'DATA'),
  ('googleapiclient\\discovery_cache\\documents\\doubleclicksearch.v2.json',
   'c:\\Users\\<USER>\\Desktop\\Mes_projets\\WritingToolsClean\\Windows_and_Linux\\myvenv\\Lib\\site-packages\\googleapiclient\\discovery_cache\\documents\\doubleclicksearch.v2.json',
   'DATA'),
  ('googleapiclient\\discovery_cache\\documents\\sts.v1.json',
   'c:\\Users\\<USER>\\Desktop\\Mes_projets\\WritingToolsClean\\Windows_and_Linux\\myvenv\\Lib\\site-packages\\googleapiclient\\discovery_cache\\documents\\sts.v1.json',
   'DATA'),
  ('googleapiclient\\discovery_cache\\documents\\workflows.v1beta.json',
   'c:\\Users\\<USER>\\Desktop\\Mes_projets\\WritingToolsClean\\Windows_and_Linux\\myvenv\\Lib\\site-packages\\googleapiclient\\discovery_cache\\documents\\workflows.v1beta.json',
   'DATA'),
  ('googleapiclient\\discovery_cache\\documents\\analyticsadmin.v1alpha.json',
   'c:\\Users\\<USER>\\Desktop\\Mes_projets\\WritingToolsClean\\Windows_and_Linux\\myvenv\\Lib\\site-packages\\googleapiclient\\discovery_cache\\documents\\analyticsadmin.v1alpha.json',
   'DATA'),
  ('googleapiclient\\discovery_cache\\documents\\cloudfunctions.v1.json',
   'c:\\Users\\<USER>\\Desktop\\Mes_projets\\WritingToolsClean\\Windows_and_Linux\\myvenv\\Lib\\site-packages\\googleapiclient\\discovery_cache\\documents\\cloudfunctions.v1.json',
   'DATA'),
  ('googleapiclient\\discovery_cache\\documents\\language.v1beta2.json',
   'c:\\Users\\<USER>\\Desktop\\Mes_projets\\WritingToolsClean\\Windows_and_Linux\\myvenv\\Lib\\site-packages\\googleapiclient\\discovery_cache\\documents\\language.v1beta2.json',
   'DATA'),
  ('googleapiclient\\discovery_cache\\documents\\alloydb.v1alpha.json',
   'c:\\Users\\<USER>\\Desktop\\Mes_projets\\WritingToolsClean\\Windows_and_Linux\\myvenv\\Lib\\site-packages\\googleapiclient\\discovery_cache\\documents\\alloydb.v1alpha.json',
   'DATA'),
  ('googleapiclient\\discovery_cache\\documents\\networksecurity.v1.json',
   'c:\\Users\\<USER>\\Desktop\\Mes_projets\\WritingToolsClean\\Windows_and_Linux\\myvenv\\Lib\\site-packages\\googleapiclient\\discovery_cache\\documents\\networksecurity.v1.json',
   'DATA'),
  ('googleapiclient\\discovery_cache\\documents\\deploymentmanager.v2.json',
   'c:\\Users\\<USER>\\Desktop\\Mes_projets\\WritingToolsClean\\Windows_and_Linux\\myvenv\\Lib\\site-packages\\googleapiclient\\discovery_cache\\documents\\deploymentmanager.v2.json',
   'DATA'),
  ('googleapiclient\\discovery_cache\\documents\\analyticshub.v1beta1.json',
   'c:\\Users\\<USER>\\Desktop\\Mes_projets\\WritingToolsClean\\Windows_and_Linux\\myvenv\\Lib\\site-packages\\googleapiclient\\discovery_cache\\documents\\analyticshub.v1beta1.json',
   'DATA'),
  ('googleapiclient\\discovery_cache\\documents\\firebasedynamiclinks.v1.json',
   'c:\\Users\\<USER>\\Desktop\\Mes_projets\\WritingToolsClean\\Windows_and_Linux\\myvenv\\Lib\\site-packages\\googleapiclient\\discovery_cache\\documents\\firebasedynamiclinks.v1.json',
   'DATA'),
  ('googleapiclient\\discovery_cache\\documents\\recommender.v1beta1.json',
   'c:\\Users\\<USER>\\Desktop\\Mes_projets\\WritingToolsClean\\Windows_and_Linux\\myvenv\\Lib\\site-packages\\googleapiclient\\discovery_cache\\documents\\recommender.v1beta1.json',
   'DATA'),
  ('googleapiclient\\discovery_cache\\documents\\cloudasset.v1p5beta1.json',
   'c:\\Users\\<USER>\\Desktop\\Mes_projets\\WritingToolsClean\\Windows_and_Linux\\myvenv\\Lib\\site-packages\\googleapiclient\\discovery_cache\\documents\\cloudasset.v1p5beta1.json',
   'DATA'),
  ('googleapiclient\\discovery_cache\\documents\\tpu.v1alpha1.json',
   'c:\\Users\\<USER>\\Desktop\\Mes_projets\\WritingToolsClean\\Windows_and_Linux\\myvenv\\Lib\\site-packages\\googleapiclient\\discovery_cache\\documents\\tpu.v1alpha1.json',
   'DATA'),
  ('googleapiclient\\discovery_cache\\documents\\groupsmigration.v1.json',
   'c:\\Users\\<USER>\\Desktop\\Mes_projets\\WritingToolsClean\\Windows_and_Linux\\myvenv\\Lib\\site-packages\\googleapiclient\\discovery_cache\\documents\\groupsmigration.v1.json',
   'DATA'),
  ('googleapiclient\\discovery_cache\\documents\\verifiedaccess.v2.json',
   'c:\\Users\\<USER>\\Desktop\\Mes_projets\\WritingToolsClean\\Windows_and_Linux\\myvenv\\Lib\\site-packages\\googleapiclient\\discovery_cache\\documents\\verifiedaccess.v2.json',
   'DATA'),
  ('googleapiclient\\discovery_cache\\documents\\compute.beta.json',
   'c:\\Users\\<USER>\\Desktop\\Mes_projets\\WritingToolsClean\\Windows_and_Linux\\myvenv\\Lib\\site-packages\\googleapiclient\\discovery_cache\\documents\\compute.beta.json',
   'DATA'),
  ('googleapiclient\\discovery_cache\\documents\\driveactivity.v2.json',
   'c:\\Users\\<USER>\\Desktop\\Mes_projets\\WritingToolsClean\\Windows_and_Linux\\myvenv\\Lib\\site-packages\\googleapiclient\\discovery_cache\\documents\\driveactivity.v2.json',
   'DATA'),
  ('googleapiclient\\discovery_cache\\documents\\alloydb.v1beta.json',
   'c:\\Users\\<USER>\\Desktop\\Mes_projets\\WritingToolsClean\\Windows_and_Linux\\myvenv\\Lib\\site-packages\\googleapiclient\\discovery_cache\\documents\\alloydb.v1beta.json',
   'DATA'),
  ('googleapiclient\\discovery_cache\\documents\\datalabeling.v1beta1.json',
   'c:\\Users\\<USER>\\Desktop\\Mes_projets\\WritingToolsClean\\Windows_and_Linux\\myvenv\\Lib\\site-packages\\googleapiclient\\discovery_cache\\documents\\datalabeling.v1beta1.json',
   'DATA'),
  ('googleapiclient\\discovery_cache\\documents\\dfareporting.v3.5.json',
   'c:\\Users\\<USER>\\Desktop\\Mes_projets\\WritingToolsClean\\Windows_and_Linux\\myvenv\\Lib\\site-packages\\googleapiclient\\discovery_cache\\documents\\dfareporting.v3.5.json',
   'DATA'),
  ('googleapiclient\\discovery_cache\\documents\\apigee.v1.json',
   'c:\\Users\\<USER>\\Desktop\\Mes_projets\\WritingToolsClean\\Windows_and_Linux\\myvenv\\Lib\\site-packages\\googleapiclient\\discovery_cache\\documents\\apigee.v1.json',
   'DATA'),
  ('googleapiclient\\discovery_cache\\documents\\firebaseappcheck.v1.json',
   'c:\\Users\\<USER>\\Desktop\\Mes_projets\\WritingToolsClean\\Windows_and_Linux\\myvenv\\Lib\\site-packages\\googleapiclient\\discovery_cache\\documents\\firebaseappcheck.v1.json',
   'DATA'),
  ('googleapiclient\\discovery_cache\\documents\\beyondcorp.v1alpha.json',
   'c:\\Users\\<USER>\\Desktop\\Mes_projets\\WritingToolsClean\\Windows_and_Linux\\myvenv\\Lib\\site-packages\\googleapiclient\\discovery_cache\\documents\\beyondcorp.v1alpha.json',
   'DATA'),
  ('googleapiclient\\discovery_cache\\documents\\civicinfo.v2.json',
   'c:\\Users\\<USER>\\Desktop\\Mes_projets\\WritingToolsClean\\Windows_and_Linux\\myvenv\\Lib\\site-packages\\googleapiclient\\discovery_cache\\documents\\civicinfo.v2.json',
   'DATA'),
  ('googleapiclient\\discovery_cache\\documents\\searchads360.v0.json',
   'c:\\Users\\<USER>\\Desktop\\Mes_projets\\WritingToolsClean\\Windows_and_Linux\\myvenv\\Lib\\site-packages\\googleapiclient\\discovery_cache\\documents\\searchads360.v0.json',
   'DATA'),
  ('googleapiclient\\discovery_cache\\documents\\advisorynotifications.v1.json',
   'c:\\Users\\<USER>\\Desktop\\Mes_projets\\WritingToolsClean\\Windows_and_Linux\\myvenv\\Lib\\site-packages\\googleapiclient\\discovery_cache\\documents\\advisorynotifications.v1.json',
   'DATA'),
  ('googleapiclient\\discovery_cache\\documents\\area120tables.v1alpha1.json',
   'c:\\Users\\<USER>\\Desktop\\Mes_projets\\WritingToolsClean\\Windows_and_Linux\\myvenv\\Lib\\site-packages\\googleapiclient\\discovery_cache\\documents\\area120tables.v1alpha1.json',
   'DATA'),
  ('googleapiclient\\discovery_cache\\documents\\serviceusage.v1.json',
   'c:\\Users\\<USER>\\Desktop\\Mes_projets\\WritingToolsClean\\Windows_and_Linux\\myvenv\\Lib\\site-packages\\googleapiclient\\discovery_cache\\documents\\serviceusage.v1.json',
   'DATA'),
  ('googleapiclient\\discovery_cache\\documents\\osconfig.v2beta.json',
   'c:\\Users\\<USER>\\Desktop\\Mes_projets\\WritingToolsClean\\Windows_and_Linux\\myvenv\\Lib\\site-packages\\googleapiclient\\discovery_cache\\documents\\osconfig.v2beta.json',
   'DATA'),
  ('googleapiclient\\discovery_cache\\documents\\dataproc.v1.json',
   'c:\\Users\\<USER>\\Desktop\\Mes_projets\\WritingToolsClean\\Windows_and_Linux\\myvenv\\Lib\\site-packages\\googleapiclient\\discovery_cache\\documents\\dataproc.v1.json',
   'DATA'),
  ('googleapiclient\\discovery_cache\\documents\\apphub.v1alpha.json',
   'c:\\Users\\<USER>\\Desktop\\Mes_projets\\WritingToolsClean\\Windows_and_Linux\\myvenv\\Lib\\site-packages\\googleapiclient\\discovery_cache\\documents\\apphub.v1alpha.json',
   'DATA'),
  ('googleapiclient\\discovery_cache\\documents\\areainsights.v1.json',
   'c:\\Users\\<USER>\\Desktop\\Mes_projets\\WritingToolsClean\\Windows_and_Linux\\myvenv\\Lib\\site-packages\\googleapiclient\\discovery_cache\\documents\\areainsights.v1.json',
   'DATA'),
  ('googleapiclient\\discovery_cache\\documents\\migrationcenter.v1.json',
   'c:\\Users\\<USER>\\Desktop\\Mes_projets\\WritingToolsClean\\Windows_and_Linux\\myvenv\\Lib\\site-packages\\googleapiclient\\discovery_cache\\documents\\migrationcenter.v1.json',
   'DATA'),
  ('googleapiclient\\discovery_cache\\documents\\firebaseml.v1.json',
   'c:\\Users\\<USER>\\Desktop\\Mes_projets\\WritingToolsClean\\Windows_and_Linux\\myvenv\\Lib\\site-packages\\googleapiclient\\discovery_cache\\documents\\firebaseml.v1.json',
   'DATA'),
  ('googleapiclient\\discovery_cache\\documents\\config.v1.json',
   'c:\\Users\\<USER>\\Desktop\\Mes_projets\\WritingToolsClean\\Windows_and_Linux\\myvenv\\Lib\\site-packages\\googleapiclient\\discovery_cache\\documents\\config.v1.json',
   'DATA'),
  ('googleapiclient\\discovery_cache\\documents\\managedidentities.v1.json',
   'c:\\Users\\<USER>\\Desktop\\Mes_projets\\WritingToolsClean\\Windows_and_Linux\\myvenv\\Lib\\site-packages\\googleapiclient\\discovery_cache\\documents\\managedidentities.v1.json',
   'DATA'),
  ('googleapiclient\\discovery_cache\\documents\\billingbudgets.v1beta1.json',
   'c:\\Users\\<USER>\\Desktop\\Mes_projets\\WritingToolsClean\\Windows_and_Linux\\myvenv\\Lib\\site-packages\\googleapiclient\\discovery_cache\\documents\\billingbudgets.v1beta1.json',
   'DATA'),
  ('googleapiclient\\discovery_cache\\documents\\sourcerepo.v1.json',
   'c:\\Users\\<USER>\\Desktop\\Mes_projets\\WritingToolsClean\\Windows_and_Linux\\myvenv\\Lib\\site-packages\\googleapiclient\\discovery_cache\\documents\\sourcerepo.v1.json',
   'DATA'),
  ('googleapiclient\\discovery_cache\\documents\\fcm.v1.json',
   'c:\\Users\\<USER>\\Desktop\\Mes_projets\\WritingToolsClean\\Windows_and_Linux\\myvenv\\Lib\\site-packages\\googleapiclient\\discovery_cache\\documents\\fcm.v1.json',
   'DATA'),
  ('googleapiclient\\discovery_cache\\documents\\playablelocations.v3.json',
   'c:\\Users\\<USER>\\Desktop\\Mes_projets\\WritingToolsClean\\Windows_and_Linux\\myvenv\\Lib\\site-packages\\googleapiclient\\discovery_cache\\documents\\playablelocations.v3.json',
   'DATA'),
  ('googleapiclient\\discovery_cache\\documents\\cloudkms.v1.json',
   'c:\\Users\\<USER>\\Desktop\\Mes_projets\\WritingToolsClean\\Windows_and_Linux\\myvenv\\Lib\\site-packages\\googleapiclient\\discovery_cache\\documents\\cloudkms.v1.json',
   'DATA'),
  ('googleapiclient\\discovery_cache\\documents\\containeranalysis.v1alpha1.json',
   'c:\\Users\\<USER>\\Desktop\\Mes_projets\\WritingToolsClean\\Windows_and_Linux\\myvenv\\Lib\\site-packages\\googleapiclient\\discovery_cache\\documents\\containeranalysis.v1alpha1.json',
   'DATA'),
  ('googleapiclient\\discovery_cache\\documents\\tpu.v1.json',
   'c:\\Users\\<USER>\\Desktop\\Mes_projets\\WritingToolsClean\\Windows_and_Linux\\myvenv\\Lib\\site-packages\\googleapiclient\\discovery_cache\\documents\\tpu.v1.json',
   'DATA'),
  ('googleapiclient\\discovery_cache\\documents\\sheets.v4.json',
   'c:\\Users\\<USER>\\Desktop\\Mes_projets\\WritingToolsClean\\Windows_and_Linux\\myvenv\\Lib\\site-packages\\googleapiclient\\discovery_cache\\documents\\sheets.v4.json',
   'DATA'),
  ('googleapiclient\\discovery_cache\\documents\\serviceconsumermanagement.v1beta1.json',
   'c:\\Users\\<USER>\\Desktop\\Mes_projets\\WritingToolsClean\\Windows_and_Linux\\myvenv\\Lib\\site-packages\\googleapiclient\\discovery_cache\\documents\\serviceconsumermanagement.v1beta1.json',
   'DATA'),
  ('googleapiclient\\discovery_cache\\documents\\tasks.v1.json',
   'c:\\Users\\<USER>\\Desktop\\Mes_projets\\WritingToolsClean\\Windows_and_Linux\\myvenv\\Lib\\site-packages\\googleapiclient\\discovery_cache\\documents\\tasks.v1.json',
   'DATA'),
  ('googleapiclient\\discovery_cache\\documents\\gamesManagement.v1management.json',
   'c:\\Users\\<USER>\\Desktop\\Mes_projets\\WritingToolsClean\\Windows_and_Linux\\myvenv\\Lib\\site-packages\\googleapiclient\\discovery_cache\\documents\\gamesManagement.v1management.json',
   'DATA'),
  ('googleapiclient\\discovery_cache\\documents\\dfareporting.v4.json',
   'c:\\Users\\<USER>\\Desktop\\Mes_projets\\WritingToolsClean\\Windows_and_Linux\\myvenv\\Lib\\site-packages\\googleapiclient\\discovery_cache\\documents\\dfareporting.v4.json',
   'DATA'),
  ('googleapiclient\\discovery_cache\\documents\\firebasedataconnect.v1beta.json',
   'c:\\Users\\<USER>\\Desktop\\Mes_projets\\WritingToolsClean\\Windows_and_Linux\\myvenv\\Lib\\site-packages\\googleapiclient\\discovery_cache\\documents\\firebasedataconnect.v1beta.json',
   'DATA'),
  ('googleapiclient\\discovery_cache\\documents\\firebaseapphosting.v1beta.json',
   'c:\\Users\\<USER>\\Desktop\\Mes_projets\\WritingToolsClean\\Windows_and_Linux\\myvenv\\Lib\\site-packages\\googleapiclient\\discovery_cache\\documents\\firebaseapphosting.v1beta.json',
   'DATA'),
  ('googleapiclient\\discovery_cache\\documents\\admin.reports_v1.json',
   'c:\\Users\\<USER>\\Desktop\\Mes_projets\\WritingToolsClean\\Windows_and_Linux\\myvenv\\Lib\\site-packages\\googleapiclient\\discovery_cache\\documents\\admin.reports_v1.json',
   'DATA'),
  ('googleapiclient\\discovery_cache\\documents\\dfareporting.v3.4.json',
   'c:\\Users\\<USER>\\Desktop\\Mes_projets\\WritingToolsClean\\Windows_and_Linux\\myvenv\\Lib\\site-packages\\googleapiclient\\discovery_cache\\documents\\dfareporting.v3.4.json',
   'DATA'),
  ('googleapiclient\\discovery_cache\\documents\\documentai.v1beta2.json',
   'c:\\Users\\<USER>\\Desktop\\Mes_projets\\WritingToolsClean\\Windows_and_Linux\\myvenv\\Lib\\site-packages\\googleapiclient\\discovery_cache\\documents\\documentai.v1beta2.json',
   'DATA'),
  ('googleapiclient\\discovery_cache\\documents\\dataproc.v1beta2.json',
   'c:\\Users\\<USER>\\Desktop\\Mes_projets\\WritingToolsClean\\Windows_and_Linux\\myvenv\\Lib\\site-packages\\googleapiclient\\discovery_cache\\documents\\dataproc.v1beta2.json',
   'DATA'),
  ('googleapiclient\\discovery_cache\\documents\\genomics.v2alpha1.json',
   'c:\\Users\\<USER>\\Desktop\\Mes_projets\\WritingToolsClean\\Windows_and_Linux\\myvenv\\Lib\\site-packages\\googleapiclient\\discovery_cache\\documents\\genomics.v2alpha1.json',
   'DATA'),
  ('googleapiclient\\discovery_cache\\documents\\sqladmin.v1.json',
   'c:\\Users\\<USER>\\Desktop\\Mes_projets\\WritingToolsClean\\Windows_and_Linux\\myvenv\\Lib\\site-packages\\googleapiclient\\discovery_cache\\documents\\sqladmin.v1.json',
   'DATA'),
  ('googleapiclient\\discovery_cache\\documents\\memcache.v1.json',
   'c:\\Users\\<USER>\\Desktop\\Mes_projets\\WritingToolsClean\\Windows_and_Linux\\myvenv\\Lib\\site-packages\\googleapiclient\\discovery_cache\\documents\\memcache.v1.json',
   'DATA'),
  ('googleapiclient\\discovery_cache\\documents\\androidpublisher.v3.json',
   'c:\\Users\\<USER>\\Desktop\\Mes_projets\\WritingToolsClean\\Windows_and_Linux\\myvenv\\Lib\\site-packages\\googleapiclient\\discovery_cache\\documents\\androidpublisher.v3.json',
   'DATA'),
  ('googleapiclient\\discovery_cache\\documents\\notebooks.v2.json',
   'c:\\Users\\<USER>\\Desktop\\Mes_projets\\WritingToolsClean\\Windows_and_Linux\\myvenv\\Lib\\site-packages\\googleapiclient\\discovery_cache\\documents\\notebooks.v2.json',
   'DATA'),
  ('googleapiclient\\discovery_cache\\documents\\cloudbuild.v2.json',
   'c:\\Users\\<USER>\\Desktop\\Mes_projets\\WritingToolsClean\\Windows_and_Linux\\myvenv\\Lib\\site-packages\\googleapiclient\\discovery_cache\\documents\\cloudbuild.v2.json',
   'DATA'),
  ('googleapiclient\\discovery_cache\\documents\\pubsub.v1.json',
   'c:\\Users\\<USER>\\Desktop\\Mes_projets\\WritingToolsClean\\Windows_and_Linux\\myvenv\\Lib\\site-packages\\googleapiclient\\discovery_cache\\documents\\pubsub.v1.json',
   'DATA'),
  ('googleapiclient\\discovery_cache\\documents\\billingbudgets.v1.json',
   'c:\\Users\\<USER>\\Desktop\\Mes_projets\\WritingToolsClean\\Windows_and_Linux\\myvenv\\Lib\\site-packages\\googleapiclient\\discovery_cache\\documents\\billingbudgets.v1.json',
   'DATA'),
  ('googleapiclient\\discovery_cache\\documents\\firebasehosting.v1beta1.json',
   'c:\\Users\\<USER>\\Desktop\\Mes_projets\\WritingToolsClean\\Windows_and_Linux\\myvenv\\Lib\\site-packages\\googleapiclient\\discovery_cache\\documents\\firebasehosting.v1beta1.json',
   'DATA'),
  ('googleapiclient\\discovery_cache\\documents\\pagespeedonline.v5.json',
   'c:\\Users\\<USER>\\Desktop\\Mes_projets\\WritingToolsClean\\Windows_and_Linux\\myvenv\\Lib\\site-packages\\googleapiclient\\discovery_cache\\documents\\pagespeedonline.v5.json',
   'DATA'),
  ('googleapiclient\\discovery_cache\\documents\\dns.v2.json',
   'c:\\Users\\<USER>\\Desktop\\Mes_projets\\WritingToolsClean\\Windows_and_Linux\\myvenv\\Lib\\site-packages\\googleapiclient\\discovery_cache\\documents\\dns.v2.json',
   'DATA'),
  ('googleapiclient\\discovery_cache\\documents\\trafficdirector.v3.json',
   'c:\\Users\\<USER>\\Desktop\\Mes_projets\\WritingToolsClean\\Windows_and_Linux\\myvenv\\Lib\\site-packages\\googleapiclient\\discovery_cache\\documents\\trafficdirector.v3.json',
   'DATA'),
  ('googleapiclient\\discovery_cache\\documents\\parallelstore.v1beta.json',
   'c:\\Users\\<USER>\\Desktop\\Mes_projets\\WritingToolsClean\\Windows_and_Linux\\myvenv\\Lib\\site-packages\\googleapiclient\\discovery_cache\\documents\\parallelstore.v1beta.json',
   'DATA'),
  ('googleapiclient\\discovery_cache\\documents\\chromemanagement.v1.json',
   'c:\\Users\\<USER>\\Desktop\\Mes_projets\\WritingToolsClean\\Windows_and_Linux\\myvenv\\Lib\\site-packages\\googleapiclient\\discovery_cache\\documents\\chromemanagement.v1.json',
   'DATA'),
  ('googleapiclient\\discovery_cache\\documents\\vision.v1.json',
   'c:\\Users\\<USER>\\Desktop\\Mes_projets\\WritingToolsClean\\Windows_and_Linux\\myvenv\\Lib\\site-packages\\googleapiclient\\discovery_cache\\documents\\vision.v1.json',
   'DATA'),
  ('googleapiclient\\discovery_cache\\documents\\parametermanager.v1.json',
   'c:\\Users\\<USER>\\Desktop\\Mes_projets\\WritingToolsClean\\Windows_and_Linux\\myvenv\\Lib\\site-packages\\googleapiclient\\discovery_cache\\documents\\parametermanager.v1.json',
   'DATA'),
  ('googleapiclient\\discovery_cache\\documents\\ondemandscanning.v1beta1.json',
   'c:\\Users\\<USER>\\Desktop\\Mes_projets\\WritingToolsClean\\Windows_and_Linux\\myvenv\\Lib\\site-packages\\googleapiclient\\discovery_cache\\documents\\ondemandscanning.v1beta1.json',
   'DATA'),
  ('googleapiclient\\discovery_cache\\documents\\composer.v1.json',
   'c:\\Users\\<USER>\\Desktop\\Mes_projets\\WritingToolsClean\\Windows_and_Linux\\myvenv\\Lib\\site-packages\\googleapiclient\\discovery_cache\\documents\\composer.v1.json',
   'DATA'),
  ('googleapiclient\\discovery_cache\\documents\\cloudcontrolspartner.v1beta.json',
   'c:\\Users\\<USER>\\Desktop\\Mes_projets\\WritingToolsClean\\Windows_and_Linux\\myvenv\\Lib\\site-packages\\googleapiclient\\discovery_cache\\documents\\cloudcontrolspartner.v1beta.json',
   'DATA'),
  ('googleapiclient\\discovery_cache\\documents\\dataportability.v1beta.json',
   'c:\\Users\\<USER>\\Desktop\\Mes_projets\\WritingToolsClean\\Windows_and_Linux\\myvenv\\Lib\\site-packages\\googleapiclient\\discovery_cache\\documents\\dataportability.v1beta.json',
   'DATA'),
  ('googleapiclient\\discovery_cache\\documents\\datapipelines.v1.json',
   'c:\\Users\\<USER>\\Desktop\\Mes_projets\\WritingToolsClean\\Windows_and_Linux\\myvenv\\Lib\\site-packages\\googleapiclient\\discovery_cache\\documents\\datapipelines.v1.json',
   'DATA'),
  ('googleapiclient\\discovery_cache\\documents\\toolresults.v1beta3.json',
   'c:\\Users\\<USER>\\Desktop\\Mes_projets\\WritingToolsClean\\Windows_and_Linux\\myvenv\\Lib\\site-packages\\googleapiclient\\discovery_cache\\documents\\toolresults.v1beta3.json',
   'DATA'),
  ('googleapiclient\\discovery_cache\\documents\\aiplatform.v1.json',
   'c:\\Users\\<USER>\\Desktop\\Mes_projets\\WritingToolsClean\\Windows_and_Linux\\myvenv\\Lib\\site-packages\\googleapiclient\\discovery_cache\\documents\\aiplatform.v1.json',
   'DATA'),
  ('googleapiclient\\discovery_cache\\documents\\videointelligence.v1beta2.json',
   'c:\\Users\\<USER>\\Desktop\\Mes_projets\\WritingToolsClean\\Windows_and_Linux\\myvenv\\Lib\\site-packages\\googleapiclient\\discovery_cache\\documents\\videointelligence.v1beta2.json',
   'DATA'),
  ('googleapiclient\\discovery_cache\\documents\\datacatalog.v1.json',
   'c:\\Users\\<USER>\\Desktop\\Mes_projets\\WritingToolsClean\\Windows_and_Linux\\myvenv\\Lib\\site-packages\\googleapiclient\\discovery_cache\\documents\\datacatalog.v1.json',
   'DATA'),
  ('googleapiclient\\discovery_cache\\documents\\accesscontextmanager.v1.json',
   'c:\\Users\\<USER>\\Desktop\\Mes_projets\\WritingToolsClean\\Windows_and_Linux\\myvenv\\Lib\\site-packages\\googleapiclient\\discovery_cache\\documents\\accesscontextmanager.v1.json',
   'DATA'),
  ('googleapiclient\\discovery_cache\\documents\\ids.v1.json',
   'c:\\Users\\<USER>\\Desktop\\Mes_projets\\WritingToolsClean\\Windows_and_Linux\\myvenv\\Lib\\site-packages\\googleapiclient\\discovery_cache\\documents\\ids.v1.json',
   'DATA'),
  ('googleapiclient\\discovery_cache\\documents\\cloudshell.v1.json',
   'c:\\Users\\<USER>\\Desktop\\Mes_projets\\WritingToolsClean\\Windows_and_Linux\\myvenv\\Lib\\site-packages\\googleapiclient\\discovery_cache\\documents\\cloudshell.v1.json',
   'DATA'),
  ('googleapiclient\\discovery_cache\\documents\\authorizedbuyersmarketplace.v1alpha.json',
   'c:\\Users\\<USER>\\Desktop\\Mes_projets\\WritingToolsClean\\Windows_and_Linux\\myvenv\\Lib\\site-packages\\googleapiclient\\discovery_cache\\documents\\authorizedbuyersmarketplace.v1alpha.json',
   'DATA'),
  ('googleapiclient\\discovery_cache\\documents\\androidenterprise.v1.json',
   'c:\\Users\\<USER>\\Desktop\\Mes_projets\\WritingToolsClean\\Windows_and_Linux\\myvenv\\Lib\\site-packages\\googleapiclient\\discovery_cache\\documents\\androidenterprise.v1.json',
   'DATA'),
  ('googleapiclient\\discovery_cache\\documents\\checks.v1alpha.json',
   'c:\\Users\\<USER>\\Desktop\\Mes_projets\\WritingToolsClean\\Windows_and_Linux\\myvenv\\Lib\\site-packages\\googleapiclient\\discovery_cache\\documents\\checks.v1alpha.json',
   'DATA'),
  ('googleapiclient\\discovery_cache\\documents\\privateca.v1.json',
   'c:\\Users\\<USER>\\Desktop\\Mes_projets\\WritingToolsClean\\Windows_and_Linux\\myvenv\\Lib\\site-packages\\googleapiclient\\discovery_cache\\documents\\privateca.v1.json',
   'DATA'),
  ('googleapiclient\\discovery_cache\\documents\\classroom.v1.json',
   'c:\\Users\\<USER>\\Desktop\\Mes_projets\\WritingToolsClean\\Windows_and_Linux\\myvenv\\Lib\\site-packages\\googleapiclient\\discovery_cache\\documents\\classroom.v1.json',
   'DATA'),
  ('googleapiclient\\discovery_cache\\documents\\managedidentities.v1beta1.json',
   'c:\\Users\\<USER>\\Desktop\\Mes_projets\\WritingToolsClean\\Windows_and_Linux\\myvenv\\Lib\\site-packages\\googleapiclient\\discovery_cache\\documents\\managedidentities.v1beta1.json',
   'DATA'),
  ('googleapiclient\\discovery_cache\\documents\\runtimeconfig.v1.json',
   'c:\\Users\\<USER>\\Desktop\\Mes_projets\\WritingToolsClean\\Windows_and_Linux\\myvenv\\Lib\\site-packages\\googleapiclient\\discovery_cache\\documents\\runtimeconfig.v1.json',
   'DATA'),
  ('googleapiclient\\discovery_cache\\documents\\discoveryengine.v1beta.json',
   'c:\\Users\\<USER>\\Desktop\\Mes_projets\\WritingToolsClean\\Windows_and_Linux\\myvenv\\Lib\\site-packages\\googleapiclient\\discovery_cache\\documents\\discoveryengine.v1beta.json',
   'DATA'),
  ('googleapiclient\\discovery_cache\\documents\\content.v2.1.json',
   'c:\\Users\\<USER>\\Desktop\\Mes_projets\\WritingToolsClean\\Windows_and_Linux\\myvenv\\Lib\\site-packages\\googleapiclient\\discovery_cache\\documents\\content.v2.1.json',
   'DATA'),
  ('googleapiclient\\discovery_cache\\documents\\workstations.v1beta.json',
   'c:\\Users\\<USER>\\Desktop\\Mes_projets\\WritingToolsClean\\Windows_and_Linux\\myvenv\\Lib\\site-packages\\googleapiclient\\discovery_cache\\documents\\workstations.v1beta.json',
   'DATA'),
  ('googleapiclient\\discovery_cache\\documents\\analyticsdata.v1beta.json',
   'c:\\Users\\<USER>\\Desktop\\Mes_projets\\WritingToolsClean\\Windows_and_Linux\\myvenv\\Lib\\site-packages\\googleapiclient\\discovery_cache\\documents\\analyticsdata.v1beta.json',
   'DATA'),
  ('googleapiclient\\discovery_cache\\documents\\contentwarehouse.v1.json',
   'c:\\Users\\<USER>\\Desktop\\Mes_projets\\WritingToolsClean\\Windows_and_Linux\\myvenv\\Lib\\site-packages\\googleapiclient\\discovery_cache\\documents\\contentwarehouse.v1.json',
   'DATA'),
  ('googleapiclient\\discovery_cache\\documents\\playgrouping.v1alpha1.json',
   'c:\\Users\\<USER>\\Desktop\\Mes_projets\\WritingToolsClean\\Windows_and_Linux\\myvenv\\Lib\\site-packages\\googleapiclient\\discovery_cache\\documents\\playgrouping.v1alpha1.json',
   'DATA'),
  ('googleapiclient\\discovery_cache\\documents\\cloudbuild.v1.json',
   'c:\\Users\\<USER>\\Desktop\\Mes_projets\\WritingToolsClean\\Windows_and_Linux\\myvenv\\Lib\\site-packages\\googleapiclient\\discovery_cache\\documents\\cloudbuild.v1.json',
   'DATA'),
  ('googleapiclient\\discovery_cache\\documents\\admin.directory_v1.json',
   'c:\\Users\\<USER>\\Desktop\\Mes_projets\\WritingToolsClean\\Windows_and_Linux\\myvenv\\Lib\\site-packages\\googleapiclient\\discovery_cache\\documents\\admin.directory_v1.json',
   'DATA'),
  ('googleapiclient\\discovery_cache\\documents\\websecurityscanner.v1beta.json',
   'c:\\Users\\<USER>\\Desktop\\Mes_projets\\WritingToolsClean\\Windows_and_Linux\\myvenv\\Lib\\site-packages\\googleapiclient\\discovery_cache\\documents\\websecurityscanner.v1beta.json',
   'DATA'),
  ('googleapiclient\\discovery_cache\\documents\\drivelabels.v2beta.json',
   'c:\\Users\\<USER>\\Desktop\\Mes_projets\\WritingToolsClean\\Windows_and_Linux\\myvenv\\Lib\\site-packages\\googleapiclient\\discovery_cache\\documents\\drivelabels.v2beta.json',
   'DATA'),
  ('googleapiclient\\discovery_cache\\documents\\metastore.v1beta.json',
   'c:\\Users\\<USER>\\Desktop\\Mes_projets\\WritingToolsClean\\Windows_and_Linux\\myvenv\\Lib\\site-packages\\googleapiclient\\discovery_cache\\documents\\metastore.v1beta.json',
   'DATA'),
  ('googleapiclient\\discovery_cache\\documents\\translate.v2.json',
   'c:\\Users\\<USER>\\Desktop\\Mes_projets\\WritingToolsClean\\Windows_and_Linux\\myvenv\\Lib\\site-packages\\googleapiclient\\discovery_cache\\documents\\translate.v2.json',
   'DATA'),
  ('googleapiclient\\discovery_cache\\documents\\cloudresourcemanager.v3.json',
   'c:\\Users\\<USER>\\Desktop\\Mes_projets\\WritingToolsClean\\Windows_and_Linux\\myvenv\\Lib\\site-packages\\googleapiclient\\discovery_cache\\documents\\cloudresourcemanager.v3.json',
   'DATA'),
  ('googleapiclient\\discovery_cache\\documents\\streetviewpublish.v1.json',
   'c:\\Users\\<USER>\\Desktop\\Mes_projets\\WritingToolsClean\\Windows_and_Linux\\myvenv\\Lib\\site-packages\\googleapiclient\\discovery_cache\\documents\\streetviewpublish.v1.json',
   'DATA'),
  ('googleapiclient\\discovery_cache\\documents\\looker.v1.json',
   'c:\\Users\\<USER>\\Desktop\\Mes_projets\\WritingToolsClean\\Windows_and_Linux\\myvenv\\Lib\\site-packages\\googleapiclient\\discovery_cache\\documents\\looker.v1.json',
   'DATA'),
  ('googleapiclient\\discovery_cache\\documents\\rapidmigrationassessment.v1.json',
   'c:\\Users\\<USER>\\Desktop\\Mes_projets\\WritingToolsClean\\Windows_and_Linux\\myvenv\\Lib\\site-packages\\googleapiclient\\discovery_cache\\documents\\rapidmigrationassessment.v1.json',
   'DATA'),
  ('googleapiclient\\discovery_cache\\documents\\kmsinventory.v1.json',
   'c:\\Users\\<USER>\\Desktop\\Mes_projets\\WritingToolsClean\\Windows_and_Linux\\myvenv\\Lib\\site-packages\\googleapiclient\\discovery_cache\\documents\\kmsinventory.v1.json',
   'DATA'),
  ('googleapiclient\\discovery_cache\\documents\\certificatemanager.v1.json',
   'c:\\Users\\<USER>\\Desktop\\Mes_projets\\WritingToolsClean\\Windows_and_Linux\\myvenv\\Lib\\site-packages\\googleapiclient\\discovery_cache\\documents\\certificatemanager.v1.json',
   'DATA'),
  ('googleapiclient\\discovery_cache\\documents\\youtubeAnalytics.v2.json',
   'c:\\Users\\<USER>\\Desktop\\Mes_projets\\WritingToolsClean\\Windows_and_Linux\\myvenv\\Lib\\site-packages\\googleapiclient\\discovery_cache\\documents\\youtubeAnalytics.v2.json',
   'DATA'),
  ('googleapiclient\\discovery_cache\\documents\\contactcenterinsights.v1.json',
   'c:\\Users\\<USER>\\Desktop\\Mes_projets\\WritingToolsClean\\Windows_and_Linux\\myvenv\\Lib\\site-packages\\googleapiclient\\discovery_cache\\documents\\contactcenterinsights.v1.json',
   'DATA'),
  ('googleapiclient\\discovery_cache\\documents\\analyticsreporting.v4.json',
   'c:\\Users\\<USER>\\Desktop\\Mes_projets\\WritingToolsClean\\Windows_and_Linux\\myvenv\\Lib\\site-packages\\googleapiclient\\discovery_cache\\documents\\analyticsreporting.v4.json',
   'DATA'),
  ('googleapiclient\\discovery_cache\\documents\\merchantapi.products_v1beta.json',
   'c:\\Users\\<USER>\\Desktop\\Mes_projets\\WritingToolsClean\\Windows_and_Linux\\myvenv\\Lib\\site-packages\\googleapiclient\\discovery_cache\\documents\\merchantapi.products_v1beta.json',
   'DATA'),
  ('googleapiclient\\discovery_cache\\documents\\adsense.v2.json',
   'c:\\Users\\<USER>\\Desktop\\Mes_projets\\WritingToolsClean\\Windows_and_Linux\\myvenv\\Lib\\site-packages\\googleapiclient\\discovery_cache\\documents\\adsense.v2.json',
   'DATA'),
  ('googleapiclient\\discovery_cache\\documents\\appengine.v1beta.json',
   'c:\\Users\\<USER>\\Desktop\\Mes_projets\\WritingToolsClean\\Windows_and_Linux\\myvenv\\Lib\\site-packages\\googleapiclient\\discovery_cache\\documents\\appengine.v1beta.json',
   'DATA'),
  ('googleapiclient\\discovery_cache\\documents\\remotebuildexecution.v1.json',
   'c:\\Users\\<USER>\\Desktop\\Mes_projets\\WritingToolsClean\\Windows_and_Linux\\myvenv\\Lib\\site-packages\\googleapiclient\\discovery_cache\\documents\\remotebuildexecution.v1.json',
   'DATA'),
  ('googleapiclient\\discovery_cache\\documents\\gkehub.v2alpha.json',
   'c:\\Users\\<USER>\\Desktop\\Mes_projets\\WritingToolsClean\\Windows_and_Linux\\myvenv\\Lib\\site-packages\\googleapiclient\\discovery_cache\\documents\\gkehub.v2alpha.json',
   'DATA'),
  ('googleapiclient\\discovery_cache\\documents\\texttospeech.v1.json',
   'c:\\Users\\<USER>\\Desktop\\Mes_projets\\WritingToolsClean\\Windows_and_Linux\\myvenv\\Lib\\site-packages\\googleapiclient\\discovery_cache\\documents\\texttospeech.v1.json',
   'DATA'),
  ('googleapiclient\\discovery_cache\\documents\\bigquery.v2.json',
   'c:\\Users\\<USER>\\Desktop\\Mes_projets\\WritingToolsClean\\Windows_and_Linux\\myvenv\\Lib\\site-packages\\googleapiclient\\discovery_cache\\documents\\bigquery.v2.json',
   'DATA'),
  ('googleapiclient\\discovery_cache\\documents\\cloudidentity.v1beta1.json',
   'c:\\Users\\<USER>\\Desktop\\Mes_projets\\WritingToolsClean\\Windows_and_Linux\\myvenv\\Lib\\site-packages\\googleapiclient\\discovery_cache\\documents\\cloudidentity.v1beta1.json',
   'DATA'),
  ('googleapiclient\\discovery_cache\\documents\\bigquerydatapolicy.v1.json',
   'c:\\Users\\<USER>\\Desktop\\Mes_projets\\WritingToolsClean\\Windows_and_Linux\\myvenv\\Lib\\site-packages\\googleapiclient\\discovery_cache\\documents\\bigquerydatapolicy.v1.json',
   'DATA'),
  ('googleapiclient\\discovery_cache\\documents\\firebasehosting.v1.json',
   'c:\\Users\\<USER>\\Desktop\\Mes_projets\\WritingToolsClean\\Windows_and_Linux\\myvenv\\Lib\\site-packages\\googleapiclient\\discovery_cache\\documents\\firebasehosting.v1.json',
   'DATA'),
  ('googleapiclient\\discovery_cache\\documents\\vmmigration.v1.json',
   'c:\\Users\\<USER>\\Desktop\\Mes_projets\\WritingToolsClean\\Windows_and_Linux\\myvenv\\Lib\\site-packages\\googleapiclient\\discovery_cache\\documents\\vmmigration.v1.json',
   'DATA'),
  ('googleapiclient\\discovery_cache\\documents\\servicenetworking.v1.json',
   'c:\\Users\\<USER>\\Desktop\\Mes_projets\\WritingToolsClean\\Windows_and_Linux\\myvenv\\Lib\\site-packages\\googleapiclient\\discovery_cache\\documents\\servicenetworking.v1.json',
   'DATA'),
  ('googleapiclient\\discovery_cache\\documents\\secretmanager.v1beta2.json',
   'c:\\Users\\<USER>\\Desktop\\Mes_projets\\WritingToolsClean\\Windows_and_Linux\\myvenv\\Lib\\site-packages\\googleapiclient\\discovery_cache\\documents\\secretmanager.v1beta2.json',
   'DATA'),
  ('googleapiclient\\discovery_cache\\documents\\tpu.v2alpha1.json',
   'c:\\Users\\<USER>\\Desktop\\Mes_projets\\WritingToolsClean\\Windows_and_Linux\\myvenv\\Lib\\site-packages\\googleapiclient\\discovery_cache\\documents\\tpu.v2alpha1.json',
   'DATA'),
  ('googleapiclient\\discovery_cache\\documents\\documentai.v1beta3.json',
   'c:\\Users\\<USER>\\Desktop\\Mes_projets\\WritingToolsClean\\Windows_and_Linux\\myvenv\\Lib\\site-packages\\googleapiclient\\discovery_cache\\documents\\documentai.v1beta3.json',
   'DATA'),
  ('googleapiclient\\discovery_cache\\documents\\gameservices.v1.json',
   'c:\\Users\\<USER>\\Desktop\\Mes_projets\\WritingToolsClean\\Windows_and_Linux\\myvenv\\Lib\\site-packages\\googleapiclient\\discovery_cache\\documents\\gameservices.v1.json',
   'DATA'),
  ('googleapiclient\\discovery_cache\\documents\\discoveryengine.v1.json',
   'c:\\Users\\<USER>\\Desktop\\Mes_projets\\WritingToolsClean\\Windows_and_Linux\\myvenv\\Lib\\site-packages\\googleapiclient\\discovery_cache\\documents\\discoveryengine.v1.json',
   'DATA'),
  ('googleapiclient\\discovery_cache\\documents\\firebaseapphosting.v1.json',
   'c:\\Users\\<USER>\\Desktop\\Mes_projets\\WritingToolsClean\\Windows_and_Linux\\myvenv\\Lib\\site-packages\\googleapiclient\\discovery_cache\\documents\\firebaseapphosting.v1.json',
   'DATA'),
  ('googleapiclient\\discovery_cache\\documents\\file.v1.json',
   'c:\\Users\\<USER>\\Desktop\\Mes_projets\\WritingToolsClean\\Windows_and_Linux\\myvenv\\Lib\\site-packages\\googleapiclient\\discovery_cache\\documents\\file.v1.json',
   'DATA'),
  ('googleapiclient\\discovery_cache\\documents\\sts.v1beta.json',
   'c:\\Users\\<USER>\\Desktop\\Mes_projets\\WritingToolsClean\\Windows_and_Linux\\myvenv\\Lib\\site-packages\\googleapiclient\\discovery_cache\\documents\\sts.v1beta.json',
   'DATA'),
  ('googleapiclient\\discovery_cache\\documents\\safebrowsing.v5.json',
   'c:\\Users\\<USER>\\Desktop\\Mes_projets\\WritingToolsClean\\Windows_and_Linux\\myvenv\\Lib\\site-packages\\googleapiclient\\discovery_cache\\documents\\safebrowsing.v5.json',
   'DATA'),
  ('googleapiclient\\discovery_cache\\documents\\merchantapi.reports_v1beta.json',
   'c:\\Users\\<USER>\\Desktop\\Mes_projets\\WritingToolsClean\\Windows_and_Linux\\myvenv\\Lib\\site-packages\\googleapiclient\\discovery_cache\\documents\\merchantapi.reports_v1beta.json',
   'DATA'),
  ('googleapiclient\\discovery_cache\\documents\\genomics.v1alpha2.json',
   'c:\\Users\\<USER>\\Desktop\\Mes_projets\\WritingToolsClean\\Windows_and_Linux\\myvenv\\Lib\\site-packages\\googleapiclient\\discovery_cache\\documents\\genomics.v1alpha2.json',
   'DATA'),
  ('googleapiclient\\discovery_cache\\documents\\libraryagent.v1.json',
   'c:\\Users\\<USER>\\Desktop\\Mes_projets\\WritingToolsClean\\Windows_and_Linux\\myvenv\\Lib\\site-packages\\googleapiclient\\discovery_cache\\documents\\libraryagent.v1.json',
   'DATA'),
  ('googleapiclient\\discovery_cache\\documents\\beyondcorp.v1.json',
   'c:\\Users\\<USER>\\Desktop\\Mes_projets\\WritingToolsClean\\Windows_and_Linux\\myvenv\\Lib\\site-packages\\googleapiclient\\discovery_cache\\documents\\beyondcorp.v1.json',
   'DATA'),
  ('googleapiclient\\discovery_cache\\documents\\dialogflow.v3.json',
   'c:\\Users\\<USER>\\Desktop\\Mes_projets\\WritingToolsClean\\Windows_and_Linux\\myvenv\\Lib\\site-packages\\googleapiclient\\discovery_cache\\documents\\dialogflow.v3.json',
   'DATA'),
  ('googleapiclient\\discovery_cache\\documents\\baremetalsolution.v2.json',
   'c:\\Users\\<USER>\\Desktop\\Mes_projets\\WritingToolsClean\\Windows_and_Linux\\myvenv\\Lib\\site-packages\\googleapiclient\\discovery_cache\\documents\\baremetalsolution.v2.json',
   'DATA'),
  ('googleapiclient\\discovery_cache\\documents\\logging.v2.json',
   'c:\\Users\\<USER>\\Desktop\\Mes_projets\\WritingToolsClean\\Windows_and_Linux\\myvenv\\Lib\\site-packages\\googleapiclient\\discovery_cache\\documents\\logging.v2.json',
   'DATA'),
  ('googleapiclient\\discovery_cache\\documents\\observability.v1.json',
   'c:\\Users\\<USER>\\Desktop\\Mes_projets\\WritingToolsClean\\Windows_and_Linux\\myvenv\\Lib\\site-packages\\googleapiclient\\discovery_cache\\documents\\observability.v1.json',
   'DATA'),
  ('googleapiclient\\discovery_cache\\documents\\policyanalyzer.v1beta1.json',
   'c:\\Users\\<USER>\\Desktop\\Mes_projets\\WritingToolsClean\\Windows_and_Linux\\myvenv\\Lib\\site-packages\\googleapiclient\\discovery_cache\\documents\\policyanalyzer.v1beta1.json',
   'DATA'),
  ('googleapiclient\\discovery_cache\\documents\\safebrowsing.v4.json',
   'c:\\Users\\<USER>\\Desktop\\Mes_projets\\WritingToolsClean\\Windows_and_Linux\\myvenv\\Lib\\site-packages\\googleapiclient\\discovery_cache\\documents\\safebrowsing.v4.json',
   'DATA'),
  ('googleapiclient\\discovery_cache\\documents\\smartdevicemanagement.v1.json',
   'c:\\Users\\<USER>\\Desktop\\Mes_projets\\WritingToolsClean\\Windows_and_Linux\\myvenv\\Lib\\site-packages\\googleapiclient\\discovery_cache\\documents\\smartdevicemanagement.v1.json',
   'DATA'),
  ('googleapiclient\\discovery_cache\\documents\\appengine.v1beta4.json',
   'c:\\Users\\<USER>\\Desktop\\Mes_projets\\WritingToolsClean\\Windows_and_Linux\\myvenv\\Lib\\site-packages\\googleapiclient\\discovery_cache\\documents\\appengine.v1beta4.json',
   'DATA'),
  ('googleapiclient\\discovery_cache\\documents\\appengine.v1beta5.json',
   'c:\\Users\\<USER>\\Desktop\\Mes_projets\\WritingToolsClean\\Windows_and_Linux\\myvenv\\Lib\\site-packages\\googleapiclient\\discovery_cache\\documents\\appengine.v1beta5.json',
   'DATA'),
  ('googleapiclient\\discovery_cache\\documents\\workflowexecutions.v1.json',
   'c:\\Users\\<USER>\\Desktop\\Mes_projets\\WritingToolsClean\\Windows_and_Linux\\myvenv\\Lib\\site-packages\\googleapiclient\\discovery_cache\\documents\\workflowexecutions.v1.json',
   'DATA'),
  ('googleapiclient\\discovery_cache\\documents\\cloudtasks.v2.json',
   'c:\\Users\\<USER>\\Desktop\\Mes_projets\\WritingToolsClean\\Windows_and_Linux\\myvenv\\Lib\\site-packages\\googleapiclient\\discovery_cache\\documents\\cloudtasks.v2.json',
   'DATA'),
  ('googleapiclient\\discovery_cache\\documents\\analyticshub.v1.json',
   'c:\\Users\\<USER>\\Desktop\\Mes_projets\\WritingToolsClean\\Windows_and_Linux\\myvenv\\Lib\\site-packages\\googleapiclient\\discovery_cache\\documents\\analyticshub.v1.json',
   'DATA'),
  ('googleapiclient\\discovery_cache\\documents\\oracledatabase.v1.json',
   'c:\\Users\\<USER>\\Desktop\\Mes_projets\\WritingToolsClean\\Windows_and_Linux\\myvenv\\Lib\\site-packages\\googleapiclient\\discovery_cache\\documents\\oracledatabase.v1.json',
   'DATA'),
  ('googleapiclient\\discovery_cache\\documents\\appengine.v1.json',
   'c:\\Users\\<USER>\\Desktop\\Mes_projets\\WritingToolsClean\\Windows_and_Linux\\myvenv\\Lib\\site-packages\\googleapiclient\\discovery_cache\\documents\\appengine.v1.json',
   'DATA'),
  ('googleapiclient\\discovery_cache\\documents\\readerrevenuesubscriptionlinking.v1.json',
   'c:\\Users\\<USER>\\Desktop\\Mes_projets\\WritingToolsClean\\Windows_and_Linux\\myvenv\\Lib\\site-packages\\googleapiclient\\discovery_cache\\documents\\readerrevenuesubscriptionlinking.v1.json',
   'DATA'),
  ('googleapiclient\\discovery_cache\\documents\\admob.v1beta.json',
   'c:\\Users\\<USER>\\Desktop\\Mes_projets\\WritingToolsClean\\Windows_and_Linux\\myvenv\\Lib\\site-packages\\googleapiclient\\discovery_cache\\documents\\admob.v1beta.json',
   'DATA'),
  ('googleapiclient\\discovery_cache\\documents\\oslogin.v1.json',
   'c:\\Users\\<USER>\\Desktop\\Mes_projets\\WritingToolsClean\\Windows_and_Linux\\myvenv\\Lib\\site-packages\\googleapiclient\\discovery_cache\\documents\\oslogin.v1.json',
   'DATA'),
  ('googleapiclient\\discovery_cache\\documents\\cloudfunctions.v2.json',
   'c:\\Users\\<USER>\\Desktop\\Mes_projets\\WritingToolsClean\\Windows_and_Linux\\myvenv\\Lib\\site-packages\\googleapiclient\\discovery_cache\\documents\\cloudfunctions.v2.json',
   'DATA'),
  ('googleapiclient\\discovery_cache\\documents\\migrationcenter.v1alpha1.json',
   'c:\\Users\\<USER>\\Desktop\\Mes_projets\\WritingToolsClean\\Windows_and_Linux\\myvenv\\Lib\\site-packages\\googleapiclient\\discovery_cache\\documents\\migrationcenter.v1alpha1.json',
   'DATA'),
  ('googleapiclient\\discovery_cache\\documents\\identitytoolkit.v3.json',
   'c:\\Users\\<USER>\\Desktop\\Mes_projets\\WritingToolsClean\\Windows_and_Linux\\myvenv\\Lib\\site-packages\\googleapiclient\\discovery_cache\\documents\\identitytoolkit.v3.json',
   'DATA'),
  ('google_api_python_client-2.177.0.dist-info\\RECORD',
   'c:\\Users\\<USER>\\Desktop\\Mes_projets\\WritingToolsClean\\Windows_and_Linux\\myvenv\\Lib\\site-packages\\google_api_python_client-2.177.0.dist-info\\RECORD',
   'DATA'),
  ('googleapiclient\\discovery_cache\\documents\\datamigration.v1beta1.json',
   'c:\\Users\\<USER>\\Desktop\\Mes_projets\\WritingToolsClean\\Windows_and_Linux\\myvenv\\Lib\\site-packages\\googleapiclient\\discovery_cache\\documents\\datamigration.v1beta1.json',
   'DATA'),
  ('googleapiclient\\discovery_cache\\documents\\jobs.v2.json',
   'c:\\Users\\<USER>\\Desktop\\Mes_projets\\WritingToolsClean\\Windows_and_Linux\\myvenv\\Lib\\site-packages\\googleapiclient\\discovery_cache\\documents\\jobs.v2.json',
   'DATA'),
  ('googleapiclient\\discovery_cache\\documents\\businessprofileperformance.v1.json',
   'c:\\Users\\<USER>\\Desktop\\Mes_projets\\WritingToolsClean\\Windows_and_Linux\\myvenv\\Lib\\site-packages\\googleapiclient\\discovery_cache\\documents\\businessprofileperformance.v1.json',
   'DATA'),
  ('googleapiclient\\discovery_cache\\documents\\cloudsupport.v2beta.json',
   'c:\\Users\\<USER>\\Desktop\\Mes_projets\\WritingToolsClean\\Windows_and_Linux\\myvenv\\Lib\\site-packages\\googleapiclient\\discovery_cache\\documents\\cloudsupport.v2beta.json',
   'DATA'),
  ('googleapiclient\\discovery_cache\\documents\\clouddeploy.v1.json',
   'c:\\Users\\<USER>\\Desktop\\Mes_projets\\WritingToolsClean\\Windows_and_Linux\\myvenv\\Lib\\site-packages\\googleapiclient\\discovery_cache\\documents\\clouddeploy.v1.json',
   'DATA'),
  ('googleapiclient\\discovery_cache\\documents\\cloudsupport.v2.json',
   'c:\\Users\\<USER>\\Desktop\\Mes_projets\\WritingToolsClean\\Windows_and_Linux\\myvenv\\Lib\\site-packages\\googleapiclient\\discovery_cache\\documents\\cloudsupport.v2.json',
   'DATA'),
  ('googleapiclient\\discovery_cache\\documents\\webmasters.v3.json',
   'c:\\Users\\<USER>\\Desktop\\Mes_projets\\WritingToolsClean\\Windows_and_Linux\\myvenv\\Lib\\site-packages\\googleapiclient\\discovery_cache\\documents\\webmasters.v3.json',
   'DATA'),
  ('googleapiclient\\discovery_cache\\documents\\firebasedataconnect.v1.json',
   'c:\\Users\\<USER>\\Desktop\\Mes_projets\\WritingToolsClean\\Windows_and_Linux\\myvenv\\Lib\\site-packages\\googleapiclient\\discovery_cache\\documents\\firebasedataconnect.v1.json',
   'DATA'),
  ('googleapiclient\\discovery_cache\\documents\\cloudtrace.v2beta1.json',
   'c:\\Users\\<USER>\\Desktop\\Mes_projets\\WritingToolsClean\\Windows_and_Linux\\myvenv\\Lib\\site-packages\\googleapiclient\\discovery_cache\\documents\\cloudtrace.v2beta1.json',
   'DATA'),
  ('googleapiclient\\discovery_cache\\documents\\deploymentmanager.v2beta.json',
   'c:\\Users\\<USER>\\Desktop\\Mes_projets\\WritingToolsClean\\Windows_and_Linux\\myvenv\\Lib\\site-packages\\googleapiclient\\discovery_cache\\documents\\deploymentmanager.v2beta.json',
   'DATA'),
  ('googleapiclient\\discovery_cache\\documents\\prod_tt_sasportal.v1alpha1.json',
   'c:\\Users\\<USER>\\Desktop\\Mes_projets\\WritingToolsClean\\Windows_and_Linux\\myvenv\\Lib\\site-packages\\googleapiclient\\discovery_cache\\documents\\prod_tt_sasportal.v1alpha1.json',
   'DATA'),
  ('googleapiclient\\discovery_cache\\documents\\licensing.v1.json',
   'c:\\Users\\<USER>\\Desktop\\Mes_projets\\WritingToolsClean\\Windows_and_Linux\\myvenv\\Lib\\site-packages\\googleapiclient\\discovery_cache\\documents\\licensing.v1.json',
   'DATA'),
  ('googleapiclient\\discovery_cache\\documents\\servicedirectory.v1beta1.json',
   'c:\\Users\\<USER>\\Desktop\\Mes_projets\\WritingToolsClean\\Windows_and_Linux\\myvenv\\Lib\\site-packages\\googleapiclient\\discovery_cache\\documents\\servicedirectory.v1beta1.json',
   'DATA'),
  ('googleapiclient\\discovery_cache\\documents\\documentai.v1.json',
   'c:\\Users\\<USER>\\Desktop\\Mes_projets\\WritingToolsClean\\Windows_and_Linux\\myvenv\\Lib\\site-packages\\googleapiclient\\discovery_cache\\documents\\documentai.v1.json',
   'DATA'),
  ('googleapiclient\\discovery_cache\\documents\\analyticsadmin.v1beta.json',
   'c:\\Users\\<USER>\\Desktop\\Mes_projets\\WritingToolsClean\\Windows_and_Linux\\myvenv\\Lib\\site-packages\\googleapiclient\\discovery_cache\\documents\\analyticsadmin.v1beta.json',
   'DATA'),
  ('googleapiclient\\discovery_cache\\documents\\cloudasset.v1.json',
   'c:\\Users\\<USER>\\Desktop\\Mes_projets\\WritingToolsClean\\Windows_and_Linux\\myvenv\\Lib\\site-packages\\googleapiclient\\discovery_cache\\documents\\cloudasset.v1.json',
   'DATA'),
  ('googleapiclient\\discovery_cache\\documents\\datastore.v1.json',
   'c:\\Users\\<USER>\\Desktop\\Mes_projets\\WritingToolsClean\\Windows_and_Linux\\myvenv\\Lib\\site-packages\\googleapiclient\\discovery_cache\\documents\\datastore.v1.json',
   'DATA'),
  ('googleapiclient\\discovery_cache\\documents\\redis.v1beta1.json',
   'c:\\Users\\<USER>\\Desktop\\Mes_projets\\WritingToolsClean\\Windows_and_Linux\\myvenv\\Lib\\site-packages\\googleapiclient\\discovery_cache\\documents\\redis.v1beta1.json',
   'DATA'),
  ('googleapiclient\\discovery_cache\\documents\\doubleclickbidmanager.v1.json',
   'c:\\Users\\<USER>\\Desktop\\Mes_projets\\WritingToolsClean\\Windows_and_Linux\\myvenv\\Lib\\site-packages\\googleapiclient\\discovery_cache\\documents\\doubleclickbidmanager.v1.json',
   'DATA'),
  ('googleapiclient\\discovery_cache\\documents\\jobs.v3p1beta1.json',
   'c:\\Users\\<USER>\\Desktop\\Mes_projets\\WritingToolsClean\\Windows_and_Linux\\myvenv\\Lib\\site-packages\\googleapiclient\\discovery_cache\\documents\\jobs.v3p1beta1.json',
   'DATA'),
  ('googleapiclient\\discovery_cache\\documents\\merchantapi.ordertracking_v1beta.json',
   'c:\\Users\\<USER>\\Desktop\\Mes_projets\\WritingToolsClean\\Windows_and_Linux\\myvenv\\Lib\\site-packages\\googleapiclient\\discovery_cache\\documents\\merchantapi.ordertracking_v1beta.json',
   'DATA'),
  ('googleapiclient\\discovery_cache\\documents\\admin.datatransfer_v1.json',
   'c:\\Users\\<USER>\\Desktop\\Mes_projets\\WritingToolsClean\\Windows_and_Linux\\myvenv\\Lib\\site-packages\\googleapiclient\\discovery_cache\\documents\\admin.datatransfer_v1.json',
   'DATA'),
  ('googleapiclient\\discovery_cache\\documents\\videointelligence.v1p1beta1.json',
   'c:\\Users\\<USER>\\Desktop\\Mes_projets\\WritingToolsClean\\Windows_and_Linux\\myvenv\\Lib\\site-packages\\googleapiclient\\discovery_cache\\documents\\videointelligence.v1p1beta1.json',
   'DATA'),
  ('googleapiclient\\discovery_cache\\documents\\ideahub.v1alpha.json',
   'c:\\Users\\<USER>\\Desktop\\Mes_projets\\WritingToolsClean\\Windows_and_Linux\\myvenv\\Lib\\site-packages\\googleapiclient\\discovery_cache\\documents\\ideahub.v1alpha.json',
   'DATA'),
  ('googleapiclient\\discovery_cache\\documents\\policysimulator.v1beta.json',
   'c:\\Users\\<USER>\\Desktop\\Mes_projets\\WritingToolsClean\\Windows_and_Linux\\myvenv\\Lib\\site-packages\\googleapiclient\\discovery_cache\\documents\\policysimulator.v1beta.json',
   'DATA'),
  ('googleapiclient\\discovery_cache\\documents\\cloudbuild.v1alpha1.json',
   'c:\\Users\\<USER>\\Desktop\\Mes_projets\\WritingToolsClean\\Windows_and_Linux\\myvenv\\Lib\\site-packages\\googleapiclient\\discovery_cache\\documents\\cloudbuild.v1alpha1.json',
   'DATA'),
  ('googleapiclient\\discovery_cache\\documents\\tpu.v2.json',
   'c:\\Users\\<USER>\\Desktop\\Mes_projets\\WritingToolsClean\\Windows_and_Linux\\myvenv\\Lib\\site-packages\\googleapiclient\\discovery_cache\\documents\\tpu.v2.json',
   'DATA'),
  ('googleapiclient\\discovery_cache\\documents\\osconfig.v1beta.json',
   'c:\\Users\\<USER>\\Desktop\\Mes_projets\\WritingToolsClean\\Windows_and_Linux\\myvenv\\Lib\\site-packages\\googleapiclient\\discovery_cache\\documents\\osconfig.v1beta.json',
   'DATA'),
  ('googleapiclient\\discovery_cache\\documents\\reseller.v1.json',
   'c:\\Users\\<USER>\\Desktop\\Mes_projets\\WritingToolsClean\\Windows_and_Linux\\myvenv\\Lib\\site-packages\\googleapiclient\\discovery_cache\\documents\\reseller.v1.json',
   'DATA'),
  ('googleapiclient\\discovery_cache\\documents\\cloudbuild.v1alpha2.json',
   'c:\\Users\\<USER>\\Desktop\\Mes_projets\\WritingToolsClean\\Windows_and_Linux\\myvenv\\Lib\\site-packages\\googleapiclient\\discovery_cache\\documents\\cloudbuild.v1alpha2.json',
   'DATA'),
  ('googleapiclient\\discovery_cache\\documents\\adexchangebuyer.v1.3.json',
   'c:\\Users\\<USER>\\Desktop\\Mes_projets\\WritingToolsClean\\Windows_and_Linux\\myvenv\\Lib\\site-packages\\googleapiclient\\discovery_cache\\documents\\adexchangebuyer.v1.3.json',
   'DATA'),
  ('googleapiclient\\discovery_cache\\documents\\language.v1.json',
   'c:\\Users\\<USER>\\Desktop\\Mes_projets\\WritingToolsClean\\Windows_and_Linux\\myvenv\\Lib\\site-packages\\googleapiclient\\discovery_cache\\documents\\language.v1.json',
   'DATA'),
  ('googleapiclient\\discovery_cache\\documents\\groupssettings.v1.json',
   'c:\\Users\\<USER>\\Desktop\\Mes_projets\\WritingToolsClean\\Windows_and_Linux\\myvenv\\Lib\\site-packages\\googleapiclient\\discovery_cache\\documents\\groupssettings.v1.json',
   'DATA'),
  ('googleapiclient\\discovery_cache\\documents\\dialogflow.v2.json',
   'c:\\Users\\<USER>\\Desktop\\Mes_projets\\WritingToolsClean\\Windows_and_Linux\\myvenv\\Lib\\site-packages\\googleapiclient\\discovery_cache\\documents\\dialogflow.v2.json',
   'DATA'),
  ('googleapiclient\\discovery_cache\\documents\\transcoder.v1.json',
   'c:\\Users\\<USER>\\Desktop\\Mes_projets\\WritingToolsClean\\Windows_and_Linux\\myvenv\\Lib\\site-packages\\googleapiclient\\discovery_cache\\documents\\transcoder.v1.json',
   'DATA'),
  ('googleapiclient\\discovery_cache\\documents\\cloudiot.v1.json',
   'c:\\Users\\<USER>\\Desktop\\Mes_projets\\WritingToolsClean\\Windows_and_Linux\\myvenv\\Lib\\site-packages\\googleapiclient\\discovery_cache\\documents\\cloudiot.v1.json',
   'DATA'),
  ('googleapiclient\\discovery_cache\\documents\\networkconnectivity.v1alpha1.json',
   'c:\\Users\\<USER>\\Desktop\\Mes_projets\\WritingToolsClean\\Windows_and_Linux\\myvenv\\Lib\\site-packages\\googleapiclient\\discovery_cache\\documents\\networkconnectivity.v1alpha1.json',
   'DATA'),
  ('googleapiclient\\discovery_cache\\documents\\analytics.v3.json',
   'c:\\Users\\<USER>\\Desktop\\Mes_projets\\WritingToolsClean\\Windows_and_Linux\\myvenv\\Lib\\site-packages\\googleapiclient\\discovery_cache\\documents\\analytics.v3.json',
   'DATA'),
  ('googleapiclient\\discovery_cache\\documents\\merchantapi.accounts_v1beta.json',
   'c:\\Users\\<USER>\\Desktop\\Mes_projets\\WritingToolsClean\\Windows_and_Linux\\myvenv\\Lib\\site-packages\\googleapiclient\\discovery_cache\\documents\\merchantapi.accounts_v1beta.json',
   'DATA'),
  ('googleapiclient\\discovery_cache\\documents\\bigqueryreservation.v1alpha2.json',
   'c:\\Users\\<USER>\\Desktop\\Mes_projets\\WritingToolsClean\\Windows_and_Linux\\myvenv\\Lib\\site-packages\\googleapiclient\\discovery_cache\\documents\\bigqueryreservation.v1alpha2.json',
   'DATA'),
  ('googleapiclient\\discovery_cache\\documents\\manufacturers.v1.json',
   'c:\\Users\\<USER>\\Desktop\\Mes_projets\\WritingToolsClean\\Windows_and_Linux\\myvenv\\Lib\\site-packages\\googleapiclient\\discovery_cache\\documents\\manufacturers.v1.json',
   'DATA'),
  ('googleapiclient\\discovery_cache\\documents\\vpcaccess.v1beta1.json',
   'c:\\Users\\<USER>\\Desktop\\Mes_projets\\WritingToolsClean\\Windows_and_Linux\\myvenv\\Lib\\site-packages\\googleapiclient\\discovery_cache\\documents\\vpcaccess.v1beta1.json',
   'DATA'),
  ('googleapiclient\\discovery_cache\\documents\\authorizedbuyersmarketplace.v1beta.json',
   'c:\\Users\\<USER>\\Desktop\\Mes_projets\\WritingToolsClean\\Windows_and_Linux\\myvenv\\Lib\\site-packages\\googleapiclient\\discovery_cache\\documents\\authorizedbuyersmarketplace.v1beta.json',
   'DATA'),
  ('googleapiclient\\discovery_cache\\documents\\metastore.v1.json',
   'c:\\Users\\<USER>\\Desktop\\Mes_projets\\WritingToolsClean\\Windows_and_Linux\\myvenv\\Lib\\site-packages\\googleapiclient\\discovery_cache\\documents\\metastore.v1.json',
   'DATA'),
  ('googleapiclient\\discovery_cache\\documents\\eventarc.v1.json',
   'c:\\Users\\<USER>\\Desktop\\Mes_projets\\WritingToolsClean\\Windows_and_Linux\\myvenv\\Lib\\site-packages\\googleapiclient\\discovery_cache\\documents\\eventarc.v1.json',
   'DATA'),
  ('googleapiclient\\discovery_cache\\documents\\ml.v1.json',
   'c:\\Users\\<USER>\\Desktop\\Mes_projets\\WritingToolsClean\\Windows_and_Linux\\myvenv\\Lib\\site-packages\\googleapiclient\\discovery_cache\\documents\\ml.v1.json',
   'DATA'),
  ('googleapiclient\\discovery_cache\\documents\\recommendationengine.v1beta1.json',
   'c:\\Users\\<USER>\\Desktop\\Mes_projets\\WritingToolsClean\\Windows_and_Linux\\myvenv\\Lib\\site-packages\\googleapiclient\\discovery_cache\\documents\\recommendationengine.v1beta1.json',
   'DATA'),
  ('googleapiclient\\discovery_cache\\documents\\merchantapi.promotions_v1beta.json',
   'c:\\Users\\<USER>\\Desktop\\Mes_projets\\WritingToolsClean\\Windows_and_Linux\\myvenv\\Lib\\site-packages\\googleapiclient\\discovery_cache\\documents\\merchantapi.promotions_v1beta.json',
   'DATA'),
  ('googleapiclient\\discovery_cache\\documents\\recommender.v1.json',
   'c:\\Users\\<USER>\\Desktop\\Mes_projets\\WritingToolsClean\\Windows_and_Linux\\myvenv\\Lib\\site-packages\\googleapiclient\\discovery_cache\\documents\\recommender.v1.json',
   'DATA'),
  ('googleapiclient\\discovery_cache\\documents\\firebaserules.v1.json',
   'c:\\Users\\<USER>\\Desktop\\Mes_projets\\WritingToolsClean\\Windows_and_Linux\\myvenv\\Lib\\site-packages\\googleapiclient\\discovery_cache\\documents\\firebaserules.v1.json',
   'DATA'),
  ('googleapiclient\\discovery_cache\\documents\\iap.v1.json',
   'c:\\Users\\<USER>\\Desktop\\Mes_projets\\WritingToolsClean\\Windows_and_Linux\\myvenv\\Lib\\site-packages\\googleapiclient\\discovery_cache\\documents\\iap.v1.json',
   'DATA'),
  ('googleapiclient\\discovery_cache\\documents\\integrations.v1alpha.json',
   'c:\\Users\\<USER>\\Desktop\\Mes_projets\\WritingToolsClean\\Windows_and_Linux\\myvenv\\Lib\\site-packages\\googleapiclient\\discovery_cache\\documents\\integrations.v1alpha.json',
   'DATA'),
  ('googleapiclient\\discovery_cache\\documents\\cloudresourcemanager.v1beta1.json',
   'c:\\Users\\<USER>\\Desktop\\Mes_projets\\WritingToolsClean\\Windows_and_Linux\\myvenv\\Lib\\site-packages\\googleapiclient\\discovery_cache\\documents\\cloudresourcemanager.v1beta1.json',
   'DATA'),
  ('googleapiclient\\discovery_cache\\documents\\osconfig.v1.json',
   'c:\\Users\\<USER>\\Desktop\\Mes_projets\\WritingToolsClean\\Windows_and_Linux\\myvenv\\Lib\\site-packages\\googleapiclient\\discovery_cache\\documents\\osconfig.v1.json',
   'DATA'),
  ('googleapiclient\\discovery_cache\\documents\\transcoder.v1beta1.json',
   'c:\\Users\\<USER>\\Desktop\\Mes_projets\\WritingToolsClean\\Windows_and_Linux\\myvenv\\Lib\\site-packages\\googleapiclient\\discovery_cache\\documents\\transcoder.v1beta1.json',
   'DATA'),
  ('googleapiclient\\discovery_cache\\documents\\fitness.v1.json',
   'c:\\Users\\<USER>\\Desktop\\Mes_projets\\WritingToolsClean\\Windows_and_Linux\\myvenv\\Lib\\site-packages\\googleapiclient\\discovery_cache\\documents\\fitness.v1.json',
   'DATA'),
  ('googleapiclient\\discovery_cache\\documents\\networkservices.v1.json',
   'c:\\Users\\<USER>\\Desktop\\Mes_projets\\WritingToolsClean\\Windows_and_Linux\\myvenv\\Lib\\site-packages\\googleapiclient\\discovery_cache\\documents\\networkservices.v1.json',
   'DATA'),
  ('googleapiclient\\discovery_cache\\documents\\firestore.v1beta2.json',
   'c:\\Users\\<USER>\\Desktop\\Mes_projets\\WritingToolsClean\\Windows_and_Linux\\myvenv\\Lib\\site-packages\\googleapiclient\\discovery_cache\\documents\\firestore.v1beta2.json',
   'DATA'),
  ('googleapiclient\\discovery_cache\\documents\\remotebuildexecution.v2.json',
   'c:\\Users\\<USER>\\Desktop\\Mes_projets\\WritingToolsClean\\Windows_and_Linux\\myvenv\\Lib\\site-packages\\googleapiclient\\discovery_cache\\documents\\remotebuildexecution.v2.json',
   'DATA'),
  ('googleapiclient\\discovery_cache\\documents\\cloudcommerceprocurement.v1.json',
   'c:\\Users\\<USER>\\Desktop\\Mes_projets\\WritingToolsClean\\Windows_and_Linux\\myvenv\\Lib\\site-packages\\googleapiclient\\discovery_cache\\documents\\cloudcommerceprocurement.v1.json',
   'DATA'),
  ('googleapiclient\\discovery_cache\\documents\\sqladmin.v1beta4.json',
   'c:\\Users\\<USER>\\Desktop\\Mes_projets\\WritingToolsClean\\Windows_and_Linux\\myvenv\\Lib\\site-packages\\googleapiclient\\discovery_cache\\documents\\sqladmin.v1beta4.json',
   'DATA'),
  ('googleapiclient\\discovery_cache\\documents\\artifactregistry.v1beta2.json',
   'c:\\Users\\<USER>\\Desktop\\Mes_projets\\WritingToolsClean\\Windows_and_Linux\\myvenv\\Lib\\site-packages\\googleapiclient\\discovery_cache\\documents\\artifactregistry.v1beta2.json',
   'DATA'),
  ('googleapiclient\\discovery_cache\\documents\\forms.v1.json',
   'c:\\Users\\<USER>\\Desktop\\Mes_projets\\WritingToolsClean\\Windows_and_Linux\\myvenv\\Lib\\site-packages\\googleapiclient\\discovery_cache\\documents\\forms.v1.json',
   'DATA'),
  ('googleapiclient\\discovery_cache\\documents\\firebaseml.v2beta.json',
   'c:\\Users\\<USER>\\Desktop\\Mes_projets\\WritingToolsClean\\Windows_and_Linux\\myvenv\\Lib\\site-packages\\googleapiclient\\discovery_cache\\documents\\firebaseml.v2beta.json',
   'DATA'),
  ('googleapiclient\\discovery_cache\\documents\\privateca.v1beta1.json',
   'c:\\Users\\<USER>\\Desktop\\Mes_projets\\WritingToolsClean\\Windows_and_Linux\\myvenv\\Lib\\site-packages\\googleapiclient\\discovery_cache\\documents\\privateca.v1beta1.json',
   'DATA'),
  ('googleapiclient\\discovery_cache\\documents\\networkconnectivity.v1.json',
   'c:\\Users\\<USER>\\Desktop\\Mes_projets\\WritingToolsClean\\Windows_and_Linux\\myvenv\\Lib\\site-packages\\googleapiclient\\discovery_cache\\documents\\networkconnectivity.v1.json',
   'DATA'),
  ('googleapiclient\\discovery_cache\\documents\\datastore.v1beta3.json',
   'c:\\Users\\<USER>\\Desktop\\Mes_projets\\WritingToolsClean\\Windows_and_Linux\\myvenv\\Lib\\site-packages\\googleapiclient\\discovery_cache\\documents\\datastore.v1beta3.json',
   'DATA'),
  ('googleapiclient\\discovery_cache\\documents\\cloudasset.v1p7beta1.json',
   'c:\\Users\\<USER>\\Desktop\\Mes_projets\\WritingToolsClean\\Windows_and_Linux\\myvenv\\Lib\\site-packages\\googleapiclient\\discovery_cache\\documents\\cloudasset.v1p7beta1.json',
   'DATA'),
  ('googleapiclient\\discovery_cache\\documents\\serviceconsumermanagement.v1.json',
   'c:\\Users\\<USER>\\Desktop\\Mes_projets\\WritingToolsClean\\Windows_and_Linux\\myvenv\\Lib\\site-packages\\googleapiclient\\discovery_cache\\documents\\serviceconsumermanagement.v1.json',
   'DATA'),
  ('googleapiclient\\discovery_cache\\documents\\metastore.v2alpha.json',
   'c:\\Users\\<USER>\\Desktop\\Mes_projets\\WritingToolsClean\\Windows_and_Linux\\myvenv\\Lib\\site-packages\\googleapiclient\\discovery_cache\\documents\\metastore.v2alpha.json',
   'DATA'),
  ('googleapiclient\\discovery_cache\\documents\\datastream.v1alpha1.json',
   'c:\\Users\\<USER>\\Desktop\\Mes_projets\\WritingToolsClean\\Windows_and_Linux\\myvenv\\Lib\\site-packages\\googleapiclient\\discovery_cache\\documents\\datastream.v1alpha1.json',
   'DATA'),
  ('googleapiclient\\discovery_cache\\documents\\apim.v1alpha.json',
   'c:\\Users\\<USER>\\Desktop\\Mes_projets\\WritingToolsClean\\Windows_and_Linux\\myvenv\\Lib\\site-packages\\googleapiclient\\discovery_cache\\documents\\apim.v1alpha.json',
   'DATA'),
  ('googleapiclient\\discovery_cache\\documents\\containeranalysis.v1beta1.json',
   'c:\\Users\\<USER>\\Desktop\\Mes_projets\\WritingToolsClean\\Windows_and_Linux\\myvenv\\Lib\\site-packages\\googleapiclient\\discovery_cache\\documents\\containeranalysis.v1beta1.json',
   'DATA'),
  ('googleapiclient\\discovery_cache\\documents\\assuredworkloads.v1beta1.json',
   'c:\\Users\\<USER>\\Desktop\\Mes_projets\\WritingToolsClean\\Windows_and_Linux\\myvenv\\Lib\\site-packages\\googleapiclient\\discovery_cache\\documents\\assuredworkloads.v1beta1.json',
   'DATA'),
  ('googleapiclient\\discovery_cache\\documents\\dialogflow.v3beta1.json',
   'c:\\Users\\<USER>\\Desktop\\Mes_projets\\WritingToolsClean\\Windows_and_Linux\\myvenv\\Lib\\site-packages\\googleapiclient\\discovery_cache\\documents\\dialogflow.v3beta1.json',
   'DATA'),
  ('googleapiclient\\discovery_cache\\documents\\gkehub.v1beta1.json',
   'c:\\Users\\<USER>\\Desktop\\Mes_projets\\WritingToolsClean\\Windows_and_Linux\\myvenv\\Lib\\site-packages\\googleapiclient\\discovery_cache\\documents\\gkehub.v1beta1.json',
   'DATA'),
  ('googleapiclient\\discovery_cache\\documents\\mybusinessverifications.v1.json',
   'c:\\Users\\<USER>\\Desktop\\Mes_projets\\WritingToolsClean\\Windows_and_Linux\\myvenv\\Lib\\site-packages\\googleapiclient\\discovery_cache\\documents\\mybusinessverifications.v1.json',
   'DATA'),
  ('googleapiclient\\discovery_cache\\documents\\parallelstore.v1.json',
   'c:\\Users\\<USER>\\Desktop\\Mes_projets\\WritingToolsClean\\Windows_and_Linux\\myvenv\\Lib\\site-packages\\googleapiclient\\discovery_cache\\documents\\parallelstore.v1.json',
   'DATA'),
  ('googleapiclient\\discovery_cache\\documents\\testing.v1.json',
   'c:\\Users\\<USER>\\Desktop\\Mes_projets\\WritingToolsClean\\Windows_and_Linux\\myvenv\\Lib\\site-packages\\googleapiclient\\discovery_cache\\documents\\testing.v1.json',
   'DATA'),
  ('googleapiclient\\discovery_cache\\documents\\script.v1.json',
   'c:\\Users\\<USER>\\Desktop\\Mes_projets\\WritingToolsClean\\Windows_and_Linux\\myvenv\\Lib\\site-packages\\googleapiclient\\discovery_cache\\documents\\script.v1.json',
   'DATA'),
  ('googleapiclient\\discovery_cache\\documents\\metastore.v1alpha.json',
   'c:\\Users\\<USER>\\Desktop\\Mes_projets\\WritingToolsClean\\Windows_and_Linux\\myvenv\\Lib\\site-packages\\googleapiclient\\discovery_cache\\documents\\metastore.v1alpha.json',
   'DATA'),
  ('googleapiclient\\discovery_cache\\documents\\spanner.v1.json',
   'c:\\Users\\<USER>\\Desktop\\Mes_projets\\WritingToolsClean\\Windows_and_Linux\\myvenv\\Lib\\site-packages\\googleapiclient\\discovery_cache\\documents\\spanner.v1.json',
   'DATA'),
  ('googleapiclient\\discovery_cache\\documents\\sasportal.v1alpha1.json',
   'c:\\Users\\<USER>\\Desktop\\Mes_projets\\WritingToolsClean\\Windows_and_Linux\\myvenv\\Lib\\site-packages\\googleapiclient\\discovery_cache\\documents\\sasportal.v1alpha1.json',
   'DATA'),
  ('googleapiclient\\discovery_cache\\documents\\gmailpostmastertools.v1.json',
   'c:\\Users\\<USER>\\Desktop\\Mes_projets\\WritingToolsClean\\Windows_and_Linux\\myvenv\\Lib\\site-packages\\googleapiclient\\discovery_cache\\documents\\gmailpostmastertools.v1.json',
   'DATA'),
  ('googleapiclient\\discovery_cache\\documents\\datastream.v1.json',
   'c:\\Users\\<USER>\\Desktop\\Mes_projets\\WritingToolsClean\\Windows_and_Linux\\myvenv\\Lib\\site-packages\\googleapiclient\\discovery_cache\\documents\\datastream.v1.json',
   'DATA'),
  ('googleapiclient\\discovery_cache\\documents\\youtube.v3.json',
   'c:\\Users\\<USER>\\Desktop\\Mes_projets\\WritingToolsClean\\Windows_and_Linux\\myvenv\\Lib\\site-packages\\googleapiclient\\discovery_cache\\documents\\youtube.v3.json',
   'DATA'),
  ('googleapiclient\\discovery_cache\\documents\\merchantapi.conversions_v1beta.json',
   'c:\\Users\\<USER>\\Desktop\\Mes_projets\\WritingToolsClean\\Windows_and_Linux\\myvenv\\Lib\\site-packages\\googleapiclient\\discovery_cache\\documents\\merchantapi.conversions_v1beta.json',
   'DATA'),
  ('googleapiclient\\discovery_cache\\documents\\dataflow.v1b3.json',
   'c:\\Users\\<USER>\\Desktop\\Mes_projets\\WritingToolsClean\\Windows_and_Linux\\myvenv\\Lib\\site-packages\\googleapiclient\\discovery_cache\\documents\\dataflow.v1b3.json',
   'DATA'),
  ('googleapiclient\\discovery_cache\\documents\\policytroubleshooter.v1.json',
   'c:\\Users\\<USER>\\Desktop\\Mes_projets\\WritingToolsClean\\Windows_and_Linux\\myvenv\\Lib\\site-packages\\googleapiclient\\discovery_cache\\documents\\policytroubleshooter.v1.json',
   'DATA'),
  ('googleapiclient\\discovery_cache\\documents\\merchantapi.lfp_v1beta.json',
   'c:\\Users\\<USER>\\Desktop\\Mes_projets\\WritingToolsClean\\Windows_and_Linux\\myvenv\\Lib\\site-packages\\googleapiclient\\discovery_cache\\documents\\merchantapi.lfp_v1beta.json',
   'DATA'),
  ('googleapiclient\\discovery_cache\\documents\\resourcesettings.v1.json',
   'c:\\Users\\<USER>\\Desktop\\Mes_projets\\WritingToolsClean\\Windows_and_Linux\\myvenv\\Lib\\site-packages\\googleapiclient\\discovery_cache\\documents\\resourcesettings.v1.json',
   'DATA'),
  ('googleapiclient\\discovery_cache\\documents\\gkehub.v1beta.json',
   'c:\\Users\\<USER>\\Desktop\\Mes_projets\\WritingToolsClean\\Windows_and_Linux\\myvenv\\Lib\\site-packages\\googleapiclient\\discovery_cache\\documents\\gkehub.v1beta.json',
   'DATA'),
  ('googleapiclient\\discovery_cache\\documents\\vectortile.v1.json',
   'c:\\Users\\<USER>\\Desktop\\Mes_projets\\WritingToolsClean\\Windows_and_Linux\\myvenv\\Lib\\site-packages\\googleapiclient\\discovery_cache\\documents\\vectortile.v1.json',
   'DATA'),
  ('googleapiclient\\discovery_cache\\documents\\playintegrity.v1.json',
   'c:\\Users\\<USER>\\Desktop\\Mes_projets\\WritingToolsClean\\Windows_and_Linux\\myvenv\\Lib\\site-packages\\googleapiclient\\discovery_cache\\documents\\playintegrity.v1.json',
   'DATA'),
  ('googleapiclient\\discovery_cache\\documents\\cloudbilling.v1.json',
   'c:\\Users\\<USER>\\Desktop\\Mes_projets\\WritingToolsClean\\Windows_and_Linux\\myvenv\\Lib\\site-packages\\googleapiclient\\discovery_cache\\documents\\cloudbilling.v1.json',
   'DATA'),
  ('googleapiclient\\discovery_cache\\documents\\keep.v1.json',
   'c:\\Users\\<USER>\\Desktop\\Mes_projets\\WritingToolsClean\\Windows_and_Linux\\myvenv\\Lib\\site-packages\\googleapiclient\\discovery_cache\\documents\\keep.v1.json',
   'DATA'),
  ('googleapiclient\\discovery_cache\\documents\\secretmanager.v1.json',
   'c:\\Users\\<USER>\\Desktop\\Mes_projets\\WritingToolsClean\\Windows_and_Linux\\myvenv\\Lib\\site-packages\\googleapiclient\\discovery_cache\\documents\\secretmanager.v1.json',
   'DATA'),
  ('googleapiclient\\discovery_cache\\documents\\bigqueryconnection.v1beta1.json',
   'c:\\Users\\<USER>\\Desktop\\Mes_projets\\WritingToolsClean\\Windows_and_Linux\\myvenv\\Lib\\site-packages\\googleapiclient\\discovery_cache\\documents\\bigqueryconnection.v1beta1.json',
   'DATA'),
  ('googleapiclient\\discovery_cache\\documents\\youtubereporting.v1.json',
   'c:\\Users\\<USER>\\Desktop\\Mes_projets\\WritingToolsClean\\Windows_and_Linux\\myvenv\\Lib\\site-packages\\googleapiclient\\discovery_cache\\documents\\youtubereporting.v1.json',
   'DATA'),
  ('googleapiclient\\discovery_cache\\documents\\datafusion.v1.json',
   'c:\\Users\\<USER>\\Desktop\\Mes_projets\\WritingToolsClean\\Windows_and_Linux\\myvenv\\Lib\\site-packages\\googleapiclient\\discovery_cache\\documents\\datafusion.v1.json',
   'DATA'),
  ('googleapiclient\\discovery_cache\\documents\\mybusinessbusinesscalls.v1.json',
   'c:\\Users\\<USER>\\Desktop\\Mes_projets\\WritingToolsClean\\Windows_and_Linux\\myvenv\\Lib\\site-packages\\googleapiclient\\discovery_cache\\documents\\mybusinessbusinesscalls.v1.json',
   'DATA'),
  ('googleapiclient\\discovery_cache\\documents\\bigqueryconnection.v1.json',
   'c:\\Users\\<USER>\\Desktop\\Mes_projets\\WritingToolsClean\\Windows_and_Linux\\myvenv\\Lib\\site-packages\\googleapiclient\\discovery_cache\\documents\\bigqueryconnection.v1.json',
   'DATA'),
  ('googleapiclient\\discovery_cache\\documents\\customsearch.v1.json',
   'c:\\Users\\<USER>\\Desktop\\Mes_projets\\WritingToolsClean\\Windows_and_Linux\\myvenv\\Lib\\site-packages\\googleapiclient\\discovery_cache\\documents\\customsearch.v1.json',
   'DATA'),
  ('googleapiclient\\discovery_cache\\documents\\apigeeregistry.v1.json',
   'c:\\Users\\<USER>\\Desktop\\Mes_projets\\WritingToolsClean\\Windows_and_Linux\\myvenv\\Lib\\site-packages\\googleapiclient\\discovery_cache\\documents\\apigeeregistry.v1.json',
   'DATA'),
  ('googleapiclient\\discovery_cache\\documents\\vision.v1p1beta1.json',
   'c:\\Users\\<USER>\\Desktop\\Mes_projets\\WritingToolsClean\\Windows_and_Linux\\myvenv\\Lib\\site-packages\\googleapiclient\\discovery_cache\\documents\\vision.v1p1beta1.json',
   'DATA'),
  ('googleapiclient\\discovery_cache\\documents\\dataportability.v1.json',
   'c:\\Users\\<USER>\\Desktop\\Mes_projets\\WritingToolsClean\\Windows_and_Linux\\myvenv\\Lib\\site-packages\\googleapiclient\\discovery_cache\\documents\\dataportability.v1.json',
   'DATA'),
  ('googleapiclient\\discovery_cache\\documents\\biglake.v1.json',
   'c:\\Users\\<USER>\\Desktop\\Mes_projets\\WritingToolsClean\\Windows_and_Linux\\myvenv\\Lib\\site-packages\\googleapiclient\\discovery_cache\\documents\\biglake.v1.json',
   'DATA'),
  ('googleapiclient\\discovery_cache\\documents\\workflows.v1.json',
   'c:\\Users\\<USER>\\Desktop\\Mes_projets\\WritingToolsClean\\Windows_and_Linux\\myvenv\\Lib\\site-packages\\googleapiclient\\discovery_cache\\documents\\workflows.v1.json',
   'DATA'),
  ('googleapiclient\\discovery_cache\\documents\\appengine.v1alpha.json',
   'c:\\Users\\<USER>\\Desktop\\Mes_projets\\WritingToolsClean\\Windows_and_Linux\\myvenv\\Lib\\site-packages\\googleapiclient\\discovery_cache\\documents\\appengine.v1alpha.json',
   'DATA'),
  ('googleapiclient\\discovery_cache\\documents\\gameservices.v1beta.json',
   'c:\\Users\\<USER>\\Desktop\\Mes_projets\\WritingToolsClean\\Windows_and_Linux\\myvenv\\Lib\\site-packages\\googleapiclient\\discovery_cache\\documents\\gameservices.v1beta.json',
   'DATA'),
  ('googleapiclient\\discovery_cache\\documents\\airquality.v1.json',
   'c:\\Users\\<USER>\\Desktop\\Mes_projets\\WritingToolsClean\\Windows_and_Linux\\myvenv\\Lib\\site-packages\\googleapiclient\\discovery_cache\\documents\\airquality.v1.json',
   'DATA'),
  ('googleapiclient\\discovery_cache\\documents\\mybusinessaccountmanagement.v1.json',
   'c:\\Users\\<USER>\\Desktop\\Mes_projets\\WritingToolsClean\\Windows_and_Linux\\myvenv\\Lib\\site-packages\\googleapiclient\\discovery_cache\\documents\\mybusinessaccountmanagement.v1.json',
   'DATA'),
  ('google_api_python_client-2.177.0.dist-info\\WHEEL',
   'c:\\Users\\<USER>\\Desktop\\Mes_projets\\WritingToolsClean\\Windows_and_Linux\\myvenv\\Lib\\site-packages\\google_api_python_client-2.177.0.dist-info\\WHEEL',
   'DATA'),
  ('googleapiclient\\discovery_cache\\documents\\workflowexecutions.v1beta.json',
   'c:\\Users\\<USER>\\Desktop\\Mes_projets\\WritingToolsClean\\Windows_and_Linux\\myvenv\\Lib\\site-packages\\googleapiclient\\discovery_cache\\documents\\workflowexecutions.v1beta.json',
   'DATA'),
  ('googleapiclient\\discovery_cache\\documents\\connectors.v2.json',
   'c:\\Users\\<USER>\\Desktop\\Mes_projets\\WritingToolsClean\\Windows_and_Linux\\myvenv\\Lib\\site-packages\\googleapiclient\\discovery_cache\\documents\\connectors.v2.json',
   'DATA'),
  ('googleapiclient\\discovery_cache\\documents\\apphub.v1.json',
   'c:\\Users\\<USER>\\Desktop\\Mes_projets\\WritingToolsClean\\Windows_and_Linux\\myvenv\\Lib\\site-packages\\googleapiclient\\discovery_cache\\documents\\apphub.v1.json',
   'DATA'),
  ('googleapiclient\\discovery_cache\\documents\\publicca.v1beta1.json',
   'c:\\Users\\<USER>\\Desktop\\Mes_projets\\WritingToolsClean\\Windows_and_Linux\\myvenv\\Lib\\site-packages\\googleapiclient\\discovery_cache\\documents\\publicca.v1beta1.json',
   'DATA'),
  ('googleapiclient\\discovery_cache\\documents\\jobs.v3.json',
   'c:\\Users\\<USER>\\Desktop\\Mes_projets\\WritingToolsClean\\Windows_and_Linux\\myvenv\\Lib\\site-packages\\googleapiclient\\discovery_cache\\documents\\jobs.v3.json',
   'DATA'),
  ('googleapiclient\\discovery_cache\\documents\\cloudasset.v1beta1.json',
   'c:\\Users\\<USER>\\Desktop\\Mes_projets\\WritingToolsClean\\Windows_and_Linux\\myvenv\\Lib\\site-packages\\googleapiclient\\discovery_cache\\documents\\cloudasset.v1beta1.json',
   'DATA'),
  ('googleapiclient\\discovery_cache\\documents\\redis.v1.json',
   'c:\\Users\\<USER>\\Desktop\\Mes_projets\\WritingToolsClean\\Windows_and_Linux\\myvenv\\Lib\\site-packages\\googleapiclient\\discovery_cache\\documents\\redis.v1.json',
   'DATA'),
  ('googleapiclient\\discovery_cache\\documents\\firestore.v1.json',
   'c:\\Users\\<USER>\\Desktop\\Mes_projets\\WritingToolsClean\\Windows_and_Linux\\myvenv\\Lib\\site-packages\\googleapiclient\\discovery_cache\\documents\\firestore.v1.json',
   'DATA'),
  ('googleapiclient\\discovery_cache\\documents\\adexchangebuyer.v1.4.json',
   'c:\\Users\\<USER>\\Desktop\\Mes_projets\\WritingToolsClean\\Windows_and_Linux\\myvenv\\Lib\\site-packages\\googleapiclient\\discovery_cache\\documents\\adexchangebuyer.v1.4.json',
   'DATA'),
  ('googleapiclient\\discovery_cache\\documents\\vault.v1.json',
   'c:\\Users\\<USER>\\Desktop\\Mes_projets\\WritingToolsClean\\Windows_and_Linux\\myvenv\\Lib\\site-packages\\googleapiclient\\discovery_cache\\documents\\vault.v1.json',
   'DATA'),
  ('googleapiclient\\discovery_cache\\documents\\ondemandscanning.v1.json',
   'c:\\Users\\<USER>\\Desktop\\Mes_projets\\WritingToolsClean\\Windows_and_Linux\\myvenv\\Lib\\site-packages\\googleapiclient\\discovery_cache\\documents\\ondemandscanning.v1.json',
   'DATA'),
  ('googleapiclient\\discovery_cache\\documents\\cloudtasks.v2beta2.json',
   'c:\\Users\\<USER>\\Desktop\\Mes_projets\\WritingToolsClean\\Windows_and_Linux\\myvenv\\Lib\\site-packages\\googleapiclient\\discovery_cache\\documents\\cloudtasks.v2beta2.json',
   'DATA'),
  ('googleapiclient\\discovery_cache\\documents\\retail.v2alpha.json',
   'c:\\Users\\<USER>\\Desktop\\Mes_projets\\WritingToolsClean\\Windows_and_Linux\\myvenv\\Lib\\site-packages\\googleapiclient\\discovery_cache\\documents\\retail.v2alpha.json',
   'DATA'),
  ('googleapiclient\\discovery_cache\\documents\\cloudscheduler.v1.json',
   'c:\\Users\\<USER>\\Desktop\\Mes_projets\\WritingToolsClean\\Windows_and_Linux\\myvenv\\Lib\\site-packages\\googleapiclient\\discovery_cache\\documents\\cloudscheduler.v1.json',
   'DATA'),
  ('googleapiclient\\discovery_cache\\documents\\policyanalyzer.v1.json',
   'c:\\Users\\<USER>\\Desktop\\Mes_projets\\WritingToolsClean\\Windows_and_Linux\\myvenv\\Lib\\site-packages\\googleapiclient\\discovery_cache\\documents\\policyanalyzer.v1.json',
   'DATA'),
  ('googleapiclient\\discovery_cache\\documents\\displayvideo.v2.json',
   'c:\\Users\\<USER>\\Desktop\\Mes_projets\\WritingToolsClean\\Windows_and_Linux\\myvenv\\Lib\\site-packages\\googleapiclient\\discovery_cache\\documents\\displayvideo.v2.json',
   'DATA'),
  ('googleapiclient\\discovery_cache\\documents\\networksecurity.v1beta1.json',
   'c:\\Users\\<USER>\\Desktop\\Mes_projets\\WritingToolsClean\\Windows_and_Linux\\myvenv\\Lib\\site-packages\\googleapiclient\\discovery_cache\\documents\\networksecurity.v1beta1.json',
   'DATA'),
  ('googleapiclient\\discovery_cache\\documents\\language.v1beta1.json',
   'c:\\Users\\<USER>\\Desktop\\Mes_projets\\WritingToolsClean\\Windows_and_Linux\\myvenv\\Lib\\site-packages\\googleapiclient\\discovery_cache\\documents\\language.v1beta1.json',
   'DATA'),
  ('googleapiclient\\discovery_cache\\documents\\cloudasset.v1p1beta1.json',
   'c:\\Users\\<USER>\\Desktop\\Mes_projets\\WritingToolsClean\\Windows_and_Linux\\myvenv\\Lib\\site-packages\\googleapiclient\\discovery_cache\\documents\\cloudasset.v1p1beta1.json',
   'DATA'),
  ('googleapiclient\\discovery_cache\\documents\\firebaseappdistribution.v1.json',
   'c:\\Users\\<USER>\\Desktop\\Mes_projets\\WritingToolsClean\\Windows_and_Linux\\myvenv\\Lib\\site-packages\\googleapiclient\\discovery_cache\\documents\\firebaseappdistribution.v1.json',
   'DATA'),
  ('googleapiclient\\discovery_cache\\documents\\addressvalidation.v1.json',
   'c:\\Users\\<USER>\\Desktop\\Mes_projets\\WritingToolsClean\\Windows_and_Linux\\myvenv\\Lib\\site-packages\\googleapiclient\\discovery_cache\\documents\\addressvalidation.v1.json',
   'DATA'),
  ('googleapiclient\\discovery_cache\\documents\\adexchangebuyer2.v2beta1.json',
   'c:\\Users\\<USER>\\Desktop\\Mes_projets\\WritingToolsClean\\Windows_and_Linux\\myvenv\\Lib\\site-packages\\googleapiclient\\discovery_cache\\documents\\adexchangebuyer2.v2beta1.json',
   'DATA'),
  ('googleapiclient\\discovery_cache\\documents\\artifactregistry.v1.json',
   'c:\\Users\\<USER>\\Desktop\\Mes_projets\\WritingToolsClean\\Windows_and_Linux\\myvenv\\Lib\\site-packages\\googleapiclient\\discovery_cache\\documents\\artifactregistry.v1.json',
   'DATA'),
  ('googleapiclient\\discovery_cache\\documents\\servicenetworking.v1beta.json',
   'c:\\Users\\<USER>\\Desktop\\Mes_projets\\WritingToolsClean\\Windows_and_Linux\\myvenv\\Lib\\site-packages\\googleapiclient\\discovery_cache\\documents\\servicenetworking.v1beta.json',
   'DATA'),
  ('googleapiclient\\discovery_cache\\documents\\gmailpostmastertools.v1beta1.json',
   'c:\\Users\\<USER>\\Desktop\\Mes_projets\\WritingToolsClean\\Windows_and_Linux\\myvenv\\Lib\\site-packages\\googleapiclient\\discovery_cache\\documents\\gmailpostmastertools.v1beta1.json',
   'DATA'),
  ('googleapiclient\\discovery_cache\\documents\\iam.v1.json',
   'c:\\Users\\<USER>\\Desktop\\Mes_projets\\WritingToolsClean\\Windows_and_Linux\\myvenv\\Lib\\site-packages\\googleapiclient\\discovery_cache\\documents\\iam.v1.json',
   'DATA'),
  ('googleapiclient\\discovery_cache\\documents\\oslogin.v1beta.json',
   'c:\\Users\\<USER>\\Desktop\\Mes_projets\\WritingToolsClean\\Windows_and_Linux\\myvenv\\Lib\\site-packages\\googleapiclient\\discovery_cache\\documents\\oslogin.v1beta.json',
   'DATA'),
  ('googleapiclient\\discovery_cache\\documents\\identitytoolkit.v1.json',
   'c:\\Users\\<USER>\\Desktop\\Mes_projets\\WritingToolsClean\\Windows_and_Linux\\myvenv\\Lib\\site-packages\\googleapiclient\\discovery_cache\\documents\\identitytoolkit.v1.json',
   'DATA'),
  ('googleapiclient\\discovery_cache\\documents\\tagmanager.v1.json',
   'c:\\Users\\<USER>\\Desktop\\Mes_projets\\WritingToolsClean\\Windows_and_Linux\\myvenv\\Lib\\site-packages\\googleapiclient\\discovery_cache\\documents\\tagmanager.v1.json',
   'DATA'),
  ('googleapiclient\\discovery_cache\\documents\\compute.v1.json',
   'c:\\Users\\<USER>\\Desktop\\Mes_projets\\WritingToolsClean\\Windows_and_Linux\\myvenv\\Lib\\site-packages\\googleapiclient\\discovery_cache\\documents\\compute.v1.json',
   'DATA'),
  ('googleapiclient\\discovery_cache\\documents\\realtimebidding.v1alpha.json',
   'c:\\Users\\<USER>\\Desktop\\Mes_projets\\WritingToolsClean\\Windows_and_Linux\\myvenv\\Lib\\site-packages\\googleapiclient\\discovery_cache\\documents\\realtimebidding.v1alpha.json',
   'DATA'),
  ('googleapiclient\\discovery_cache\\documents\\vision.v1p2beta1.json',
   'c:\\Users\\<USER>\\Desktop\\Mes_projets\\WritingToolsClean\\Windows_and_Linux\\myvenv\\Lib\\site-packages\\googleapiclient\\discovery_cache\\documents\\vision.v1p2beta1.json',
   'DATA'),
  ('googleapiclient\\discovery_cache\\documents\\run.v2.json',
   'c:\\Users\\<USER>\\Desktop\\Mes_projets\\WritingToolsClean\\Windows_and_Linux\\myvenv\\Lib\\site-packages\\googleapiclient\\discovery_cache\\documents\\run.v2.json',
   'DATA'),
  ('googleapiclient\\discovery_cache\\documents\\chromeuxreport.v1.json',
   'c:\\Users\\<USER>\\Desktop\\Mes_projets\\WritingToolsClean\\Windows_and_Linux\\myvenv\\Lib\\site-packages\\googleapiclient\\discovery_cache\\documents\\chromeuxreport.v1.json',
   'DATA'),
  ('googleapiclient\\discovery_cache\\documents\\container.v1.json',
   'c:\\Users\\<USER>\\Desktop\\Mes_projets\\WritingToolsClean\\Windows_and_Linux\\myvenv\\Lib\\site-packages\\googleapiclient\\discovery_cache\\documents\\container.v1.json',
   'DATA'),
  ('googleapiclient\\discovery_cache\\documents\\firebaseappdistribution.v1alpha.json',
   'c:\\Users\\<USER>\\Desktop\\Mes_projets\\WritingToolsClean\\Windows_and_Linux\\myvenv\\Lib\\site-packages\\googleapiclient\\discovery_cache\\documents\\firebaseappdistribution.v1alpha.json',
   'DATA'),
  ('googleapiclient\\discovery_cache\\documents\\storage.v1.json',
   'c:\\Users\\<USER>\\Desktop\\Mes_projets\\WritingToolsClean\\Windows_and_Linux\\myvenv\\Lib\\site-packages\\googleapiclient\\discovery_cache\\documents\\storage.v1.json',
   'DATA'),
  ('googleapiclient\\discovery_cache\\documents\\cloudtrace.v2.json',
   'c:\\Users\\<USER>\\Desktop\\Mes_projets\\WritingToolsClean\\Windows_and_Linux\\myvenv\\Lib\\site-packages\\googleapiclient\\discovery_cache\\documents\\cloudtrace.v2.json',
   'DATA'),
  ('googleapiclient\\discovery_cache\\documents\\domains.v1.json',
   'c:\\Users\\<USER>\\Desktop\\Mes_projets\\WritingToolsClean\\Windows_and_Linux\\myvenv\\Lib\\site-packages\\googleapiclient\\discovery_cache\\documents\\domains.v1.json',
   'DATA'),
  ('googleapiclient\\discovery_cache\\documents\\cloudtasks.v2beta3.json',
   'c:\\Users\\<USER>\\Desktop\\Mes_projets\\WritingToolsClean\\Windows_and_Linux\\myvenv\\Lib\\site-packages\\googleapiclient\\discovery_cache\\documents\\cloudtasks.v2beta3.json',
   'DATA'),
  ('googleapiclient\\discovery_cache\\documents\\retail.v2beta.json',
   'c:\\Users\\<USER>\\Desktop\\Mes_projets\\WritingToolsClean\\Windows_and_Linux\\myvenv\\Lib\\site-packages\\googleapiclient\\discovery_cache\\documents\\retail.v2beta.json',
   'DATA'),
  ('googleapiclient\\discovery_cache\\documents\\oauth2.v2.json',
   'c:\\Users\\<USER>\\Desktop\\Mes_projets\\WritingToolsClean\\Windows_and_Linux\\myvenv\\Lib\\site-packages\\googleapiclient\\discovery_cache\\documents\\oauth2.v2.json',
   'DATA'),
  ('googleapiclient\\discovery_cache\\documents\\people.v1.json',
   'c:\\Users\\<USER>\\Desktop\\Mes_projets\\WritingToolsClean\\Windows_and_Linux\\myvenv\\Lib\\site-packages\\googleapiclient\\discovery_cache\\documents\\people.v1.json',
   'DATA'),
  ('googleapiclient\\discovery_cache\\documents\\genomics.v1.json',
   'c:\\Users\\<USER>\\Desktop\\Mes_projets\\WritingToolsClean\\Windows_and_Linux\\myvenv\\Lib\\site-packages\\googleapiclient\\discovery_cache\\documents\\genomics.v1.json',
   'DATA'),
  ('googleapiclient\\discovery_cache\\documents\\backupdr.v1.json',
   'c:\\Users\\<USER>\\Desktop\\Mes_projets\\WritingToolsClean\\Windows_and_Linux\\myvenv\\Lib\\site-packages\\googleapiclient\\discovery_cache\\documents\\backupdr.v1.json',
   'DATA'),
  ('googleapiclient\\discovery_cache\\documents\\webrisk.v1.json',
   'c:\\Users\\<USER>\\Desktop\\Mes_projets\\WritingToolsClean\\Windows_and_Linux\\myvenv\\Lib\\site-packages\\googleapiclient\\discovery_cache\\documents\\webrisk.v1.json',
   'DATA'),
  ('googleapiclient\\discovery_cache\\documents\\netapp.v1beta1.json',
   'c:\\Users\\<USER>\\Desktop\\Mes_projets\\WritingToolsClean\\Windows_and_Linux\\myvenv\\Lib\\site-packages\\googleapiclient\\discovery_cache\\documents\\netapp.v1beta1.json',
   'DATA'),
  ('googleapiclient\\discovery_cache\\documents\\policysimulator.v1beta1.json',
   'c:\\Users\\<USER>\\Desktop\\Mes_projets\\WritingToolsClean\\Windows_and_Linux\\myvenv\\Lib\\site-packages\\googleapiclient\\discovery_cache\\documents\\policysimulator.v1beta1.json',
   'DATA'),
  ('googleapiclient\\discovery_cache\\documents\\run.v1alpha1.json',
   'c:\\Users\\<USER>\\Desktop\\Mes_projets\\WritingToolsClean\\Windows_and_Linux\\myvenv\\Lib\\site-packages\\googleapiclient\\discovery_cache\\documents\\run.v1alpha1.json',
   'DATA'),
  ('google_api_python_client-2.177.0.dist-info\\licenses\\LICENSE',
   'c:\\Users\\<USER>\\Desktop\\Mes_projets\\WritingToolsClean\\Windows_and_Linux\\myvenv\\Lib\\site-packages\\google_api_python_client-2.177.0.dist-info\\licenses\\LICENSE',
   'DATA'),
  ('googleapiclient\\discovery_cache\\documents\\apihub.v1.json',
   'c:\\Users\\<USER>\\Desktop\\Mes_projets\\WritingToolsClean\\Windows_and_Linux\\myvenv\\Lib\\site-packages\\googleapiclient\\discovery_cache\\documents\\apihub.v1.json',
   'DATA'),
  ('googleapiclient\\discovery_cache\\documents\\mybusinessnotifications.v1.json',
   'c:\\Users\\<USER>\\Desktop\\Mes_projets\\WritingToolsClean\\Windows_and_Linux\\myvenv\\Lib\\site-packages\\googleapiclient\\discovery_cache\\documents\\mybusinessnotifications.v1.json',
   'DATA'),
  ('googleapiclient\\discovery_cache\\documents\\iam.v2beta.json',
   'c:\\Users\\<USER>\\Desktop\\Mes_projets\\WritingToolsClean\\Windows_and_Linux\\myvenv\\Lib\\site-packages\\googleapiclient\\discovery_cache\\documents\\iam.v2beta.json',
   'DATA'),
  ('googleapiclient\\discovery_cache\\documents\\workspaceevents.v1.json',
   'c:\\Users\\<USER>\\Desktop\\Mes_projets\\WritingToolsClean\\Windows_and_Linux\\myvenv\\Lib\\site-packages\\googleapiclient\\discovery_cache\\documents\\workspaceevents.v1.json',
   'DATA'),
  ('googleapiclient\\discovery_cache\\documents\\youtubeAnalytics.v1.json',
   'c:\\Users\\<USER>\\Desktop\\Mes_projets\\WritingToolsClean\\Windows_and_Linux\\myvenv\\Lib\\site-packages\\googleapiclient\\discovery_cache\\documents\\youtubeAnalytics.v1.json',
   'DATA'),
  ('googleapiclient\\discovery_cache\\documents\\pubsub.v1beta1a.json',
   'c:\\Users\\<USER>\\Desktop\\Mes_projets\\WritingToolsClean\\Windows_and_Linux\\myvenv\\Lib\\site-packages\\googleapiclient\\discovery_cache\\documents\\pubsub.v1beta1a.json',
   'DATA'),
  ('googleapiclient\\discovery_cache\\documents\\ideahub.v1beta.json',
   'c:\\Users\\<USER>\\Desktop\\Mes_projets\\WritingToolsClean\\Windows_and_Linux\\myvenv\\Lib\\site-packages\\googleapiclient\\discovery_cache\\documents\\ideahub.v1beta.json',
   'DATA'),
  ('googleapiclient\\discovery_cache\\documents\\gmail.v1.json',
   'c:\\Users\\<USER>\\Desktop\\Mes_projets\\WritingToolsClean\\Windows_and_Linux\\myvenv\\Lib\\site-packages\\googleapiclient\\discovery_cache\\documents\\gmail.v1.json',
   'DATA'),
  ('googleapiclient\\discovery_cache\\documents\\accesscontextmanager.v1beta.json',
   'c:\\Users\\<USER>\\Desktop\\Mes_projets\\WritingToolsClean\\Windows_and_Linux\\myvenv\\Lib\\site-packages\\googleapiclient\\discovery_cache\\documents\\accesscontextmanager.v1beta.json',
   'DATA'),
  ('googleapiclient\\discovery_cache\\documents\\networkservices.v1beta1.json',
   'c:\\Users\\<USER>\\Desktop\\Mes_projets\\WritingToolsClean\\Windows_and_Linux\\myvenv\\Lib\\site-packages\\googleapiclient\\discovery_cache\\documents\\networkservices.v1beta1.json',
   'DATA'),
  ('googleapiclient\\discovery_cache\\documents\\assuredworkloads.v1.json',
   'c:\\Users\\<USER>\\Desktop\\Mes_projets\\WritingToolsClean\\Windows_and_Linux\\myvenv\\Lib\\site-packages\\googleapiclient\\discovery_cache\\documents\\assuredworkloads.v1.json',
   'DATA'),
  ('googleapiclient\\discovery_cache\\documents\\vpcaccess.v1.json',
   'c:\\Users\\<USER>\\Desktop\\Mes_projets\\WritingToolsClean\\Windows_and_Linux\\myvenv\\Lib\\site-packages\\googleapiclient\\discovery_cache\\documents\\vpcaccess.v1.json',
   'DATA'),
  ('googleapiclient\\discovery_cache\\documents\\kgsearch.v1.json',
   'c:\\Users\\<USER>\\Desktop\\Mes_projets\\WritingToolsClean\\Windows_and_Linux\\myvenv\\Lib\\site-packages\\googleapiclient\\discovery_cache\\documents\\kgsearch.v1.json',
   'DATA'),
  ('googleapiclient\\discovery_cache\\documents\\bigtableadmin.v2.json',
   'c:\\Users\\<USER>\\Desktop\\Mes_projets\\WritingToolsClean\\Windows_and_Linux\\myvenv\\Lib\\site-packages\\googleapiclient\\discovery_cache\\documents\\bigtableadmin.v2.json',
   'DATA'),
  ('googleapiclient\\discovery_cache\\documents\\docs.v1.json',
   'c:\\Users\\<USER>\\Desktop\\Mes_projets\\WritingToolsClean\\Windows_and_Linux\\myvenv\\Lib\\site-packages\\googleapiclient\\discovery_cache\\documents\\docs.v1.json',
   'DATA'),
  ('googleapiclient\\discovery_cache\\documents\\monitoring.v1.json',
   'c:\\Users\\<USER>\\Desktop\\Mes_projets\\WritingToolsClean\\Windows_and_Linux\\myvenv\\Lib\\site-packages\\googleapiclient\\discovery_cache\\documents\\monitoring.v1.json',
   'DATA'),
  ('googleapiclient\\discovery_cache\\documents\\calendar.v3.json',
   'c:\\Users\\<USER>\\Desktop\\Mes_projets\\WritingToolsClean\\Windows_and_Linux\\myvenv\\Lib\\site-packages\\googleapiclient\\discovery_cache\\documents\\calendar.v3.json',
   'DATA'),
  ('googleapiclient\\discovery_cache\\documents\\androiddeviceprovisioning.v1.json',
   'c:\\Users\\<USER>\\Desktop\\Mes_projets\\WritingToolsClean\\Windows_and_Linux\\myvenv\\Lib\\site-packages\\googleapiclient\\discovery_cache\\documents\\androiddeviceprovisioning.v1.json',
   'DATA'),
  ('googleapiclient\\discovery_cache\\documents\\policysimulator.v1alpha.json',
   'c:\\Users\\<USER>\\Desktop\\Mes_projets\\WritingToolsClean\\Windows_and_Linux\\myvenv\\Lib\\site-packages\\googleapiclient\\discovery_cache\\documents\\policysimulator.v1alpha.json',
   'DATA'),
  ('googleapiclient\\discovery_cache\\documents\\integrations.v1.json',
   'c:\\Users\\<USER>\\Desktop\\Mes_projets\\WritingToolsClean\\Windows_and_Linux\\myvenv\\Lib\\site-packages\\googleapiclient\\discovery_cache\\documents\\integrations.v1.json',
   'DATA'),
  ('googleapiclient\\discovery_cache\\documents\\discoveryengine.v1alpha.json',
   'c:\\Users\\<USER>\\Desktop\\Mes_projets\\WritingToolsClean\\Windows_and_Linux\\myvenv\\Lib\\site-packages\\googleapiclient\\discovery_cache\\documents\\discoveryengine.v1alpha.json',
   'DATA'),
  ('googleapiclient\\discovery_cache\\documents\\speech.v1.json',
   'c:\\Users\\<USER>\\Desktop\\Mes_projets\\WritingToolsClean\\Windows_and_Linux\\myvenv\\Lib\\site-packages\\googleapiclient\\discovery_cache\\documents\\speech.v1.json',
   'DATA'),
  ('googleapiclient\\discovery_cache\\documents\\pubsublite.v1.json',
   'c:\\Users\\<USER>\\Desktop\\Mes_projets\\WritingToolsClean\\Windows_and_Linux\\myvenv\\Lib\\site-packages\\googleapiclient\\discovery_cache\\documents\\pubsublite.v1.json',
   'DATA'),
  ('googleapiclient\\discovery_cache\\documents\\containeranalysis.v1.json',
   'c:\\Users\\<USER>\\Desktop\\Mes_projets\\WritingToolsClean\\Windows_and_Linux\\myvenv\\Lib\\site-packages\\googleapiclient\\discovery_cache\\documents\\containeranalysis.v1.json',
   'DATA'),
  ('googleapiclient\\discovery_cache\\documents\\blockchainnodeengine.v1.json',
   'c:\\Users\\<USER>\\Desktop\\Mes_projets\\WritingToolsClean\\Windows_and_Linux\\myvenv\\Lib\\site-packages\\googleapiclient\\discovery_cache\\documents\\blockchainnodeengine.v1.json',
   'DATA'),
  ('googleapiclient\\discovery_cache\\documents\\gamesConfiguration.v1configuration.json',
   'c:\\Users\\<USER>\\Desktop\\Mes_projets\\WritingToolsClean\\Windows_and_Linux\\myvenv\\Lib\\site-packages\\googleapiclient\\discovery_cache\\documents\\gamesConfiguration.v1configuration.json',
   'DATA'),
  ('googleapiclient\\discovery_cache\\documents\\translate.v3beta1.json',
   'c:\\Users\\<USER>\\Desktop\\Mes_projets\\WritingToolsClean\\Windows_and_Linux\\myvenv\\Lib\\site-packages\\googleapiclient\\discovery_cache\\documents\\translate.v3beta1.json',
   'DATA'),
  ('googleapiclient\\discovery_cache\\documents\\doubleclickbidmanager.v1.1.json',
   'c:\\Users\\<USER>\\Desktop\\Mes_projets\\WritingToolsClean\\Windows_and_Linux\\myvenv\\Lib\\site-packages\\googleapiclient\\discovery_cache\\documents\\doubleclickbidmanager.v1.1.json',
   'DATA'),
  ('googleapiclient\\discovery_cache\\documents\\runtimeconfig.v1beta1.json',
   'c:\\Users\\<USER>\\Desktop\\Mes_projets\\WritingToolsClean\\Windows_and_Linux\\myvenv\\Lib\\site-packages\\googleapiclient\\discovery_cache\\documents\\runtimeconfig.v1beta1.json',
   'DATA'),
  ('googleapiclient\\discovery_cache\\documents\\chat.v1.json',
   'c:\\Users\\<USER>\\Desktop\\Mes_projets\\WritingToolsClean\\Windows_and_Linux\\myvenv\\Lib\\site-packages\\googleapiclient\\discovery_cache\\documents\\chat.v1.json',
   'DATA'),
  ('googleapiclient\\discovery_cache\\documents\\poly.v1.json',
   'c:\\Users\\<USER>\\Desktop\\Mes_projets\\WritingToolsClean\\Windows_and_Linux\\myvenv\\Lib\\site-packages\\googleapiclient\\discovery_cache\\documents\\poly.v1.json',
   'DATA'),
  ('googleapiclient\\discovery_cache\\documents\\dlp.v2.json',
   'c:\\Users\\<USER>\\Desktop\\Mes_projets\\WritingToolsClean\\Windows_and_Linux\\myvenv\\Lib\\site-packages\\googleapiclient\\discovery_cache\\documents\\dlp.v2.json',
   'DATA'),
  ('googleapiclient\\discovery_cache\\documents\\displayvideo.v3.json',
   'c:\\Users\\<USER>\\Desktop\\Mes_projets\\WritingToolsClean\\Windows_and_Linux\\myvenv\\Lib\\site-packages\\googleapiclient\\discovery_cache\\documents\\displayvideo.v3.json',
   'DATA'),
  ('googleapiclient\\discovery_cache\\documents\\gkehub.v2.json',
   'c:\\Users\\<USER>\\Desktop\\Mes_projets\\WritingToolsClean\\Windows_and_Linux\\myvenv\\Lib\\site-packages\\googleapiclient\\discovery_cache\\documents\\gkehub.v2.json',
   'DATA'),
  ('googleapiclient\\discovery_cache\\documents\\secretmanager.v1beta1.json',
   'c:\\Users\\<USER>\\Desktop\\Mes_projets\\WritingToolsClean\\Windows_and_Linux\\myvenv\\Lib\\site-packages\\googleapiclient\\discovery_cache\\documents\\secretmanager.v1beta1.json',
   'DATA'),
  ('googleapiclient\\discovery_cache\\documents\\cloudtrace.v1.json',
   'c:\\Users\\<USER>\\Desktop\\Mes_projets\\WritingToolsClean\\Windows_and_Linux\\myvenv\\Lib\\site-packages\\googleapiclient\\discovery_cache\\documents\\cloudtrace.v1.json',
   'DATA'),
  ('googleapiclient\\discovery_cache\\documents\\dataplex.v1.json',
   'c:\\Users\\<USER>\\Desktop\\Mes_projets\\WritingToolsClean\\Windows_and_Linux\\myvenv\\Lib\\site-packages\\googleapiclient\\discovery_cache\\documents\\dataplex.v1.json',
   'DATA'),
  ('googleapiclient\\discovery_cache\\documents\\siteVerification.v1.json',
   'c:\\Users\\<USER>\\Desktop\\Mes_projets\\WritingToolsClean\\Windows_and_Linux\\myvenv\\Lib\\site-packages\\googleapiclient\\discovery_cache\\documents\\siteVerification.v1.json',
   'DATA'),
  ('googleapiclient\\discovery_cache\\documents\\androidmanagement.v1.json',
   'c:\\Users\\<USER>\\Desktop\\Mes_projets\\WritingToolsClean\\Windows_and_Linux\\myvenv\\Lib\\site-packages\\googleapiclient\\discovery_cache\\documents\\androidmanagement.v1.json',
   'DATA'),
  ('googleapiclient\\discovery_cache\\documents\\cloudfunctions.v2alpha.json',
   'c:\\Users\\<USER>\\Desktop\\Mes_projets\\WritingToolsClean\\Windows_and_Linux\\myvenv\\Lib\\site-packages\\googleapiclient\\discovery_cache\\documents\\cloudfunctions.v2alpha.json',
   'DATA'),
  ('googleapiclient\\discovery_cache\\documents\\datamigration.v1.json',
   'c:\\Users\\<USER>\\Desktop\\Mes_projets\\WritingToolsClean\\Windows_and_Linux\\myvenv\\Lib\\site-packages\\googleapiclient\\discovery_cache\\documents\\datamigration.v1.json',
   'DATA'),
  ('googleapiclient\\discovery_cache\\documents\\cloudshell.v1alpha1.json',
   'c:\\Users\\<USER>\\Desktop\\Mes_projets\\WritingToolsClean\\Windows_and_Linux\\myvenv\\Lib\\site-packages\\googleapiclient\\discovery_cache\\documents\\cloudshell.v1alpha1.json',
   'DATA'),
  ('googleapiclient\\discovery_cache\\documents\\travelimpactmodel.v1.json',
   'c:\\Users\\<USER>\\Desktop\\Mes_projets\\WritingToolsClean\\Windows_and_Linux\\myvenv\\Lib\\site-packages\\googleapiclient\\discovery_cache\\documents\\travelimpactmodel.v1.json',
   'DATA'),
  ('googleapiclient\\discovery_cache\\documents\\gkehub.v1.json',
   'c:\\Users\\<USER>\\Desktop\\Mes_projets\\WritingToolsClean\\Windows_and_Linux\\myvenv\\Lib\\site-packages\\googleapiclient\\discovery_cache\\documents\\gkehub.v1.json',
   'DATA'),
  ('googleapiclient\\discovery_cache\\documents\\dfareporting.v3.3.json',
   'c:\\Users\\<USER>\\Desktop\\Mes_projets\\WritingToolsClean\\Windows_and_Linux\\myvenv\\Lib\\site-packages\\googleapiclient\\discovery_cache\\documents\\dfareporting.v3.3.json',
   'DATA'),
  ('googleapiclient\\discovery_cache\\documents\\artifactregistry.v1beta1.json',
   'c:\\Users\\<USER>\\Desktop\\Mes_projets\\WritingToolsClean\\Windows_and_Linux\\myvenv\\Lib\\site-packages\\googleapiclient\\discovery_cache\\documents\\artifactregistry.v1beta1.json',
   'DATA'),
  ('googleapiclient\\discovery_cache\\documents\\meet.v2.json',
   'c:\\Users\\<USER>\\Desktop\\Mes_projets\\WritingToolsClean\\Windows_and_Linux\\myvenv\\Lib\\site-packages\\googleapiclient\\discovery_cache\\documents\\meet.v2.json',
   'DATA'),
  ('googleapiclient\\discovery_cache\\documents\\alertcenter.v1beta1.json',
   'c:\\Users\\<USER>\\Desktop\\Mes_projets\\WritingToolsClean\\Windows_and_Linux\\myvenv\\Lib\\site-packages\\googleapiclient\\discovery_cache\\documents\\alertcenter.v1beta1.json',
   'DATA'),
  ('googleapiclient\\discovery_cache\\documents\\verifiedaccess.v1.json',
   'c:\\Users\\<USER>\\Desktop\\Mes_projets\\WritingToolsClean\\Windows_and_Linux\\myvenv\\Lib\\site-packages\\googleapiclient\\discovery_cache\\documents\\verifiedaccess.v1.json',
   'DATA'),
  ('googleapiclient\\discovery_cache\\documents\\essentialcontacts.v1.json',
   'c:\\Users\\<USER>\\Desktop\\Mes_projets\\WritingToolsClean\\Windows_and_Linux\\myvenv\\Lib\\site-packages\\googleapiclient\\discovery_cache\\documents\\essentialcontacts.v1.json',
   'DATA'),
  ('googleapiclient\\discovery_cache\\documents\\css.v1.json',
   'c:\\Users\\<USER>\\Desktop\\Mes_projets\\WritingToolsClean\\Windows_and_Linux\\myvenv\\Lib\\site-packages\\googleapiclient\\discovery_cache\\documents\\css.v1.json',
   'DATA'),
  ('googleapiclient\\discovery_cache\\documents\\texttospeech.v1beta1.json',
   'c:\\Users\\<USER>\\Desktop\\Mes_projets\\WritingToolsClean\\Windows_and_Linux\\myvenv\\Lib\\site-packages\\googleapiclient\\discovery_cache\\documents\\texttospeech.v1beta1.json',
   'DATA'),
  ('googleapiclient\\discovery_cache\\documents\\dataform.v1beta1.json',
   'c:\\Users\\<USER>\\Desktop\\Mes_projets\\WritingToolsClean\\Windows_and_Linux\\myvenv\\Lib\\site-packages\\googleapiclient\\discovery_cache\\documents\\dataform.v1beta1.json',
   'DATA'),
  ('googleapiclient\\discovery_cache\\documents\\drivelabels.v2.json',
   'c:\\Users\\<USER>\\Desktop\\Mes_projets\\WritingToolsClean\\Windows_and_Linux\\myvenv\\Lib\\site-packages\\googleapiclient\\discovery_cache\\documents\\drivelabels.v2.json',
   'DATA'),
  ('googleapiclient\\discovery_cache\\documents\\cloudasset.v1p4beta1.json',
   'c:\\Users\\<USER>\\Desktop\\Mes_projets\\WritingToolsClean\\Windows_and_Linux\\myvenv\\Lib\\site-packages\\googleapiclient\\discovery_cache\\documents\\cloudasset.v1p4beta1.json',
   'DATA'),
  ('googleapiclient\\discovery_cache\\documents\\retail.v2.json',
   'c:\\Users\\<USER>\\Desktop\\Mes_projets\\WritingToolsClean\\Windows_and_Linux\\myvenv\\Lib\\site-packages\\googleapiclient\\discovery_cache\\documents\\retail.v2.json',
   'DATA'),
  ('googleapiclient\\discovery_cache\\documents\\mybusinesslodging.v1.json',
   'c:\\Users\\<USER>\\Desktop\\Mes_projets\\WritingToolsClean\\Windows_and_Linux\\myvenv\\Lib\\site-packages\\googleapiclient\\discovery_cache\\documents\\mybusinesslodging.v1.json',
   'DATA'),
  ('googleapiclient\\discovery_cache\\documents\\baremetalsolution.v1.json',
   'c:\\Users\\<USER>\\Desktop\\Mes_projets\\WritingToolsClean\\Windows_and_Linux\\myvenv\\Lib\\site-packages\\googleapiclient\\discovery_cache\\documents\\baremetalsolution.v1.json',
   'DATA'),
  ('googleapiclient\\discovery_cache\\documents\\baremetalsolution.v1alpha1.json',
   'c:\\Users\\<USER>\\Desktop\\Mes_projets\\WritingToolsClean\\Windows_and_Linux\\myvenv\\Lib\\site-packages\\googleapiclient\\discovery_cache\\documents\\baremetalsolution.v1alpha1.json',
   'DATA'),
  ('googleapiclient\\discovery_cache\\documents\\file.v1beta1.json',
   'c:\\Users\\<USER>\\Desktop\\Mes_projets\\WritingToolsClean\\Windows_and_Linux\\myvenv\\Lib\\site-packages\\googleapiclient\\discovery_cache\\documents\\file.v1beta1.json',
   'DATA'),
  ('googleapiclient\\discovery_cache\\documents\\datafusion.v1beta1.json',
   'c:\\Users\\<USER>\\Desktop\\Mes_projets\\WritingToolsClean\\Windows_and_Linux\\myvenv\\Lib\\site-packages\\googleapiclient\\discovery_cache\\documents\\datafusion.v1beta1.json',
   'DATA'),
  ('googleapiclient\\discovery_cache\\documents\\serviceusage.v1beta1.json',
   'c:\\Users\\<USER>\\Desktop\\Mes_projets\\WritingToolsClean\\Windows_and_Linux\\myvenv\\Lib\\site-packages\\googleapiclient\\discovery_cache\\documents\\serviceusage.v1beta1.json',
   'DATA'),
  ('googleapiclient\\discovery_cache\\documents\\jobs.v4.json',
   'c:\\Users\\<USER>\\Desktop\\Mes_projets\\WritingToolsClean\\Windows_and_Linux\\myvenv\\Lib\\site-packages\\googleapiclient\\discovery_cache\\documents\\jobs.v4.json',
   'DATA'),
  ('googleapiclient\\discovery_cache\\documents\\pubsub.v1beta2.json',
   'c:\\Users\\<USER>\\Desktop\\Mes_projets\\WritingToolsClean\\Windows_and_Linux\\myvenv\\Lib\\site-packages\\googleapiclient\\discovery_cache\\documents\\pubsub.v1beta2.json',
   'DATA'),
  ('googleapiclient\\discovery_cache\\documents\\oslogin.v1alpha.json',
   'c:\\Users\\<USER>\\Desktop\\Mes_projets\\WritingToolsClean\\Windows_and_Linux\\myvenv\\Lib\\site-packages\\googleapiclient\\discovery_cache\\documents\\oslogin.v1alpha.json',
   'DATA'),
  ('googleapiclient\\discovery_cache\\documents\\acmedns.v1.json',
   'c:\\Users\\<USER>\\Desktop\\Mes_projets\\WritingToolsClean\\Windows_and_Linux\\myvenv\\Lib\\site-packages\\googleapiclient\\discovery_cache\\documents\\acmedns.v1.json',
   'DATA'),
  ('googleapiclient\\discovery_cache\\documents\\servicecontrol.v1.json',
   'c:\\Users\\<USER>\\Desktop\\Mes_projets\\WritingToolsClean\\Windows_and_Linux\\myvenv\\Lib\\site-packages\\googleapiclient\\discovery_cache\\documents\\servicecontrol.v1.json',
   'DATA'),
  ('googleapiclient\\discovery_cache\\documents\\recaptchaenterprise.v1.json',
   'c:\\Users\\<USER>\\Desktop\\Mes_projets\\WritingToolsClean\\Windows_and_Linux\\myvenv\\Lib\\site-packages\\googleapiclient\\discovery_cache\\documents\\recaptchaenterprise.v1.json',
   'DATA'),
  ('googleapiclient\\discovery_cache\\documents\\merchantapi.datasources_v1beta.json',
   'c:\\Users\\<USER>\\Desktop\\Mes_projets\\WritingToolsClean\\Windows_and_Linux\\myvenv\\Lib\\site-packages\\googleapiclient\\discovery_cache\\documents\\merchantapi.datasources_v1beta.json',
   'DATA'),
  ('googleapiclient\\discovery_cache\\documents\\workloadmanager.v1.json',
   'c:\\Users\\<USER>\\Desktop\\Mes_projets\\WritingToolsClean\\Windows_and_Linux\\myvenv\\Lib\\site-packages\\googleapiclient\\discovery_cache\\documents\\workloadmanager.v1.json',
   'DATA'),
  ('googleapiclient\\discovery_cache\\documents\\adexperiencereport.v1.json',
   'c:\\Users\\<USER>\\Desktop\\Mes_projets\\WritingToolsClean\\Windows_and_Linux\\myvenv\\Lib\\site-packages\\googleapiclient\\discovery_cache\\documents\\adexperiencereport.v1.json',
   'DATA'),
  ('googleapiclient\\discovery_cache\\documents\\cloudresourcemanager.v2.json',
   'c:\\Users\\<USER>\\Desktop\\Mes_projets\\WritingToolsClean\\Windows_and_Linux\\myvenv\\Lib\\site-packages\\googleapiclient\\discovery_cache\\documents\\cloudresourcemanager.v2.json',
   'DATA'),
  ('googleapiclient\\discovery_cache\\documents\\videointelligence.v1.json',
   'c:\\Users\\<USER>\\Desktop\\Mes_projets\\WritingToolsClean\\Windows_and_Linux\\myvenv\\Lib\\site-packages\\googleapiclient\\discovery_cache\\documents\\videointelligence.v1.json',
   'DATA'),
  ('googleapiclient\\discovery_cache\\documents\\language.v2.json',
   'c:\\Users\\<USER>\\Desktop\\Mes_projets\\WritingToolsClean\\Windows_and_Linux\\myvenv\\Lib\\site-packages\\googleapiclient\\discovery_cache\\documents\\language.v2.json',
   'DATA'),
  ('googleapiclient\\discovery_cache\\documents\\marketingplatformadmin.v1alpha.json',
   'c:\\Users\\<USER>\\Desktop\\Mes_projets\\WritingToolsClean\\Windows_and_Linux\\myvenv\\Lib\\site-packages\\googleapiclient\\discovery_cache\\documents\\marketingplatformadmin.v1alpha.json',
   'DATA'),
  ('googleapiclient\\discovery_cache\\documents\\securitycenter.v1beta1.json',
   'c:\\Users\\<USER>\\Desktop\\Mes_projets\\WritingToolsClean\\Windows_and_Linux\\myvenv\\Lib\\site-packages\\googleapiclient\\discovery_cache\\documents\\securitycenter.v1beta1.json',
   'DATA'),
  ('googleapiclient\\discovery_cache\\documents\\trafficdirector.v2.json',
   'c:\\Users\\<USER>\\Desktop\\Mes_projets\\WritingToolsClean\\Windows_and_Linux\\myvenv\\Lib\\site-packages\\googleapiclient\\discovery_cache\\documents\\trafficdirector.v2.json',
   'DATA'),
  ('googleapiclient\\discovery_cache\\documents\\games.v1.json',
   'c:\\Users\\<USER>\\Desktop\\Mes_projets\\WritingToolsClean\\Windows_and_Linux\\myvenv\\Lib\\site-packages\\googleapiclient\\discovery_cache\\documents\\games.v1.json',
   'DATA'),
  ('googleapiclient\\discovery_cache\\documents\\storagetransfer.v1.json',
   'c:\\Users\\<USER>\\Desktop\\Mes_projets\\WritingToolsClean\\Windows_and_Linux\\myvenv\\Lib\\site-packages\\googleapiclient\\discovery_cache\\documents\\storagetransfer.v1.json',
   'DATA'),
  ('googleapiclient\\discovery_cache\\documents\\authorizedbuyersmarketplace.v1.json',
   'c:\\Users\\<USER>\\Desktop\\Mes_projets\\WritingToolsClean\\Windows_and_Linux\\myvenv\\Lib\\site-packages\\googleapiclient\\discovery_cache\\documents\\authorizedbuyersmarketplace.v1.json',
   'DATA'),
  ('googleapiclient\\discovery_cache\\documents\\drive.v3.json',
   'c:\\Users\\<USER>\\Desktop\\Mes_projets\\WritingToolsClean\\Windows_and_Linux\\myvenv\\Lib\\site-packages\\googleapiclient\\discovery_cache\\documents\\drive.v3.json',
   'DATA'),
  ('googleapiclient\\discovery_cache\\documents\\datalineage.v1.json',
   'c:\\Users\\<USER>\\Desktop\\Mes_projets\\WritingToolsClean\\Windows_and_Linux\\myvenv\\Lib\\site-packages\\googleapiclient\\discovery_cache\\documents\\datalineage.v1.json',
   'DATA'),
  ('googleapiclient\\discovery_cache\\documents\\firebasestorage.v1beta.json',
   'c:\\Users\\<USER>\\Desktop\\Mes_projets\\WritingToolsClean\\Windows_and_Linux\\myvenv\\Lib\\site-packages\\googleapiclient\\discovery_cache\\documents\\firebasestorage.v1beta.json',
   'DATA'),
  ('googleapiclient\\discovery_cache\\documents\\merchantapi.issueresolution_v1beta.json',
   'c:\\Users\\<USER>\\Desktop\\Mes_projets\\WritingToolsClean\\Windows_and_Linux\\myvenv\\Lib\\site-packages\\googleapiclient\\discovery_cache\\documents\\merchantapi.issueresolution_v1beta.json',
   'DATA'),
  ('httplib2\\cacerts.txt',
   'c:\\Users\\<USER>\\Desktop\\Mes_projets\\WritingToolsClean\\Windows_and_Linux\\myvenv\\Lib\\site-packages\\httplib2\\cacerts.txt',
   'DATA'),
  ('PySide6\\translations\\qt_help_cs.qm',
   'C:\\Users\\<USER>\\Desktop\\Mes_projets\\WritingToolsClean\\Windows_and_Linux\\myvenv\\Lib\\site-packages\\PySide6\\translations\\qt_help_cs.qm',
   'DATA'),
  ('PySide6\\translations\\qtbase_pl.qm',
   'C:\\Users\\<USER>\\Desktop\\Mes_projets\\WritingToolsClean\\Windows_and_Linux\\myvenv\\Lib\\site-packages\\PySide6\\translations\\qtbase_pl.qm',
   'DATA'),
  ('PySide6\\translations\\qt_uk.qm',
   'C:\\Users\\<USER>\\Desktop\\Mes_projets\\WritingToolsClean\\Windows_and_Linux\\myvenv\\Lib\\site-packages\\PySide6\\translations\\qt_uk.qm',
   'DATA'),
  ('PySide6\\translations\\qt_tr.qm',
   'C:\\Users\\<USER>\\Desktop\\Mes_projets\\WritingToolsClean\\Windows_and_Linux\\myvenv\\Lib\\site-packages\\PySide6\\translations\\qt_tr.qm',
   'DATA'),
  ('PySide6\\translations\\qtbase_en.qm',
   'C:\\Users\\<USER>\\Desktop\\Mes_projets\\WritingToolsClean\\Windows_and_Linux\\myvenv\\Lib\\site-packages\\PySide6\\translations\\qtbase_en.qm',
   'DATA'),
  ('PySide6\\translations\\qt_hu.qm',
   'C:\\Users\\<USER>\\Desktop\\Mes_projets\\WritingToolsClean\\Windows_and_Linux\\myvenv\\Lib\\site-packages\\PySide6\\translations\\qt_hu.qm',
   'DATA'),
  ('PySide6\\translations\\qt_it.qm',
   'C:\\Users\\<USER>\\Desktop\\Mes_projets\\WritingToolsClean\\Windows_and_Linux\\myvenv\\Lib\\site-packages\\PySide6\\translations\\qt_it.qm',
   'DATA'),
  ('PySide6\\translations\\qt_ko.qm',
   'C:\\Users\\<USER>\\Desktop\\Mes_projets\\WritingToolsClean\\Windows_and_Linux\\myvenv\\Lib\\site-packages\\PySide6\\translations\\qt_ko.qm',
   'DATA'),
  ('PySide6\\translations\\qt_gd.qm',
   'C:\\Users\\<USER>\\Desktop\\Mes_projets\\WritingToolsClean\\Windows_and_Linux\\myvenv\\Lib\\site-packages\\PySide6\\translations\\qt_gd.qm',
   'DATA'),
  ('PySide6\\translations\\qt_help_es.qm',
   'C:\\Users\\<USER>\\Desktop\\Mes_projets\\WritingToolsClean\\Windows_and_Linux\\myvenv\\Lib\\site-packages\\PySide6\\translations\\qt_help_es.qm',
   'DATA'),
  ('PySide6\\translations\\qt_fi.qm',
   'C:\\Users\\<USER>\\Desktop\\Mes_projets\\WritingToolsClean\\Windows_and_Linux\\myvenv\\Lib\\site-packages\\PySide6\\translations\\qt_fi.qm',
   'DATA'),
  ('PySide6\\translations\\qt_help_bg.qm',
   'C:\\Users\\<USER>\\Desktop\\Mes_projets\\WritingToolsClean\\Windows_and_Linux\\myvenv\\Lib\\site-packages\\PySide6\\translations\\qt_help_bg.qm',
   'DATA'),
  ('PySide6\\translations\\qt_sv.qm',
   'C:\\Users\\<USER>\\Desktop\\Mes_projets\\WritingToolsClean\\Windows_and_Linux\\myvenv\\Lib\\site-packages\\PySide6\\translations\\qt_sv.qm',
   'DATA'),
  ('PySide6\\translations\\qtbase_hu.qm',
   'C:\\Users\\<USER>\\Desktop\\Mes_projets\\WritingToolsClean\\Windows_and_Linux\\myvenv\\Lib\\site-packages\\PySide6\\translations\\qtbase_hu.qm',
   'DATA'),
  ('PySide6\\translations\\qt_es.qm',
   'C:\\Users\\<USER>\\Desktop\\Mes_projets\\WritingToolsClean\\Windows_and_Linux\\myvenv\\Lib\\site-packages\\PySide6\\translations\\qt_es.qm',
   'DATA'),
  ('PySide6\\translations\\qtbase_zh_CN.qm',
   'C:\\Users\\<USER>\\Desktop\\Mes_projets\\WritingToolsClean\\Windows_and_Linux\\myvenv\\Lib\\site-packages\\PySide6\\translations\\qtbase_zh_CN.qm',
   'DATA'),
  ('PySide6\\translations\\qtbase_nl.qm',
   'C:\\Users\\<USER>\\Desktop\\Mes_projets\\WritingToolsClean\\Windows_and_Linux\\myvenv\\Lib\\site-packages\\PySide6\\translations\\qtbase_nl.qm',
   'DATA'),
  ('PySide6\\translations\\qt_help_pt_BR.qm',
   'C:\\Users\\<USER>\\Desktop\\Mes_projets\\WritingToolsClean\\Windows_and_Linux\\myvenv\\Lib\\site-packages\\PySide6\\translations\\qt_help_pt_BR.qm',
   'DATA'),
  ('PySide6\\translations\\qt_help_sl.qm',
   'C:\\Users\\<USER>\\Desktop\\Mes_projets\\WritingToolsClean\\Windows_and_Linux\\myvenv\\Lib\\site-packages\\PySide6\\translations\\qt_help_sl.qm',
   'DATA'),
  ('PySide6\\translations\\qtbase_de.qm',
   'C:\\Users\\<USER>\\Desktop\\Mes_projets\\WritingToolsClean\\Windows_and_Linux\\myvenv\\Lib\\site-packages\\PySide6\\translations\\qtbase_de.qm',
   'DATA'),
  ('PySide6\\translations\\qt_help_ru.qm',
   'C:\\Users\\<USER>\\Desktop\\Mes_projets\\WritingToolsClean\\Windows_and_Linux\\myvenv\\Lib\\site-packages\\PySide6\\translations\\qt_help_ru.qm',
   'DATA'),
  ('PySide6\\translations\\qt_ar.qm',
   'C:\\Users\\<USER>\\Desktop\\Mes_projets\\WritingToolsClean\\Windows_and_Linux\\myvenv\\Lib\\site-packages\\PySide6\\translations\\qt_ar.qm',
   'DATA'),
  ('PySide6\\translations\\qt_fa.qm',
   'C:\\Users\\<USER>\\Desktop\\Mes_projets\\WritingToolsClean\\Windows_and_Linux\\myvenv\\Lib\\site-packages\\PySide6\\translations\\qt_fa.qm',
   'DATA'),
  ('PySide6\\translations\\qtbase_zh_TW.qm',
   'C:\\Users\\<USER>\\Desktop\\Mes_projets\\WritingToolsClean\\Windows_and_Linux\\myvenv\\Lib\\site-packages\\PySide6\\translations\\qtbase_zh_TW.qm',
   'DATA'),
  ('PySide6\\translations\\qt_help_nn.qm',
   'C:\\Users\\<USER>\\Desktop\\Mes_projets\\WritingToolsClean\\Windows_and_Linux\\myvenv\\Lib\\site-packages\\PySide6\\translations\\qt_help_nn.qm',
   'DATA'),
  ('PySide6\\translations\\qt_ja.qm',
   'C:\\Users\\<USER>\\Desktop\\Mes_projets\\WritingToolsClean\\Windows_and_Linux\\myvenv\\Lib\\site-packages\\PySide6\\translations\\qt_ja.qm',
   'DATA'),
  ('PySide6\\translations\\qt_help_nl.qm',
   'C:\\Users\\<USER>\\Desktop\\Mes_projets\\WritingToolsClean\\Windows_and_Linux\\myvenv\\Lib\\site-packages\\PySide6\\translations\\qt_help_nl.qm',
   'DATA'),
  ('PySide6\\translations\\qtbase_lv.qm',
   'C:\\Users\\<USER>\\Desktop\\Mes_projets\\WritingToolsClean\\Windows_and_Linux\\myvenv\\Lib\\site-packages\\PySide6\\translations\\qtbase_lv.qm',
   'DATA'),
  ('PySide6\\translations\\qtbase_ko.qm',
   'C:\\Users\\<USER>\\Desktop\\Mes_projets\\WritingToolsClean\\Windows_and_Linux\\myvenv\\Lib\\site-packages\\PySide6\\translations\\qtbase_ko.qm',
   'DATA'),
  ('PySide6\\translations\\qt_en.qm',
   'C:\\Users\\<USER>\\Desktop\\Mes_projets\\WritingToolsClean\\Windows_and_Linux\\myvenv\\Lib\\site-packages\\PySide6\\translations\\qt_en.qm',
   'DATA'),
  ('PySide6\\translations\\qt_help_ja.qm',
   'C:\\Users\\<USER>\\Desktop\\Mes_projets\\WritingToolsClean\\Windows_and_Linux\\myvenv\\Lib\\site-packages\\PySide6\\translations\\qt_help_ja.qm',
   'DATA'),
  ('PySide6\\translations\\qt_ru.qm',
   'C:\\Users\\<USER>\\Desktop\\Mes_projets\\WritingToolsClean\\Windows_and_Linux\\myvenv\\Lib\\site-packages\\PySide6\\translations\\qt_ru.qm',
   'DATA'),
  ('PySide6\\translations\\qtbase_es.qm',
   'C:\\Users\\<USER>\\Desktop\\Mes_projets\\WritingToolsClean\\Windows_and_Linux\\myvenv\\Lib\\site-packages\\PySide6\\translations\\qtbase_es.qm',
   'DATA'),
  ('PySide6\\translations\\qtbase_ru.qm',
   'C:\\Users\\<USER>\\Desktop\\Mes_projets\\WritingToolsClean\\Windows_and_Linux\\myvenv\\Lib\\site-packages\\PySide6\\translations\\qtbase_ru.qm',
   'DATA'),
  ('PySide6\\translations\\qt_help_zh_TW.qm',
   'C:\\Users\\<USER>\\Desktop\\Mes_projets\\WritingToolsClean\\Windows_and_Linux\\myvenv\\Lib\\site-packages\\PySide6\\translations\\qt_help_zh_TW.qm',
   'DATA'),
  ('PySide6\\translations\\qt_cs.qm',
   'C:\\Users\\<USER>\\Desktop\\Mes_projets\\WritingToolsClean\\Windows_and_Linux\\myvenv\\Lib\\site-packages\\PySide6\\translations\\qt_cs.qm',
   'DATA'),
  ('PySide6\\translations\\qt_nl.qm',
   'C:\\Users\\<USER>\\Desktop\\Mes_projets\\WritingToolsClean\\Windows_and_Linux\\myvenv\\Lib\\site-packages\\PySide6\\translations\\qt_nl.qm',
   'DATA'),
  ('PySide6\\translations\\qt_sl.qm',
   'C:\\Users\\<USER>\\Desktop\\Mes_projets\\WritingToolsClean\\Windows_and_Linux\\myvenv\\Lib\\site-packages\\PySide6\\translations\\qt_sl.qm',
   'DATA'),
  ('PySide6\\translations\\qtbase_bg.qm',
   'C:\\Users\\<USER>\\Desktop\\Mes_projets\\WritingToolsClean\\Windows_and_Linux\\myvenv\\Lib\\site-packages\\PySide6\\translations\\qtbase_bg.qm',
   'DATA'),
  ('PySide6\\translations\\qt_sk.qm',
   'C:\\Users\\<USER>\\Desktop\\Mes_projets\\WritingToolsClean\\Windows_and_Linux\\myvenv\\Lib\\site-packages\\PySide6\\translations\\qt_sk.qm',
   'DATA'),
  ('PySide6\\translations\\qt_zh_TW.qm',
   'C:\\Users\\<USER>\\Desktop\\Mes_projets\\WritingToolsClean\\Windows_and_Linux\\myvenv\\Lib\\site-packages\\PySide6\\translations\\qt_zh_TW.qm',
   'DATA'),
  ('PySide6\\translations\\qt_bg.qm',
   'C:\\Users\\<USER>\\Desktop\\Mes_projets\\WritingToolsClean\\Windows_and_Linux\\myvenv\\Lib\\site-packages\\PySide6\\translations\\qt_bg.qm',
   'DATA'),
  ('PySide6\\translations\\qt_pl.qm',
   'C:\\Users\\<USER>\\Desktop\\Mes_projets\\WritingToolsClean\\Windows_and_Linux\\myvenv\\Lib\\site-packages\\PySide6\\translations\\qt_pl.qm',
   'DATA'),
  ('PySide6\\translations\\qtbase_pt_BR.qm',
   'C:\\Users\\<USER>\\Desktop\\Mes_projets\\WritingToolsClean\\Windows_and_Linux\\myvenv\\Lib\\site-packages\\PySide6\\translations\\qtbase_pt_BR.qm',
   'DATA'),
  ('PySide6\\translations\\qt_lv.qm',
   'C:\\Users\\<USER>\\Desktop\\Mes_projets\\WritingToolsClean\\Windows_and_Linux\\myvenv\\Lib\\site-packages\\PySide6\\translations\\qt_lv.qm',
   'DATA'),
  ('PySide6\\translations\\qt_help_tr.qm',
   'C:\\Users\\<USER>\\Desktop\\Mes_projets\\WritingToolsClean\\Windows_and_Linux\\myvenv\\Lib\\site-packages\\PySide6\\translations\\qt_help_tr.qm',
   'DATA'),
  ('PySide6\\translations\\qt_ca.qm',
   'C:\\Users\\<USER>\\Desktop\\Mes_projets\\WritingToolsClean\\Windows_and_Linux\\myvenv\\Lib\\site-packages\\PySide6\\translations\\qt_ca.qm',
   'DATA'),
  ('PySide6\\translations\\qt_help_hu.qm',
   'C:\\Users\\<USER>\\Desktop\\Mes_projets\\WritingToolsClean\\Windows_and_Linux\\myvenv\\Lib\\site-packages\\PySide6\\translations\\qt_help_hu.qm',
   'DATA'),
  ('PySide6\\translations\\qtbase_fi.qm',
   'C:\\Users\\<USER>\\Desktop\\Mes_projets\\WritingToolsClean\\Windows_and_Linux\\myvenv\\Lib\\site-packages\\PySide6\\translations\\qtbase_fi.qm',
   'DATA'),
  ('PySide6\\translations\\qt_help_ka.qm',
   'C:\\Users\\<USER>\\Desktop\\Mes_projets\\WritingToolsClean\\Windows_and_Linux\\myvenv\\Lib\\site-packages\\PySide6\\translations\\qt_help_ka.qm',
   'DATA'),
  ('PySide6\\translations\\qt_ka.qm',
   'C:\\Users\\<USER>\\Desktop\\Mes_projets\\WritingToolsClean\\Windows_and_Linux\\myvenv\\Lib\\site-packages\\PySide6\\translations\\qt_ka.qm',
   'DATA'),
  ('PySide6\\translations\\qt_help_it.qm',
   'C:\\Users\\<USER>\\Desktop\\Mes_projets\\WritingToolsClean\\Windows_and_Linux\\myvenv\\Lib\\site-packages\\PySide6\\translations\\qt_help_it.qm',
   'DATA'),
  ('PySide6\\translations\\qt_pt_PT.qm',
   'C:\\Users\\<USER>\\Desktop\\Mes_projets\\WritingToolsClean\\Windows_and_Linux\\myvenv\\Lib\\site-packages\\PySide6\\translations\\qt_pt_PT.qm',
   'DATA'),
  ('PySide6\\translations\\qtbase_it.qm',
   'C:\\Users\\<USER>\\Desktop\\Mes_projets\\WritingToolsClean\\Windows_and_Linux\\myvenv\\Lib\\site-packages\\PySide6\\translations\\qtbase_it.qm',
   'DATA'),
  ('PySide6\\translations\\qt_nn.qm',
   'C:\\Users\\<USER>\\Desktop\\Mes_projets\\WritingToolsClean\\Windows_and_Linux\\myvenv\\Lib\\site-packages\\PySide6\\translations\\qt_nn.qm',
   'DATA'),
  ('PySide6\\translations\\qt_help_ar.qm',
   'C:\\Users\\<USER>\\Desktop\\Mes_projets\\WritingToolsClean\\Windows_and_Linux\\myvenv\\Lib\\site-packages\\PySide6\\translations\\qt_help_ar.qm',
   'DATA'),
  ('PySide6\\translations\\qtbase_he.qm',
   'C:\\Users\\<USER>\\Desktop\\Mes_projets\\WritingToolsClean\\Windows_and_Linux\\myvenv\\Lib\\site-packages\\PySide6\\translations\\qtbase_he.qm',
   'DATA'),
  ('PySide6\\translations\\qtbase_uk.qm',
   'C:\\Users\\<USER>\\Desktop\\Mes_projets\\WritingToolsClean\\Windows_and_Linux\\myvenv\\Lib\\site-packages\\PySide6\\translations\\qtbase_uk.qm',
   'DATA'),
  ('PySide6\\translations\\qtbase_nn.qm',
   'C:\\Users\\<USER>\\Desktop\\Mes_projets\\WritingToolsClean\\Windows_and_Linux\\myvenv\\Lib\\site-packages\\PySide6\\translations\\qtbase_nn.qm',
   'DATA'),
  ('PySide6\\translations\\qt_lt.qm',
   'C:\\Users\\<USER>\\Desktop\\Mes_projets\\WritingToolsClean\\Windows_and_Linux\\myvenv\\Lib\\site-packages\\PySide6\\translations\\qt_lt.qm',
   'DATA'),
  ('PySide6\\translations\\qt_da.qm',
   'C:\\Users\\<USER>\\Desktop\\Mes_projets\\WritingToolsClean\\Windows_and_Linux\\myvenv\\Lib\\site-packages\\PySide6\\translations\\qt_da.qm',
   'DATA'),
  ('PySide6\\translations\\qt_he.qm',
   'C:\\Users\\<USER>\\Desktop\\Mes_projets\\WritingToolsClean\\Windows_and_Linux\\myvenv\\Lib\\site-packages\\PySide6\\translations\\qt_he.qm',
   'DATA'),
  ('PySide6\\translations\\qt_help_de.qm',
   'C:\\Users\\<USER>\\Desktop\\Mes_projets\\WritingToolsClean\\Windows_and_Linux\\myvenv\\Lib\\site-packages\\PySide6\\translations\\qt_help_de.qm',
   'DATA'),
  ('PySide6\\translations\\qt_hr.qm',
   'C:\\Users\\<USER>\\Desktop\\Mes_projets\\WritingToolsClean\\Windows_and_Linux\\myvenv\\Lib\\site-packages\\PySide6\\translations\\qt_hr.qm',
   'DATA'),
  ('PySide6\\translations\\qtbase_cs.qm',
   'C:\\Users\\<USER>\\Desktop\\Mes_projets\\WritingToolsClean\\Windows_and_Linux\\myvenv\\Lib\\site-packages\\PySide6\\translations\\qtbase_cs.qm',
   'DATA'),
  ('PySide6\\translations\\qt_help_sk.qm',
   'C:\\Users\\<USER>\\Desktop\\Mes_projets\\WritingToolsClean\\Windows_and_Linux\\myvenv\\Lib\\site-packages\\PySide6\\translations\\qt_help_sk.qm',
   'DATA'),
  ('PySide6\\translations\\qtbase_fa.qm',
   'C:\\Users\\<USER>\\Desktop\\Mes_projets\\WritingToolsClean\\Windows_and_Linux\\myvenv\\Lib\\site-packages\\PySide6\\translations\\qtbase_fa.qm',
   'DATA'),
  ('PySide6\\translations\\qt_de.qm',
   'C:\\Users\\<USER>\\Desktop\\Mes_projets\\WritingToolsClean\\Windows_and_Linux\\myvenv\\Lib\\site-packages\\PySide6\\translations\\qt_de.qm',
   'DATA'),
  ('PySide6\\translations\\qt_help_pl.qm',
   'C:\\Users\\<USER>\\Desktop\\Mes_projets\\WritingToolsClean\\Windows_and_Linux\\myvenv\\Lib\\site-packages\\PySide6\\translations\\qt_help_pl.qm',
   'DATA'),
  ('PySide6\\translations\\qtbase_gd.qm',
   'C:\\Users\\<USER>\\Desktop\\Mes_projets\\WritingToolsClean\\Windows_and_Linux\\myvenv\\Lib\\site-packages\\PySide6\\translations\\qtbase_gd.qm',
   'DATA'),
  ('PySide6\\translations\\qtbase_hr.qm',
   'C:\\Users\\<USER>\\Desktop\\Mes_projets\\WritingToolsClean\\Windows_and_Linux\\myvenv\\Lib\\site-packages\\PySide6\\translations\\qtbase_hr.qm',
   'DATA'),
  ('PySide6\\translations\\qt_help_da.qm',
   'C:\\Users\\<USER>\\Desktop\\Mes_projets\\WritingToolsClean\\Windows_and_Linux\\myvenv\\Lib\\site-packages\\PySide6\\translations\\qt_help_da.qm',
   'DATA'),
  ('PySide6\\translations\\qtbase_ja.qm',
   'C:\\Users\\<USER>\\Desktop\\Mes_projets\\WritingToolsClean\\Windows_and_Linux\\myvenv\\Lib\\site-packages\\PySide6\\translations\\qtbase_ja.qm',
   'DATA'),
  ('PySide6\\translations\\qt_fr.qm',
   'C:\\Users\\<USER>\\Desktop\\Mes_projets\\WritingToolsClean\\Windows_and_Linux\\myvenv\\Lib\\site-packages\\PySide6\\translations\\qt_fr.qm',
   'DATA'),
  ('PySide6\\translations\\qtbase_ca.qm',
   'C:\\Users\\<USER>\\Desktop\\Mes_projets\\WritingToolsClean\\Windows_and_Linux\\myvenv\\Lib\\site-packages\\PySide6\\translations\\qtbase_ca.qm',
   'DATA'),
  ('PySide6\\translations\\qtbase_ka.qm',
   'C:\\Users\\<USER>\\Desktop\\Mes_projets\\WritingToolsClean\\Windows_and_Linux\\myvenv\\Lib\\site-packages\\PySide6\\translations\\qtbase_ka.qm',
   'DATA'),
  ('PySide6\\translations\\qt_help_ko.qm',
   'C:\\Users\\<USER>\\Desktop\\Mes_projets\\WritingToolsClean\\Windows_and_Linux\\myvenv\\Lib\\site-packages\\PySide6\\translations\\qt_help_ko.qm',
   'DATA'),
  ('PySide6\\translations\\qt_gl.qm',
   'C:\\Users\\<USER>\\Desktop\\Mes_projets\\WritingToolsClean\\Windows_and_Linux\\myvenv\\Lib\\site-packages\\PySide6\\translations\\qt_gl.qm',
   'DATA'),
  ('PySide6\\translations\\qt_help_ca.qm',
   'C:\\Users\\<USER>\\Desktop\\Mes_projets\\WritingToolsClean\\Windows_and_Linux\\myvenv\\Lib\\site-packages\\PySide6\\translations\\qt_help_ca.qm',
   'DATA'),
  ('PySide6\\translations\\qtbase_ar.qm',
   'C:\\Users\\<USER>\\Desktop\\Mes_projets\\WritingToolsClean\\Windows_and_Linux\\myvenv\\Lib\\site-packages\\PySide6\\translations\\qtbase_ar.qm',
   'DATA'),
  ('PySide6\\translations\\qtbase_da.qm',
   'C:\\Users\\<USER>\\Desktop\\Mes_projets\\WritingToolsClean\\Windows_and_Linux\\myvenv\\Lib\\site-packages\\PySide6\\translations\\qtbase_da.qm',
   'DATA'),
  ('PySide6\\translations\\qt_help_zh_CN.qm',
   'C:\\Users\\<USER>\\Desktop\\Mes_projets\\WritingToolsClean\\Windows_and_Linux\\myvenv\\Lib\\site-packages\\PySide6\\translations\\qt_help_zh_CN.qm',
   'DATA'),
  ('PySide6\\translations\\qt_help_gl.qm',
   'C:\\Users\\<USER>\\Desktop\\Mes_projets\\WritingToolsClean\\Windows_and_Linux\\myvenv\\Lib\\site-packages\\PySide6\\translations\\qt_help_gl.qm',
   'DATA'),
  ('PySide6\\translations\\qt_help_hr.qm',
   'C:\\Users\\<USER>\\Desktop\\Mes_projets\\WritingToolsClean\\Windows_and_Linux\\myvenv\\Lib\\site-packages\\PySide6\\translations\\qt_help_hr.qm',
   'DATA'),
  ('PySide6\\translations\\qt_zh_CN.qm',
   'C:\\Users\\<USER>\\Desktop\\Mes_projets\\WritingToolsClean\\Windows_and_Linux\\myvenv\\Lib\\site-packages\\PySide6\\translations\\qt_zh_CN.qm',
   'DATA'),
  ('PySide6\\translations\\qtbase_fr.qm',
   'C:\\Users\\<USER>\\Desktop\\Mes_projets\\WritingToolsClean\\Windows_and_Linux\\myvenv\\Lib\\site-packages\\PySide6\\translations\\qtbase_fr.qm',
   'DATA'),
  ('PySide6\\translations\\qtbase_tr.qm',
   'C:\\Users\\<USER>\\Desktop\\Mes_projets\\WritingToolsClean\\Windows_and_Linux\\myvenv\\Lib\\site-packages\\PySide6\\translations\\qtbase_tr.qm',
   'DATA'),
  ('PySide6\\translations\\qt_help_en.qm',
   'C:\\Users\\<USER>\\Desktop\\Mes_projets\\WritingToolsClean\\Windows_and_Linux\\myvenv\\Lib\\site-packages\\PySide6\\translations\\qt_help_en.qm',
   'DATA'),
  ('PySide6\\translations\\qt_help_uk.qm',
   'C:\\Users\\<USER>\\Desktop\\Mes_projets\\WritingToolsClean\\Windows_and_Linux\\myvenv\\Lib\\site-packages\\PySide6\\translations\\qt_help_uk.qm',
   'DATA'),
  ('PySide6\\translations\\qt_pt_BR.qm',
   'C:\\Users\\<USER>\\Desktop\\Mes_projets\\WritingToolsClean\\Windows_and_Linux\\myvenv\\Lib\\site-packages\\PySide6\\translations\\qt_pt_BR.qm',
   'DATA'),
  ('PySide6\\translations\\qt_help_fr.qm',
   'C:\\Users\\<USER>\\Desktop\\Mes_projets\\WritingToolsClean\\Windows_and_Linux\\myvenv\\Lib\\site-packages\\PySide6\\translations\\qt_help_fr.qm',
   'DATA'),
  ('PySide6\\translations\\qtbase_sk.qm',
   'C:\\Users\\<USER>\\Desktop\\Mes_projets\\WritingToolsClean\\Windows_and_Linux\\myvenv\\Lib\\site-packages\\PySide6\\translations\\qtbase_sk.qm',
   'DATA'),
  ('ollama-0.5.1.dist-info\\RECORD',
   'c:\\Users\\<USER>\\Desktop\\Mes_projets\\WritingToolsClean\\Windows_and_Linux\\myvenv\\Lib\\site-packages\\ollama-0.5.1.dist-info\\RECORD',
   'DATA'),
  ('ollama-0.5.1.dist-info\\WHEEL',
   'c:\\Users\\<USER>\\Desktop\\Mes_projets\\WritingToolsClean\\Windows_and_Linux\\myvenv\\Lib\\site-packages\\ollama-0.5.1.dist-info\\WHEEL',
   'DATA'),
  ('ollama-0.5.1.dist-info\\INSTALLER',
   'c:\\Users\\<USER>\\Desktop\\Mes_projets\\WritingToolsClean\\Windows_and_Linux\\myvenv\\Lib\\site-packages\\ollama-0.5.1.dist-info\\INSTALLER',
   'DATA'),
  ('ollama-0.5.1.dist-info\\licenses\\LICENSE',
   'c:\\Users\\<USER>\\Desktop\\Mes_projets\\WritingToolsClean\\Windows_and_Linux\\myvenv\\Lib\\site-packages\\ollama-0.5.1.dist-info\\licenses\\LICENSE',
   'DATA'),
  ('ollama-0.5.1.dist-info\\METADATA',
   'c:\\Users\\<USER>\\Desktop\\Mes_projets\\WritingToolsClean\\Windows_and_Linux\\myvenv\\Lib\\site-packages\\ollama-0.5.1.dist-info\\METADATA',
   'DATA'),
  ('ollama-0.5.1.dist-info\\REQUESTED',
   'c:\\Users\\<USER>\\Desktop\\Mes_projets\\WritingToolsClean\\Windows_and_Linux\\myvenv\\Lib\\site-packages\\ollama-0.5.1.dist-info\\REQUESTED',
   'DATA'),
  ('base_library.zip',
   'c:\\Users\\<USER>\\Desktop\\Mes_projets\\WritingToolsClean\\Windows_and_Linux\\build\\Writing '
   'Tools\\base_library.zip',
   'DATA')],
 'python312.dll',
 False,
 False,
 False,
 [],
 None,
 None,
 None)
